package com.fh.cloud.screen.websocket.manager;

import com.fh.cloud.screen.websocket.dto.SessionDto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 会话管理器
 *
 * <AUTHOR>
 * @date 2021 /2/4 9:36
 */
public class SessionManager {

    /**
     * 实例
     */
    private static SessionManager sessionManager;

    /**
     * 连接会话 channel --> session
     */
    private Map<String, SessionDto> channelSessionMap = new ConcurrentHashMap<>();
    /**
     * 连接会话 clientId --> session
     */
    private Map<String, SessionDto> clientSessionMap = new ConcurrentHashMap<>();

    /**
     * 业务存储 roomId --> clientIds
     */
    private Map<String, List<String>> roomClientMap = new ConcurrentHashMap<>();

    /**
     * Instantiates a new Session manager.
     */
    private SessionManager() {}

    /**
     * 实例化
     *
     * @return instance instance
     * <AUTHOR>
     * @date 2021 -02-04 13:43:19
     */
    public static SessionManager getInstance() {
        if (sessionManager == null) {
            synchronized (SessionManager.class) {
                if (sessionManager == null) {
                    sessionManager = new SessionManager();
                }
            }
        }
        return sessionManager;
    }

    /**
     * 添加channel
     *
     * @param channelId the channel id
     * @param sessionDto the session dto
     * <AUTHOR>
     * @date 2021 -02-04 13:50:03
     */
    public void addChannel(String channelId, SessionDto sessionDto) {
        this.channelSessionMap.put(channelId, sessionDto);
    }

    /**
     * 返回channelSessionMap
     *
     * @return channel session map
     * <AUTHOR>
     * @date 2021 -02-04 13:50:03
     */
    public Map<String, SessionDto> getChannelSessionMap() {
        return this.channelSessionMap;
    }

    /**
     * 添加client
     *
     * @param clientId the client id
     * @param sessionDto the session dto
     * <AUTHOR>
     * @date 2021 -02-04 13:50:03
     */
    public void addClient(String clientId, SessionDto sessionDto) {
        this.clientSessionMap.put(clientId, sessionDto);
    }

    /**
     * 返回clientSessionMap
     *
     * @return client session map
     * <AUTHOR>
     * @date 2021 -02-04 13:50:03
     */
    public Map<String, SessionDto> getClientSessionMap() {
        return this.clientSessionMap;
    }

}
