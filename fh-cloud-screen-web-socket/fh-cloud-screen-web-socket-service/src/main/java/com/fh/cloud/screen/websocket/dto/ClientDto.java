package com.fh.cloud.screen.websocket.dto;

import java.io.Serializable;

/**
 * 客户端Dto
 * 
 * <AUTHOR>
 * @date 2021/2/23 18:53
 */
public class ClientDto<T> implements Serializable {
    /**
     * 状态码
     */
    private String code;
    /**
     * 状态信息
     */
    private String message;
    /**
     * 数据
     */
    private T value;
    /**
     * 签名
     */
    private String sign;

    public ClientDto() {}

    public ClientDto(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public ClientDto(String code, T value) {
        this.code = code;
        this.value = value;
    }

    public ClientDto(String code, String message, T value) {
        this.code = code;
        this.message = message;
        this.value = value;
    }

    public ClientDto(String code, T value, String sign) {
        this.code = code;
        this.value = value;
        this.sign = sign;
    }

    public ClientDto(String code, String message, String sign) {
        this.code = code;
        this.message = message;
        this.sign = sign;
    }

    public ClientDto(String code, String message, T value, String sign) {
        this.code = code;
        this.message = message;
        this.value = value;
        this.sign = sign;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public T getValue() {
        return this.value;
    }

    public void setValue(T value) {
        this.value = value;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSign() {
        return this.sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
