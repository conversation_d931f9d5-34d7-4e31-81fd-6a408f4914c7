package com.fh.cloud.screen.websocket.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.websocekt.api.message.api.MessageApi;
import com.fh.cloud.screen.websocekt.api.message.entity.bo.WsSendBo;
import com.fh.cloud.screen.websocket.verticle.WebSocketVerticle;
import com.light.core.entity.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ws的处理类。websocekt改为vertx处理
 *
 * <AUTHOR>
 * @date 2022/4/6 16:17
 */
@Slf4j
@RestController
public class WsServerController implements MessageApi {

    @Autowired
    private WebSocketVerticle webSocketVerticle;

    /**
     * 查看当前的webSocket的session
     *
     * @return
     */
    @ResponseBody
    public AjaxResult sessionLook() {
        return AjaxResult.success(WebSocketVerticle.getRoomClientMap());
    }

    @Override
    public AjaxResult channelSessionDto() {
        return AjaxResult.success(webSocketVerticle.getChannelSessionMap());
    }

    @Override
    public AjaxResult clientSessionDto() {
        return AjaxResult.success(webSocketVerticle.getClientSessionMap());
    }

    /**
     * 查看指定房间的所有连接
     *
     * @return
     */
    @ResponseBody
    public AjaxResult sessionLookOne(@RequestParam String roomId) {
        return AjaxResult.success(WebSocketVerticle.getRoomClientMap().get(roomId));
    }

    /**
     * 查看当前的webSocket的session
     *
     * @param wsSendBo the ws send bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -05-06 10:04:40
     */
    @ResponseBody
    public AjaxResult sendWs(@RequestBody WsSendBo wsSendBo) {
        try {
            webSocketVerticle.sendServer2Client(wsSendBo.getRoomId(), wsSendBo.getClientIds(), wsSendBo.getValue());
            if (log.isInfoEnabled()) {
                try {
                    log.info("send-ws:" + JSONObject.toJSONString(wsSendBo));
                } catch (Exception e) {
                    log.error("send-ws-log-error:", e);
                }
            }
            return AjaxResult.success("成功");
        } catch (Exception e) {
            log.error("send-ws-error:", e);
            return AjaxResult.fail("失败");
        }
    }

    /**
     * 批量查询房间的所有连接
     *
     * @param roomIds
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/2 11:36
     **/
    @Override
    public AjaxResult sessionLookBatch(List<String> roomIds) {
        List<String> result = Lists.newArrayList();
        for (String roomId : roomIds) {
            List<String> list = WebSocketVerticle.getRoomClientMap().get(roomId);
            if (CollectionUtils.isNotEmpty(list)) {
                result.addAll(list);
            }
        }
        return AjaxResult.success(result);
    }
}
