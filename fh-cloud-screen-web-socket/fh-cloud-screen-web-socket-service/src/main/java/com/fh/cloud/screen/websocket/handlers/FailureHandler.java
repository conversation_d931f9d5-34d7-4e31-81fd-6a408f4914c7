package com.fh.cloud.screen.websocket.handlers;

import io.vertx.core.Handler;
import io.vertx.core.impl.logging.Logger;
import io.vertx.core.impl.logging.LoggerFactory;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import javax.xml.bind.ValidationException;

public class <PERSON>ailureH<PERSON>ler implements Handler<RoutingContext> {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void handle(RoutingContext context) {

        Throwable thrown = context.failure();
        String userId = context.request().getHeader("Authorization");
        recordError(userId, thrown);

        if (thrown instanceof ValidationException) {
            context.response().setStatusCode(HttpStatus.SC_BAD_REQUEST).end(thrown.getMessage());
        } else {
            context.response().setStatusCode(HttpStatus.SC_INTERNAL_SERVER_ERROR).end(thrown.getMessage());
        }
    }

    private void recordError(String userId, Throwable thrown) {
        String dynamicMetadata = "";
        if (userId != null) {
            dynamicMetadata = String.format("userId=%s ", userId);
        }

        logger.error(dynamicMetadata + thrown.getMessage());
    }
}