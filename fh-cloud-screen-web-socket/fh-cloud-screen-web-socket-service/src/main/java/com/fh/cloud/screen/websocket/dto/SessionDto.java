package com.fh.cloud.screen.websocket.dto;

import com.fh.cloud.screen.websocket.enums.CmdType;
import io.vertx.core.http.ServerWebSocket;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/1 19:52
 */
public class SessionDto {
    /**
     * 客户端连接id，系统生成的。
     */
    private String id;
    /**
     * 客户端对应的webSocket连接
     */
    private ServerWebSocket webSocket;
    /**
     * 客户端id
     */
    private String clientId;
    /**
     * 客户端对应的房间id
     */
    private String roomId;
    /**
     * 主备类型，客户端属性。{@link CmdType}
     */
    private Integer clientType;
    /**
     * 房间内的所有客户端，包含本客户端
     */
    private List<String> clientIds = new ArrayList<>();
    /**
     * 本房间内的主客户端id
     */
    private String mainClientId;

    /**
     * websocket分片text数据临时存储
     */
    private StringBuffer data = new StringBuffer();

    public SessionDto() {}

    public SessionDto(String id, ServerWebSocket webSocket) {
        this.id = id;
        this.webSocket = webSocket;
    }

    public StringBuffer getData() {
        return data;
    }

    public void setData(StringBuffer data) {
        this.data = data;
    }

    public String getMainClientId() {
        return mainClientId;
    }

    public void setMainClientId(String mainClientId) {
        this.mainClientId = mainClientId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public ServerWebSocket getWebSocket() {
        return webSocket;
    }

    public void setWebSocket(ServerWebSocket webSocket) {
        this.webSocket = webSocket;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public List<String> getClientIds() {
        return clientIds;
    }

    public void setClientIds(List<String> clientIds) {
        this.clientIds = clientIds;
    }
}
