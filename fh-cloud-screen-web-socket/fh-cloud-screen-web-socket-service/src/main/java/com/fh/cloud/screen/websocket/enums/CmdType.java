package com.fh.cloud.screen.websocket.enums;

/**
 * 命令类型
 * 
 * <AUTHOR>
 * @date 2021/2/1 20:03
 */
public enum CmdType {
    /**
     * 登录
     */
    LOGIN(0),
    /**
     * 登录响应
     */
    LOGIN_RESPONSE(1),
    /***
     * 广播(透传)
     */
    BROADCAST(2),;

    private Integer value;

    CmdType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}
