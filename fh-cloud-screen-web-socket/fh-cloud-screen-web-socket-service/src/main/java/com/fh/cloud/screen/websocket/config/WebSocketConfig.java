package com.fh.cloud.screen.websocket.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * webSocket服务的自配置。注释改为vertx处理
 *
 * <AUTHOR>
 * @date 2022/4/6 16:15
 */
@Configuration
public class WebSocketConfig {

    // @Bean
    // public ServerEndpointExporter serverEndpoint() {
    // return new ServerEndpointExporter();
    // }
}
