package com.fh.cloud.screen.websocket.dto;

import com.fh.cloud.screen.websocket.enums.CmdType;

import java.io.Serializable;

/**
 * webSocketData，消息请求体。消息协议。
 * 
 * <AUTHOR>
 * @date 2021/2/1 19:10
 */
public class WebSocketData implements Serializable {
    /**
     * 消息类型{@link CmdType}
     */
    private Integer cmd;
    /**
     * 消息头
     */
    private HeaderDto header;
    /**
     * websocket消息体(接收请求的时候是String，发送请求的时候是ClientDto)
     */
    private Object body;

    public WebSocketData() {}

    public Integer getCmd() {
        return cmd;
    }

    public void setCmd(Integer cmd) {
        this.cmd = cmd;
    }

    public HeaderDto getHeader() {
        return header;
    }

    public void setHeader(HeaderDto header) {
        this.header = header;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }
}
