package com.fh.cloud.screen.websocket.dto;

import java.io.Serializable;

/**
 * 消息头
 * 
 * <AUTHOR>
 * @date 2021/2/1 19:11
 */
public class HeaderDto implements Serializable {
    /**
     * 房间标识
     */
    private String roomId;
    /**
     * 客户端标识
     */
    private String clientId;
    /**
     * 客户端类型{@link com.focusteach.enums.ClientType}
     */
    private Integer clientType;

    /**
     * 消息发送方
     */
    private String from;
    /**
     * 消息接收方
     */
    private String to;
    /**
     * 发送广播消息是否包含自己，默认是true
     */
    private Boolean containsMe = true;

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public Boolean getContainsMe() {
        return containsMe;
    }

    public void setContainsMe(Boolean containsMe) {
        this.containsMe = containsMe;
    }
}
