package com.fh.cloud.screen.websocket;

import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import com.fh.cloud.screen.websocket.consts.ConstantsRedis;
import com.fh.cloud.screen.websocket.dto.SessionDto;
import com.fh.cloud.screen.websocket.verticle.WebSocketVerticle;
import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import com.light.redis.component.RedisComponent;

import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableFeignClients(basePackages = {"com.fh.cloud.screen", "com.light"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},
    scanBasePackages = {"com.fh.cloud.screen.**.**.*", "com.light.**.**.*"})
@EnableSwagger2
@EnableKnife4j
@Slf4j
public class CloudScreenWebSocketServiceApplication {

    @Autowired
    private WebSocketVerticle webSocketVerticle;
    @Autowired
    private Vertx vertx;
    @Autowired
    private RedisComponent redisComponent;

    public static void main(String[] args) {
        SpringApplication.run(CloudScreenWebSocketServiceApplication.class, args);
    }

    @PostConstruct
    public void init() {
        vertx.deployVerticle(webSocketVerticle);

        // 5分钟检测一次客户端有没有发送心跳，如果没有则断开连接：1、断电断网情况，2、客户端未关闭连接直接创建新连接
        vertx.setPeriodic(5 * 60 * 1000, id -> {
            Map<String, SessionDto> sessionDtoMap = webSocketVerticle.getChannelSessionMap();
            sessionDtoMap.forEach((channelId, sessionDto) -> {
                String cacheKey = StringUtils.join(ConstantsRedis.client_cache_prefix, channelId);
                if (!redisComponent.hasKey(cacheKey)) {
                    log.info(channelId + " 不存在缓存中，因此关闭连接");
                    webSocketVerticle.closeHandler(channelId);
                }
            });
        });
    }
}
