package com.fh.cloud.screen.websocekt.api.message.entity.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * http调用发送ws消息的Bo对象
 *
 * <AUTHOR>
 * @date 2022/5/6 9:59
 */
@Data
public class WsSendBo implements Serializable {
    /**
     * 房间id
     */
    private String roomId;
    /**
     * 客户端id(可单个，可多个发送)
     */
    private List<String> clientIds;
    /**
     * 发送的数据
     */
    private Object value;
}
