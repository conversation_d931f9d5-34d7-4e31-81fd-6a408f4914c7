package com.fh.cloud.screen.service.leaveschool.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 放学推送信息
 *
 * <AUTHOR>
 * @date 2023-08-28 9:39
 */
@Data
public class LeaveSchoolPublishVo {
    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 校区id
     */
    @ApiModelProperty("校区id")
    private Long campusId;

    /**
     * 区域id或者classesId
     */
    @ApiModelProperty("区域id或者classesId")
    private Long spaceInfoId;

    /**
     * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String spaceName;

    /**
     * 放学状态 1-未放学 2-放学中 3-已放学
     */
    @ApiModelProperty("放学状态 1-未放学 2-放学中 3-已放学")
    private Integer leaveSchoolType;

    /**
     * 播报文件url
     */
    @ApiModelProperty("播报文件url")
    private String broadcastUrl;

    /**
     * 播报文件oid
     */
    @ApiModelProperty("播报文件oid")
    private String broadcastId;
}
