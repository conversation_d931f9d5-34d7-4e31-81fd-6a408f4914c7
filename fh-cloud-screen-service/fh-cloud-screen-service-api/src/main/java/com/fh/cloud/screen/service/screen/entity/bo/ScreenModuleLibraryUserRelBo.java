package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 模块用户关系表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
@Data
public class ScreenModuleLibraryUserRelBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String userOid;

    /**
     * 模块FK
     */
    @ApiModelProperty("模块FK")
    private Long screenModuleLibraryId;

    /**
     * 用户模块关系，1：创建
     */
    @ApiModelProperty("用户模块关系，1：创建")
    private Integer type;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
