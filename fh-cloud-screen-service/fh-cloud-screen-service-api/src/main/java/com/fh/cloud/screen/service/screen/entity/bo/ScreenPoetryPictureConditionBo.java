package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 共话诗词图片资源表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
@Data
public class ScreenPoetryPictureConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenPoetryPictureId;

	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long organizationId;

	/**
	 * 共话诗词图片url
	 */
	@ApiModelProperty("共话诗词图片url")
	private String screenPoetryPictureMediaUrl;

	/**
	 * 共话诗词图片名称（不包含后缀）
	 */
	@ApiModelProperty("共话诗词图片名称（不包含后缀）")
	private String screenPoetryPictureMediaName;

	/**
	 * 共话诗词图片名称（包含后缀）
	 */
	@ApiModelProperty("共话诗词图片名称（包含后缀）")
	private String screenPoetryPictureMediaNameOri;

	/**
	 * 共话诗词图片fileOid
	 */
	@ApiModelProperty("共话诗词图片fileOid")
	private String screenPoetryPictureMediaId;

	/**
	 * 文件md5
	 */
	@ApiModelProperty("文件md5")
	private String screenPoetryPictureMediaMd5;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
