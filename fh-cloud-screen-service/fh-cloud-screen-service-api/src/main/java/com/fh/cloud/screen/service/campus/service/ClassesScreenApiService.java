package com.fh.cloud.screen.service.campus.service;

import com.fh.cloud.screen.service.campus.api.ClassesScreenApi;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.user.clazz.entity.vo.ClazzVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = ClassesScreenApiService.ClassesApiFallbackFactory.class)
@Component
public interface ClassesScreenApiService extends ClassesScreenApi {
    @Component
    class ClassesApiFallbackFactory implements FallbackFactory<ClassesScreenApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ClassesApiFallbackFactory.class);

        @Override
        public ClassesScreenApiService create(Throwable cause) {
            ClassesApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ClassesScreenApiService() {

                @Override
                public AjaxResult<ClazzVo> getById(Long classesId) {
                    return AjaxResult.fail("获取失败");
                }
            };
        }
    }
}
