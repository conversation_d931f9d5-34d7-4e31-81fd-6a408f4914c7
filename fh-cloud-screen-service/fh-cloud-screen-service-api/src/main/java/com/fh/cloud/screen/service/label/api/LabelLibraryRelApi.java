package com.fh.cloud.screen.service.label.api;

import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签海报关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
public interface LabelLibraryRelApi {

    /**
     * 查询标签海报关联表分页列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/library/rel/page/list")
    public AjaxResult<PageInfo<LabelLibraryRelVo>>
        getLabelLibraryRelPageListByCondition(@RequestBody LabelLibraryRelConditionBo condition);

    /**
     * 查询标签海报关联表列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/library/rel/list")
    public AjaxResult<List<LabelLibraryRelVo>>
        getLabelLibraryRelListByCondition(@RequestBody LabelLibraryRelConditionBo condition);

    /**
     * 新增标签海报关联表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/library/rel/add")
    public AjaxResult addLabelLibraryRel(@Validated @RequestBody LabelLibraryRelBo labelLibraryRelBo);

    /**
     * 新增或编辑 标签和节日 与海报关联关系
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/library/festival/rel/add")
    public AjaxResult updateLabelOrFestivalLibraryRel(@Validated @RequestBody LabelLibraryRelBo labelLibraryRelBo);

    /**
     * 修改标签海报关联表
     * 
     * @param labelLibraryRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/library/rel/update")
    public AjaxResult updateLabelLibraryRel(@Validated @RequestBody LabelLibraryRelBo labelLibraryRelBo);

    /**
     * 查询标签海报关联表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @GetMapping("label/library/rel/detail")
    public AjaxResult<LabelLibraryRelVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除标签海报关联表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @GetMapping("label/library/rel/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
