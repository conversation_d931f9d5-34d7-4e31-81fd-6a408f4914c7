package com.fh.cloud.screen.service.er.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/10
 */
@Data
public class ExamInfoImportModel implements Serializable {

    /**
     * 考场号（名称）
     */
    @Excel(name = "*考场号",  isImportField = "true")
    private String examRoomName;

    /**
     * 考试地点
     */
    @Excel(name = "*考试地点",  isImportField = "true")
    private String spaceInfoName;

    /**
     * 
     * 考试科目等多行
     */
    @ExcelCollection(name = "考试详细信息")
    private List<ExamInfoImportChildrenModel> examInfoImportChildrenModels;
}
