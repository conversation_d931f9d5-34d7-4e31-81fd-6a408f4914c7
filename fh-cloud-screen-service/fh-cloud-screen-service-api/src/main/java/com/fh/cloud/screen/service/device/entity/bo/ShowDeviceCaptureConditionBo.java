package com.fh.cloud.screen.service.device.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 设备抓图表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
@Data
public class ShowDeviceCaptureConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 学校id，0表示公共配置
	 */
	@ApiModelProperty("学校id，0表示公共配置")
	private Long showDeviceId;

	/**
	 * 学校id，0表示公共配置，冗余存储
	 */
	@ApiModelProperty("学校id，0表示公共配置，冗余存储")
	private Long organizationId;

	/**
	 * 设备号,冗余存储
	 */
	@ApiModelProperty("设备号,冗余存储")
	private String deviceNumber;

	/**
	 * 抓图状态：1抓取中，2抓取成功，3抓取失败
	 */
	@ApiModelProperty("抓图状态：1抓取中，2抓取成功，3抓取失败")
	private Integer deviceCaptureStatus;

	/**
	 * 文件oid
	 */
	@ApiModelProperty("文件oid")
	private String deviceCaptureFileOid;

	/**
	 * 截图地址
	 */
	@ApiModelProperty("截图地址")
	private String deviceCaptureMediaUrl;

	/**
	 * 截图压缩图地址
	 */
	@ApiModelProperty("截图压缩图地址")
	private String deviceCaptureMediaUrlCompress;

	/**
	 * 截图名称（不包含后缀）
	 */
	@ApiModelProperty("截图名称（不包含后缀）")
	private String deviceCaptureMediaName;

	/**
	 * 截图名称（包含后缀）
	 */
	@ApiModelProperty("截图名称（包含后缀）")
	private String deviceCaptureMediaNameOri;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
