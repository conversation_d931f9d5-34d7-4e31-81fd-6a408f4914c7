package com.fh.cloud.screen.service.card.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户卡表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class UserCardVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long userCardId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡类型：1学生卡，2教师卡
     */
    private Integer cardType;

    /**
     * 卡设备型号：1默认卡
     */
    private Integer cardDeviceModel;

    /**
     * 卡所属人user_oid
     */
    private String userOid;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;
}
