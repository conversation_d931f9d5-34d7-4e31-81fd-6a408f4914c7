package com.fh.cloud.screen.service.meeting.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingStudentImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingTeacherImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.meeting.api.MeetingApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.light.core.entity.AjaxResult;

import java.util.Date;

/**
 * 会议表
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@FeignClient(contextId = "meetingApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class, fallbackFactory = MeetingApiService.MeetingApiFallbackFactory.class)
@Component
public interface MeetingApiService extends MeetingApi {

    @Component
    class MeetingApiFallbackFactory implements FallbackFactory<MeetingApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(MeetingApiFallbackFactory.class);

        @Override
        public MeetingApiService create(Throwable cause) {
            MeetingApiFallbackFactory.LOGGER.error("云屏会议服务调用失败:{}", cause.getMessage());
            return new MeetingApiService() {
                public AjaxResult getMeetingPageListByCondition(MeetingConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getMyMeetingPageListByCondition(MeetingConditionBo condition) {
                    return AjaxResult.fail("查询我的会议列表失败");
                }

                public AjaxResult getMeetingListByDate(MeetingConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addMeeting(MeetingBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult addMeetingBatch(MeetingBo meetingBo) {
                    return AjaxResult.fail("批量新增失败");
                }

                public AjaxResult updateMeeting(MeetingBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long meetingId) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(MeetingBo Bo) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult signIn(MeetingUserBo meetingUserBo) {
                    return AjaxResult.fail("会议签到失败");
                }

                @Override
                public AjaxResult getTeacherListByCondition(TeacherConditionBo teacherConditionBo) {
                    return AjaxResult.fail("查询教师列表失败");
                }

                @Override
                public AjaxResult getNowAndNextMeeting(MeetingConditionBo meetingConditionBo) {
                    return AjaxResult.fail("获取当前会议失败");
                }

                @Override
                public AjaxResult getDetailCacheByMeetingId(Long meetingId) {
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult getTeacherImportCheckResult(MeetingImportBo<MeetingTeacherImportBo> meetingImportBo) {
                    return AjaxResult.fail("教师导入失败");
                }

                @Override
                public AjaxResult getStudentImportCheckResult(MeetingImportBo<MeetingStudentImportBo> meetingImportBo) {
                    return AjaxResult.fail("学生导入失败");
                }
            };
        }
    }
}