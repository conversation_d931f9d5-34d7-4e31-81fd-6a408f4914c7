package com.fh.cloud.screen.service.leaveschool.api;


import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo;

import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 放学配置表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
public interface LeaveSchoolConfigApi {

    /**
     * 查询放学配置表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @PostMapping("/leave/school/config/page/list")
    public AjaxResult<PageInfo<LeaveSchoolConfigVo>> getLeaveSchoolConfigPageListByCondition(@RequestBody LeaveSchoolConfigConditionBo condition);

    /**
     * 查询放学配置表列表
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @PostMapping("/leave/school/config/list")
    public AjaxResult<List<LeaveSchoolConfigVo>> getLeaveSchoolConfigListByCondition(@RequestBody LeaveSchoolConfigConditionBo condition);


    /**
     * 新增放学配置表
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @PostMapping("/leave/school/config/add")
    public AjaxResult addLeaveSchoolConfig(@Validated @RequestBody LeaveSchoolConfigBo leaveSchoolConfigBo);

    /**
     * 修改放学配置表
     * @param leaveSchoolConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @PostMapping("/leave/school/config/update")
    public AjaxResult updateLeaveSchoolConfig(@Validated @RequestBody LeaveSchoolConfigBo leaveSchoolConfigBo);

    /**
     * 查询放学配置表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @GetMapping("/leave/school/config/detail")
    public AjaxResult<LeaveSchoolConfigVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除放学配置表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @GetMapping("/leave/school/config/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 获取放学配置
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 14:30
     **/
    @PostMapping("/leave/school/config/get-detail")
    public AjaxResult getLeaveSchoolConfig(@RequestBody LeaveSchoolConfigConditionBo condition);
    
    /**
     * 获取班主任班级列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult 
     * <AUTHOR>
     * @date 2023/8/23 17:30
     **/
    @GetMapping("/leave/school/config/teacher-classes")
    public AjaxResult getTeacherClasses();

    /**
     * 获取放学场景班级列表
     *
     * @param clazzConditionBoExt
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/25 15:39
     **/
    @PostMapping("/leave/school/config/clazz-list")
    public AjaxResult getClazzList(@RequestBody ClazzConditionBoExt clazzConditionBoExt);

    /**
     * 获取开了放学的组织列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/12 9:15
     **/
    @GetMapping("/leave/school/config/organization-list")
    public AjaxResult<List<OrganizationVo>> getLeaveSchoolOrganizationList();
}
