package com.fh.cloud.screen.service.wx.service;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.wx.api.WxMsgSubDeviceApi;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceBo;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;


/**
 * 微信消息订阅设备配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
@FeignClient(contextId = "wxMsgSubDeviceApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = WxMsgSubDeviceApiService.WxMsgSubDeviceApiFallbackFactory.class)
@Component
public interface WxMsgSubDeviceApiService extends WxMsgSubDeviceApi {

    @Component
    class WxMsgSubDeviceApiFallbackFactory implements FallbackFactory<WxMsgSubDeviceApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(WxMsgSubDeviceApiFallbackFactory.class);
        @Override
        public WxMsgSubDeviceApiService create(Throwable cause) {
            WxMsgSubDeviceApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new WxMsgSubDeviceApiService() {
                public AjaxResult getWxMsgSubDevicePageListByCondition(WxMsgSubDeviceConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getWxMsgSubDeviceListByCondition(WxMsgSubDeviceConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addWxMsgSubDevice(WxMsgSubDeviceBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateWxMsgSubDevice(WxMsgSubDeviceBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult followDevice(WxMsgSubDeviceBo wxMsgSubDeviceBo) {
                    return AjaxResult.fail("关注/取消关注失败");
                }

                @Override
                public AjaxResult wxMsgSubCount() {
                    return AjaxResult.fail("获取关注设备数失败");
                }
            };
        }
    }
}