package com.fh.cloud.screen.service.calendar.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 校历主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Data
public class SchoolCalendarBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long schoolCalendarId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

}
