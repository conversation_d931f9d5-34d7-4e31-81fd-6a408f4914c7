package com.fh.cloud.screen.service.leaveschool.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigDeviceApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 放学配置设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
@FeignClient(contextId = "leaveSchoolConfigDeviceApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LeaveSchoolConfigDeviceApiService.LeaveSchoolConfigDeviceApiFallbackFactory.class)
@Component
public interface LeaveSchoolConfigDeviceApiService extends LeaveSchoolConfigDeviceApi {

    @Component
    class LeaveSchoolConfigDeviceApiFallbackFactory implements FallbackFactory<LeaveSchoolConfigDeviceApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LeaveSchoolConfigDeviceApiFallbackFactory.class);
        @Override
        public LeaveSchoolConfigDeviceApiService create(Throwable cause) {
            LeaveSchoolConfigDeviceApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LeaveSchoolConfigDeviceApiService() {
                public AjaxResult getLeaveSchoolConfigDevicePageListByCondition(LeaveSchoolConfigDeviceConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLeaveSchoolConfigDeviceListByCondition(LeaveSchoolConfigDeviceConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLeaveSchoolConfigDevice(LeaveSchoolConfigDeviceBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLeaveSchoolConfigDevice(LeaveSchoolConfigDeviceBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}