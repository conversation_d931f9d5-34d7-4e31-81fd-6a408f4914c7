package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 标签海报关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
public interface LabelLibraryAuditRelApi {

    /**
     * 查询标签海报关联表分页列表
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @PostMapping("/label/library/audit/rel/page/list")
    public AjaxResult<PageInfo<LabelLibraryAuditRelVo>> getLabelLibraryAuditRelPageListByCondition(@RequestBody LabelLibraryAuditRelConditionBo condition);

    /**
     * 查询标签海报关联表列表
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @PostMapping("/label/library/audit/rel/list")
    public AjaxResult<List<LabelLibraryAuditRelVo>> getLabelLibraryAuditRelListByCondition(@RequestBody LabelLibraryAuditRelConditionBo condition);


    /**
     * 新增标签海报关联表
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @PostMapping("/label/library/audit/rel/add")
    public AjaxResult addLabelLibraryAuditRel(@Validated @RequestBody LabelLibraryAuditRelBo labelLibraryAuditRelBo);

    /**
     * 修改标签海报关联表
     * @param labelLibraryAuditRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @PostMapping("/label/library/audit/rel/update")
    public AjaxResult updateLabelLibraryAuditRel(@Validated @RequestBody LabelLibraryAuditRelBo labelLibraryAuditRelBo);

    /**
     * 查询标签海报关联表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @GetMapping("/label/library/audit/rel/detail")
    public AjaxResult<LabelLibraryAuditRelVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除标签海报关联表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:26:05
     */
    @GetMapping("/label/library/audit/rel/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
