package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryMediaApi;
import com.fh.cloud.screen.service.screen.entity.bo.*;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenModuleLibraryMediaApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenModuleLibraryMediaApiService.ScreenModuleLibraryMediaApiFallbackFactory.class)
@Component
public interface ScreenModuleLibraryMediaApiService extends ScreenModuleLibraryMediaApi {
    @Component
    class ScreenModuleLibraryMediaApiFallbackFactory implements FallbackFactory<ScreenModuleLibraryMediaApiService> {
        private static final Logger LOGGER = LoggerFactory
            .getLogger(ScreenModuleLibraryMediaApiService.ScreenModuleLibraryMediaApiFallbackFactory.class);

        @Override
        public ScreenModuleLibraryMediaApiService create(Throwable cause) {
            ScreenModuleLibraryMediaApiService.ScreenModuleLibraryMediaApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}",
                cause.getMessage());

            return new ScreenModuleLibraryMediaApiService() {

                @Override
                public AjaxResult getScreenModuleLibraryListByCondition(ScreenModuleLibraryMediaBo condition) {
                    return AjaxResult.fail("查询云屏模块库媒体资源列表失败");
                }

                @Override
                public AjaxResult getThemePosterList(Long organizationId, Integer selectType, Integer pattern) {
                    return AjaxResult.fail("查询全部海报列表失败");
                }

                @Override
                public AjaxResult getMyCollectPosters(Long organizationId, Integer pattern) {
                    return AjaxResult.fail("查询我收藏的海报列表失败");
                }

                @Override
                public AjaxResult
                    getMyCollectPostersPage(ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo) {
                    return AjaxResult.fail("分页查询我收藏的海报列表失败");
                }

                @Override
                public AjaxResult addScreenModuleLibraryMedia(ScreenModuleLibraryMediaBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult
                    addScreenModuleLibraryMediaBatch(List<ScreenModuleLibraryMediaBo> screenModuleLibraryMediaBos) {
                    return AjaxResult.fail("批量新增失败");
                }

                @Override
                public AjaxResult updateScreenModuleLibraryMedia(ScreenModuleLibraryMediaBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult delete(Long screenModuleLibraryMediaId) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult getPosterPageList(Long organizationId, Integer selectType, Integer pattern, Long classesId) {
                    return AjaxResult.fail("查询海报分页列表失败");
                }

                @Override
                public AjaxResult getScreenModuleLibraryListByConditionDefault(ScreenModuleLibraryMediaBo condition) {
                    return AjaxResult.fail("查询默认列表失败");
                }
            };
        }
    }
}
