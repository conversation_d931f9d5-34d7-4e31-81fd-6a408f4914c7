package com.fh.cloud.screen.service.leaveschool.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolBroadcastInfoApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 放学播报信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
@FeignClient(contextId = "leaveSchoolBroadcastInfoApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LeaveSchoolBroadcastInfoApiService.LeaveSchoolBroadcastInfoApiFallbackFactory.class)
@Component
public interface LeaveSchoolBroadcastInfoApiService extends LeaveSchoolBroadcastInfoApi {

    @Component
    class LeaveSchoolBroadcastInfoApiFallbackFactory implements FallbackFactory<LeaveSchoolBroadcastInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LeaveSchoolBroadcastInfoApiFallbackFactory.class);
        @Override
        public LeaveSchoolBroadcastInfoApiService create(Throwable cause) {
            LeaveSchoolBroadcastInfoApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LeaveSchoolBroadcastInfoApiService() {
                public AjaxResult getLeaveSchoolBroadcastInfoPageListByCondition(LeaveSchoolBroadcastInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLeaveSchoolBroadcastInfoListByCondition(LeaveSchoolBroadcastInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLeaveSchoolBroadcastInfo(LeaveSchoolBroadcastInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLeaveSchoolBroadcastInfo(LeaveSchoolBroadcastInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}