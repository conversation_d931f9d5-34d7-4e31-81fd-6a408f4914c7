package com.fh.cloud.screen.service.meeting.entity.bo;

import java.io.Serializable;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 场地预约教师导入数据封装
 * 
 * <AUTHOR>
 * @date 2024/5/29 14:48
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MeetingTeacherImportBo implements Serializable {
    /**
     * 类别（教学人员，非教学人员）
     */
    @Excel(name = "*类别", fixedIndex = 0, replace = {"教学人员_1", "非教学人员_2"})
    private String teacherType;
    /**
     * 姓名
     */
    @Excel(name = "*姓名")
    private String realName;
    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobilePhone;
}
