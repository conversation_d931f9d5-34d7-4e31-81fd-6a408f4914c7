package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 海报收藏表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
public interface ScreenModuleLibraryCollectApi {

    /**
     * 查询海报收藏表分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("screen/module/library/collect/page/list")
    public AjaxResult<PageInfo<ScreenModuleLibraryCollectVo>>
        getScreenModuleLibraryCollectPageListByCondition(@RequestBody ScreenModuleLibraryCollectConditionBo condition);

    /**
     * 查询海报收藏表列表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("screen/module/library/collect/list")
    public AjaxResult<List<ScreenModuleLibraryCollectVo>>
        getScreenModuleLibraryCollectListByCondition(@RequestBody ScreenModuleLibraryCollectConditionBo condition);

    /**
     * 新增海报收藏表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("screen/module/library/collect/add")
    public AjaxResult addScreenModuleLibraryCollect(
        @Validated @RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo);

    /**
     * 修改海报收藏表
     * 
     * @param screenModuleLibraryCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("screen/module/library/collect/update")
    public AjaxResult updateScreenModuleLibraryCollect(
        @Validated @RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo);

    /**
     * 查询海报收藏表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @GetMapping("screen/module/library/collect/detail")
    public AjaxResult<ScreenModuleLibraryCollectVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除海报收藏表
     * 
     * @param screenModuleLibraryCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("screen/module/library/collect/delete")
    public AjaxResult delete(@RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo);
}
