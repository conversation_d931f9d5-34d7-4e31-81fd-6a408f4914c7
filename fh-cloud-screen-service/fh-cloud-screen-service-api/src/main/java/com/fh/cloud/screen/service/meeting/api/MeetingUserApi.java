package com.fh.cloud.screen.service.meeting.api;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 会议人员表
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
public interface MeetingUserApi {

    /**
     * 查询会议人员表分页列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/user/page/list")
    public AjaxResult<PageInfo<MeetingUserVo>>
        getMeetingUserPageListByCondition(@RequestBody MeetingUserConditionBo condition);

    /**
     * 查询会议人员表列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/user/list")
    public AjaxResult<List<MeetingUserVo>> getMeetingUserListByCondition(@RequestBody MeetingUserConditionBo condition);

    /**
     * 新增会议人员表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/user/add")
    public AjaxResult addMeetingUser(@Validated @RequestBody MeetingUserBo meetingUserBo);

    /**
     * 修改会议人员表
     * 
     * @param meetingUserBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/user/update")
    public AjaxResult updateMeetingUser(@Validated @RequestBody MeetingUserBo meetingUserBo);

    /**
     * 查询会议人员表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @GetMapping("meeting/user/detail")
    public AjaxResult<MeetingUserVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除会议人员表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @GetMapping("meeting/user/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
