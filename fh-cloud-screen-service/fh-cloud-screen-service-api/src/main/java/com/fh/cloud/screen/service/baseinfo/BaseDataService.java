package com.fh.cloud.screen.service.baseinfo;

import com.fh.app.role.service.role.entity.bo.OrganizationPackageRelConditionBo;
import com.fh.app.role.service.role.entity.vo.OrganizationPackageRelVo;
import com.fh.cloud.screen.service.baseinfo.entity.vo.OrganizationVoExt;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.subject.entity.bo.SubjectBo;
import com.google.common.collect.Maps;
import com.light.base.attachment.entity.bo.MultipartFormData;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.entity.AjaxResult;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.campus.entity.vo.CampusVo;
import com.light.user.clazz.entity.bo.ClazzHeadmasterConditionBo;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.organization.entity.bo.OrganizationTermDelSaveBo;
import com.light.user.organization.entity.vo.OrganizationTermVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.vo.KeeperRelationVo;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 基础数据调用service(解耦基础信息库)
 */
public interface BaseDataService {

    /**
     * 一个spi实现的名称，通常可以用于业务方传入一个name的配置来选择启用哪一个实现
     *
     * @return
     */
    default String name() {
        return "name";
    }

    /**
     * 测试方法。仅用于输出
     *
     * @return
     */
    default String echo() {
        return "echo";
    }

    /**
     * 根据用户oid集合查询用户真实姓名
     *
     * @param userOids 用户oid集合
     * @return real name by user oids
     */
    default Map<String, String> getRealNameByUserOids(List<String> userOids) {
        return Maps.newHashMap();
    }

    /**
     * 根据组织机构ID 获取组织机构信息
     *
     * @param orgId
     * @return
     */
    default OrganizationVoExt getOrganizationVoByOrgId(Long orgId) {
        return null;
    }

    /**
     * 获取组织机构列表
     *
     * @param orgIds
     * @return
     */
    default List<OrganizationVo> getOrganizationVoList(List<Long> orgIds) {
        return null;
    }

    /**
     * 根据组织id获取校区列表
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/6 11:41
     */
    default AjaxResult getCampusListByOrganizationId(Long organizationId) {
        return new AjaxResult();
    }

    /**
     * 根据班级检索条件获取班级列表，并根据年级value 封装年级label
     *
     * @param clazzConditionBo
     * @return java.util.List<com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo>
     * <AUTHOR>
     * @date 2022/6/6 14:08
     */
    default Map<String, Object> getClazzInfoVoList(ClazzConditionBoExt clazzConditionBo) {
        return Maps.newHashMap();
    }

    /**
     * 查询班级信息
     *
     * @param clazzConditionBo
     * @return
     */
    @Deprecated
    default AjaxResult getClassesListByCondition(ClazzConditionBoExt clazzConditionBo) {
        return AjaxResult.success();
    }

    /**
     * 查询班级信息-只根据classesIds来查询，内部不根据数据权限判断
     *
     * @param clazzConditionBo
     * @return
     */
    default AjaxResult getClassesListByClassesIds(ClazzConditionBoExt clazzConditionBo) {
        return AjaxResult.success();
    }

    /**
     * 根据数据权限查询班级id集合-不分页
     *
     * @param clazzConditionBo
     * @return
     */
    default AjaxResult<List<Long>> getClassesIdsListByDataAuthority(ClazzConditionBoExt clazzConditionBo) {
        return AjaxResult.success();
    }

    /**
     * 根据ID 获取班级信息
     *
     * @param clazzId
     * @return
     */
    default ClazzVo getByClazzId(Long clazzId) {
        return null;
    }

    /**
     * 根据条件获取学生信息
     *
     * @param studentConditionBo
     * @return
     */
    default List<StudentVo> getStudentVoList(StudentConditionBo studentConditionBo) {
        return Lists.newArrayList();
    }

    /**
     * 根据条件获取学生信息-slow查询
     *
     * @param studentConditionBo
     * @return
     */
    default List<StudentVo> getStudentVoListSlow(StudentConditionBo studentConditionBo) {
        return Lists.newArrayList();
    }

    /**
     * 根据条件获取学生信息-分页
     *
     * @param studentConditionBo
     * @return
     */
    default Map<String, Object> getStudentListByCondition(StudentConditionBo studentConditionBo) {
        return Maps.newHashMap();
    }

    /**
     * 根据条件获取学生信息-分页-slow方法
     *
     * @param studentConditionBo
     * @return
     */
    default Map<String, Object> getStudentListByConditionSlow(StudentConditionBo studentConditionBo) {
        return Maps.newHashMap();
    }

    /**
     * 根据条件获取老师基础信息
     *
     * @param teacherConditionBo
     * @return
     */
    default List<TeacherVo> getTeacherVoList(TeacherConditionBo teacherConditionBo) {
        return Lists.newArrayList();
    }

    /**
     * 根据条件获取老师基础信息 -分页
     *
     * @param teacherConditionBo
     * @return
     */
    default Map<String, Object> getTeacherListByCondition(TeacherConditionBo teacherConditionBo) {
        return Maps.newHashMap();
    }

    /**
     * 根据学生姓名和班级id获取学生信息
     *
     * @param realName the real name
     * @param classesId the classes id
     * @return student vo by real name
     */
    default List<StudentVo> getStudentVoByRealName(String realName, Long classesId) {
        return Lists.newArrayList();
    }

    /**
     * 根据手机号查询教师
     *
     * @param phone the phone
     * @return teacher vo by phone
     */
    default List<TeacherVo> getTeacherVoByPhone(String phone) {
        return Lists.newArrayList();
    }

    /**
     * 根据姓名 组织机构查询老师信息
     *
     * @param realName the realName
     * @param orgId the orgId
     * @return teacher vo by realName
     */
    default List<TeacherVo> getTeacherVoByRealName(String realName, Long orgId) {
        return Lists.newArrayList();
    }

    /**
     * 根据条件获取 校区列表
     *
     * @param campusListConditionBo
     * @return
     */
    default List<CampusVo> getCampusVoByCondition(CampusListConditionBo campusListConditionBo) {
        return Lists.newArrayList();
    }

    /**
     * 根据班级ID 获取学生列表
     *
     * @param classesId the classesId 班级ID
     * @return
     */
    default List<StudentVo> getStudentVoListByClassesId(Long classesId) {
        return Lists.newArrayList();
    }

    /**
     * 根据班级ID 获取学生列表:作废，用另外一个方法代替
     *
     * @param classesId the classesId 班级ID
     * @return
     */
    @Deprecated
    default List<StudentVo> getStudentVoAllListByClassesId(Long classesId) {
        return Lists.newArrayList();
    }

    /**
     * 根据用户oid 获取用户的班级id及年级
     *
     * @param userOid
     * @return com.light.user.student.entity.vo.StudentVo
     */
    default StudentVo getStudentVoByUserOid(String userOid) {
        return null;
    }

    /**
     * 根据组织机构ID获取老师列表
     *
     * @param orgId the orgId 学校ID
     * @return
     */
    default List<TeacherVo> getTeacherVoByOrgId(Long orgId) {
        return Lists.newArrayList();
    }

    /**
     * 获取某个班级的班主任任教信息
     *
     * @return
     */
    default List<ClazzHeadmasterVo> listClazzHeadmasterVoByClassesId(Long classesId) {
        return Lists.newArrayList();
    }

    /**
     * 根据教师ID获取老师列表
     *
     * @param teacherIds the teacher ids
     * @return teacher vo by teacher ids
     */
    default List<TeacherVo> getTeacherVoByTeacherIds(List<Long> teacherIds) {
        return Lists.newArrayList();
    }

    /**
     * 上传文件
     *
     * @param multipartFormData file
     * @return com.light.base.attachment.entity.vo.AttachmentVo
     */
    default AttachmentVo upload(MultipartFormData multipartFormData) {
        return null;
    }

    /**
     * 上传文件
     *
     * @param file
     * @return com.light.base.attachment.entity.vo.AttachmentVo
     */
    default AttachmentVo upload(MultipartFile file) {
        return null;
    }

    /**
     * 获取当前用户Oid
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/9/27 11:42
     */
    default String getCurrentUserOid() {
        return null;
    }

    default LoginAccountVo getCurrentUser() {
        return null;
    }

    /**
     * 根据组织机构查询学年学期
     * 
     * @param organizationId
     * @return
     */
    default AjaxResult<List<OrganizationTermVo>> getByOrganizationId(Long organizationId) {
        return AjaxResult.success();
    }

    /**
     * 根据组织机构查询学年学期
     *
     * @param organizationId
     * @return
     */
    default AjaxResult<List<OrganizationTermVo>> getByOrganizationIdAndStudyYear(Long organizationId,
        String studyYear) {
        return AjaxResult.success();
    }

    /**
     * 根据组织机构保存学期
     * 
     * @param organizationTermDelSaveBo
     * @return
     */
    default AjaxResult delAndSaveByOrgIdAndStudyYear(OrganizationTermDelSaveBo organizationTermDelSaveBo) {
        return AjaxResult.success();
    }

    /**
     * 分页查询科目列表
     *
     * @param subjectBo the subject bo
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2022 /5/6 10:07
     */
    default AjaxResult listSubject(SubjectBo subjectBo) {
        return AjaxResult.success();
    }

    /**
     * 根据真是姓名列表获取 组织机构下的学生列表
     *
     * @param orgId org id
     * @param realNames 真实姓名
     * @return
     */
    default List<StudentVo> getOrgStudentListByRealNames(Long orgId, List<String> realNames) {
        return Lists.newArrayList();
    }

    /**
     * 根据真是姓名列表获取 组织机构下的老师列表
     *
     * @param orgId org id
     * @param realNames 真实姓名
     * @return {@link AjaxResult}<{@link List}<{@link TeacherVo}>>
     */
    default List<TeacherVo> getOrgTeacherListByRealNames(Long orgId, List<String> realNames) {
        return Lists.newArrayList();
    }

    /**
     * 根据班级检索条件获取班级列表，并根据年级value 封装年级label。-慢查询，并且不带is_delete条件
     *
     * @param clazzConditionBo
     * @return java.util.List<com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo>
     * <AUTHOR>
     * @date 2023/3/30 14:08
     */
    default Map<String, Object> getClazzInfoVoListSlow(ClazzConditionBoExt clazzConditionBo) {
        return Maps.newHashMap();
    }

    /**
     * 获取易盾检测开关
     *
     * @return boolean 开true,关false
     * <AUTHOR>
     * @date 2023/5/17 9:55
     */
    default boolean getCheckSwitch() {
        return false;
    }

    /**
     * 检查文本是否合规
     *
     * @param text 文本
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 9:55
     */
    default boolean checkSingleText(String text) {
        return false;
    }

    /**
     * 检查富文本
     *
     * @param text 文本
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 9:55
     */
    default boolean checkRichText(String richText) {
        return false;
    }

    /**
     * 检查图片是否合规
     *
     * @param imageUrl 图片路径
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 9:56
     */
    default boolean checkSingleImage(String imageUrl) {
        return false;
    }

    /**
     * 获取全量班级列表(不根据数据权限判断)
     *
     * @param clazzConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/25 15:46
     **/
    default AjaxResult<List<ClazzInfoVo>> getClazzListWithoutDataAuthority(ClazzConditionBoExt clazzConditionBo) {
        return AjaxResult.fail();
    }

    /**
     * 获取学校/组织和套餐关系
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 10:56
     **/
    default List<OrganizationPackageRelVo> getOrganizationPackageRelListByCondition(OrganizationPackageRelConditionBo condition) {
        return null;
    }

    /**
     * 获取班主任信息
     *
     * @param condition
     * @return
     */
    default AjaxResult getClassesHeadmasterListByCondition(@RequestBody ClazzHeadmasterConditionBo condition) {
        return AjaxResult.fail();
    }


    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictTypes the dict types
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    default List<DictionaryDataVo> listValueByTypes(List<String> dictTypes) {
        return null;
    }

    /**
     * 获取被监管的学校id列表
     *
     * @param parentOrganizationId
     * @return
     */
    default List<Long> getSuperviseOrganizationIds(Long parentOrganizationId) {
        return null;
    }

    /**
     * 查询监管教育局
     *
     * @param organizationId
     * @return
     */
    default OrganizationVoExt getSuperviseParentOrganization(Long organizationId) {
        return null;
    }

    /**
     * 获取监管学校列表
     *
     * @param parentOrganizationId
     * @return java.util.List<com.light.user.organization.entity.vo.OrganizationVo>
     * <AUTHOR>
     * @date 2024/8/9 15:40
     **/
    default List<OrganizationVo> getSuperviseOrganizationList(Long parentOrganizationId) {
        return null;
    }

    /**
     * 根据学生oids获取家长
     *
     * @param userOidList
     * @return java.util.List<com.light.user.user.entity.vo.KeeperRelationVo>
     * <AUTHOR>
     * @date 2024/9/10 17:27
     **/
    default List<KeeperRelationVo> getKeeperRelationListByUserOidList(List<String> userOidList) {
        return null;
    }

    /**
     * 根据appId获取开启应用的组织列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/12 9:17
     **/
    default AjaxResult<List<OrganizationVo>> getOrganizationListByAppId(Long appId) {
        return AjaxResult.fail();
    }

}
