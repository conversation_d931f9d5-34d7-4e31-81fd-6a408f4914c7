package com.fh.cloud.screen.service.meeting.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;
import java.util.List;

/**
 * 长期预约表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@Data
public class MeetingLongVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long meetingLongId;

    /**
     * FK所属组织ID
     */
    @ApiModelProperty("FK所属组织ID")
    private Long organizationId;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 会议室地点id
     */
    @ApiModelProperty("会议室地点id")
    private Long spaceInfoId;

    /**
     * 申请人user_oid
     */
    @ApiModelProperty("申请人user_oid")
    private String userOid;

    /**
     * 会议主题
     */
    @ApiModelProperty("会议主题")
    private String title;

    /**
     * 长期会议开始日期:yyyy-MM-dd
     */
    @ApiModelProperty("长期会议开始日期:yyyy-MM-dd ")
    private Date meetingStartDate;

    /**
     * 长期会议结束日期:yyyy-MM-dd
     */
    @ApiModelProperty("长期会议结束日期:yyyy-MM-dd ")
    private Date meetingEndDate;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Time meetingStartTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Time meetingEndTime;

    /**
     * 正常签到时间
     */
    @ApiModelProperty("正常签到时间")
    private Time normalSignInTime;

    /**
     * 是否签到（1：是，2：否）
     */
    @ApiModelProperty("是否签到（1：是，2：否）")
    private Integer isSignIn;

    /**
     * 会议内容
     */
    @ApiModelProperty("会议内容")
    private String content;

    /**
     * 会议备注
     */
    @ApiModelProperty("会议备注")
    private String note;

    /**
     * 会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）
     */
    @ApiModelProperty("会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 会议用户类型（1：教师，2：学生）
     */
    @ApiModelProperty("会议用户类型（1：教师，2：学生）")
    private Integer meetingUserType;

    /**
     * 批量会议uuid，埋点字段，后续用于批量取消
     */
    @ApiModelProperty("批量会议uuid，埋点字段，后续用于批量取消")
    private String meetingUuid;

    /*
     * 方便steam流存入自身
     * */
    public MeetingLongVo returnOwn() {
        return this;
    }


    /**
     * 与会人列表
     */
    @ApiModelProperty("与会人列表")
    private List<MeetingLongUserVo> meetingLongUserVos;

    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String userName;

    /**
     * 会议室名称
     */
    @ApiModelProperty("会议室名称")
    private String meetingName;

    /**
     * 生效的星期，多个使用英文逗号分割。周一到周日对应：1-7
     */
    @ApiModelProperty("生效的星期，多个使用英文逗号分割。周一到周日对应：1-7")
    private String weeks;

    /**
     * 是否有电脑，0：否，1：是
     */
    private Integer computerUse;

    /**
     * 是否有网络，0：否，1：是
     */
    private Integer networkUse;

    /**
     * 是否有投影，0：否，1：是
     */
    private Integer shadowUse;

    /**
     * 容纳人数
     */
    private Integer userCapacity;

    /**
     * 会议地点相关属性拼接
     */
    private String spaceProperty;
}
