package com.fh.cloud.screen.service.enums;

/**
 * <AUTHOR>
 * @date 2023-08-23 19:25
 */
public enum LeaveSchoolType {
    NOT_LEAVE_SCHOOL(1, "未放学"),
    LEAVE_SCHOOL_IN_PROGRESS(2, "放学中"),
    ALREADY_LEAVE_SCHOOL(3, "已放学");

    private Integer code;
    private String value;

    LeaveSchoolType(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
