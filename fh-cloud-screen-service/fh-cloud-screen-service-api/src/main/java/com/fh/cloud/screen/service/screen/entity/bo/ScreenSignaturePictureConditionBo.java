package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 电子签名图片资源表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Data
public class ScreenSignaturePictureConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenSignaturePictureId;

	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long organizationId;

	/**
	 * 电子签名图片url
	 */
	@ApiModelProperty("电子签名图片url")
	private String screenSignaturePictureMediaUrl;

	/**
	 * 电子签名图片名称（不包含后缀）
	 */
	@ApiModelProperty("电子签名图片名称（不包含后缀）")
	private String screenSignaturePictureMediaName;

	/**
	 * 电子签名图片名称（包含后缀）
	 */
	@ApiModelProperty("电子签名图片名称（包含后缀）")
	private String screenSignaturePictureMediaNameOri;

	/**
	 * 电子签名图片fileOid
	 */
	@ApiModelProperty("电子签名图片fileOid")
	private String screenSignaturePictureMediaId;

	/**
	 * 文件md5
	 */
	@ApiModelProperty("文件md5")
	private String screenSignaturePictureMediaMd5;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
