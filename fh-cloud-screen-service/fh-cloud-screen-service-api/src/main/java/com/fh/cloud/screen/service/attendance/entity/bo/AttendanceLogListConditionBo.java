package com.fh.cloud.screen.service.attendance.entity.bo;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

/**
 * 考勤流水表，不用于业务查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceLogListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long attendanceLogId;

    /**
     * 考勤规则id，当天考勤使用的考勤规则
     */
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    private Integer attendanceType;

    /**
     * 考勤设备号，例如云屏设备的设备号
     */
    private String deviceNumber;

    /**
     * 打卡地点
     */
    private String address;

    /**
     * 考勤卡号，例如学生卡卡号
     */
    private String cardNumber;

    /**
     * 用户oid
     */
    private String userOid;

    /**
     * 学生年级
     */
    private String grade;

    /**
     * 学生所属班级id
     */
    private Long classesId;

    /**
     * 用户OID列表
     */
    private Collection<String> userOids;

    /**
     * 考勤时间
     */
    private String attendanceTime;

    /**
     * 考勤日期 yyyy-MM-dd
     */
    private String attendanceDay;

    /**
     * 签到签退类型：1签到，2签退
     */
    private Integer signType;

    /**
     * 考勤方式：1实体卡，2人脸识别
     */
    private Integer attendanceMethod;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
