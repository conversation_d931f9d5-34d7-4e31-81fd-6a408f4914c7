package com.fh.cloud.screen.service.device.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.api.ShowDeviceApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceListConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceOperateBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceOperateListBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.light.core.entity.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = ShowDeviceApiService.ShowDeviceApiFallbackFactory.class)
@Component
public interface ShowDeviceApiService extends ShowDeviceApi {

    @Component
    class ShowDeviceApiFallbackFactory implements FallbackFactory<ShowDeviceApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ShowDeviceApiFallbackFactory.class);

        @Override
        public ShowDeviceApiService create(Throwable cause) {
            ShowDeviceApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ShowDeviceApiService() {

                @Override
                public AjaxResult<ShowDeviceVo> activate(ShowDeviceBo bo) {
                    return AjaxResult.fail("激活失败");
                }

                @Override
                public AjaxResult<ShowDeviceVo> getByDeviceNumber(String deviceNubmer) {
                    return AjaxResult.fail("获取设备信息失败");
                }

                @Override
                public AjaxResult listShowDeviceDataByCondition(ShowDeviceListConditionBo condition) {
                    return AjaxResult.fail("查询设备列表失败");
                }

                @Override
                public AjaxResult switchOperate(ShowDeviceOperateBo showDeviceOperateBo) {
                    return AjaxResult.fail("开关机操作失败");
                }

                @Override
                public AjaxResult changePattern(String deviceNumber, Integer pattern) {
                    return AjaxResult.fail("更新模式失败");
                }

                @Override
                public AjaxResult updateStatusByDeviceNum(String deviceNumber, Integer status) {
                    return AjaxResult.fail("根据设备号更新状态");
                }

                @Override
                public AjaxResult<ShowDeviceVo> full(ShowDeviceBo bo) {
                    return AjaxResult.fail("根据设备号修改横竖屏版式失败");
                }

                @Override
                public AjaxResult aboutByDeviceNum(String deviceNumber) {
                    return AjaxResult.fail("获取设备详情失败");
                }

                @Override
                public AjaxResult qrcodeContent(ScreenBusinessBo bo) {
                    return AjaxResult.fail("获取二维码内容失败");
                }

                @Override
                public AjaxResult qrDecode(String qrcodeContent) {
                    return AjaxResult.fail("解密二维码内容失败");
                }

                @Override
                public AjaxResult listByCondition(ShowDeviceListConditionBo condition) {
                    return AjaxResult.fail("查询设备列表失败");
                }

                @Override
                public AjaxResult addShowDevice(ShowDeviceBo showDeviceBo) {
                    return AjaxResult.fail("新增设备失败");
                }

                @Override
                public AjaxResult updateShowDevice(ShowDeviceBo showDeviceBo) {
                    return AjaxResult.fail("修改设备失败");
                }

                @Override
                public AjaxResult delete(Long showDeviceId) {
                    return AjaxResult.fail("删除设备失败");
                }

                @Override
                public AjaxResult upload(MultipartFile file, Long organizationId) {
                    return AjaxResult.fail("导入设备失败");
                }

                @Override
                public AjaxResult countDeviceByCondition(ShowDeviceListConditionBo conditionBo) {
                    return AjaxResult.fail("查询设备统计失败");
                }

                @Override
                public AjaxResult updateVersionByDeviceNumber(String deviceNumBer, String version) {
                    return AjaxResult.fail("修改设备版本失败");
                }

                @Override
                public AjaxResult setPosterRule(ShowDeviceBo showDeviceBo) {
                    return AjaxResult.fail("修改设备的海报推送规则及标签失败");
                }

                @Override
                public AjaxResult setPosterRuleBatch(ShowDeviceBo showDeviceBo) {
                    return AjaxResult.fail("批量修改设备的海报推送规则失败");
                }

                @Override
                public AjaxResult getPosterRule(Long showDeviceId) {
                    return AjaxResult.fail("获取设备的海报推送规则及标签失败");
                }

                @Override
                public AjaxResult initDeviceByOrganizationIdAndDeviceNumbers(ShowDeviceOperateBo showDeviceOperateBo) {
                    return AjaxResult.fail("手动推送初始化云屏的接口失败");
                }

                @Override
                public AjaxResult updateDeviceByDeviceNumberWithSendApp(ShowDeviceBo showDeviceBo) {
                    return AjaxResult.fail("更新设备横竖版或全屏或海报间隔时长失败");
                }

                @Override
                public AjaxResult deviceOverview() {
                    return AjaxResult.fail("获取设备总览数据失败");
                }

                @Override
                public AjaxResult arcCode(ShowDeviceBo bo) {
                    return AjaxResult.fail("云屏提交修改人脸激活码失败");
                }

                @Override
                public AjaxResult restartApp(ShowDeviceBo showDeviceBo) {
                    return AjaxResult.fail("重启应用失败");
                }

                @Override
                public AjaxResult listShowDeviceDataByParentOrganizationId(ShowDeviceListConditionBo condition) {
                    return AjaxResult.fail("根据监管教育局id查询绑定了地点的设备列表失败");
                }

                @Override
                public AjaxResult switchOperateBatch(ShowDeviceOperateListBo operateListBo) {
                    return AjaxResult.fail("批量开关机失败");
                }

                @Override
                public AjaxResult changeFaceMod(ShowDeviceOperateBo showDeviceOperateBo) {
                    return AjaxResult.fail("设置人脸建模类型失败");
                }

                @Override
                public AjaxResult changeSuperviseState(ShowDeviceOperateBo showDeviceOperateBo) {
                    return AjaxResult.fail("修改监管状态失败");
                }
            };
        }
    }
}
