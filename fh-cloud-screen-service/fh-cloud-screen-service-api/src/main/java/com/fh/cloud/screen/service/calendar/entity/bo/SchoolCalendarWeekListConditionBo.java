package com.fh.cloud.screen.service.calendar.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 校历上课日星期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
@Data
public class SchoolCalendarWeekListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long schoolCalendarWeekId;

    /**
     * FK校历主表主键id
     */
    private Long schoolCalendarId;

    /**
     * 上课类型：1上课、2不上课
     */
    private Integer type;

    /**
     * 星期几：1-7，分别为星期一到星期日
     */
    private Integer week;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

}
