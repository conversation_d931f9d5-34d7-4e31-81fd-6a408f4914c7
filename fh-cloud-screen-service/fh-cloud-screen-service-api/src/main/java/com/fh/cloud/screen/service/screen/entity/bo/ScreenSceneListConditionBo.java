package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏场景表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ScreenSceneListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long screenSceneId;

    /**
     * FK所属组织ID
     */
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    private Long campusId;

    /**
     * 场景名称
     */
    private String screenSceneName;

    /**
     * 云屏场景布局，透传前端数据
     */
    private String screenSceneLayout;

    /**
     * 地点组id
     */
    private Long spaceGroupId;

    /**
     * 场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景
     */
    private Long screenSceneType;

    /**
     * 设备模式：1横屏，2竖屏。
     */
    private Integer screenDevicePattern;

    /**
     * 场景顺序：1，2，3...
     */
    private Long screenIndex;

    /**
     * 同一个场景内轮播的场景名称
     */
    private String screenPlayName;

    /**
     * 同一个场景内的轮播的场景顺序：1，2，3...
     */
    private Long screenPlayIndex;

    /**
     * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
     */
    private Date startTime;

    /**
     * 场景时间-结束时间，多个轮播场景的时候，场景时间相同
     */
    private Date endTime;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 空间id或者班级id
     */
    private Long spaceInfoId;

    /**
     * 空间群组使用类型：1行政，2非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 设备id
     */
    private Long showDeviceId;

    /**
     * 全校和校区多个场景的时候是否按照各自独立展示。true表示是。辅助区分web和云屏的查询，db无此字段。
     */
    private boolean singleShow = false;

    /**
     * 发布方式：1统一发布，2点位发布。辅助web端的查询，云屏端查询不适用该参数，db也增加该类型区分。
     */
    @ApiModelProperty(value = "发布方式：1统一发布，2点位发布。辅助web端的查询，云屏端查询不适用该参数，db也增加该类型区分。")
    private Integer publishType;

    /**
     * 是否查询模块详细信息：为了H5编辑场景时候处理方便一点，因为目前H5使用的数据是从dataVos里面获取的
     */
    private boolean queryScreenModuleDataVos = false;

    /**
     * 自定义场景是否全屏类型：1全屏，2不是全屏
     */
    @ApiModelProperty(value = "自定义场景是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date startDate;

    /**
     * 结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date endDate;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效")
    private String weeks;

    /**
     * 一次性查询多个设备的场景时候使用
     */
    private List<Long> showDeviceIds;

    /**
     * 监管教育局id
     */
    @ApiModelProperty("监管教育局id")
    private Long parentOrganizationId;

    /**
     * 是否查询功能场景 true：查询 默认false：不查询
     */
    @ApiModelProperty("是否查询功能场景")
    private Boolean queryFunctionalScreenScene = false;

    /**
     * 1教育局监管，2未监管
     */
    private Integer superviseState;
}
