package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface ScreenModuleLibraryMediaApi {

    /**
     * 查询云屏模块库媒体资源列表
     *
     * <AUTHOR>
     * @date 2022/6/29 15:25
     */
    @PostMapping("/screen-module-library-media/list")
    @ApiOperation(value = "查询云屏模块库媒体资源列表", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryListByCondition(@RequestBody ScreenModuleLibraryMediaBo condition);

    /**
     * 查询云屏模块库媒体资源列表-default
     *
     * <AUTHOR>
     * @date 2023/3/27 15:25
     */
    @PostMapping("/screen-module-library-media/list-default")
    @ApiOperation(value = "查询云屏模块库媒体资源列表-默认", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryListByConditionDefault(@RequestBody ScreenModuleLibraryMediaBo condition);

    /**
     * 全部海报列表
     *
     * @param selectType 包含主题热门海报
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/16 15:21
     */
    @Deprecated
    @GetMapping("/screen-module-library-media/posters")
    @ApiOperation(value = "全部海报列表", httpMethod = "GET")
    public AjaxResult getThemePosterList(@RequestParam("organizationId") Long organizationId,
        @RequestParam("selectType") Integer selectType,
        @RequestParam(name = "pattern", required = false) Integer pattern);

    /**
     * 我收藏的海报模块列表
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/16 17:07
     */
    @ApiOperation(value = "我收藏的海报模块列表", httpMethod = "GET")
    @GetMapping("/screen-module-library-media/my-posters")
    AjaxResult getMyCollectPosters(@RequestParam("organizationId") Long organizationId,
        @RequestParam(name = "pattern", required = false) Integer pattern);

    /**
     * 分页获取我收藏的海报列表
     *
     * @param libraryCollectConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/20 15:31
     */
    @ApiOperation(value = "分页获取我收藏的海报列表", httpMethod = "POST")
    @PostMapping("/screen-module-library-media/my-posters-page")
    public AjaxResult
        getMyCollectPostersPage(@RequestBody ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo);

    /**
     * 新增云屏模块库媒体资源表
     * 
     * <AUTHOR>
     * @date 2023-01-03 14:57:48
     */
    @ApiOperation(value = "新增云屏模块库媒体资源表", httpMethod = "POST")
    @PostMapping("/screen-module-library-media/add")
    public AjaxResult addScreenModuleLibraryMedia(@RequestBody ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo);

    /**
     * 批量新增海报图片
     *
     * @param screenModuleLibraryMediaBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/10 16:38
     */
    @ApiOperation(value = "批量新增海报图片", httpMethod = "POST")
    @PostMapping("/screen-module-library-media/add-batch")
    public AjaxResult
        addScreenModuleLibraryMediaBatch(@RequestBody List<ScreenModuleLibraryMediaBo> screenModuleLibraryMediaBos);

    /**
     * 修改云屏模块库媒体资源表
     * 
     * @param screenModuleLibraryMediaBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-01-03 14:57:48
     */
    @ApiOperation(value = "修改云屏模块库媒体资源表", httpMethod = "POST")
    @PostMapping("/screen-module-library-media/update")
    public AjaxResult
        updateScreenModuleLibraryMedia(@RequestBody ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo);

    /**
     * 删除云屏模块库媒体资源表
     * 
     * @param screenModuleLibraryMediaId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-01-03 14:57:48
     */
    @ApiOperation(value = "删除云屏模块库媒体资源表", httpMethod = "GET")
    @GetMapping("/screen-module-library-media/delete")
    public AjaxResult
        delete(@NotNull(message = "请选择数据") @RequestParam("screenModuleLibraryMediaId") Long screenModuleLibraryMediaId);

    /**
     * 海报前20个分组和前10主题列表（移动端）
     *
     * @param organizationId, selectType, pattern
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/4 9:34
     */
    @GetMapping("/screen-module-library-media/poster-page")
    @ApiOperation(value = "分组和主题的分页列表", httpMethod = "GET")
    public AjaxResult getPosterPageList(@RequestParam("organizationId") Long organizationId,
        @RequestParam("selectType") Integer selectType,
        @RequestParam(name = "pattern", required = false) Integer pattern,
        @RequestParam(value = "classesId", required = false) Long classesId);
}
