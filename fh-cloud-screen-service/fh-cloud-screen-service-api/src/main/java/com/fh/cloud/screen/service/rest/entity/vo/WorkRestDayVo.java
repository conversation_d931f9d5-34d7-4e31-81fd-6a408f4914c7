package com.fh.cloud.screen.service.rest.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * 作息时间天设置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestDayVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long workRestDayId;

    /**
     * FK作息时间年级表主键
     */
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    private Long workRestId;

    /**
     * 课程节次：1，2，3...
     */
    private Integer courseClassesPosition;

    /**
     * 课程节次名称，例如第1节
     */
    private String courseClassesName;

    /**
     * 课程节次类型：1普通课，2活动课
     */
    private Integer courseClassesType;

    /**
     * 课程节次顺序（含活动课）：1，2，3...
     */
    private Integer courseClassesIndex;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    private Integer week;

    /**
     * 上午1、下午2、晚上3
     */
    private Integer dayType;

    /**
     * 起始时间
     */
    private Time startTime;

    /**
     * 截止时间
     */
    private Time endTime;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
