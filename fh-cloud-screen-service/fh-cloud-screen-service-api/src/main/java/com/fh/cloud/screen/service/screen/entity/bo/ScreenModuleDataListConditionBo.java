package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏模块表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenModuleDataListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long screenModuleDataId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * FK模块库表id，学校自定义模块的时候，该值为0
     */
    private Long screenModuleLibraryId;

    /**
     * 模块来源类型：1预置模块，2自定义模块
     */
    private Integer moduleSource;

    /**
     * 自定义模块名称
     */
    private String customModuleName;

    /**
     * 自定义模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private Long customModuleGroupType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 模块组-查询使用（可以控制系统模块和自定义模块）
     */
    private Long moduleGroupType;

}
