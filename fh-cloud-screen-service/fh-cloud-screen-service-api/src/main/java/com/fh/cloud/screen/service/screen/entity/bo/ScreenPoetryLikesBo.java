package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 共话诗词点赞记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
@Data
public class ScreenPoetryLikesBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenPoetryLikesId;

	/**
	 * FK共话诗词id
	 */
	@ApiModelProperty("FK共话诗词id")
	private Long screenPoetryContentId;

	/**
	 * 点赞数
	 */
	@ApiModelProperty("点赞数")
	private Long likesNum;



	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
