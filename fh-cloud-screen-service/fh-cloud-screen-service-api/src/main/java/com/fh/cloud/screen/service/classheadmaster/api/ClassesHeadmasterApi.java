package com.fh.cloud.screen.service.classheadmaster.api;

import com.fh.cloud.screen.service.baseinfo.entity.bo.ClazzHeadmasterConditionBoExt;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @date 2024-01-17 9:40
 */
public interface ClassesHeadmasterApi {

    /**
     * 班主任任教班级列表
     *
     * @param clazzHeadmasterConditionBoExt the clazz headmaster condition bo ext
     * @return com.light.core.entity.AjaxResult ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-29 16:15:00
     */
    @ApiOperation(value = "班主任任教班级列表", httpMethod = "POST")
    @RequestMapping(value = "/classes-headmaster/headmaster-classes", method = RequestMethod.POST)
    public AjaxResult headmasterClasses(@RequestBody ClazzHeadmasterConditionBoExt clazzHeadmasterConditionBoExt) throws Exception;

}
