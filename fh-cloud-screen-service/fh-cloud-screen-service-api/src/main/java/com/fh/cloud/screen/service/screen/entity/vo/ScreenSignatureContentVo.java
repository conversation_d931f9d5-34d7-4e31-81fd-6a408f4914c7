package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 电子签名表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Data
public class ScreenSignatureContentVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenSignatureContentId;

    /**
     * FK云屏模块表id
     */
    @ApiModelProperty("FK云屏模块表id")
    private Long screenModuleDataId;

    /**
     * 电子签名标题
     */
    @ApiModelProperty("电子签名标题")
    private String screenSignatureContentTitle;

    /**
     * 电子签名文本内容
     */
    @ApiModelProperty("电子签名文本内容")
    private String screenSignatureContentTxt;

    /**
     * FK所属班级ID
     */
    @ApiModelProperty("FK所属班级ID")
    private Long classesId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 【冗余】创建人姓名
     */
    @ApiModelProperty("【冗余】创建人姓名")
    private String createUserName;

    /**
     * 【冗余】创建人班级名称
     */
    @ApiModelProperty("【冗余】创建人班级名称")
    private String createUserClassesName;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ScreenSignatureContentVo returnOwn() {
        return this;
    }

}
