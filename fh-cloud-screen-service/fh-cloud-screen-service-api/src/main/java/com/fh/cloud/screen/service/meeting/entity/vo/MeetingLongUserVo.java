package com.fh.cloud.screen.service.meeting.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 长期预约表人员表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@Data
public class MeetingLongUserVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long meetingLongUserId;

    /**
     * FK长期预约表ID
     */
    @ApiModelProperty("FK长期预约表ID")
    private Long meetingLongId;

    /**
     * 参与人user_oid
     */
    @ApiModelProperty("参与人user_oid")
    private String userOid;

    /**
     * 签到时间
     */
    @ApiModelProperty("签到时间")
    private Date signTime;

    /**
     * 与会状态，1：未签到，2：已签到[提前]，3：已签到[正常]，4已签到[迟到]
     */
    @ApiModelProperty("与会状态，1：未签到，2：已签到[提前]，3：已签到[正常]，4已签到[迟到]")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * grade 的code值
     */
    @ApiModelProperty("grade 的code值")
    private String grade;

    /**
     * 班级ID
     */
    @ApiModelProperty("班级ID")
    private Long classesId;

    /**
     * 班级名称
     */
    @ApiModelProperty("班级名称")
    private String classesName;

    /*
     * 方便steam流存入自身
     * */
    public MeetingLongUserVo returnOwn() {
        return this;
    }

    /**
     * 申请人名称
     */
    @ApiModelProperty("申请人名称")
    private String userName;
}
