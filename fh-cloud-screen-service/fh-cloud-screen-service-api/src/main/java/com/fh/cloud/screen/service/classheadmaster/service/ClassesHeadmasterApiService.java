package com.fh.cloud.screen.service.classheadmaster.service;

import java.util.List;

import com.fh.cloud.screen.service.baseinfo.entity.bo.ClazzHeadmasterConditionBoExt;
import com.fh.cloud.screen.service.classheadmaster.api.ClassesHeadmasterApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.card.api.UserCardApi;
import com.fh.cloud.screen.service.card.entity.bo.TeacherCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardExportVo;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardExportVo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

@FeignClient(contextId = "classesHeadmasterApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class, fallbackFactory = ClassesHeadmasterApiService.ClassesHeadmasterApiFactory.class)
@Component
public interface ClassesHeadmasterApiService extends ClassesHeadmasterApi {

    @Component
    class ClassesHeadmasterApiFactory implements FallbackFactory<ClassesHeadmasterApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ClassesHeadmasterApiService.class);

        @Override
        public ClassesHeadmasterApiService create(Throwable cause) {
            ClassesHeadmasterApiFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ClassesHeadmasterApiService() {
                @Override
                public AjaxResult headmasterClasses(ClazzHeadmasterConditionBoExt clazzHeadmasterConditionBoExt) throws Exception {
                    return AjaxResult.fail("查询班主任任教班级失败");
                }
            };
        }
    }
}
