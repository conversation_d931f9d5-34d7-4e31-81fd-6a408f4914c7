package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 海报收藏表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@Data
public class ScreenModuleLibraryCollectBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * FK模块库表id
	 */
	@ApiModelProperty("FK模块库表id")
	private Long screenModuleLibraryId;

	/**
	 * 收藏人id
	 */
	@ApiModelProperty("收藏人id")
	private String userOid;

	/**
	 * 收藏人组织id
	 */
	@ApiModelProperty("收藏人组织id")
	private Long organizationId;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
