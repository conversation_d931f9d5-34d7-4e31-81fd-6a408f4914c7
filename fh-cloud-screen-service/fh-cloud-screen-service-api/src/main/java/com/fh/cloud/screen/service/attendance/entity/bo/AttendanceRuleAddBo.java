package com.fh.cloud.screen.service.attendance.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/30
 */
@Data
public class AttendanceRuleAddBo implements Serializable {
    /**
     * 主键
     */
    private Long attendanceRuleId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @NotNull(message = "考勤类型：1教师考勤，2学生考勤不能为空")
    private Integer attendanceType;

    /**
     * 考勤方一天几次：1，2，3，4...
     */
    @NotNull(message = "考勤方一天几次：1，2，3，4...不能为空")
    private Integer attendanceModeNum;

    /**
     * 年级考勤是否一致：1一致，2不一致
     */
    private Integer gradeSameType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime = new Date();

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 到天的考勤规则
     */
    List<AttendanceRuleDayBo> attendanceRuleDays;
}
