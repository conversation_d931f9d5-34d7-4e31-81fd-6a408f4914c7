package com.fh.cloud.screen.service.festival.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.festival.api.FestivalApi;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalBo;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * 节日表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
@FeignClient(contextId = "festivalApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
        configuration = FeignClientInterceptor.class, fallbackFactory = FestivalApiService.FestivalApiFallbackFactory.class)
@Component
public interface FestivalApiService extends FestivalApi {

    @Component
    class FestivalApiFallbackFactory implements FallbackFactory<FestivalApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(FestivalApiFallbackFactory.class);
        @Override
        public FestivalApiService create(Throwable cause) {
            FestivalApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new FestivalApiService() {
                public AjaxResult getFestivalPageListByCondition(FestivalConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getFestivalListByCondition(FestivalConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addFestival(FestivalBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateFestival(FestivalBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getFestivalListByDate(String date) {
                    return AjaxResult.fail("查询列表失败");
                }
            };
        }
    }
}