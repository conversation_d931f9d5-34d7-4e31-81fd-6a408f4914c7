package com.fh.cloud.screen.service.wx.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.wx.api.OrganizationWxMsgTemplateConfigApi;
import com.fh.cloud.screen.service.wx.entity.bo.OrganizationWxMsgTemplateConfigBo;
import com.fh.cloud.screen.service.wx.entity.bo.OrganizationWxMsgTemplateConfigConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 学校微信推送模板配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-09-24 17:12:03
 */
@FeignClient(contextId = "organizationWxMsgTemplateConfigApiService",
        value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
        configuration = FeignClientInterceptor.class, fallbackFactory = OrganizationWxMsgTemplateConfigApiService.OrganizationWxMsgTemplateConfigApiFallbackFactory.class)
@Component
public interface OrganizationWxMsgTemplateConfigApiService extends OrganizationWxMsgTemplateConfigApi {

    @Component
    class OrganizationWxMsgTemplateConfigApiFallbackFactory implements FallbackFactory<OrganizationWxMsgTemplateConfigApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(OrganizationWxMsgTemplateConfigApiFallbackFactory.class);
        @Override
        public OrganizationWxMsgTemplateConfigApiService create(Throwable cause) {
            OrganizationWxMsgTemplateConfigApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new OrganizationWxMsgTemplateConfigApiService() {
                public AjaxResult getOrganizationWxMsgTemplateConfigPageListByCondition(OrganizationWxMsgTemplateConfigConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getOrganizationWxMsgTemplateConfigListByCondition(OrganizationWxMsgTemplateConfigConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addOrganizationWxMsgTemplateConfig(OrganizationWxMsgTemplateConfigBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateOrganizationWxMsgTemplateConfig(OrganizationWxMsgTemplateConfigBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}