package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
@Data
public class ScreenModuleLibraryMediaBo extends PageLimitBo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long screenModuleLibraryMediaId;

    @ApiModelProperty(value = "FK模块库表")
    private Long screenModuleLibraryId;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址")
    private String screenModuleLibraryMediaUrl;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址-压缩后")
    private String screenModuleLibraryMediaUrlCompress;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址-封面")
    private String screenModuleLibraryMediaUrlCover;

    @ApiModelProperty(value = "云屏图片或者视频媒体名称（不包含后缀）")
    private String screenModuleLibraryMediaName;

    @ApiModelProperty(value = "云屏图片或者视频原始媒体名称（包含后缀）")
    private String screenModuleLibraryMediaNameOri;

    @ApiModelProperty(value = "云屏图片或者视频媒体fileoid")
    private String screenContentMediaId;

    @ApiModelProperty(value = "云屏图片或者视频媒体fileoid-压缩后")
    private String screenContentMediaIdCompress;

    @ApiModelProperty(value = "更新时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否删除，0：否，1：是")
    private Integer isDelete;

    @ApiModelProperty(value = "设备模式：1横屏，2竖屏。设备绑定的时候更新")
    private Integer devicePattern;

    @ApiModelProperty(value = "文件md5")
    private String screenContentMediaMd5;

    /**
     * 海报图片排序
     */
    @ApiModelProperty(value = "海报图片排序")
    private Long mediaSort;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private Long moduleGroupType;

    /**
     * 海报主题id集合。前端查询时候不传该值，请传screenModuleLibrarySelIds
     */
    private List<Long> screenModuleLibraryIds;

    /**
     * 是否查询海报对接应用数据
     */
    private boolean queryPlugin = false;

    /**
     * 获取海报列表检索，dictionary_data organizationId 标识主题和热门海报
     */
    private Long dictionaryDataOrganizationId;

    /**
     * 获取海报列表检索，dictionary_data classesId 标识主题和热门海报
     */
    private Long dictionaryDataClassesId;

    /**
     * 标签id集合
     */
    private List<Long> labelIds;

    /**
     * 是否增加推送海报的处理逻辑，如果true则增加：节日节点主动推送+标签订阅推送+公益主题海报
     */
    private boolean isWithPushLibraryProcess = false;

    /**
     * 设备序列号
     */
    private String deviceNumber;

    // /**
    // * 模块分组类型：1非海报，2海报展示分组，3海报不展示分组
    // */
    // private Integer moduleDataType;

    // /**
    // * 主题版式 1：横版，2竖版
    // */
    // private Integer libraryPattern;

    /**
     * 海报多选的id，多个使用逗号分割。前端传参，用于查询多主题的海报图片
     */
    private String screenModuleLibrarySelIds;

    /**
     * 指定H5查询的标签组
     */
    private Integer moduleDefaultH5LabelGroup;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    private Integer mediaSource;

    /**
     * 模块来源id
     */
    private String thirdId;
}
