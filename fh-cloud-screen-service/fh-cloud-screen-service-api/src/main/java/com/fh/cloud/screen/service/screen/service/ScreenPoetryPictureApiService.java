package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenPoetryPictureApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 共话诗词图片资源表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
@FeignClient(contextId = "screenPoetryPictureApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenPoetryPictureApiService.ScreenPoetryPictureApiFallbackFactory.class)
@Component
public interface ScreenPoetryPictureApiService extends ScreenPoetryPictureApi {

    @Component
    class ScreenPoetryPictureApiFallbackFactory implements FallbackFactory<ScreenPoetryPictureApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenPoetryPictureApiFallbackFactory.class);
        @Override
        public ScreenPoetryPictureApiService create(Throwable cause) {
            ScreenPoetryPictureApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenPoetryPictureApiService() {
                public AjaxResult getScreenPoetryPicturePageListByCondition(ScreenPoetryPictureConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenPoetryPictureListByCondition(ScreenPoetryPictureConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenPoetryPicture(ScreenPoetryPictureBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenPoetryPicture(ScreenPoetryPictureBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}