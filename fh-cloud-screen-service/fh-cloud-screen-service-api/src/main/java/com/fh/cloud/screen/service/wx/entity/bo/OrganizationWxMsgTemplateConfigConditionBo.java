package com.fh.cloud.screen.service.wx.entity.bo;

import com.light.core.entity.PageLimitBo;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 学校微信推送模板配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-09-24 17:12:03
 */
@Data
public class OrganizationWxMsgTemplateConfigConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 模板id
	 */
	@ApiModelProperty("模板id")
	private String templateId;

	/**
	 * 跳转地址
	 */
	@ApiModelProperty("跳转地址")
	private String url;

	/**
	 * 业务类型 1-放学推送（放学中） 2-放学推送（已放学）
	 */
	@ApiModelProperty("业务类型 1-放学推送（放学中） 2-放学推送（已放学）")
	private Integer type;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
