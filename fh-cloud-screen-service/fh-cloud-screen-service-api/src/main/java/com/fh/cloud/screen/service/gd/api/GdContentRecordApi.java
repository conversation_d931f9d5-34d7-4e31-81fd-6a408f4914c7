package com.fh.cloud.screen.service.gd.api;

import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordConditionBo;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordBo;
import com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 稿定内容记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
public interface GdContentRecordApi {

    /**
     * 查询稿定内容记录分页列表
     * 
     * <AUTHOR>
     * @date 2024-07-12 13:54:37
     */
    @PostMapping("/gd/content/record/page/list")
    public AjaxResult<PageInfo<GdContentRecordVo>>
        getGdContentRecordPageListByCondition(@RequestBody GdContentRecordConditionBo condition);

    /**
     * 查询稿定内容记录列表
     * 
     * <AUTHOR>
     * @date 2024-07-12 13:54:37
     */
    @PostMapping("/gd/content/record/list")
    public AjaxResult<List<GdContentRecordVo>>
        getGdContentRecordListByCondition(@RequestBody GdContentRecordConditionBo condition);

    /**
     * 新增稿定内容记录
     * 
     * <AUTHOR>
     * @date 2024-07-12 13:54:37
     */
    @PostMapping("/gd/content/record/add")
    public AjaxResult addGdContentRecord(@Validated @RequestBody GdContentRecordBo gdContentRecordBo);

    /**
     * 修改稿定内容记录
     * 
     * @param gdContentRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-12 13:54:37
     */
    @PostMapping("/gd/content/record/update")
    public AjaxResult updateGdContentRecord(@Validated @RequestBody GdContentRecordBo gdContentRecordBo);

    /**
     * 查询稿定内容记录详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-12 13:54:37
     */
    @GetMapping("/gd/content/record/detail")
    public AjaxResult<GdContentRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除稿定内容记录
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-12 13:54:37
     */
    @GetMapping("/gd/content/record/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 查询稿定内容记录详情-根据条件
     *
     * @param gdContentRecordConditionBo the gd content record condition bo
     * @return detail condition
     * <AUTHOR>
     * @date 2024 -07-12 13:54:37
     * @returnType AjaxResult
     */
    @PostMapping("/gd/content/record/detail-condition")
    public AjaxResult<GdContentRecordVo>
    getDetailCondition(@RequestBody GdContentRecordConditionBo gdContentRecordConditionBo);

    /**
     * 修改稿定内容记录-根据条件
     *
     * @param gdContentRecordConditionBo the gd content record condition bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -07-12 13:54:37
     * @returnType AjaxResult
     */
    @PostMapping("/gd/content/record/update-condition")
    public AjaxResult updateCondition(@RequestBody GdContentRecordConditionBo gdContentRecordConditionBo);

    /**
     * 保存记录(根据gdId新增或更新)
     *
     * @param gdContentRecordBo the gd content record bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -07-12 13:54:37
     * @returnType AjaxResult
     */
    @PostMapping("/gd/content/record/save")
    public AjaxResult save(@RequestBody GdContentRecordBo gdContentRecordBo);

}
