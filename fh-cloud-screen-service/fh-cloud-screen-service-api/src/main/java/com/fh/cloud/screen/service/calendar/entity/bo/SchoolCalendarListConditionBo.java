package com.fh.cloud.screen.service.calendar.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 校历主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Data
public class SchoolCalendarListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long schoolCalendarId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
