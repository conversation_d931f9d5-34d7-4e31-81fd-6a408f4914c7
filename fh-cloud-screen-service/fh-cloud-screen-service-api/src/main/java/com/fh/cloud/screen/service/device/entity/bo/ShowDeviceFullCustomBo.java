package com.fh.cloud.screen.service.device.entity.bo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 云屏全屏非全屏设置自定义
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
@Data
public class ShowDeviceFullCustomBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long fullCustomId;

	/**
	 * 设备id
	 */
	@ApiModelProperty("设备id")
	private Long showDeviceId;

	/**
	 * 设备序列号【冗余】
	 */
	@ApiModelProperty("设备序列号【冗余】")
	private String deviceNumber;

	/**
	 * 是否全屏类型：1全屏，2不是全屏
	 */
	@ApiModelProperty("是否全屏类型：1全屏，2不是全屏")
	private Integer deviceFullType;

	/**
	 * 设置的改变状态的时间
	 */
	@ApiModelProperty("设置的改变状态的时间")
	private Date customTime;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 星期几：默认0不生效表示每一天；1-7，分别为星期一到星期日。
	 */
	@ApiModelProperty("星期几：默认0不生效表示每一天；1-7，分别为星期一到星期日。")
	private Integer week;

}
