package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 共话诗词图片资源表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
public interface ScreenPoetryPictureApi {

    /**
     * 查询共话诗词图片资源表分页列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @PostMapping("/screen/poetry/picture/page/list")
    public AjaxResult<PageInfo<ScreenPoetryPictureVo>> getScreenPoetryPicturePageListByCondition(@RequestBody ScreenPoetryPictureConditionBo condition);

    /**
     * 查询共话诗词图片资源表列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @PostMapping("/screen/poetry/picture/list")
    public AjaxResult<List<ScreenPoetryPictureVo>> getScreenPoetryPictureListByCondition(@RequestBody ScreenPoetryPictureConditionBo condition);


    /**
     * 新增共话诗词图片资源表
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @PostMapping("/screen/poetry/picture/add")
    public AjaxResult addScreenPoetryPicture(@Validated @RequestBody ScreenPoetryPictureBo screenPoetryPictureBo);

    /**
     * 修改共话诗词图片资源表
     * @param screenPoetryPictureBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @PostMapping("/screen/poetry/picture/update")
    public AjaxResult updateScreenPoetryPicture(@Validated @RequestBody ScreenPoetryPictureBo screenPoetryPictureBo);

    /**
     * 查询共话诗词图片资源表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @GetMapping("/screen/poetry/picture/detail")
    public AjaxResult<ScreenPoetryPictureVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除共话诗词图片资源表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @GetMapping("/screen/poetry/picture/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
