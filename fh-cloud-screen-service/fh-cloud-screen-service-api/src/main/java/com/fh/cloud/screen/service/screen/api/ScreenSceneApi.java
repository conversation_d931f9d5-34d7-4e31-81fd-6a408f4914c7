package com.fh.cloud.screen.service.screen.api;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.ApiOperation;

public interface ScreenSceneApi {

    /**
     * 查询云屏场景列表（附带模块id）
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/scene/list")
    @ApiOperation(value = "查询云屏场景列表（附带模块id）", httpMethod = "POST")
    AjaxResult getScreenSceneListByCondition(@RequestBody ScreenSceneListConditionBo condition);

    /**
     * 新增或者修改云屏场景
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/scene/save-update")
    @ApiOperation(value = "新增或者修改云屏场景", httpMethod = "POST")
    AjaxResult saveOrUpdateScreenScene(@RequestBody ScreenSceneBo screenSceneBo);

    /**
     * 新增或者修改云屏场景-批量
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/scene/save-batch")
    @ApiOperation(value = "新增或者修改云屏场景批量", httpMethod = "POST")
    AjaxResult saveOrUpdateScreenSceneBath(@RequestBody List<ScreenSceneBo> screenSceneBos);

    /**
     * 删除云屏场景表
     *
     * @param screenSceneId the screen scene id
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -06-14 12:07:45
     */
    @GetMapping("/screen/scene/delete")
    @ApiOperation(value = "删除云屏场景表", httpMethod = "GET")
    AjaxResult delete(@RequestParam("screenSceneId") Long screenSceneId,
                      @RequestParam(value = "organizationId", required = false) Long organizationId,
                      @RequestParam(value = "parentOrganizationId", required = false) Long parentOrganizationId);

    /**
     * 查询云屏场景表详情
     *
     * @param screenSceneId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/screen/scene/detail")
    @ApiOperation(value = "查询云屏场景表详情", httpMethod = "GET")
    AjaxResult getDetail(@RequestParam("screenSceneId") Long screenSceneId);

}
