package com.fh.cloud.screen.service.syllabus.service;

import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoWithRestVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.syllabus.api.SyllabusInfoApi;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 课表信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:16:07
 */
@FeignClient(contextId = "syllabusInfoApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = SyllabusInfoApiService.SyllabusInfoApiFallbackFactory.class)
@Component
public interface SyllabusInfoApiService extends SyllabusInfoApi {

    @Component
    class SyllabusInfoApiFallbackFactory implements FallbackFactory<SyllabusInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(SyllabusInfoApiFallbackFactory.class);

        @Override
        public SyllabusInfoApiService create(Throwable cause) {
            SyllabusInfoApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new SyllabusInfoApiService() {
                public AjaxResult getSyllabusInfoPageListByCondition(SyllabusInfoConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getSyllabusInfoListByCondition(SyllabusInfoConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addSyllabusInfo(SyllabusInfoBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateSyllabusInfo(SyllabusInfoBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<SyllabusInfoWithRestVo> getSyllabusInfoOfClasses(SyllabusInfoConditionBo syllabusInfoBo) {
                    return AjaxResult.fail("查询班级课表失败");
                }
            };
        }
    }
}