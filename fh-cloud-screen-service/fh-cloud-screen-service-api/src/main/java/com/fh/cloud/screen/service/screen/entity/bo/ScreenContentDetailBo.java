package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 云屏内容详情表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentDetailBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long screenContentDetailId;

    /**
     * FK云屏内容表
     */
    @NotNull(message = "FK云屏内容表不能为空")
    private Long screenContentId;

    /**
     * 顺序
     */
    @NotNull(message = "顺序不能为空")
    private Long screenContentIndex;

    /**
     * 云屏内容-标题
     */
    @NotBlank(message = "云屏内容-标题不能为空")
    private String screenContentTitle;

    /**
     * 云屏内容-文本
     */
    @NotBlank(message = "云屏内容-文本不能为空")
    private String screenContentTxt;

    /**
     * 云屏内容-url
     */
    @NotBlank(message = "云屏内容-url不能为空")
    private String screenContentUrl;

    /**
     * 云屏图片或者视频媒体地址
     */
    @NotBlank(message = "云屏图片或者视频媒体地址不能为空")
    private String screenContentMediaUrl;

    /**
     * 云屏图片或者视频媒体地址-压缩后
     */
    @NotBlank(message = "云屏图片或者视频媒体地址-压缩后不能为空")
    private String screenContentMediaUrlCompress;

    /**
     * 云屏图片或者视频媒体地址-封面
     */
    @NotBlank(message = "云屏图片或者视频媒体地址-封面不能为空")
    private String screenContentMediaUrlCover;

    /**
     * 云屏图片或者视频媒体名称（不包含后缀）
     */
    @NotBlank(message = "云屏图片或者视频媒体名称（不包含后缀）不能为空")
    private String screenContentMediaName;

    /**
     * 云屏图片或者视频原始媒体名称（包含后缀）
     */
    @NotBlank(message = "云屏图片或者视频原始媒体名称（包含后缀）不能为空")
    private String screenContentMediaNameOri;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 称呼内容
     */
    private String callContent;

    /**
     * 落款内容
     */
    private String signContent;

    /**
     * 云屏图片或者视频媒体fileoid
     */
    private String screenContentMediaId;

    /**
     * 云屏图片或者视频媒体fileoid-压缩后
     */
    private String screenContentMediaIdCompress;

    /**
     * 适用设备模式：1横屏，2竖屏。(目前欢迎图使用到该字段)
     */
    private Integer screenDevicePattern;

    /**
     * 云屏图片或者视频媒体md5
     */
    @ApiModelProperty("云屏图片或者视频媒体md5")
    private String screenContentMediaMd5;

    /**
     * 云屏图片或者视频媒体md5-压缩后
     */
    @ApiModelProperty("云屏图片或者视频媒体md5-压缩后")
    private String screenContentMediaMd5Compress;
}
