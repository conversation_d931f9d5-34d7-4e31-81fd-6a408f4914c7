package com.fh.cloud.screen.service.er.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考场_考试计划里面一次考试科目信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@Data
public class ExamInfoSubjectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoSubjectId;

    /**
     * 考试id
     */
    @ApiModelProperty("考试id")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 学科code
     */
    @ApiModelProperty("学科code")
    private String subjectCode;

    /**
     * 学科名称
     */
    @ApiModelProperty("学科名称")
    private String subjectName;

    /**
     * 准考证开始
     */
    @ApiModelProperty("准考证开始")
    private String atNoStart;

    /**
     * 准考证结束
     */
    @ApiModelProperty("准考证结束")
    private String atNoEnd;

    /**
     * 该场考试开始时间
     */
    @ApiModelProperty("该场考试开始时间")
    private Date examStartTime;

    /**
     * 该场考试结束时间
     */
    @ApiModelProperty("该场考试结束时间")
    private Date examEndTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 本场考试学生信息
     */
    private List<ExamInfoStudentVo> examInfoStudentVos;

    /**
     * 本场考试监考老师信息
     */
    private List<ExamInfoTeacherVo> examInfoTeacherVos;

    /**
     * 考场计划名称
     */
    @ApiModelProperty("考场计划名称")
    private String examPlanName;
    /**
     * 考场计划说明
     */
    @ApiModelProperty("考场计划说明")
    private String examPlanRemark;

    /**
     * 考场号（名称）
     */
    @ApiModelProperty("考场号（名称）")
    private String examRoomName;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 地点id
     */
    @ApiModelProperty("地点id")
    private Long spaceInfoId;

    /**
     * 科目的typeId：1全，2局，3校
     */
    private Long typeId;

    /**
     * 考场更新时间
     */
    private Date planUpdateTime;
}
