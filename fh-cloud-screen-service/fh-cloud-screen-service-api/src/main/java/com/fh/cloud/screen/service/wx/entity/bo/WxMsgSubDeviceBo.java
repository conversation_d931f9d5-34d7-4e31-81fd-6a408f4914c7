package com.fh.cloud.screen.service.wx.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 微信消息订阅设备配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
@Data
public class WxMsgSubDeviceBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 设备id
	 */
	@ApiModelProperty("设备id")
	private Long showDeviceId;

	/**
	 * 设备号
	 */
	@ApiModelProperty("设备号")
	private String deviceNumber;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 关注类型，1：关注，2：取消关注
	 */
	private Integer followType;

}
