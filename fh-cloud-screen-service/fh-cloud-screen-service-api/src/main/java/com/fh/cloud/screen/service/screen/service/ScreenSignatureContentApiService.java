package com.fh.cloud.screen.service.screen.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenSignatureContentApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 电子签名表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@FeignClient(contextId = "screenSignatureContentApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenSignatureContentApiService.ScreenSignatureContentApiFallbackFactory.class)
@Component
public interface ScreenSignatureContentApiService extends ScreenSignatureContentApi {

    @Component
    class ScreenSignatureContentApiFallbackFactory implements FallbackFactory<ScreenSignatureContentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenSignatureContentApiFallbackFactory.class);

        @Override
        public ScreenSignatureContentApiService create(Throwable cause) {
            ScreenSignatureContentApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ScreenSignatureContentApiService() {
                public AjaxResult
                    getScreenSignatureContentPageListByCondition(ScreenSignatureContentConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult
                    getScreenSignatureContentListByCondition(ScreenSignatureContentConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenSignatureContent(ScreenSignatureContentBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenSignatureContent(ScreenSignatureContentBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}