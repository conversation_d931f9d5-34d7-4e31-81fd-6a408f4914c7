package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏场景审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
@Data
public class ScreenSceneAuditVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenSceneAuditId;

    /**
     * FK所属组织ID
     */
    @ApiModelProperty("FK所属组织ID")
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    @ApiModelProperty("FK所属校区ID")
    private Long campusId;

    /**
     * FK空间ID或者班级id
     */
    @ApiModelProperty("FK空间ID或者班级id")
    private Long spaceInfoId;

    /**
     * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * FK设备id
     */
    @ApiModelProperty("FK设备id")
    private Long showDeviceId;

    /**
     * 场景名称
     */
    @ApiModelProperty("场景名称")
    private String screenSceneName;

    /**
     * 云屏场景布局，透传前端数据
     */
    @ApiModelProperty("云屏场景布局，透传前端数据")
    private String screenSceneLayout;

    /**
     * 地点组id
     */
    @ApiModelProperty("地点组id")
    private Long spaceGroupId;

    /**
     * 场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景
     */
    @ApiModelProperty("场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景")
    private Long screenSceneType;

    /**
     * 设备模式：1横屏，2竖屏。
     */
    @ApiModelProperty("设备模式：1横屏，2竖屏。")
    private Integer screenDevicePattern;

    /**
     * 场景顺序：1，2，3...
     */
    @ApiModelProperty("场景顺序：1，2，3...")
    private Long screenIndex;

    /**
     * 同一个场景内轮播的场景名称
     */
    @ApiModelProperty("同一个场景内轮播的场景名称")
    private String screenPlayName;

    /**
     * 同一个场景内的轮播的场景顺序：1，2，3...
     */
    @ApiModelProperty("同一个场景内的轮播的场景顺序：1，2，3...")
    private Long screenPlayIndex;

    /**
     * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
     */
    @ApiModelProperty("场景时间-开始时间，多个轮播场景的时候，场景时间相同")
    private Date startTime;

    /**
     * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
     */
    @ApiModelProperty("场景时间-开始时间，多个轮播场景的时候，场景时间相同")
    private Date endTime;

    /**
     * 发布方式：1全局发布，2点位发布
     */
    @ApiModelProperty("发布方式：1全局发布，2点位发布")
    private Integer publishType;

    /**
     * 自定义场景是否全屏类型：1全屏，2不是全屏
     */
    @ApiModelProperty("自定义场景是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 审核状态 1-待审核 2-审核通过 3-审核驳回
     */
    @ApiModelProperty("审核状态 1-待审核 2-审核通过 3-审核驳回")
    private Integer auditType;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String reason;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 设备号
     */
    @ApiModelProperty("设备号")
    private String deviceNumber;

    /**
     * 设备类型：1云屏。设备绑定的时候更新
     */
    @ApiModelProperty("设备类型：1云屏。设备绑定的时候更新")
    private Integer deviceType;

    /**
     * 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    @ApiModelProperty("设备模式：1横屏，2竖屏。设备绑定的时候更新")
    private Integer devicePattern;

    /**
     * 开关机状态：1开机，2关机，3异常，4开机中，5关机中
     */
    @ApiModelProperty("开关机状态：1开机，2关机，3异常，4开机中，5关机中")
    private Integer deviceStatus;

    /**
     * 系统主动推送海报：1接受；2不接受
     */
    @ApiModelProperty("系统主动推送海报：1接受；2不接受")
    private Integer pushType;

    /**
     * 设备海报播放间隔时长（秒）
     */
    @ApiModelProperty("设备海报播放间隔时长（秒）")
    private Integer devicePosterDuration;

    /**
     * 地点名称
     */
    private String spaceInfoName;

    /**
     * 校区名称
     */
    private String campusName;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    private String auditUser;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private Date auditTime;

    /**
     * 场景下的模块列表
     */
    private List<ScreenModuleDataVo> screenModuleDataVos;

    /**
     * 开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date startDate;

    /**
     * 结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date endDate;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效")
    private String weeks;

    /**
     * 监管教育局id
     */
    @ApiModelProperty("监管教育局id")
    private Long parentOrganizationId;

    @ApiModelProperty("组织名")
    private String organizationName;

    /**
     * 区域组名称
     */
    @ApiModelProperty("区域组名称")
    private String spaceGroupName;

    /*
     * 方便steam流存入自身
     * */
    public ScreenSceneAuditVo returnOwn() {
        return this;
    }

}
