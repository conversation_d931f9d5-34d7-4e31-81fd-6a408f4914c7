package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 云屏模块库媒体资源审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
@Data
public class ScreenModuleLibraryMediaAuditBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenModuleLibraryMediaAuditId;

	/**
	 * FK模块库表
	 */
	@ApiModelProperty("FK模块库表")
	private Long screenModuleLibraryAuditId;

	/**
	 * 云屏图片或者视频媒体地址
	 */
	@ApiModelProperty("云屏图片或者视频媒体地址")
	private String screenModuleLibraryMediaUrl;

	/**
	 * 云屏图片或者视频媒体地址-压缩后
	 */
	@ApiModelProperty("云屏图片或者视频媒体地址-压缩后")
	private String screenModuleLibraryMediaUrlCompress;

	/**
	 * 云屏图片或者视频媒体地址-封面
	 */
	@ApiModelProperty("云屏图片或者视频媒体地址-封面")
	private String screenModuleLibraryMediaUrlCover;

	/**
	 * 云屏图片或者视频媒体名称（不包含后缀）
	 */
	@ApiModelProperty("云屏图片或者视频媒体名称（不包含后缀）")
	private String screenModuleLibraryMediaName;

	/**
	 * 云屏图片或者视频原始媒体名称（包含后缀）
	 */
	@ApiModelProperty("云屏图片或者视频原始媒体名称（包含后缀）")
	private String screenModuleLibraryMediaNameOri;

	/**
	 * 云屏图片或者视频媒体fileoid
	 */
	@ApiModelProperty("云屏图片或者视频媒体fileoid")
	private String screenContentMediaId;

	/**
	 * 云屏图片或者视频媒体fileoid-压缩后
	 */
	@ApiModelProperty("云屏图片或者视频媒体fileoid-压缩后")
	private String screenContentMediaIdCompress;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 图片版式：1横屏，2竖屏。
	 */
	@ApiModelProperty("图片版式：1横屏，2竖屏。")
	private Integer devicePattern;

	/**
	 * 文件md5
	 */
	@ApiModelProperty("文件md5")
	private String screenContentMediaMd5;

	/**
	 * 海报图片排序
	 */
	@ApiModelProperty("海报图片排序")
	private Long mediaSort;

	/**
	 * 更新时间
	 */
	@NotNull(message = "更新时间不能为空")
	private Date createTime;

	/**
	 * 创建人
	 */
	@NotBlank(message = "创建人不能为空")
	private String createBy;

	/**
	 * 创建时间
	 */
	@NotNull(message = "创建时间不能为空")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@NotBlank(message = "更新人不能为空")
	private String updateBy;

}
