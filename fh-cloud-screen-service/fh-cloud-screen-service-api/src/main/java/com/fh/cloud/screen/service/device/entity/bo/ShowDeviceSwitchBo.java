package com.fh.cloud.screen.service.device.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 开关机设置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ShowDeviceSwitchBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long showDeviceSwitchId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @NotNull(message = "所属校区ID不能为空")
    private Long campusId;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    @NotNull(message = "星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值不能为空")
    private Integer week;

    /**
     * 开机时间，前端控制传递格式
     */
    @NotNull(message = "开机时间不能为空")
    private Date onTime;

    /**
     * 关机时间，前端控制传递格式
     */
    @NotNull(message = "关机时间不能为空")
    private Date offTime;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

}
