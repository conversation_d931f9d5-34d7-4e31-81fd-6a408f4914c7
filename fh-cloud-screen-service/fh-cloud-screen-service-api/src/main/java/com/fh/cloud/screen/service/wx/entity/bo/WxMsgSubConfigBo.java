package com.fh.cloud.screen.service.wx.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 微信消息订阅用户配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
@Data
public class WxMsgSubConfigBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 用户oid
	 */
	@ApiModelProperty("用户oid")
	private String userOid;

	/**
	 * 星期，1-7对应周一到周日
	 */
	@ApiModelProperty("星期，1-7对应周一到周日")
	private String week;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
