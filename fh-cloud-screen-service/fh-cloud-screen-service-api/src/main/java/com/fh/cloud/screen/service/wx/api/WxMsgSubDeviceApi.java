package com.fh.cloud.screen.service.wx.api;


import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceConditionBo;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceBo;
import com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubDeviceVo;

import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 微信消息订阅设备配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
public interface WxMsgSubDeviceApi {

    /**
     * 查询微信消息订阅设备配置表分页列表
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/device/page/list")
    public AjaxResult<PageInfo<WxMsgSubDeviceVo>> getWxMsgSubDevicePageListByCondition(@RequestBody WxMsgSubDeviceConditionBo condition);

    /**
     * 查询微信消息订阅设备配置表列表
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/device/list")
    public AjaxResult<List<WxMsgSubDeviceVo>> getWxMsgSubDeviceListByCondition(@RequestBody WxMsgSubDeviceConditionBo condition);


    /**
     * 新增微信消息订阅设备配置表
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/device/add")
    public AjaxResult addWxMsgSubDevice(@Validated @RequestBody WxMsgSubDeviceBo wxMsgSubDeviceBo);

    /**
     * 修改微信消息订阅设备配置表
     * @param wxMsgSubDeviceBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/device/update")
    public AjaxResult updateWxMsgSubDevice(@Validated @RequestBody WxMsgSubDeviceBo wxMsgSubDeviceBo);

    /**
     * 查询微信消息订阅设备配置表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @GetMapping("/wx/msg/sub/device/detail")
    public AjaxResult<WxMsgSubDeviceVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除微信消息订阅设备配置表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @GetMapping("/wx/msg/sub/device/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 关注/取消关注设备
     *
     * @param wxMsgSubDeviceBo
     * @return
     */
    @PostMapping("/wx/msg/sub/device/follow")
    public AjaxResult followDevice(@RequestBody WxMsgSubDeviceBo wxMsgSubDeviceBo);

    /**
     * 获取关注设备数
     *
     * @return
     */
    @GetMapping("/wx/msg/sub/count")
    public AjaxResult wxMsgSubCount();

}
