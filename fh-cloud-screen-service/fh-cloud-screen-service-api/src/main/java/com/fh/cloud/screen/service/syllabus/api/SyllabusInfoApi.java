package com.fh.cloud.screen.service.syllabus.api;

import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoWithRestVo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 课表信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:16:07
 */
public interface SyllabusInfoApi {

    /**
     * 查询课表信息分页列表
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @PostMapping("/syllabus/info/page/list")
    public AjaxResult<PageInfo<SyllabusInfoVo>>
        getSyllabusInfoPageListByCondition(@RequestBody SyllabusInfoConditionBo condition);

    /**
     * 查询课表信息列表
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @PostMapping("/syllabus/info/list")
    public AjaxResult<List<SyllabusInfoVo>>
        getSyllabusInfoListByCondition(@RequestBody SyllabusInfoConditionBo condition);

    /**
     * 新增课表信息
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @PostMapping("/syllabus/info/add")
    public AjaxResult addSyllabusInfo(@Validated @RequestBody SyllabusInfoBo syllabusInfoBo);

    /**
     * 修改课表信息
     * 
     * @param syllabusInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @PostMapping("/syllabus/info/update")
    public AjaxResult updateSyllabusInfo(@Validated @RequestBody SyllabusInfoBo syllabusInfoBo);

    /**
     * 查询课表信息详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @GetMapping("/syllabus/info/detail")
    public AjaxResult<SyllabusInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除课表信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @GetMapping("/syllabus/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询带作息时间的班级课表数据
     *
     * @param syllabusInfoBo 参数：organizationId,classesId
     * @return syllabus info with rest vo
     * <AUTHOR>
     * @date 2023 -09-19 14:49:19
     */
    @PostMapping("/syllabus/info-classes")
    AjaxResult<SyllabusInfoWithRestVo> getSyllabusInfoOfClasses(@RequestBody SyllabusInfoConditionBo syllabusInfoBo);

}
