package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 云屏场景审核计数vo
 *
 * <AUTHOR>
 * @date 2023-11-30 17:31
 */
@Data
public class ScreenSceneAuditCountVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("待审核数量")
    private Integer toAuditCount;

    @ApiModelProperty("审核通过数量")
    private Integer auditPassCount;

    @ApiModelProperty("审核驳回数量")
    private Integer auditRejectCount;
}
