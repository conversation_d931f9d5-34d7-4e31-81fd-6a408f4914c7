package com.fh.cloud.screen.service.screen.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 云屏场景模块关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
@Data
public class ScreenSceneModuleRelBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * FK场景ID
     */
    @NotNull(message = "FK场景ID不能为空")
    private Long screenSceneId;

    /**
     * FK模块ID
     */
    @NotNull(message = "FK模块ID不能为空")
    private Long screenModuleDataId;

    /**
     * FK模块库ID
     */
    private Long screenModuleLibraryId;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 海报多选id，使用逗号分割
     */
    private String screenModuleLibrarySelIds;

}
