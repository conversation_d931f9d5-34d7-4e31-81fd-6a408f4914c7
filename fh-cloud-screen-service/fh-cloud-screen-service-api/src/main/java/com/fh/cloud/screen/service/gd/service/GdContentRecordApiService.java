package com.fh.cloud.screen.service.gd.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.gd.api.GdContentRecordApi;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordBo;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordConditionBo;
import com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 稿定内容记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
@FeignClient(contextId = "gdContentRecordApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = GdContentRecordApiService.GdContentRecordApiFallbackFactory.class)
@Component
public interface GdContentRecordApiService extends GdContentRecordApi {

    @Component
    class GdContentRecordApiFallbackFactory implements FallbackFactory<GdContentRecordApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(GdContentRecordApiFallbackFactory.class);
        @Override
        public GdContentRecordApiService create(Throwable cause) {
            GdContentRecordApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new GdContentRecordApiService() {
                public AjaxResult getGdContentRecordPageListByCondition(GdContentRecordConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getGdContentRecordListByCondition(GdContentRecordConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addGdContentRecord(GdContentRecordBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateGdContentRecord(GdContentRecordBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<GdContentRecordVo> getDetailCondition(GdContentRecordConditionBo gdContentRecordConditionBo) {
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult updateCondition(GdContentRecordConditionBo gdContentRecordConditionBo) {
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult save(GdContentRecordBo gdContentRecordBo) {
                    return AjaxResult.fail("保存失败");
                }
            };
        }
    }
}