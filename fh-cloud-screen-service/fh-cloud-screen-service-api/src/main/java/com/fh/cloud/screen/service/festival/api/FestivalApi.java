package com.fh.cloud.screen.service.festival.api;


import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalBo;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
/**
 * 节日表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
public interface FestivalApi {

    /**
     * 查询节日表分页列表
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @PostMapping("festival/page/list")
    public AjaxResult<PageInfo<FestivalVo>> getFestivalPageListByCondition(@RequestBody FestivalConditionBo condition);

    /**
     * 查询节日表列表
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @PostMapping("festival/list")
    public AjaxResult<List<FestivalVo>> getFestivalListByCondition(@RequestBody FestivalConditionBo condition);


    /**
     * 新增节日表
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @PostMapping("festival/add")
    public AjaxResult addFestival(@Validated @RequestBody FestivalBo festivalBo);

    /**
     * 修改节日表
     * @param festivalBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @PostMapping("festival/update")
    public AjaxResult updateFestival(@Validated @RequestBody FestivalBo festivalBo);

    /**
     * 查询节日表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @GetMapping("festival/detail")
    public AjaxResult<FestivalVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("festivalId") Long festivalId);

    /**
     * 删除节日表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:17:01
     */
    @GetMapping("festival/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("festivalId") Long festivalId);

    /**
    *通过日期获取当天节假日列表
    *
    * @param date
    * @return com.light.core.entity.AjaxResult
    * <AUTHOR>
    * @date 2023/3/3 11:39
    */
    @GetMapping("festival/list-by-date")
    public AjaxResult getFestivalListByDate(@RequestParam("date") String date);
}
