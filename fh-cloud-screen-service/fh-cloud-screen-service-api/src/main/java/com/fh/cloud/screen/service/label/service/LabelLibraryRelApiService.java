package com.fh.cloud.screen.service.label.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.label.api.LabelLibraryRelApi;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 标签海报关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@FeignClient(contextId = "labelLibraryRelApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = LabelLibraryRelApiService.LabelLibraryRelApiFallbackFactory.class)
@Component
public interface LabelLibraryRelApiService extends LabelLibraryRelApi {

    @Component
    class LabelLibraryRelApiFallbackFactory implements FallbackFactory<LabelLibraryRelApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LabelLibraryRelApiFallbackFactory.class);

        @Override
        public LabelLibraryRelApiService create(Throwable cause) {
            LabelLibraryRelApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LabelLibraryRelApiService() {
                public AjaxResult getLabelLibraryRelPageListByCondition(LabelLibraryRelConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLabelLibraryRelListByCondition(LabelLibraryRelConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLabelLibraryRel(LabelLibraryRelBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLabelOrFestivalLibraryRel(LabelLibraryRelBo labelLibraryRelBo) {
                    return AjaxResult.fail("新增或编辑标签和节日与海报关联关系失败");
                }

                public AjaxResult updateLabelLibraryRel(LabelLibraryRelBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}