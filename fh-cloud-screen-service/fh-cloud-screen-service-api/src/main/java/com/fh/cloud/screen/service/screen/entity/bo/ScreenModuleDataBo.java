package com.fh.cloud.screen.service.screen.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏模块表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenModuleDataBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long screenModuleDataId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * FK模块库表id，学校自定义模块的时候，该值为0
     */
    @NotNull(message = "FK模块库表id，学校自定义模块的时候，该值为0不能为空")
    private Long screenModuleLibraryId;

    /**
     * 模块来源类型：1预置模块，2自定义模块
     */
    @NotNull(message = "模块来源类型：1预置模块，2自定义模块不能为空")
    private Integer moduleSource;

    /**
     * 自定义模块名称
     */
    @NotBlank(message = "自定义模块名称不能为空")
    private String customModuleName;

    /**
     * 自定义模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    @NotNull(message = "自定义模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容不能为空")
    private Long customModuleGroupType;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 前端选择的模块字段库id(已有+新选择+去掉的[不会提交])
     */
    private List<Long> selectModuleLibraryIds;

    /**
     * 海报多选的id，多个使用逗号分割
     */
    private String screenModuleLibrarySelIds;
}
