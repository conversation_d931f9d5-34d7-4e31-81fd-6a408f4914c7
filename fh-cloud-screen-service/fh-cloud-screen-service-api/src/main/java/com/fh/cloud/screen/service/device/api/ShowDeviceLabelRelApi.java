package com.fh.cloud.screen.service.device.api;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备订阅标签表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
public interface ShowDeviceLabelRelApi {

    /**
     * 查询设备订阅标签表分页列表
     * 
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("show/device/label/rel/page/list")
    public AjaxResult<PageInfo<ShowDeviceLabelRelVo>>
        getShowDeviceLabelRelPageListByCondition(@RequestBody ShowDeviceLabelRelConditionBo condition);

    /**
     * 查询设备订阅标签表列表
     * 
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("show/device/label/rel/list")
    public AjaxResult<List<ShowDeviceLabelRelVo>>
        getShowDeviceLabelRelListByCondition(@RequestBody ShowDeviceLabelRelConditionBo condition);

    /**
     * 新增设备订阅标签表
     * 
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("show/device/label/rel/add")
    public AjaxResult addShowDeviceLabelRel(@Validated @RequestBody ShowDeviceLabelRelBo showDeviceLabelRelBo);

    /**
     * 修改设备订阅标签表
     * 
     * @param showDeviceLabelRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("show/device/label/rel/update")
    public AjaxResult updateShowDeviceLabelRel(@Validated @RequestBody ShowDeviceLabelRelBo showDeviceLabelRelBo);

    /**
     * 查询设备订阅标签表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @GetMapping("show/device/label/rel/detail")
    public AjaxResult<ShowDeviceLabelRelVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除设备订阅标签表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @GetMapping("show/device/label/rel/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
