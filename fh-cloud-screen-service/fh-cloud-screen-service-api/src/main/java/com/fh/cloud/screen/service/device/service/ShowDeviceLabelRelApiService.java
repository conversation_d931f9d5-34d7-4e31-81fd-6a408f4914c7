package com.fh.cloud.screen.service.device.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.api.ShowDeviceLabelRelApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 设备订阅标签表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
@FeignClient(contextId = "showDeviceLabelRelApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ShowDeviceLabelRelApiService.ShowDeviceLabelRelApiFallbackFactory.class)
@Component
public interface ShowDeviceLabelRelApiService extends ShowDeviceLabelRelApi {

    @Component
    class ShowDeviceLabelRelApiFallbackFactory implements FallbackFactory<ShowDeviceLabelRelApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ShowDeviceLabelRelApiFallbackFactory.class);

        @Override
        public ShowDeviceLabelRelApiService create(Throwable cause) {
            ShowDeviceLabelRelApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ShowDeviceLabelRelApiService() {
                public AjaxResult getShowDeviceLabelRelPageListByCondition(ShowDeviceLabelRelConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getShowDeviceLabelRelListByCondition(ShowDeviceLabelRelConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addShowDeviceLabelRel(ShowDeviceLabelRelBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateShowDeviceLabelRel(ShowDeviceLabelRelBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}