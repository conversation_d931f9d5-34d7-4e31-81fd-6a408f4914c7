package com.fh.cloud.screen.service.campus.api;

import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.light.core.entity.AjaxResult;
import com.light.user.clazz.entity.vo.ClazzVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ClassesScreenApi {

    /**
     * 根据ID获取班级
     * 
     * @return
     */
    @GetMapping("/classes/getById/{classesId}")
    AjaxResult<ClazzVo> getById(@PathVariable("classesId") Long classesId);

}
