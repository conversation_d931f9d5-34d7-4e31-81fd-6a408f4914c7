package com.fh.cloud.screen.service.delayqueue.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.lang.UUID;
import lombok.Data;

/**
 * 一个延迟任务，注意依赖服务器时钟
 *
 * <AUTHOR>
 * @date 2022 /12/21 14:07
 */
@Data
public class TaskDelayed<T> implements Delayed, Serializable {

    /**
     * 任务id(唯一id，可以是业务的唯一码或主键都可以)
     */
    private String taskId;

    /**
     * 开始时间，添加任务时候的时间戳
     */
    private long startTime;
    /**
     * 延迟时间（毫秒）
     */
    private long delay;
    /**
     * 到期时间=startTime + delay（毫秒）
     */
    private long expire;
    /**
     * 创建时间
     */
    private Date now;
    /**
     * 泛型data
     */
    private T data;

    /**
     * Instantiates a new Task delayed.
     *
     * @param taskId 任务id(如果为空则自动生成uuid)
     * @param startTime 任务开始时间(如果为空则默认未当前时间戳)
     * @param secondsDelay 延迟的时间（不可为空，单位s）
     * @param data 数据
     */
    public TaskDelayed(String taskId, Long startTime, Long secondsDelay, T data) {
        super();
        if (StringUtils.isBlank(taskId)) {
            taskId = UUID.fastUUID().toString();
        }
        Date now = new Date();
        if (startTime == null) {
            startTime = now.getTime();
        }

        this.taskId = taskId;
        this.startTime = startTime;
        this.delay = secondsDelay * 1000;
        this.expire = startTime + (secondsDelay * 1000);
        this.now = now;
        this.data = data;
    }

    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert(this.expire - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        return (int)(this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS));
    }
}
