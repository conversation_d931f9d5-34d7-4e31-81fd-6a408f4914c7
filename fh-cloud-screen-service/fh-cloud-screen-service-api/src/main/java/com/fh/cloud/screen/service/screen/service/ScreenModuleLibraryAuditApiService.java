package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryAuditApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 模块库审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
@FeignClient(contextId = "screenModuleLibraryAuditApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenModuleLibraryAuditApiService.ScreenModuleLibraryAuditApiFallbackFactory.class)
@Component
public interface ScreenModuleLibraryAuditApiService extends ScreenModuleLibraryAuditApi {

    @Component
    class ScreenModuleLibraryAuditApiFallbackFactory implements FallbackFactory<ScreenModuleLibraryAuditApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenModuleLibraryAuditApiFallbackFactory.class);

        @Override
        public ScreenModuleLibraryAuditApiService create(Throwable cause) {
            ScreenModuleLibraryAuditApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenModuleLibraryAuditApiService() {
                public AjaxResult
                    getScreenModuleLibraryAuditPageListByCondition(ScreenModuleLibraryAuditConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult
                    getScreenModuleLibraryAuditListByCondition(ScreenModuleLibraryAuditConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenModuleLibraryAudit(ScreenModuleLibraryAuditBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenModuleLibraryAudit(ScreenModuleLibraryAuditBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult getPosterAuditPage(ScreenModuleLibraryAuditConditionBo condition) {
                    return AjaxResult.fail("获取模块库审核表列表失败");
                }

                @Override
                public AjaxResult addPosterAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("新增海报审核数据失败");
                }

                @Override
                public AjaxResult updatePosterAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("编辑海报审核数据失败");
                }

                @Override
                public AjaxResult audit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("海报资源审核失败");
                }

                @Override
                public AjaxResult auditBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("海报资源批量审核失败");
                }

                @Override
                public AjaxResult getPosterAuditDetail(Long screenModuleLibraryAuditId) {
                    return AjaxResult.fail("海报资源审核详情");
                }

                @Override
                public AjaxResult releasePoster(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("海报资源发布、取消发布失败");
                }

                @Override
                public AjaxResult updatePosterAuditLabel(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("批量更换海报标签失败");
                }

                @Override
                public AjaxResult deleteBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("批量删除失败");
                }

                @Override
                public AjaxResult releasePosterBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
                    return AjaxResult.fail("批量发布/取消发布失败");
                }
            };
        }
    }
}