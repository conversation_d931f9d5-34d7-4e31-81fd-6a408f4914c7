package com.fh.cloud.screen.service.slow.service;

import java.util.List;

import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.fh.cloud.screen.service.slow.api.OpenSlowScreenApi;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.github.pagehelper.PageInfo;

/**
 * 学生信息表接口
 *
 */
@FeignClient(contextId = "open-slow-screen-api", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = OpenSlowScreenApiService.OpenSlowApiFallbackFactory.class)
@Component
public interface OpenSlowScreenApiService extends OpenSlowScreenApi {

    @Component
    class OpenSlowApiFallbackFactory implements FallbackFactory<OpenSlowScreenApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(OpenSlowApiFallbackFactory.class);

        @Override
        public OpenSlowScreenApiService create(Throwable cause) {
            OpenSlowApiFallbackFactory.LOGGER.error("open-slow-screen-api服务调用失败:{}", cause.getMessage());
            return new OpenSlowScreenApiService() {

                @Override
                public AjaxResult<PageInfo<SpaceInfoVo>> querySpacePageList(SpaceInfoListConditionBo condition) {
                    return AjaxResult.fail("querySpacePageList数据获取失败");
                }

                @Override
                public AjaxResult<List<SpaceInfoVo>> querySpaceList(SpaceInfoListConditionBo condition) {
                    return AjaxResult.fail("querySpaceList数据获取失败");
                }

                @Override
                public AjaxResult<PageInfo<ScreenSceneThirdVo>> queryThirdScenePageList(ScreenSceneThirdConditionBo condition) {
                    return AjaxResult.fail("queryThirdScenePageList数据获取失败");
                }

                @Override
                public AjaxResult<List<ScreenSceneThirdVo>> queryThirdSceneList(ScreenSceneThirdConditionBo condition) {
                    return AjaxResult.fail("queryThirdSceneList数据获取失败");
                }

                @Override
                public AjaxResult<String> syncFaceRecordsStudent(List<FaceRecordStudentBo> faceRecordStudentBoList) {
                    return AjaxResult.fail("同步学生人脸数据失败");
                }

                @Override
                public AjaxResult<String> syncFaceRecordsTeacher(List<FaceRecordTeacherBo> faceRecordTeacherBoList) {
                    return AjaxResult.fail("同步教师人脸数据失败");
                }

                @Override
                public AjaxResult<String> syncCards(List<UserCardBo> userCardBos) {
                    return AjaxResult.fail("同步卡号失败");
                }

                @Override
                public AjaxResult<String> syncSyllabus(List<SyllabusInfoBo> syllabusInfoBos) {
                    return AjaxResult.fail("同步课表失败");
                }
            };
        }
    }
}
