package com.fh.cloud.screen.service.calendar.api;

import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayOfMonthVo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SchoolCalendarApi {

    /**
     * 查询校历主表列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/9 16:44
     */
    @PostMapping("/calendar/list")
    AjaxResult getSchoolCalendarListByCondition(@RequestBody SchoolCalendarListConditionBo condition);

    /**
     * 通过组织id查询校历主表详情
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/10 11:02
     */
    @GetMapping("/calendar/detail-by-organizationId")
    AjaxResult getDetail(@RequestParam("organizationId") Long organizationId);

    /**
     * 根据月份 组织机构ID 获取 该月份 校历情况
     *
     *
     * @param attendanceMonth the attendance month 月份 （yyyy-MM）
     * @param organizationId the organization id 组织机构ID
     * @return
     */
    @GetMapping("/calendar/getDayInfoByMonthAndOrgId")
    AjaxResult<List<SchoolCalendarDayOfMonthVo>> getDayInfoByMonthAndOrgId(
        @RequestParam("attendanceMonth") String attendanceMonth, @RequestParam("organizationId") Long organizationId);
}
