package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏密码
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ScreenPwdListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long screenPwdId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 云屏密码
     */
    private String pwdValue;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
