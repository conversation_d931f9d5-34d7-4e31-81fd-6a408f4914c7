package com.fh.cloud.screen.service.device.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 开关机设置
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ShowDeviceSwitchListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long showDeviceSwitchId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 所属校区ID
     */
    private Long campusId;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    private Integer week;

    /**
     * 开机时间
     */
    private Date onTime;

    /**
     * 关机时间
     */
    private Date offTime;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
