package com.fh.cloud.screen.service.leaveschool.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 放学配置详情表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
@Data
public class LeaveSchoolConfigDetailConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 放学配置详情表id
	 */
	@ApiModelProperty("放学配置详情表id")
	private Long leaveSchoolConfigDetailId;

	/**
	 * 放学配置表id
	 */
	@ApiModelProperty("放学配置表id")
	private Long leaveSchoolConfigId;

	/**
	 * 年级
	 */
	@ApiModelProperty("年级")
	private String grade;

	/**
	 * 周几
	 */
	@ApiModelProperty("周几")
	private Long weekDay;

	/**
	 * 放学开始时间
	 */
	@ApiModelProperty("放学开始时间")
	private Date leaveSchoolStartTime;

	/**
	 * 放学结束时间
	 */
	@ApiModelProperty("放学结束时间")
	private Date leaveSchoolEndTime;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
