package com.fh.cloud.screen.service.device.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备巡查vo
 *
 * <AUTHOR>
 * @date 2023-12-04 11:37
 */
@Data
public class ShowDevicePatrolVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long showDeviceId;

    /**
     * 设备号
     */
    @ApiModelProperty("设备号")
    private String deviceNumber;

    /**
     * 巡查备注
     */
    @ApiModelProperty("巡查备注")
    private String patrolRemark;

    /**
     * 设备巡查状态：1未巡查，2已巡查
     */
    @ApiModelProperty("设备巡查状态：1未巡查，2已巡查")
    private Integer patrolType;

}
