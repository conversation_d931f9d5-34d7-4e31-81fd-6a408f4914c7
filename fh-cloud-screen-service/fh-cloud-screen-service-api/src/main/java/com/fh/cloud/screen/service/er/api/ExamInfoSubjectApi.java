package com.fh.cloud.screen.service.er.api;

import com.fh.cloud.screen.service.er.entity.bo.ExamInfoBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;

import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 考场_考试计划里面一次考试科目信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
public interface ExamInfoSubjectApi {

    /**
     * 查询考场_考试计划里面一次考试科目信息分页列表
     * 
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @PostMapping("/exam/info/subject/page/list")
    public AjaxResult<PageInfo<ExamInfoSubjectVo>>
        getExamInfoSubjectPageListByCondition(@RequestBody ExamInfoSubjectConditionBo condition);

    /**
     * 查询考场_考试计划里面一次考试科目信息列表
     * 
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @PostMapping("/exam/info/subject/list")
    public AjaxResult<List<ExamInfoSubjectVo>>
        getExamInfoSubjectListByCondition(@RequestBody ExamInfoSubjectConditionBo condition);

    /**
     * 新增考场_考试计划里面一次考试科目信息
     * 
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @PostMapping("/exam/info/subject/add")
    public AjaxResult addExamInfoSubject(@Validated @RequestBody ExamInfoSubjectBo examInfoSubjectBo);

    /**
     * 修改考场_考试计划里面一次考试科目信息
     * 
     * @param examInfoSubjectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @PostMapping("/exam/info/subject/update")
    public AjaxResult updateExamInfoSubject(@Validated @RequestBody ExamInfoSubjectBo examInfoSubjectBo);

    /**
     * 查询考场_考试计划里面一次考试科目信息详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @GetMapping("/exam/info/subject/detail")
    public AjaxResult<ExamInfoSubjectVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除考场_考试计划里面一次考试科目信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @GetMapping("/exam/info/subject/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 获取当前考试详情
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/1/16 10:00
     */
    @PostMapping("/now")
    @ApiOperation(value = "获取当前考试科目详情", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getNowExamInfoByCondition(@RequestBody ExamInfoSubjectConditionBo conditionBo);
}
