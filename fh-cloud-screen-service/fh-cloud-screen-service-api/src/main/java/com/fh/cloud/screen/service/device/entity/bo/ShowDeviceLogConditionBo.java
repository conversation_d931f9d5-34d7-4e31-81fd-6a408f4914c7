package com.fh.cloud.screen.service.device.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 设备日志表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
@Data
public class ShowDeviceLogConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 学校id，0表示公共配置
	 */
	@ApiModelProperty("学校id，0表示公共配置")
	private Long showDeviceId;

	/**
	 * 学校id，0表示公共配置，冗余存储
	 */
	@ApiModelProperty("学校id，0表示公共配置，冗余存储")
	private Long organizationId;

	/**
	 * 设备号,冗余存储
	 */
	@ApiModelProperty("设备号,冗余存储")
	private String deviceNumber;

	/**
	 * 抓日志状态：1抓取中，2抓取成功，3抓取失败
	 */
	@ApiModelProperty("抓日志状态：1抓取中，2抓取成功，3抓取失败")
	private Integer deviceLogStatus;

	/**
	 * 文件oid
	 */
	@ApiModelProperty("文件oid")
	private String deviceLogFileOid;

	/**
	 * 日志地址
	 */
	@ApiModelProperty("日志地址")
	private String deviceLogMediaUrl;

	/**
	 * 日志名称（不包含后缀）
	 */
	@ApiModelProperty("日志名称（不包含后缀）")
	private String deviceLogMediaName;

	/**
	 * 日志名称（包含后缀）
	 */
	@ApiModelProperty("日志名称（包含后缀）")
	private String deviceLogMediaNameOri;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
