package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 共话诗词图片资源表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
@Data
public class ScreenPoetryPictureVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenPoetryPictureId;

    /**
     * 所属组织id
     */
    @ApiModelProperty("所属组织id")
    private Long organizationId;

    /**
     * 共话诗词图片url
     */
    @ApiModelProperty("共话诗词图片url")
    private String screenPoetryPictureMediaUrl;

    /**
     * 共话诗词图片名称（不包含后缀）
     */
    @ApiModelProperty("共话诗词图片名称（不包含后缀）")
    private String screenPoetryPictureMediaName;

    /**
     * 共话诗词图片名称（包含后缀）
     */
    @ApiModelProperty("共话诗词图片名称（包含后缀）")
    private String screenPoetryPictureMediaNameOri;

    /**
     * 共话诗词图片fileOid
     */
    @ApiModelProperty("共话诗词图片fileOid")
    private String screenPoetryPictureMediaId;

    /**
     * 文件md5
     */
    @ApiModelProperty("文件md5")
    private String screenPoetryPictureMediaMd5;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ScreenPoetryPictureVo returnOwn() {
        return this;
    }

}
