package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenContentSpecialApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenContentSpecialApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenContentSpecialApiService.ScreenContentSpecialApiFallbackFactory.class)
@Component
public interface ScreenContentSpecialApiService extends ScreenContentSpecialApi {
    @Component
    class ScreenContentSpecialApiFallbackFactory implements FallbackFactory<ScreenContentSpecialApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenContentSpecialApiService.ScreenContentSpecialApiFallbackFactory.class);

        @Override
        public ScreenContentSpecialApiService create(Throwable cause) {
            ScreenContentSpecialApiService.ScreenContentSpecialApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}",
                cause.getMessage());

            return new ScreenContentSpecialApiService() {
                public AjaxResult
                    getScreenContentSpecialListByCondition(ScreenContentSpecialListConditionBo condition) {
                    return AjaxResult.fail("查询云屏紧急发布内容表列表失败");
                }

                public AjaxResult saveScreenContentSpecial(ScreenContentSpecialBo screenContentSpecialBo) {
                    return AjaxResult.fail("保存云屏紧急发布内容表失败");
                }

                public AjaxResult getDetail(Long screenContentSpecialId) {
                    return AjaxResult.fail("查询云屏紧急发布内容表详情失败");
                }

                public AjaxResult cancelSubmit(Long screenContentSpecialId) {
                    return AjaxResult.fail("紧急发布撤回失败");
                }

                @Override
                public AjaxResult submit(ScreenContentSpecialBo screenContentSpecialBo) {
                    return AjaxResult.fail("紧急发布发布失败");
                }
            };
        }
    }
}
