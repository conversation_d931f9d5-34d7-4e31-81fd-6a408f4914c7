package com.fh.cloud.screen.service.screen.entity.vo;

import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏场景表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ScreenSceneVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * （冗余一份场景优先级）场景优先级从高到底：1 -> 3
     */
    private Integer screenPriority;

    /**
     * 主键
     */
    private Long screenSceneId;

    /**
     * FK所属组织ID
     */
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    private Long campusId;

    /**
     * 场景名称
     */
    private String screenSceneName;

    /**
     * 云屏场景布局，透传前端数据
     */
    private String screenSceneLayout;

    /**
     * 地点组id
     */
    private Long spaceGroupId;

    /**
     * 场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景
     */
    private Long screenSceneType;

    /**
     * 设备模式：1横屏，2竖屏。
     */
    private Integer screenDevicePattern;

    /**
     * 场景顺序：1，2，3...
     */
    private Long screenIndex;

    /**
     * 同一个场景内轮播的场景名称
     */
    private String screenPlayName;

    /**
     * 同一个场景内的轮播的场景顺序：1，2，3...
     */
    private Long screenPlayIndex;

    /**
     * 场景轮播key（地点组id#场景类型#场景名称#轮播名称）
     */
    private String screenPlayKey;

    /**
     * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
     */
    private Date startTime;

    /**
     * 场景时间-结束时间，多个轮播场景的时候，场景时间相同
     */
    private Date endTime;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 空间id或者班级id
     */
    private Long spaceInfoId;

    /**
     * 空间群组使用类型：1行政，2非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 设备id
     */
    private Long showDeviceId;

    /**
     * 该场景下的模块库关系列表（由原先的ids改为对象，增加了libraryId字段）
     */
    private List<ScreenSceneModuleRelVo> screenSceneModuleRelVos;

    /**
     * 场景下的模块列表
     */
    private List<ScreenModuleDataVo> screenModuleDataVos;

    /**
     * 根据screenPlayKey组装为不同的组
     */
    private List<ScreenSceneVo> children;

    /**
     * 紧急场景的内容列表
     */
    private List<ScreenContentSpecialVo> screenContentSpecialVos;

    /**
     * 功能场景的类型：1课后延迟服务。2会议
     */
    private Integer functionalType;

    /**
     * 发布方式：1统一发布，2点位发布
     */
    private Integer publishType;

    /**
     * 第三方对接展示url
     */
    private String screenSceneThirdShowUrl;

    /**
     * 第三方对接提交url
     */
    private String screenSceneThirdPostUrl;

    /**
     * 自定义场景是否全屏类型：1全屏，2不是全屏
     */
    @ApiModelProperty(value = "自定义场景是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 审核表主键
     */
    @ApiModelProperty("审核表主键")
    private Long screenSceneAuditId;

    /**
     * 审核状态 1-待审核 2-审核通过 3-审核驳回
     */
    @ApiModelProperty("审核状态 1-待审核 2-审核通过 3-审核驳回")
    private Integer auditType;

    /**
     * 驳回原因
     */
    @ApiModelProperty("驳回原因")
    private String reason;

    /**
     * 开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date startDate;

    /**
     * 结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date endDate;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效")
    private String weeks;

    /**
     * 监管教育局id
     */
    @ApiModelProperty("监管教育局id")
    private Long parentOrganizationId;

    @ApiModelProperty("校区名称")
    private String campusName;

    @ApiModelProperty("地点名称")
    private String spaceInfoName;
}
