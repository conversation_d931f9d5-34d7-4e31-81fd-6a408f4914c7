package com.fh.cloud.screen.service.screen.entity.bo;

import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模块库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenModuleLibraryBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long screenModuleLibraryId;

    /**
     * 模块名称
     */
    @NotBlank(message = "模块名称不能为空")
    private String moduleName;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    @NotNull(message = "模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容不能为空")
    private Long moduleGroupType;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 预置类型：1预置，2不预置
     */
    @NotNull(message = "预置类型：1预置，2不预置")
    private Integer presetType;

    /**
     * 父模块库id
     */
    private Long parentScreenModuleLibraryId;

    /**
     * 排序
     */
    private Integer librarySort;

    /**
     * 是否海报：1是，2否
     */
    private Integer isPoster;

    // /**
    // * 海报模块版式：1横屏，2竖屏
    // */
    // private Integer libraryPattern;
    //
    // /**
    // * 海报模块类型：1系统上传，2用户上传
    // */
    // private Integer posterSource;
    /**
     * 主题关联标签列表
     */
    private List<LabelBo> labelBos;

    /**
     * 主题关联海报图片
     */
    private List<ScreenModuleLibraryMediaBo> screenModuleLibraryMediaBos;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    private Integer librarySource;

    /**
     * 模块来源id
     */
    private String thirdId;
}
