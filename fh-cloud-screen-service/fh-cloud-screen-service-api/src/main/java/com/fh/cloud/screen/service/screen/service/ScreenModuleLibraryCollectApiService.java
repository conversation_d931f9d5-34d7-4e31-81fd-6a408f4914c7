package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryCollectApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 海报收藏表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@FeignClient(contextId = "screenModuleLibraryCollectApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenModuleLibraryCollectApiService.ScreenModuleLibraryCollectApiFallbackFactory.class)
@Component
public interface ScreenModuleLibraryCollectApiService extends ScreenModuleLibraryCollectApi {

    @Component
    class ScreenModuleLibraryCollectApiFallbackFactory
        implements FallbackFactory<ScreenModuleLibraryCollectApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenModuleLibraryCollectApiFallbackFactory.class);

        @Override
        public ScreenModuleLibraryCollectApiService create(Throwable cause) {
            ScreenModuleLibraryCollectApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenModuleLibraryCollectApiService() {
                public AjaxResult
                    getScreenModuleLibraryCollectPageListByCondition(ScreenModuleLibraryCollectConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult
                    getScreenModuleLibraryCollectListByCondition(ScreenModuleLibraryCollectConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}