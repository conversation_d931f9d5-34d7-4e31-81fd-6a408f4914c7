package com.fh.cloud.screen.service.leaveschool.api;


import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordSaveBatchBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 学生进出记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:58:52
 */
public interface LeaveSchoolStudentSignRecordApi {

    /**
     * 查询学生进出记录表分页列表
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @PostMapping("/leave/school/student/sign/record/page/list")
    public AjaxResult<PageInfo<LeaveSchoolStudentSignRecordVo>> getLeaveSchoolStudentSignRecordPageListByCondition(@RequestBody LeaveSchoolStudentSignRecordConditionBo condition);

    /**
     * 查询学生进出记录表列表
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @PostMapping("/leave/school/student/sign/record/list")
    public AjaxResult<List<LeaveSchoolStudentSignRecordVo>> getLeaveSchoolStudentSignRecordListByCondition(@RequestBody LeaveSchoolStudentSignRecordConditionBo condition);


    /**
     * 新增学生进出记录表
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @PostMapping("/leave/school/student/sign/record/add")
    public AjaxResult addLeaveSchoolStudentSignRecord(@Validated @RequestBody LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo);

    /**
     * 修改学生进出记录表
     * @param leaveSchoolStudentSignRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @PostMapping("/leave/school/student/sign/record/update")
    public AjaxResult updateLeaveSchoolStudentSignRecord(@Validated @RequestBody LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo);

    /**
     * 查询学生进出记录表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @GetMapping("/leave/school/student/sign/record/detail")
    public AjaxResult<LeaveSchoolStudentSignRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除学生进出记录表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @GetMapping("/leave/school/student/sign/record/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 批量新增学生进出记录
     *
     * @param leaveSchoolStudentSignRecordSaveBatchBo
     * @return
     */
    @PostMapping("/leave/school/student/sign/record/add-batch")
    public AjaxResult addBatch(@RequestBody LeaveSchoolStudentSignRecordSaveBatchBo leaveSchoolStudentSignRecordSaveBatchBo);

}
