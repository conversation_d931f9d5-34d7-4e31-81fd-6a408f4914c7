package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long screenContentId;

    /**
     * FK所属组织ID
     */
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    private Long campusId;

    /**
     * FK所属班级ID
     */
    private Long classesId;

    /**
     * 内容所属范围：1校级，2班级
     */
    private Integer scopeType;

    /**
     * FK云屏模块表id
     */
    private Long screenModuleDataId;

    /**
     * 发布状态：1未发布，2已发布
     */
    private Integer screenContentStatus;

    /**
     * 模块内容类型：默认0，1网页地址，2富文本，3图片，4视频，5欢迎图
     */
    private Integer screenContentType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 标题
     */
    private String screenContentTitle;

    /**
     * 云屏文本内容
     */
    private String screenContentTxt;

    /**
     * 有效时间-开始时间
     */
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    private Date endTime;

    /**
     * 模块数据id集合，用于限定查询
     */
    private List<Long> screenModuleDataIds;

    /**
     * 当天查询条件（设备查询的数据，首页只查询当天可以展示的数据）
     */
    private Date nowDate;

    /**
     * 传一个日期，会查询小于这个日期的数据（endTime< overdueDate）。overdueDate通常传当前时间
     */
    private Date overdueDate;

    /**
     * FK模块库表id，学校自定义模块的时候，该值为0
     */
    private Long screenModuleLibraryId;

    /**
     * 全校和校区多个的时候是否按照各自独立展示。true表示是，仅查询全校或者校区（web端）
     */
    private boolean singleShow = false;

    /**
     * 是否云屏首页查询（查询校级+班级混合数据，云屏首页）
     */
    private boolean screenIndexShow = false;

    /**
     * 内容数据来源
     */
    private Integer screenContentSource;

    /**
     * 通知nice值，越小优越在前面（用于实现置顶功能）
     */
    private Integer contentNice;
}
