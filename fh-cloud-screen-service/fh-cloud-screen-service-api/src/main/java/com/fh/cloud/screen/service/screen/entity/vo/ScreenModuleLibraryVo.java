package com.fh.cloud.screen.service.screen.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模块库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenModuleLibraryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long screenModuleLibraryId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private Long moduleGroupType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 预置类型：1预置，2不预置
     */
    private Integer presetType;

    /**
     * 父模块库id
     */
    private Long parentScreenModuleLibraryId;

    /**
     * 排序
     */
    private Integer librarySort;

    /**
     * 是否海报：1是，2否
     */
    private Integer isPoster;

    // /**
    // * 海报模块版式：1横屏，2竖屏
    // */
    // private Integer libraryPattern;
    //
    // /**
    // * 海报模块类型：1系统上传，2用户上传
    // */
    // private Integer posterSource;

    /**
     * 海报列表
     */
    private List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVos;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private String moduleGroupTypeName;
    /**
     * 模块分组类型组织id
     */
    private Long organizationId;

    /**
     * 是否被收藏
     */
    private boolean collect;

    /**
     * 是否校本海报 1是，2否
     */
    private Integer isSchoolPoster;

    /**
     * 前端用于旧的逻辑部分模块比较，判断是否海报
     */
    private Integer moduleSource = 1;

    /**
     * 这个海报主题的标签拼接，多个用逗号隔开
     */
    private String labelNameConcat;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    private Integer librarySource;

    /**
     * 模块来源id
     */
    private String thirdId;
}
