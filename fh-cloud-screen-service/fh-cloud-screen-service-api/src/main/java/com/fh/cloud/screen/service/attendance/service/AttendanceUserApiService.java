package com.fh.cloud.screen.service.attendance.service;

import com.fh.cloud.screen.service.attendance.api.AttendanceUserApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserResultVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "attendanceUserApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = AttendanceUserApiService.AttendanceUserApiFallbackFactory.class)
@Component
public interface AttendanceUserApiService extends AttendanceUserApi {
    @Component
    class AttendanceUserApiFallbackFactory implements FallbackFactory<AttendanceUserApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(AttendanceUserApiFallbackFactory.class);

        @Override
        public AttendanceUserApiService create(Throwable cause) {
            AttendanceUserApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new AttendanceUserApiService() {

                @Override
                public AjaxResult pageList(AttendanceUserListConditionBo condition) {
                    return AjaxResult.fail("查询失败");
                }

                @Override
                public AjaxResult<List<AttendanceUserVo>> list(AttendanceUserListConditionBo condition) {
                    return AjaxResult.fail("查询失败");
                }

                @Override
                public AjaxResult<AttendanceUserCensusVo>
                    getListByDateCondition(AttendanceUserListConditionBo condition) {
                    return AjaxResult.fail("查询失败");
                }

                @Override
                public AjaxResult<AttendanceUserResultVo> getListExportByDateCondition(AttendanceUserListConditionBo condition) {
                    return AjaxResult.fail("导出失败");
                }

                @Override
                public AjaxResult addAttendanceUser(AttendanceUserBo attendanceUserBo) {
                    return AjaxResult.fail("添加失败");
                }

                @Override
                public AjaxResult updateAttendanceUser(AttendanceUserBo attendanceUserBo) {
                    return AjaxResult.fail("更新失败");
                }

                @Override
                public AjaxResult getDetail(Long attendanceUserId) {
                    return AjaxResult.fail("获取详情失败");
                }

                @Override
                public AjaxResult delete(Long attendanceUserId) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult changeState(AttendanceUserBo bo) {
                    return AjaxResult.fail("更改状态失败");
                }
            };
        }
    }
}
