package com.fh.cloud.screen.service.er.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import lombok.Data;

/**
 * 考场_考试计划里面一次考试的老师
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamInfoTeacherBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long examInfoTeacherId;

	/**
	 * 考试科目表id
	 */
	@ApiModelProperty("考试科目表id")
	private Long examInfoSubjectId;

	/**
	 * 考试id
	 */
	@ApiModelProperty("考试id")
	private Long examInfoId;

	/**
	 * 考试计划id
	 */
	@ApiModelProperty("考试计划id")
	private Long examPlanId;

	/**
	 * 教师的user_oid
	 */
	@ApiModelProperty("教师的user_oid")
	private String userOid;

	/**
	 * 教师的姓名
	 */
	@ApiModelProperty("教师的姓名")
	private String realName;

	/**
	 * 用户来源类型：1校内，2校外
	 */
	@ApiModelProperty("用户来源类型：1校内，2校外")
	private Integer userFromType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
