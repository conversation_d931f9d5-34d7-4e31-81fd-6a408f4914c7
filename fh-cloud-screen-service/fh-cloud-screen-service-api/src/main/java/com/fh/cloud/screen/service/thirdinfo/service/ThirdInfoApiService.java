package com.fh.cloud.screen.service.thirdinfo.service;

import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.fh.cloud.screen.service.thirdinfo.api.ThirdInfoApi;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.syllabus.api.SyllabusInfoApi;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoWithRestVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 第三方信息同步
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:16:07
 */
@FeignClient(contextId = "thirdInfoApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ThirdInfoApiService.ThirdInfoApiFallbackFactory.class)
@Component
public interface ThirdInfoApiService extends ThirdInfoApi {

    @Component
    class ThirdInfoApiFallbackFactory implements FallbackFactory<ThirdInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ThirdInfoApiFallbackFactory.class);

        @Override
        public ThirdInfoApiService create(Throwable cause) {
            ThirdInfoApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ThirdInfoApiService() {
                @Override
                public AjaxResult<String> syncThirdSyllabusInfo(SyllabusInfoBo syllabusInfoBo) {
                    return AjaxResult.fail("同步课表信息失败");
                }
            };
        }
    }
}