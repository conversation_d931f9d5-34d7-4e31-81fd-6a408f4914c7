package com.fh.cloud.screen.service.device.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/15
 */
@Data
public class DeviceCountVo implements Serializable {

    /**
     * 总数
     */
    private int count;

    /**
     * 开机总数
     */
    private long openCount;

    /**
     * 关机总数
     */
    private long closeCount;

    /**
     * 异常总数
     */
    private long errorCount;

    /**
     * 未激活总数
     */
    private Integer notActiveCount;
}
