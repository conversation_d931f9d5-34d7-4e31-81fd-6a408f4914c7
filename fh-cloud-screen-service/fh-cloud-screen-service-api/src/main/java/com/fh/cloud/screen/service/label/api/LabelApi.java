package com.fh.cloud.screen.service.label.api;

import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
public interface LabelApi {

    /**
     * 查询标签表分页列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("label/page/list")
    public AjaxResult<PageInfo<LabelVo>> getLabelPageListByCondition(@RequestBody LabelConditionBo condition);

    /**
     * 查询标签表列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("label/list")
    public AjaxResult<List<LabelVo>> getLabelListByCondition(@RequestBody LabelConditionBo condition);

    /**
     * 新增标签表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("label/add")
    public AjaxResult addLabel(@Validated @RequestBody LabelBo labelBo);

    /**
     * 修改标签表
     * 
     * @param labelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @PostMapping("label/update")
    public AjaxResult updateLabel(@Validated @RequestBody LabelBo labelBo);

    /**
     * 查询标签表详情
     * 
     * @param labelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @GetMapping("label/detail")
    public AjaxResult<LabelVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("labelId") Long labelId);

    /**
     * 删除标签表
     * 
     * @param labelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @GetMapping("label/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("labelId") Long labelId);

    /**
     * 根据标签类型 获取二级目录结构标
     * 
     * @param conditionBo type 标签类型 默认1，海报标签
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 9:33
     */
    @PostMapping("label/tree")
    public AjaxResult getLabelTreeByCondition(@RequestBody LabelConditionBo conditionBo);

    /**
     * 按照id顺序批量更新顺序
     *
     * @param idList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 14:27
     */
    @PostMapping("label/update-sort")
    public AjaxResult updateLabelSortByIdList(@RequestBody List<Long> idList);

    /**
     * 交换标签排序
     *
     * @param preLabelId the pre label id
     * @param nextLabelId the next label id
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2024 -06-19 16:34:24
     */
    @GetMapping("label/exchange-sort")
    public AjaxResult exchangeLabelSort(@RequestParam("preLabelId") Long preLabelId,
        @RequestParam("nextLabelId") Long nextLabelId,
        @RequestParam(value = "organizationId", required = false) Long organizationId);
}
