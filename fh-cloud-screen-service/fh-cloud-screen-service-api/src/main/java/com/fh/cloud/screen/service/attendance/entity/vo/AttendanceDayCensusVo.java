package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考勤一天统计返回信息vo
 * 
 * <AUTHOR>
 * @date 2023/8/27 10:32
 */
@Data
public class AttendanceDayCensusVo implements Serializable {
    /**
     * 本次统计使用的考勤规则id
     */
    private Long attendanceRuleId;
    /**
     * 本次统计的考勤类型
     */
    private Integer attendanceType;

    /**
     * 一天的考勤规则
     */
    private List<AttendanceRuleDayVo> attendanceRuleDayVos;

    /**
     * 一天的考勤数据（两者之间前端通过signKey映射）
     */
    private List<AttendanceLogCensusVo> attendanceLogCensusVos;
}
