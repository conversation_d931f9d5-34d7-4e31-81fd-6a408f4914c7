package com.fh.cloud.screen.service.leaveschool.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 放学配置表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
@Data
public class LeaveSchoolConfigVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 放学配置表id
     */
    @ApiModelProperty("放学配置表id")
    private Long leaveSchoolConfigId;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 校区id
     */
    @ApiModelProperty("校区id")
    private Long campusId;

    /**
     * 各年级放学时间 1-一致 2-不一致
     */
    @ApiModelProperty("各年级放学时间 1-一致 2-不一致")
    private Integer leaveSchoolGradeType;

    /**
     * 一周内放学时间 1-一周一致 2-一周不一致
     */
    @ApiModelProperty("一周内放学时间 1-一周一致 2-一周不一致")
    private Integer leaveSchoolWeekType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public LeaveSchoolConfigVo returnOwn() {
        return this;
    }

    /**
     * 放学配置详情列表
     */
    @ApiModelProperty("放学配置详情列表")
    private List<LeaveSchoolConfigDetailVo> configDetailList;

    /**
     * 放学配置设备列表
     */
    @ApiModelProperty("放学配置设备列表")
    private List<LeaveSchoolConfigDeviceVo> deviceList;


    /**
     * 放学自动确认时间（单位：分钟）
     */
    @ApiModelProperty("放学自动确认时间（单位：分钟）")
    private Integer autoConfirmTime;
}
