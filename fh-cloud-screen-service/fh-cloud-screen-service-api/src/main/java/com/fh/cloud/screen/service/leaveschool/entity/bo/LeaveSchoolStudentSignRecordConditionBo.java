package com.fh.cloud.screen.service.leaveschool.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.light.core.entity.PageLimitBo;

import java.math.BigDecimal;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 学生进出记录表
 * 
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:32:34
 */
@Data
public class LeaveSchoolStudentSignRecordConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long leaveSchoolStudentSignRecordId;

	/**
	 * 学生id
	 */
	@ApiModelProperty("学生id")
	private Long studentId;

	/**
	 * 学生姓名
	 */
	@ApiModelProperty("学生姓名")
	private String studentName;

	/**
	 * 证件号
	 */
	@ApiModelProperty("证件号")
	private String identityCardNumber;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 组织名称
	 */
	@ApiModelProperty("组织名称")
	private String organizationName;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;

	/**
	 * 签到时间
	 */
	@ApiModelProperty("签到时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date signTime;

	/**
	 * 签到类型 0-进校 1-离校
	 */
	@ApiModelProperty("签到类型 0-进校 1-离校")
	private Integer signType;

	/**
	 * 签到类型名称
	 */
	@ApiModelProperty("签到类型名称")
	private String signTypeName;

	/**
	 * 签到方式 0-刷脸 1-刷卡
	 */
	@ApiModelProperty("签到方式 0-刷脸 1-刷卡")
	private Integer signWay;

	/**
	 * 签到方式名称
	 */
	@ApiModelProperty("签到方式名称")
	private String signWayName;

	/**
	 * 第三方学校id
	 */
	@ApiModelProperty("第三方学校id")
	private String thirdSchoolId;

	/**
	 * 第三方学校名称
	 */
	@ApiModelProperty("第三方学校名称")
	private String thirdSchoolName;

	/**
	 * 第三方学生id
	 */
	@ApiModelProperty("第三方学生id")
	private String thirdStudentUserId;

	/**
	 * 第三方学生姓名
	 */
	@ApiModelProperty("第三方学生姓名")
	private String thirdStudentName;

	/**
	 * 第三方卡号
	 */
	@ApiModelProperty("第三方卡号")
	private String thirdCardNumber;

	/**
	 * 第三方记录id
	 */
	@ApiModelProperty("第三方记录id")
	private String thirdSignRecordId;

	/**
	 * 应用code
	 */
	@ApiModelProperty("应用code")
	private String appCode;

	/**
	 * 数据来源 0-本系统 1-第三方推送
	 */
	@ApiModelProperty("数据来源 0-本系统 1-第三方推送")
	private Integer sourceType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
