package com.fh.cloud.screen.service.meeting.entity.bo;

import java.sql.Time;
import java.util.Date;
import java.util.List;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 会议表
 * 
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Data
public class MeetingConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long meetingId;

    /**
     * FK所属组织ID
     */
    @ApiModelProperty("FK所属组织ID")
    @NotNull(message = "FK所属组织ID不能为空")
    private Long organizationId;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 会议室地点id
     */
    @ApiModelProperty("会议室地点id")
    private Long spaceInfoId;

    /**
     * 申请人user_oid
     */
    @ApiModelProperty("申请人user_oid")
    @NotNull(message = "申请人user_oid不能为空")
    private String userOid;

    /**
     * 会议主题
     */
    @ApiModelProperty("会议主题")
    private String title;

    /**
     * 会议日期:yyyy-MM-dd
     */
    @ApiModelProperty("会议日期:yyyy-MM-dd ")
    @NotNull(message = "会议日期不能为空")
    private Date meetingDate;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Time meetingStartTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Time meetingEndTime;

    /**
     * 是否签到（1：是，2：否）
     */
    @ApiModelProperty("是否签到（1：是，2：否）")
    private Integer isSignIn;

    /**
     * 会议内容
     */
    @ApiModelProperty("会议内容")
    private String content;

    /**
     * 会议备注
     */
    @ApiModelProperty("会议备注")
    private String note;

    /**
     * 会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）
     */
    @ApiModelProperty("会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 与会人员ids
     */
    private List<Long> meetingIds;

    /**
     * 会议空间地点名称
     */
    private String meetingName;

    /**
     * 会议空间地点名称排序规则
     */
    private Integer orderByMeetingNameFlag;

    /**
     * 云屏时间
     */
    private Date screenNowDate;

    /**
     * app场景列表检索时,去除状态为4(提前结束)的会议
     */
    private Integer notEnd;

    /**
     * 开始日期，用户检索条件（云屏设备的会议接口使用，暂时注释掉了）
     */
    private Date startDate;

    /**
     * 会议人员类型：1：教师，2：学生
     */
    private Integer meetingUserType;

    /**
     * 正常签到时间
     */
    private Time normalSignInTime;

    /**
     * 会议uuid
     */
    @ApiModelProperty("会议uuid")
    private String meetingUuid;

    /**
     * 是否查询重复的会议，通过startDate和endDate来查询出重复的会议
     */
    private boolean queryRepeat = false;
    /**
     * 会议开始日期：查询重复会议时使用
     */
    private Date meetingStartDate;
    /**
     * 会议结束日期：查询重复会议时使用
     */
    private Date meetingEndDate;
}
