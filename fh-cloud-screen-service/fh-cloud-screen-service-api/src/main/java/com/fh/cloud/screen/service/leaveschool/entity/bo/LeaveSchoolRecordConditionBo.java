package com.fh.cloud.screen.service.leaveschool.entity.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 放学记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
@Data
public class LeaveSchoolRecordConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 放学记录表id
	 */
	@ApiModelProperty("放学记录表id")
	private Long leaveSchoolRecordId;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@ApiModelProperty("校区id")
	private Long campusId;

	/**
	 * 区域id或者classesId
	 */
	@ApiModelProperty("区域id或者classesId")
	private Long spaceInfoId;

	/**
	 * 区域ids或classesIds
	 */
	@ApiModelProperty("区域ids或classesIds")
	private List<Long> spaceInfoIds;

	/**
	 * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
	 */
	@ApiModelProperty("区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室")
	private Integer spaceGroupUseType;

	/**
	 * 放学状态 1-未放学 2-放学中 3-已放学
	 */
	@ApiModelProperty("放学状态 1-未放学 2-放学中 3-已放学")
	private Integer leaveSchoolType;

	/**
	 * 放学日期
	 */
	@ApiModelProperty("放学日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date leaveSchoolDay;



	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	@ApiModelProperty("是否查询放学自动确认时间，默认false不查询")
	private Boolean queryAutoConfirmTime = false;

}
