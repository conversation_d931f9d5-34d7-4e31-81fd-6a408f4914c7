package com.fh.cloud.screen.service.calendar.service;

import com.fh.cloud.screen.service.calendar.api.SchoolCalendarApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayOfMonthVo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "schoolCalendarApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = SchoolCalendarApiService.SchoolCalendarApiFallbackFactory.class)
@Component
public interface SchoolCalendarApiService extends SchoolCalendarApi {
    @Component
    class SchoolCalendarApiFallbackFactory implements FallbackFactory<SchoolCalendarApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(SchoolCalendarApiFallbackFactory.class);

        @Override
        public SchoolCalendarApiService create(Throwable cause) {
            SchoolCalendarApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new SchoolCalendarApiService() {

                @Override
                public AjaxResult getSchoolCalendarListByCondition(SchoolCalendarListConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                @Override
                public AjaxResult getDetail(Long organizationId) {
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult<List<SchoolCalendarDayOfMonthVo>> getDayInfoByMonthAndOrgId(String attendanceMonth,
                    Long organizationId) {
                    return AjaxResult.fail("获取失败");
                }
            };
        }
    }
}
