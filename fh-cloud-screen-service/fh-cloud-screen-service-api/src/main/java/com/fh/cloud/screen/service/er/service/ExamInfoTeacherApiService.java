package com.fh.cloud.screen.service.er.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.er.api.ExamInfoTeacherApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 考场_考试计划里面一次考试的老师
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@FeignClient(contextId = "examInfoTeacherApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ExamInfoTeacherApiService.ExamInfoTeacherApiFallbackFactory.class)
@Component
public interface ExamInfoTeacherApiService extends ExamInfoTeacherApi {

    @Component
    class ExamInfoTeacherApiFallbackFactory implements FallbackFactory<ExamInfoTeacherApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ExamInfoTeacherApiFallbackFactory.class);
        @Override
        public ExamInfoTeacherApiService create(Throwable cause) {
            ExamInfoTeacherApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ExamInfoTeacherApiService() {
                public AjaxResult getExamInfoTeacherPageListByCondition(ExamInfoTeacherConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getExamInfoTeacherListByCondition(ExamInfoTeacherConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addExamInfoTeacher(ExamInfoTeacherBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateExamInfoTeacher(ExamInfoTeacherBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}