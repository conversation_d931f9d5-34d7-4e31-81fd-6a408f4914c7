package com.fh.cloud.screen.service.role.api;

import com.fh.app.role.service.role.entity.bo.RoleAppRelConditionBo;
import com.fh.app.role.service.role.entity.bo.RoleDataAuthorityConditionBo;
import com.fh.sso.service.index.entity.bo.IndexListConditionBo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:48 上午 @description：
 */
public interface UserRolePermissionApi {

    @PostMapping("/role/permission/list")
    AjaxResult getUserPermissionAuthority(@RequestBody RoleAppRelConditionBo condition);

    @PostMapping("/role/max/data/authority")
    AjaxResult getUserMaxDataAuthority(@RequestBody RoleDataAuthorityConditionBo conditionBo);

    @PostMapping("/role/codes")
    AjaxResult getUserRoleCodes();

    @PostMapping("/role/user/app/info")
    AjaxResult getUserAppInfo(@RequestBody IndexListConditionBo conditionBo);
}
