package com.fh.cloud.screen.service.campus.api;

import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface CampusScreenApi {

    /**
     * 查询区域信息表列表
     */
    @PostMapping("/campus/list")
    AjaxResult getCampusByCondition(@RequestBody CampusListConditionBo condition);

}
