package com.fh.cloud.screen.service.screen.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenSignaturePictureApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 电子签名图片资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@FeignClient(contextId = "screenSignaturePictureApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenSignaturePictureApiService.ScreenSignaturePictureApiFallbackFactory.class)
@Component
public interface ScreenSignaturePictureApiService extends ScreenSignaturePictureApi {

    @Component
    class ScreenSignaturePictureApiFallbackFactory implements FallbackFactory<ScreenSignaturePictureApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenSignaturePictureApiFallbackFactory.class);

        @Override
        public ScreenSignaturePictureApiService create(Throwable cause) {
            ScreenSignaturePictureApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ScreenSignaturePictureApiService() {
                public AjaxResult
                    getScreenSignaturePicturePageListByCondition(ScreenSignaturePictureConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult
                    getScreenSignaturePictureListByCondition(ScreenSignaturePictureConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenSignaturePicture(ScreenSignaturePictureBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenSignaturePicture(ScreenSignaturePictureBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}