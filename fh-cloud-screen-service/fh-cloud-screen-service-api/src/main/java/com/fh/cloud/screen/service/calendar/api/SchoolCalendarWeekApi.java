package com.fh.cloud.screen.service.calendar.api;

import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekSaveUpdateConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface SchoolCalendarWeekApi {

    /**
     * 查询校历上课日星期表列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/9 16:51
     */
    @PostMapping("/calendar/week/list")
    @ApiOperation(value = "查询校历上课日星期表列表", httpMethod = "POST")
    AjaxResult getSchoolCalendarWeekListByCondition(@RequestBody SchoolCalendarWeekListConditionBo condition);

    /**
     * 保存或修改校历上课日星期表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/9 17:07
     */
    @PostMapping("/calendar/week/save-or-update")
    AjaxResult
        saveOrUpdateSchoolCalendarWeek(@Validated @RequestBody SchoolCalendarWeekSaveUpdateConditionBo conditionBo);
}
