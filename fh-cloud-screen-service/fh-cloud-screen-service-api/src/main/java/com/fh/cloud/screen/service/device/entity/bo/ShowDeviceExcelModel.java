package com.fh.cloud.screen.service.device.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.*;

/**
 * @ClassName: TeacherImportJXModel
 * @Description:
 * <AUTHOR> @Date 2022/4/14
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ShowDeviceExcelModel {

    /**
     * 设备类型：1班牌，2画屛，3信息发布屏
     */
    @Excel(name = "设备类型", width = 10.0, fixedIndex = 0, replace = {"班牌设备_1", "画屏设备_2", "信息发布屏设备_3"})
    private String deviceType;

    @Excel(name = "设备名称", width = 10.0, fixedIndex = 1)
    private String deviceName;

    @Excel(name = "设备型号", width = 10.0, fixedIndex = 2)
    private String deviceModel;

    @Excel(name = "产品序列号（SN）", width = 10.0, fixedIndex = 3)
    private String productSerialNumber;

    /**
     * 需要查询字典带出字典值
     */
    @Excel(name = "品牌", width = 10.0, fixedIndex = 4, replace = {"海康_1", "京东方_2", "互视达_3", "南京普利视智能科技有限公司_4", "其他_0"})
    private String deviceBrand;

}
