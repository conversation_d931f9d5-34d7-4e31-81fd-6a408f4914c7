package com.fh.cloud.screen.service.leaveschool.api;


import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 放学播报信息表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
public interface LeaveSchoolBroadcastInfoApi {

    /**
     * 查询放学播报信息表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @PostMapping("/leave/school/broadcast/info/page/list")
    public AjaxResult<PageInfo<LeaveSchoolBroadcastInfoVo>> getLeaveSchoolBroadcastInfoPageListByCondition(@RequestBody LeaveSchoolBroadcastInfoConditionBo condition);

    /**
     * 查询放学播报信息表列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @PostMapping("/leave/school/broadcast/info/list")
    public AjaxResult<List<LeaveSchoolBroadcastInfoVo>> getLeaveSchoolBroadcastInfoListByCondition(@RequestBody LeaveSchoolBroadcastInfoConditionBo condition);


    /**
     * 新增放学播报信息表
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @PostMapping("/leave/school/broadcast/info/add")
    public AjaxResult addLeaveSchoolBroadcastInfo(@Validated @RequestBody LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo);

    /**
     * 修改放学播报信息表
     * @param leaveSchoolBroadcastInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @PostMapping("/leave/school/broadcast/info/update")
    public AjaxResult updateLeaveSchoolBroadcastInfo(@Validated @RequestBody LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo);

    /**
     * 查询放学播报信息表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @GetMapping("/leave/school/broadcast/info/detail")
    public AjaxResult<LeaveSchoolBroadcastInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除放学播报信息表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:38
     */
    @GetMapping("/leave/school/broadcast/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
