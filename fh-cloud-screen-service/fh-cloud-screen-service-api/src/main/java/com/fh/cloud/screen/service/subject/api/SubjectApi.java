package com.fh.cloud.screen.service.subject.api;

import com.fh.cloud.screen.service.subject.entity.bo.SubjectBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 科目api
 * 
 * <AUTHOR>
 * @date 2022/10/14 10:42
 */
public interface SubjectApi {

    /**
     * 分页查询科目列表
     *
     * @param subjectBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/6 10:07
     */
    @ApiOperation(value = "分页查询科目列表", httpMethod = "POST")
    @RequestMapping(value = "/subject/list", method = RequestMethod.POST)
    public AjaxResult listSubject(@RequestBody SubjectBo subjectBo);
}