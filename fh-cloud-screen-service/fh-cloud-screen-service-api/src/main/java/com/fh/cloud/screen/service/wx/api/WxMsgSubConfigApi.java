package com.fh.cloud.screen.service.wx.api;

import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubConfigConditionBo;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubConfigBo;
import com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubConfigVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 微信消息订阅用户配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
public interface WxMsgSubConfigApi {

    /**
     * 查询微信消息订阅用户配置表分页列表
     * 
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/config/page/list")
    public AjaxResult<PageInfo<WxMsgSubConfigVo>> getWxMsgSubConfigPageListByCondition(
            @RequestBody WxMsgSubConfigConditionBo condition);

    /**
     * 查询微信消息订阅用户配置表列表
     * 
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/config/list")
    public AjaxResult<List<WxMsgSubConfigVo>> getWxMsgSubConfigListByCondition(
            @RequestBody WxMsgSubConfigConditionBo condition);

    /**
     * 新增微信消息订阅用户配置表
     * 
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/config/add")
    public AjaxResult addWxMsgSubConfig(@Validated @RequestBody WxMsgSubConfigBo wxMsgSubConfigBo);

    /**
     * 修改微信消息订阅用户配置表
     * 
     * @param wxMsgSubConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/config/update")
    public AjaxResult updateWxMsgSubConfig(@Validated @RequestBody WxMsgSubConfigBo wxMsgSubConfigBo);

    /**
     * 查询微信消息订阅用户配置表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @GetMapping("/wx/msg/sub/config/detail")
    public AjaxResult<WxMsgSubConfigVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除微信消息订阅用户配置表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @GetMapping("/wx/msg/sub/config/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 根据userOid查询微信消息订阅用户配置表详情
     * 
     * @param userOid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @GetMapping("/wx/msg/sub/config/detailByUserOid")
    public AjaxResult<WxMsgSubConfigVo> getDetailByUserOid(
            @NotNull(message = "请选择数据") @RequestParam("userOid") String userOid);

    /**
     * 新增或保存微信消息订阅用户配置表
     * @param wxMsgSubConfigBo
     * <AUTHOR>
     * @date 2024-04-02 14:54:41
     */
    @PostMapping("/wx/msg/sub/config/save")
    public AjaxResult saveWxMsgSubConfig(@Validated @RequestBody WxMsgSubConfigBo wxMsgSubConfigBo);

}
