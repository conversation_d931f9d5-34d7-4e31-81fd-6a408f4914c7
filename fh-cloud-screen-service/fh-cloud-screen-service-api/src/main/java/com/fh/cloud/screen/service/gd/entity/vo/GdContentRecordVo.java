package com.fh.cloud.screen.service.gd.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 稿定内容记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
@Data
public class GdContentRecordVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String userOid;

    /**
     * 稿定系统用户oid
     */
    @ApiModelProperty("稿定系统用户oid")
    private String gdOid;

    /**
     * 文件oid
     */
    @ApiModelProperty("文件oid")
    private String gdId;

    /**
     * 文件信息json
     */
    @ApiModelProperty("文件信息json")
    private String fileInfoJson;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public GdContentRecordVo returnOwn() {
        return this;
    }

}
