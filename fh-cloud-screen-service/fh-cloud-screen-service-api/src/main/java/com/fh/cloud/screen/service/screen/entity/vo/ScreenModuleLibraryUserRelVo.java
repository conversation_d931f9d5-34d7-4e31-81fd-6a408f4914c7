package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 模块用户关系表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
@Data
public class ScreenModuleLibraryUserRelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String userOid;

    /**
     * 模块FK
     */
    @ApiModelProperty("模块FK")
    private Long screenModuleLibraryId;

    /**
     * 用户模块关系，1：创建
     */
    @ApiModelProperty("用户模块关系，1：创建")
    private Integer type;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
