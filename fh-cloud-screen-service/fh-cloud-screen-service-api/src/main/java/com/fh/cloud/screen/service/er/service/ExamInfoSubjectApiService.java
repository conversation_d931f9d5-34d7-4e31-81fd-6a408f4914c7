package com.fh.cloud.screen.service.er.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.er.api.ExamInfoSubjectApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 考场_考试计划里面一次考试科目信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@FeignClient(contextId = "examInfoSubjectApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ExamInfoSubjectApiService.ExamInfoSubjectApiFallbackFactory.class)
@Component
public interface ExamInfoSubjectApiService extends ExamInfoSubjectApi {

    @Component
    class ExamInfoSubjectApiFallbackFactory implements FallbackFactory<ExamInfoSubjectApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ExamInfoSubjectApiFallbackFactory.class);

        @Override
        public ExamInfoSubjectApiService create(Throwable cause) {
            ExamInfoSubjectApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ExamInfoSubjectApiService() {
                public AjaxResult getExamInfoSubjectPageListByCondition(ExamInfoSubjectConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getExamInfoSubjectListByCondition(ExamInfoSubjectConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addExamInfoSubject(ExamInfoSubjectBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateExamInfoSubject(ExamInfoSubjectBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult getNowExamInfoByCondition(ExamInfoSubjectConditionBo conditionBo) {
                    return AjaxResult.fail("获取当前考试详情");
                }
            };
        }
    }
}