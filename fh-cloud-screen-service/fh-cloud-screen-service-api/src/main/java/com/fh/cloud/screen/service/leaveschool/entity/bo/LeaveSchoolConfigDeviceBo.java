package com.fh.cloud.screen.service.leaveschool.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 放学配置设备表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
@Data
public class LeaveSchoolConfigDeviceBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学配置设备表id
	 */
	@ApiModelProperty("放学配置设备表id")
	private Long leaveSchoolConfigDeviceId;

	/**
	 * 放学配置表id
	 */
	@ApiModelProperty("放学配置表id")
	private Long leaveSchoolConfigId;

	/**
	 * 展示设备id
	 */
	@ApiModelProperty("展示设备id")
	private Long showDeviceId;


	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 学校id
	 */
	private Long organizationId;
	/**
	 * 校区id
	 */
	private Long campusId;
	/**
	 * 地点名称
	 */
	private String spaceInfoName;

}
