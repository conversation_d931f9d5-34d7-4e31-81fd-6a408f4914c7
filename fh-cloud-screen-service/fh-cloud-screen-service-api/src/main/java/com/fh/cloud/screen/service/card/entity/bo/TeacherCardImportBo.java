package com.fh.cloud.screen.service.card.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TeacherCardImportBo implements Serializable {

    @Excel(name = "教师姓名", width = 20.0, fixedIndex = 0)
    private String userName;

    @Excel(name = "手机号", width = 30.0, fixedIndex = 1)
    private String phone;

    @Excel(name = "卡号", width = 30.0, fixedIndex = 2)
    private String cardNumber;
}
