package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 云屏场景审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
public interface ScreenSceneAuditApi {

    /**
     * 查询云屏场景审核表分页列表
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @PostMapping("/screen/scene/audit/page/list")
    public AjaxResult<PageInfo<ScreenSceneAuditVo>> getScreenSceneAuditPageListByCondition(@RequestBody ScreenSceneAuditConditionBo condition);

    /**
     * 查询云屏场景审核表列表
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @PostMapping("/screen/scene/audit/list")
    public AjaxResult<List<ScreenSceneAuditVo>> getScreenSceneAuditListByCondition(@RequestBody ScreenSceneAuditConditionBo condition);


    /**
     * 新增云屏场景审核表
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @PostMapping("/screen/scene/audit/add")
    public AjaxResult addScreenSceneAudit(@Validated @RequestBody ScreenSceneAuditBo screenSceneAuditBo);

    /**
     * 修改云屏场景审核表
     * @param screenSceneAuditBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @PostMapping("/screen/scene/audit/update")
    public AjaxResult updateScreenSceneAudit(@Validated @RequestBody ScreenSceneAuditBo screenSceneAuditBo);

    /**
     * 查询云屏场景审核表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @GetMapping("/screen/scene/audit/detail")
    public AjaxResult<ScreenSceneAuditVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除云屏场景审核表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @GetMapping("/screen/scene/audit/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 审核
     *
     * @param screenSceneAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 13:44
     **/
    @PostMapping("/screen/scene/audit/update-audit")
    public AjaxResult screenSceneAudit(@RequestBody ScreenSceneAuditBo screenSceneAuditBo);

    /**
     * 批量审核
     *
     * @param screenSceneAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 15:01
     **/
    @PostMapping("/screen/scene/audit/update-audit-batch")
    public AjaxResult screenSceneAuditBatch(@RequestBody ScreenSceneAuditBo screenSceneAuditBo);

    /**
     * 待发布云屏数据
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 16:20
     **/
    @GetMapping("/screen/scene/audit/screen-to-audit")
    public AjaxResult screenToAudit(@RequestParam("screenSceneAuditId") Long screenSceneAuditId);
    
    /**
     * 获取审核数量
     *
     * @param condition 
     * @return com.light.core.entity.AjaxResult 
     * <AUTHOR>
     * @date 2023/12/1 9:23
     **/
    @PostMapping("/screen/scene/audit/audit-count")
    public AjaxResult getScreenSceneAuditCount(@RequestBody ScreenSceneAuditConditionBo condition);

}
