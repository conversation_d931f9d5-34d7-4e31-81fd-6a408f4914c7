package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 共话诗词点赞记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
@Data
public class ScreenPoetryLikesVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenPoetryLikesId;

    /**
     * FK共话诗词id
     */
    @ApiModelProperty("FK共话诗词id")
    private Long screenPoetryContentId;

    /**
     * 点赞数
     */
    @ApiModelProperty("点赞数")
    private Long likesNum;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ScreenPoetryLikesVo returnOwn() {
        return this;
    }

}
