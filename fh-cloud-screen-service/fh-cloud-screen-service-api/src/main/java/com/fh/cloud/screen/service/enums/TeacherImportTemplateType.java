package com.fh.cloud.screen.service.enums;

/**
 * 教师导入模板类型
 *
 * <AUTHOR>
 */
public enum TeacherImportTemplateType {
    /**
     * 学校-教学人员
     */
    SCHOOL_JX(1),
    /***
     * 学校-非教学人员
     */
    SCHOOL_FJX(2),
    /**
     * 教育局
     */
    BUREAU(3);

    private int value;

    TeacherImportTemplateType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    /**
     * 根据类型获取模板名称
     *
     * @param type the type
     * @return template name
     */
    public static String getTemplateName(Integer type) {
        if (type == null) {
            return "导入教职工模板";
        }
        if (type.equals(TeacherImportTemplateType.SCHOOL_JX.getValue())) {
            return "导入教职工信息模板（校端：教学人员）";
        }
        if (type.equals(TeacherImportTemplateType.SCHOOL_FJX.getValue())) {
            return "导入职工信息模板（校端：非教学人员）";
        }
        if (type.equals(TeacherImportTemplateType.BUREAU.getValue())) {
            return "导入职工信息模板（局端：教职工）";
        }
        return "导入教职工模板";
    }

    /**
     * 根据类型获取模板文件名称
     *
     * @param type the type
     * @return template name
     */
    public static String getTemplateFileName(Integer type) {
        if (type == null) {
            return "teacher_import_template_school_jx.xls";
        }
        if (type.equals(TeacherImportTemplateType.SCHOOL_JX.getValue())) {
            return "teacher_import_template_school_jx.xls";
        }
        if (type.equals(TeacherImportTemplateType.SCHOOL_FJX.getValue())) {
            return "teacher_import_template_school_fjx.xls";
        }
        if (type.equals(TeacherImportTemplateType.BUREAU.getValue())) {
            return "teacher_import_template_bureau.xls";
        }
        return "teacher_import_template_school_jx.xls";
    }
}