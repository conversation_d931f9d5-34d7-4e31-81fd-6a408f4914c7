package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 紧急发布interface
 */
public interface ScreenContentSpecialApi {

    /**
     * 紧急发布历史查询
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/content-special/list")
    @ApiOperation(value = "查询云屏紧急发布内容表列表", httpMethod = "POST")
    AjaxResult getScreenContentSpecialListByCondition(@RequestBody ScreenContentSpecialListConditionBo condition);

    /**
     * 保存紧急发布（新增或者修改）
     */
    @PostMapping("/screen/content-special/save")
    @ApiOperation(value = "保存云屏紧急发布内容表", httpMethod = "POST")
    AjaxResult saveScreenContentSpecial(@RequestBody ScreenContentSpecialBo screenContentSpecialBo);

    /**
     * 紧急发布-撤回
     */
    @PostMapping("/screen/content-special/cancel")
    @ApiOperation(value = "紧急发布撤回", httpMethod = "POST")
    AjaxResult cancelSubmit(@RequestParam("screenContentSpecialId") Long screenContentSpecialId);

    /**
     * 紧急发布-查看
     */
    @GetMapping("/screen/content-special/detail")
    @ApiOperation(value = "查询云屏紧急发布内容表详情", httpMethod = "GET")
    AjaxResult getDetail(@RequestParam("screenContentSpecialId") Long screenContentSpecialId);

    /**
     * 紧急发布-发布
     */
    @PostMapping("/screen/content-special/submit")
    @ApiOperation(value = "紧急发布发布", httpMethod = "POST")
    AjaxResult submit(@RequestBody ScreenContentSpecialBo screenContentSpecialBo);

}
