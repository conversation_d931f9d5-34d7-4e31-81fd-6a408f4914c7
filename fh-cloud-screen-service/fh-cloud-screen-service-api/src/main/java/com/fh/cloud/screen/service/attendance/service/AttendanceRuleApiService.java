package com.fh.cloud.screen.service.attendance.service;

import com.fh.cloud.screen.service.attendance.api.AttendanceRuleApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022/6/7 17:40
 */
@FeignClient(contextId = "attendanceRuleApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = AttendanceRuleApiService.AttendanceRuleApiFallbackFactory.class)
@Component
public interface AttendanceRuleApiService extends AttendanceRuleApi {
    @Component
    class AttendanceRuleApiFallbackFactory implements FallbackFactory<AttendanceRuleApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(AttendanceRuleApiService.AttendanceRuleApiFallbackFactory.class);

        @Override
        public AttendanceRuleApiService create(Throwable cause) {
            AttendanceRuleApiService.AttendanceRuleApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new AttendanceRuleApiService() {

                public AjaxResult saveOrUpdateAttendanceRule(AttendanceRuleAddBo attendanceRuleAddBo) {
                    return AjaxResult.fail("保存考勤规则失败");
                }

                @Override
                public AjaxResult getDetail(AttendanceRuleBo attendanceRuleBo) {
                    return AjaxResult.fail("获取考勤规则详情失败");
                }

                @Override
                public AjaxResult getDate(AttendanceRuleBo attendanceRuleBo) {
                    return AjaxResult.fail("获取时间失败");
                }

                @Override
                public AjaxResult getInfo(AttendanceRuleBo attendanceRuleBo) {
                    return AjaxResult.fail("获取考勤规则失败");
                }

            };
        }
    }

}
