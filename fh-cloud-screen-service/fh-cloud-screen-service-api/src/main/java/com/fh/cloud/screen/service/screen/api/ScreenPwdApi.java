package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ScreenPwdApi {

    /**
     * 根据组织机构获取 密码信息
     * 
     * @param orgId
     * @return
     */
    @GetMapping("screen/pwd/getByOrgId")
    public AjaxResult<ScreenPwdVo> getByOrgId(@RequestParam("orgId") Long orgId);

    /**
     * 查询云屏密码列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("screen/pwd/list")
    @ApiOperation(value = "查询云屏密码列表", httpMethod = "POST")
    public AjaxResult getScreenPwdListByCondition(@RequestBody ScreenPwdListConditionBo condition);

    /**
     * 新增云屏密码
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("screen/pwd/add")
    @ApiOperation(value = "新增云屏密码", httpMethod = "POST")
    public AjaxResult addScreenPwd(@RequestBody ScreenPwdBo screenPwdBo);

    /**
     * 修改云屏密码
     *
     * @param screenPwdBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("screen/pwd/update")
    @ApiOperation(value = "修改云屏密码", httpMethod = "POST")
    public AjaxResult updateScreenPwd(@RequestBody ScreenPwdBo screenPwdBo);
}
