package com.fh.cloud.screen.service.common.service;

import com.fh.cloud.screen.service.common.api.ScreenDictionaryDataApi;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataBo;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenDictionaryDataApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenDictionaryDataApiService.ScreenDictionaryDataApiFallbackFactory.class)
@Component
public interface ScreenDictionaryDataApiService extends ScreenDictionaryDataApi {
    @Component
    class ScreenDictionaryDataApiFallbackFactory implements FallbackFactory<ScreenDictionaryDataApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenDictionaryDataApiFallbackFactory.class);

        @Override
        public ScreenDictionaryDataApiService create(Throwable cause) {
            ScreenDictionaryDataApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenDictionaryDataApiService() {
                @Override
                public AjaxResult getDictionaryDataListByCondition(DictionaryDataListConditionBo condition) {
                    return AjaxResult.fail("查询字典数据失败");
                }

                @Override
                public AjaxResult getPosterGroupListByCondition(DictionaryDataListConditionBo condition) {
                    return AjaxResult.fail("查询海报分组列表失败");
                }

                @Override
                public AjaxResult addDictionaryData(DictionaryDataBo dictionaryDataBo) {
                    return AjaxResult.fail("新增字典数据失败");
                }

                @Override
                public AjaxResult updateDictionaryData(DictionaryDataBo dictionaryDataBo) {
                    return AjaxResult.fail("修改字典数据失败");
                }

                @Override
                public AjaxResult exchange(Long firstId, Long secondId) {
                    return AjaxResult.fail("交换顺序失败");
                }

                @Override
                public AjaxResult updateGroupSortByIdList(List<Long> idList) {
                    return AjaxResult.fail("更新顺序失败");
                }
            };
        }
    }
}
