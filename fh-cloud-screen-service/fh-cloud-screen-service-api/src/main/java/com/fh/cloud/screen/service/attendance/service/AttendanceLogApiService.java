package com.fh.cloud.screen.service.attendance.service;

import com.fh.cloud.screen.service.attendance.api.AttendanceLogApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "attendanceLogApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = AttendanceLogApiService.AttendanceLogApiFallbackFactory.class)
@Component
public interface AttendanceLogApiService extends AttendanceLogApi {
    @Component
    class AttendanceLogApiFallbackFactory implements FallbackFactory<AttendanceLogApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(AttendanceLogApiService.AttendanceLogApiFallbackFactory.class);

        @Override
        public AttendanceLogApiService create(Throwable cause) {
            AttendanceLogApiService.AttendanceLogApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new AttendanceLogApiService() {
                @Override
                public AjaxResult getAttendanceLogListByCondition(AttendanceLogListConditionBo condition) {
                    return AjaxResult.fail("查询考勤流水列表失败");
                }

                @Override
                public AjaxResult addAttendanceLog(AttendanceLogBo attendanceLogBo) {
                    return AjaxResult.fail("新增考勤流水失败");
                }

                @Override
                public AjaxResult updateAttendanceLog(AttendanceLogBo attendanceLogBo) {
                    return AjaxResult.fail("更新考勤流水失败");
                }

                @Override
                public AjaxResult getDetail(Long attendanceLogId) {
                    return AjaxResult.fail("查询考勤流水详情失败");
                }

                @Override
                public AjaxResult delete(Long attendanceLogId) {
                    return AjaxResult.fail("删除考勤流水失败");
                }

                @Override
                public AjaxResult getStudentCensusByClassesId(Long classesId, String dateTime) {
                    return AjaxResult.fail("获取数据失败");
                }

                @Override
                public AjaxResult<AttendanceLogCensusVo> getClockTeacherCensusByOrgId(Long orgId, String dateTime) {
                    return AjaxResult.fail("获取数据失败");
                }

                @Override
                public AjaxResult<AttendanceDayCensusVo> getClockStudentCensusByClassesId(Long organizationId,
                    Long classesId) {
                    return AjaxResult.fail("获取学生当天打卡数据失败");
                }

                @Override
                public AjaxResult preInsertAttendanceLogByClass(Long classesId, String date) {
                    return AjaxResult.fail("预插学生考勤数据失败");
                }

                @Override
                public AjaxResult preInsertAttendanceLogByTeacher(Long organizationId, String date) {
                    return AjaxResult.fail("预插学生考勤教师数据失败");
                }

            };
        }
    }
}
