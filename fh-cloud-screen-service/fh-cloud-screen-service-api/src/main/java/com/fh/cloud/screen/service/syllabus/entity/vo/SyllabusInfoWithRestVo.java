package com.fh.cloud.screen.service.syllabus.entity.vo;

import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 融合了作息时间的班级课表信息，按照班级查询该记录
 * 
 * <AUTHOR>
 * @date 2023/9/19 14:28
 */
@Data
public class SyllabusInfoWithRestVo implements Serializable {

    /**
     * 作息时间：如果年级不一致则该列表就是该年级的作息时间（如果作息时间每天不一致则week不一样，前端要根据类型判断），排序为：course_classes_index，course_classes_type，course_classes_position
     * 查询的时候：work_rest_grade条件为grade=0或具体的grade，work_rest_day条件为week=0或具体的week。最终数据根据grade#week#节次分组
     */
    private List<WorkRestDayVo> workRestDayVoList;

    /**
     * 常规课表信息列表（每个数据都有星期weekId和节次sort）
     */
    private List<SyllabusInfoVo> syllabusInfoVoList;

}
