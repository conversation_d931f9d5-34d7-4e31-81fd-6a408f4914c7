package com.fh.cloud.screen.service.calendar.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 校历上课日日期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Data
public class SchoolCalendarDayVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long schoolCalendarDayId;

    /**
     * FK校历主表主键id
     */
    private Long schoolCalendarId;

    /**
     * 上课类型：1上课、2不上课
     */
    private Integer type;

    /**
     * 日期
     */
    private Date day;

    /**
     * 上课上周几的课
     */
    private Integer week;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 返回日历节日名称列表
     */
    private List<String> festivalNames;

    /**
     * 返回日历节日名称
     */
    private String festivalName;

    /**
     * 返回自定义节点列表
     */
    private List<String> nodeNames;

    /**
     * 返回自定义节点名称
     */
    private String nodeName;

}
