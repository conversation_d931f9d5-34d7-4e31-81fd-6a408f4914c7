package com.fh.cloud.screen.service.cs.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.cs.api.CsLikeApi;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * Cultural-Station文化小站喜欢记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
@FeignClient(contextId = "csLikeApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CsLikeApiService.CsLikeApiFallbackFactory.class)
@Component
public interface CsLikeApiService extends CsLikeApi {

    @Component
    class CsLikeApiFallbackFactory implements FallbackFactory<CsLikeApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CsLikeApiFallbackFactory.class);
        @Override
        public CsLikeApiService create(Throwable cause) {
            CsLikeApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new CsLikeApiService() {
                public AjaxResult getCsLikePageListByCondition(CsLikeConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCsLikeListByCondition(CsLikeConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCsLike(CsLikeBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCsLike(CsLikeBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}