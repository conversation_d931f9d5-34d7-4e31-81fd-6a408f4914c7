package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 考勤日志统计
 * 
 * <AUTHOR>
 * @email <EMAIL>
 */
@Data
public class AttendanceLogCensusVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总数量
     */
    private long count = 0;

    /**
     * 打卡数量
     */
    private long clockNum = 0;

    /**
     * 未打卡数量
     */
    private long unClockNum = 0;

    /**
     * 打卡百分比
     *
     */
    private BigDecimal percent = new BigDecimal("0.00");

    /**
     * 已打卡用户数据
     */
    private List<AttendanceLogVo> clockLog = new ArrayList<>();

    /**
     * 为打卡用户数据
     */
    private List<AttendanceLogVo> unClockLog = new ArrayList<>();

    /**
     * 考勤记录的唯一key
     */
    private String signKey;
}
