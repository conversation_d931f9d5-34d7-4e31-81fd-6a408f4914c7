package com.fh.cloud.screen.service.calendar.entity.bo;

import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 校历上课日星期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
@Data
@Validated
public class SchoolCalendarWeekBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long schoolCalendarWeekId;

    /**
     * FK校历主表主键id
     */
    @NotNull(message = "FK校历主表主键id不能为空")
    private Long schoolCalendarId;

    /**
     * 上课类型：1上课、2不上课
     */
    @NotNull(message = "上课类型：1上课、2不上课不能为空")
    private Integer type;

    /**
     * 星期几：1-7，分别为星期一到星期日
     */
    @NotNull(message = "星期几：1-7，分别为星期一到星期日不能为空")
    private Integer week;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;
}
