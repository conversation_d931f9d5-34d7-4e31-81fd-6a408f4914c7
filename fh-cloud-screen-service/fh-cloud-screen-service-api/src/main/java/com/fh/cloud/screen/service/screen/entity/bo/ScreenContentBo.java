package com.fh.cloud.screen.service.screen.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long screenContentId;

    /**
     * FK所属组织ID
     */
    @NotNull(message = "FK所属组织ID不能为空")
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    @NotNull(message = "FK所属校区ID不能为空")
    private Long campusId;

    /**
     * FK所属班级ID
     */
    @NotNull(message = "FK所属班级ID不能为空")
    private Long classesId;

    /**
     * 内容所属范围：1校级，2班级
     */
    @NotNull(message = "内容所属范围：1校级，2班级不能为空")
    private Integer scopeType;

    /**
     * FK云屏模块表id
     */
    @NotNull(message = "FK云屏模块表id不能为空")
    private Long screenModuleDataId;

    /**
     * 发布状态：1未发布，2已发布
     */
    @NotNull(message = "发布状态：1未发布，2已发布不能为空")
    private Integer screenContentStatus;

    /**
     * 模块内容类型：默认0，1网页地址，2富文本，3图片，4视频，5欢迎图
     */
    @NotNull(message = "模块内容类型：默认0，1网页地址，2富文本，3图片，4视频，5欢迎图不能为空")
    private Integer screenContentType;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 有效时间-开始时间
     */
    @NotNull(message = "有效时间-开始时间不能为空")
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    @NotNull(message = "有效时间-结束时间不能为空")
    private Date endTime;

    /**
     * 云屏内容详情
     */
    private List<ScreenContentDetailBo> screenContentDetailBos;

    /**
     * 云屏内容详情是否需要先删除后新增
     */
    private boolean deleteAndAddDetail;

    /**
     * 内容数据来源
     */
    private Integer screenContentSource;

    /**
     * 通知nice值，越小优越在前面（用于实现置顶功能）
     */
    private Integer contentNice;

}
