package com.fh.cloud.screen.service.wx.api;


import com.fh.cloud.screen.service.wx.entity.bo.OrganizationWxMsgTemplateConfigConditionBo;
import com.fh.cloud.screen.service.wx.entity.bo.OrganizationWxMsgTemplateConfigBo;
import com.fh.cloud.screen.service.wx.entity.vo.OrganizationWxMsgTemplateConfigVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 学校微信推送模板配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2024-09-24 17:12:03
 */
public interface OrganizationWxMsgTemplateConfigApi {

    /**
     * 查询学校微信推送模板配置表分页列表
     * <AUTHOR>
     * @date 2024-09-24 17:12:03
     */
    @PostMapping("/organization/wx/msg/template/config/page/list")
    public AjaxResult<PageInfo<OrganizationWxMsgTemplateConfigVo>> getOrganizationWxMsgTemplateConfigPageListByCondition(@RequestBody OrganizationWxMsgTemplateConfigConditionBo condition);

    /**
     * 查询学校微信推送模板配置表列表
     * <AUTHOR>
     * @date 2024-09-24 17:12:03
     */
    @PostMapping("/organization/wx/msg/template/config/list")
    public AjaxResult<List<OrganizationWxMsgTemplateConfigVo>> getOrganizationWxMsgTemplateConfigListByCondition(@RequestBody OrganizationWxMsgTemplateConfigConditionBo condition);


    /**
     * 新增学校微信推送模板配置表
     * <AUTHOR>
     * @date 2024-09-24 17:12:03
     */
    @PostMapping("/organization/wx/msg/template/config/add")
    public AjaxResult addOrganizationWxMsgTemplateConfig(@Validated @RequestBody OrganizationWxMsgTemplateConfigBo organizationWxMsgTemplateConfigBo);

    /**
     * 修改学校微信推送模板配置表
     * @param organizationWxMsgTemplateConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-09-24 17:12:03
     */
    @PostMapping("/organization/wx/msg/template/config/update")
    public AjaxResult updateOrganizationWxMsgTemplateConfig(@Validated @RequestBody OrganizationWxMsgTemplateConfigBo organizationWxMsgTemplateConfigBo);

    /**
     * 查询学校微信推送模板配置表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-09-24 17:12:03
     */
    @GetMapping("/organization/wx/msg/template/config/detail")
    public AjaxResult<OrganizationWxMsgTemplateConfigVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除学校微信推送模板配置表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-09-24 17:12:03
     */
    @GetMapping("/organization/wx/msg/template/config/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
