package com.fh.cloud.screen.service.rest.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 作息时间年级表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestGradeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    private Long workRestId;

    /**
     * grade 的code值，年级一致的情况这个值为默认值
     */
    private String grade;

    /**
     * 一周作息时间是否一致：1一致，2不一致
     */
    private Integer weekSameType;

    /**
     * 上午课节数：0，1，2，3...
     */
    private Integer courseNumAm;

    /**
     * 下午课节数：0，1，2，3...
     */
    private Integer courseNumPm;

    /**
     * 晚上课节数：0，1，2，3...
     */
    private Integer courseNumNt;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 作息时间天列表(1,2, ----------- list)
     */
    private Map<Integer, List<WorkRestDayVo>> weekMap ;

}
