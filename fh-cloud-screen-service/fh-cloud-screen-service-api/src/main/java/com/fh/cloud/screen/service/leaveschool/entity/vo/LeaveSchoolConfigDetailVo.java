package com.fh.cloud.screen.service.leaveschool.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * 放学配置详情表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
@Data
public class LeaveSchoolConfigDetailVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 放学配置详情表id
     */
    @ApiModelProperty("放学配置详情表id")
    private Long leaveSchoolConfigDetailId;

    /**
     * 放学配置表id
     */
    @ApiModelProperty("放学配置表id")
    private Long leaveSchoolConfigId;

    /**
     * 年级
     */
    @ApiModelProperty("年级")
    private String grade;

    /**
     * 周几
     */
    @ApiModelProperty("周几")
    private Long weekDay;

    /**
     * 放学开始时间
     */
    @ApiModelProperty("放学开始时间")
    @DateTimeFormat(pattern = "HH:mm")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date leaveSchoolStartTime;

    /**
     * 放学结束时间
     */
    @ApiModelProperty("放学结束时间")
    @DateTimeFormat(pattern = "HH:mm")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private Date leaveSchoolEndTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public LeaveSchoolConfigDetailVo returnOwn() {
        return this;
    }

}
