package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 模块库审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
@Data
public class ScreenModuleLibraryAuditConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenModuleLibraryAuditId;

	/**
	 * 模块名称
	 */
	@ApiModelProperty("模块名称")
	private String moduleName;

	/**
	 * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
	 */
	@ApiModelProperty("模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容")
	private Long moduleGroupType;

	/**
	 * 预置类型：1预置，2不预置
	 */
	@ApiModelProperty("预置类型：1预置，2不预置")
	private Integer presetType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 父模块库id
	 */
	@ApiModelProperty("父模块库id")
	private Long parentScreenModuleLibraryId;

	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Long librarySort;

	/**
	 * 是否海报模块：1是，2否
	 */
	@ApiModelProperty("是否海报模块：1是，2否")
	private Long isPoster;

	/**
	 * 审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交
	 */
	@ApiModelProperty("审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交")
	private Integer auditType;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty("驳回原因")
	private String reason;

	/**
	 * 审核人
	 */
	@ApiModelProperty("审核人")
	private String auditUser;

	/**
	 * 审核时间
	 */
	@ApiModelProperty("审核时间")
	private Date auditTime;

	/**
	 * 发布状态 1-未发布 2-已发布
	 */
	@ApiModelProperty("发布状态 1-未发布 2-已发布")
	private Integer releaseType;

	/**
	 * 图片版式：1横屏，2竖屏
	 */
	@ApiModelProperty("图片版式：1横屏，2竖屏")
	private Integer devicePattern;

	/**
	 * 模块库表id
	 */
	@ApiModelProperty("模块库表id")
	private Long screenModuleLibraryId;

	/**
	 * 分类id
	 */
	@ApiModelProperty("分类id")
	private Long labelId;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;

	/**
	 * 更新时间
	 */
	private Date createTime;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private Date updateTime;

	/**
	 * 更新人
	 */
	private String updateBy;

	/**
	 * 审核表id列表（用于批量查询）
	 */
	private List<Long> screenModuleLibraryAuditIds;
}
