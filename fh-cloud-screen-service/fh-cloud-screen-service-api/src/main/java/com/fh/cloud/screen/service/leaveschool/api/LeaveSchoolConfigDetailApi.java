package com.fh.cloud.screen.service.leaveschool.api;


import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 放学配置详情表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
public interface LeaveSchoolConfigDetailApi {

    /**
     * 查询放学配置详情表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @PostMapping("/leave/school/config/detail/page/list")
    public AjaxResult<PageInfo<LeaveSchoolConfigDetailVo>> getLeaveSchoolConfigDetailPageListByCondition(@RequestBody LeaveSchoolConfigDetailConditionBo condition);

    /**
     * 查询放学配置详情表列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @PostMapping("/leave/school/config/detail/list")
    public AjaxResult<List<LeaveSchoolConfigDetailVo>> getLeaveSchoolConfigDetailListByCondition(@RequestBody LeaveSchoolConfigDetailConditionBo condition);


    /**
     * 新增放学配置详情表
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @PostMapping("/leave/school/config/detail/add")
    public AjaxResult addLeaveSchoolConfigDetail(@Validated @RequestBody LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo);

    /**
     * 修改放学配置详情表
     * @param leaveSchoolConfigDetailBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @PostMapping("/leave/school/config/detail/update")
    public AjaxResult updateLeaveSchoolConfigDetail(@Validated @RequestBody LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo);

    /**
     * 查询放学配置详情表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @GetMapping("/leave/school/config/detail/detail")
    public AjaxResult<LeaveSchoolConfigDetailVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除放学配置详情表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @GetMapping("/leave/school/config/detail/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
