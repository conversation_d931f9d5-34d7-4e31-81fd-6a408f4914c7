package com.fh.cloud.screen.service.crm.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.crm.api.CrmInfoApi;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoConditionBo;
import com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * CRM商讯管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@FeignClient(contextId = "crmInfoApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CrmInfoApiService.CrmInfoApiFallbackFactory.class)
@Component
public interface CrmInfoApiService extends CrmInfoApi {

    @Component
    class CrmInfoApiFallbackFactory implements FallbackFactory<CrmInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CrmInfoApiFallbackFactory.class);
        @Override
        public CrmInfoApiService create(Throwable cause) {
            CrmInfoApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new CrmInfoApiService() {
                public AjaxResult getCrmInfoPageListByCondition(CrmInfoConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCrmInfoListByCondition(CrmInfoConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCrmInfo(CrmInfoBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCrmInfo(CrmInfoBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult addCrmInfoWithContact(CrmInfoBo crmInfoBo) {
                    return AjaxResult.fail("新增失败");
                }

                @Override
                public AjaxResult<CrmInfoVo> getDetailWithContact(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult<PageInfo<CrmInfoVo>> getCrmInfoPageListByConditionWithContact(CrmInfoConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                @Override
                public AjaxResult<List<CrmInfoVo>> getCrmInfoListByConditionWithContact(CrmInfoConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }
            };
        }
    }
}