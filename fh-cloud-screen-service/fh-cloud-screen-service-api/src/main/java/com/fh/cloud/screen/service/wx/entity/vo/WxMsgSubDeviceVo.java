package com.fh.cloud.screen.service.wx.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 微信消息订阅设备配置表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
@Data
public class WxMsgSubDeviceVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 用户oid
     */
    @ApiModelProperty("用户oid")
    private String userOid;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long showDeviceId;

    /**
     * 设备号
     */
    @ApiModelProperty("设备号")
    private String deviceNumber;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 关注设备数
     */
    @ApiModelProperty("关注设备数")
    private Integer count;

    /*
     * 方便steam流存入自身
     * */
    public WxMsgSubDeviceVo returnOwn() {
        return this;
    }

}
