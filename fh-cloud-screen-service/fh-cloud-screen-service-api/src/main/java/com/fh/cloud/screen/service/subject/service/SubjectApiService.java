package com.fh.cloud.screen.service.subject.service;

import com.fh.cloud.screen.service.subject.entity.bo.SubjectBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.space.service.SpaceInfoApiService;
import com.fh.cloud.screen.service.subject.api.SubjectApi;
import com.light.core.entity.AjaxResult;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = SubjectApiService.SubjectApiFallbackFactory.class)
@Component
public interface SubjectApiService extends SubjectApi {
    @Component
    class SubjectApiFallbackFactory implements FallbackFactory<SubjectApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(SpaceInfoApiService.SpaceInfoApiFallbackFactory.class);

        @Override
        public SubjectApiService create(Throwable cause) {
            SubjectApiService.SubjectApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new SubjectApiService() {
                @Override
                public AjaxResult listSubject(SubjectBo subjectBo) {
                    return AjaxResult.fail("listSubject失败");
                }
            };
        }
    }
}
