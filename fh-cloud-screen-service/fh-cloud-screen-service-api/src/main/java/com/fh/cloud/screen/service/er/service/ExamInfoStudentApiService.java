package com.fh.cloud.screen.service.er.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.er.api.ExamInfoStudentApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 考场_考试计划里面一次考试的学生
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@FeignClient(contextId = "examInfoStudentApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ExamInfoStudentApiService.ExamInfoStudentApiFallbackFactory.class)
@Component
public interface ExamInfoStudentApiService extends ExamInfoStudentApi {

    @Component
    class ExamInfoStudentApiFallbackFactory implements FallbackFactory<ExamInfoStudentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ExamInfoStudentApiFallbackFactory.class);
        @Override
        public ExamInfoStudentApiService create(Throwable cause) {
            ExamInfoStudentApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ExamInfoStudentApiService() {
                public AjaxResult getExamInfoStudentPageListByCondition(ExamInfoStudentConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getExamInfoStudentListByCondition(ExamInfoStudentConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addExamInfoStudent(ExamInfoStudentBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateExamInfoStudent(ExamInfoStudentBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult examSign(ExamInfoStudentBo examInfoStudentBo) {
                    return AjaxResult.fail("考试签到失败");
                }
            };
        }
    }
}