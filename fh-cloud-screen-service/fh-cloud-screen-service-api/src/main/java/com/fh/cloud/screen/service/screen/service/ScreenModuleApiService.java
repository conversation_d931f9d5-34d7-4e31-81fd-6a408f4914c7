package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenModuleApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenModuleApiService.ScreenModuleApiFallbackFactory.class)
@Component
public interface ScreenModuleApiService extends ScreenModuleApi {
    @Component
    class ScreenModuleApiFallbackFactory implements FallbackFactory<ScreenModuleApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenModuleApiService.ScreenModuleApiFallbackFactory.class);

        @Override
        public ScreenModuleApiService create(Throwable cause) {
            ScreenModuleApiService.ScreenModuleApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ScreenModuleApiService() {
                public AjaxResult getScreenModuleDataListByCondition(ScreenModuleDataListConditionBo condition) {
                    return AjaxResult.fail("查询云屏模块列表失败");
                }

                public AjaxResult addScreenModuleData(ScreenModuleDataBo screenModuleDataBo) {
                    return AjaxResult.fail("新增云屏模块表失败");
                }

                public AjaxResult updateScreenModuleData(ScreenModuleDataBo screenModuleDataBo) {
                    return AjaxResult.fail("修改云屏模块表失败");
                }

                public AjaxResult getDetail(Long screenModuleDataId) {
                    return AjaxResult.fail("查询云屏模块表详情失败");
                }

                public AjaxResult delete(Long screenModuleDataId) {
                    return AjaxResult.fail("删除云屏模块表失败");
                }

                @Override
                public AjaxResult
                    getScreenModuleDataGroupMapByConditionOfSchool(ScreenModuleDataListConditionBo condition) {
                    return AjaxResult.fail("获取模块组失败");
                }

                @Override
                public AjaxResult updatePresetModule(ScreenModuleDataBo screenModuleDataBo) {
                    return AjaxResult.fail("学校模块新增或移除预置模块错误");
                }
            };
        }
    }
}
