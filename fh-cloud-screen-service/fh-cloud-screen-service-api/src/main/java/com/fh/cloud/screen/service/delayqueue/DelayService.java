package com.fh.cloud.screen.service.delayqueue;

import java.util.List;

import com.fh.cloud.screen.service.delayqueue.entity.TaskDelayed;

/**
 * 通用的延迟队列业务service
 * 
 * <AUTHOR>
 * @date 2022 /12/21 14:05
 */
public interface DelayService<T> {

    /**
     * 初始化，一个业务的延迟队列的初始化
     *
     * <AUTHOR>
     * @date 2022 -12-21 14:01:01
     */
    void init();

    /**
     * 批量添加任务到延迟队列
     *
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -12-21 14:03:04
     */
    boolean addToDelayQueueBatch(List<TaskDelayed<T>> dataList);

    /**
     * 单个任务添加到延迟队列
     *
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -12-21 14:24:21
     */
    boolean addToDelayQueue(TaskDelayed<T> data);

    /**
     * 从队列中移除任务
     *
     * @param data the data
     * <AUTHOR>
     * @date 2022 -12-21 15:12:03
     */
    void removeFromDelayQueue(String taskId);
}
