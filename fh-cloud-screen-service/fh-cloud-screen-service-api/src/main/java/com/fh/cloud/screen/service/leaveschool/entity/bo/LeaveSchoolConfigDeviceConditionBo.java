package com.fh.cloud.screen.service.leaveschool.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 放学配置设备表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
@Data
public class LeaveSchoolConfigDeviceConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 放学配置设备表id
	 */
	@ApiModelProperty("放学配置设备表id")
	private Long leaveSchoolConfigDeviceId;

	/**
	 * 放学配置表id
	 */
	@ApiModelProperty("放学配置表id")
	private Long leaveSchoolConfigId;

	/**
	 * 展示设备id
	 */
	@ApiModelProperty("展示设备id")
	private Long showDeviceId;


	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@ApiModelProperty("校区id")
	private Long campusId;

	/**
	 * 班级id或者空间id（即非行政教室id），根据spaceGroupUseType区分
	 */
	@ApiModelProperty("班级id或者空间id（即非行政教室id），根据spaceGroupUseType区分")
	private List<Long> spaceInfoIds;

	/**
	 * 地点组使用类型:1行政教室，2非行政教室
	 */
	@ApiModelProperty("地点组使用类型:1行政教室，2非行政教室")
	private Integer spaceGroupUseType;


	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 设备ids
	 */
	@ApiModelProperty("设备ids")
	private List<Long> showDeviceIds;
}
