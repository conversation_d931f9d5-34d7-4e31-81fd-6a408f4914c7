package com.fh.cloud.screen.service.attendance.entity.bo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 考勤流水表，不用于业务查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceLogBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceLogId;

    /**
     * 考勤规则id，当天考勤使用的考勤规则
     */
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @NotNull(message = "考勤类型：1教师考勤，2学生考勤不能为空")
    private Integer attendanceType;

    /**
     * 考勤设备号，例如云屏设备的设备号
     */
    @NotBlank(message = "考勤设备号，例如云屏设备的设备号不能为空")
    private String deviceNumber;

    /**
     * 打卡地点
     */
    @NotBlank(message = "打卡地点不能为空")
    private String address;

    /**
     * 考勤卡号，例如学生卡卡号
     */
    private String cardNumber;

    /**
     * 用户oid
     */
    @NotBlank(message = "用户oid不能为空")
    private String userOid;

    /**
     * 学生年级
     */
    private String grade;

    /**
     * 学生所属班级id
     */
    private Long classesId;

    /**
     * 考勤时间
     */
    @NotNull(message = "考勤时间不能为空")
    private Date attendanceTime;

    /**
     * 签到签退类型：1签到，2签退
     */
    private Integer signType;

    /**
     * 考勤方式：1实体卡，2人脸识别
     */
    private Integer attendanceMethod;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime = new Date();

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 人脸图片fileOid
     */
    private String faceMediaId;

}
