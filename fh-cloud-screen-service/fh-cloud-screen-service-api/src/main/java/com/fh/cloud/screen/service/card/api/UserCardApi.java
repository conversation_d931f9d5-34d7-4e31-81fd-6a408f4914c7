package com.fh.cloud.screen.service.card.api;

import com.fh.cloud.screen.service.card.entity.bo.TeacherCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardExportVo;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardExportVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

public interface UserCardApi {

    /**
     * 查询学生卡列表
     * 
     * @param conditionBo
     * @return
     */
    @PostMapping("/userCard/getStudentCardList")
    @ApiOperation(value = "查询学生卡列表", httpMethod = "POST")
    AjaxResult getStudentCardList(@RequestBody UserCardListConditionBo conditionBo);

    /**
     * 查询教师卡列表
     * 
     * @param conditionBo
     * @return
     */
    @PostMapping("/userCard/getTeacherCardList")
    @ApiOperation(value = "查询教师卡列表", httpMethod = "POST")
    AjaxResult getTeacherCardList(@RequestBody UserCardListConditionBo conditionBo);

    /**
     * 学生卡统计
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/getStudentCardCount")
    @ApiOperation(value = "学生卡统计", httpMethod = "POST")
    AjaxResult getStudentCardCount(@RequestBody UserCardBo userCardBo);

    /**
     * 教师卡统计
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/getTeacherCardCount")
    @ApiOperation(value = "教师卡统计", httpMethod = "POST")
    AjaxResult getTeacherCardCount(@RequestBody UserCardBo userCardBo);

    /**
     * 查询用户卡详情
     * 
     * @param userCardId
     * @return
     */
    @GetMapping("/userCard/detail")
    @ApiOperation(value = "查询用户卡详情", httpMethod = "GET")
    AjaxResult getDetail(@NotNull(message = "请选择用户") @RequestParam("userCardId") Long userCardId);

    /**
     * 编辑卡
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/updateCard")
    @ApiOperation(value = "编辑卡", httpMethod = "POST")
    AjaxResult updateCard(@RequestBody UserCardBo userCardBo);

    /**
     * 绑卡
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/tiedCard")
    @ApiOperation(value = "绑卡", httpMethod = "POST")
    AjaxResult tiedCard(@RequestBody UserCardBo userCardBo);

    /**
     * 解绑卡
     * 
     * @return
     */
    @GetMapping("/userCard/unbindCard")
    @ApiOperation(value = "解绑卡", httpMethod = "GET")
    AjaxResult unbindCard(@NotNull(message = "请选择用户") @RequestParam("userCardId") Long userCardId);

    /**
     * 批量解绑卡
     * 
     * @return
     */
    @GetMapping("/userCard/batchUnbindCard")
    @ApiOperation(value = "批量解绑卡", httpMethod = "GET")
    AjaxResult batchUnbindCard(@NotBlank(message = "请选择用户") @RequestParam("userCardIds") String userCardIds);

    /**
     * 学生卡导出
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/studentCardExport")
    @ApiOperation(value = "学生卡导出", httpMethod = "POST")
    List<StudentCardExportVo> studentCardExport(@RequestBody UserCardBo userCardBo);

    /**
     * 学生卡导入
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/studentCardImport")
    @ApiOperation(value = "学生卡导入", httpMethod = "POST")
    AjaxResult studentCardImport(@RequestBody UserCardBo userCardBo);

    /**
     * 教师卡导出
     * 
     * @param userCardBo
     * @return
     */
    @PostMapping("/userCard/teacherCardExport")
    @ApiOperation(value = "教师卡导出", httpMethod = "POST")
    List<TeacherCardExportVo> teacherCardExport(@RequestBody UserCardBo userCardBo);

    /**
     * 教师卡导入
     * 
     * @param list
     * @return
     */
    @PostMapping("/userCard/teacherCardImport")
    @ApiOperation(value = "教师卡导入", httpMethod = "POST")
    AjaxResult teacherCardImport(@RequestBody List<TeacherCardImportBo> list);
}
