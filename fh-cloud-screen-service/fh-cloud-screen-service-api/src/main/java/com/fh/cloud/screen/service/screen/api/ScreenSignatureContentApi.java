package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 电子签名表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
public interface ScreenSignatureContentApi {

    /**
     * 查询电子签名表分页列表
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/content/page/list")
    public AjaxResult<PageInfo<ScreenSignatureContentVo>> getScreenSignatureContentPageListByCondition(@RequestBody ScreenSignatureContentConditionBo condition);

    /**
     * 查询电子签名表列表
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/content/list")
    public AjaxResult<List<ScreenSignatureContentVo>> getScreenSignatureContentListByCondition(@RequestBody ScreenSignatureContentConditionBo condition);


    /**
     * 新增电子签名表
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/content/add")
    public AjaxResult addScreenSignatureContent(@Validated @RequestBody ScreenSignatureContentBo screenSignatureContentBo);

    /**
     * 修改电子签名表
     * @param screenSignatureContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/content/update")
    public AjaxResult updateScreenSignatureContent(@Validated @RequestBody ScreenSignatureContentBo screenSignatureContentBo);

    /**
     * 查询电子签名表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @GetMapping("/screen/signature/content/detail")
    public AjaxResult<ScreenSignatureContentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除电子签名表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @GetMapping("/screen/signature/content/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
