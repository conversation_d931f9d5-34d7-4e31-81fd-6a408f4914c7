package com.fh.cloud.screen.service.device.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 设备订阅标签表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
@Data
public class ShowDeviceLabelRelConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long showDeviceId;

    /**
     * 标签id
     */
    @ApiModelProperty("标签id")
    private Long labelId;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

}
