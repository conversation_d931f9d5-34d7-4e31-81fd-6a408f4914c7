package com.fh.cloud.screen.service.device.entity.bo;

import com.fh.cloud.screen.service.enums.FaceModType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/12 17:17
 */
@Data
public class ShowDeviceOperateBo implements Serializable {
    /**
     * 开关机状态类型{@link DeviceStatusType}
     */
    Integer deviceStatusType;
    /**
     * 人脸模式类型{@link FaceModType}
     */
    Integer faceModType;
    /**
     * 监管状态1监管,2未被监管
     */
    Integer superviseState;
    /**
     * 学校id
     */
    Long organizationId;

    /**
     * 设备号
     */
    String deviceNumber;

    /**
     * 设备号列表
     */
    List<String> deviceNumbers;
}
