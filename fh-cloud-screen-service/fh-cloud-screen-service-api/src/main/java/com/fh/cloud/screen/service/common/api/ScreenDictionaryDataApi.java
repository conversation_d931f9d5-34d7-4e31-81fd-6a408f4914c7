package com.fh.cloud.screen.service.common.api;

import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataBo;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/30 17:04
 */
public interface ScreenDictionaryDataApi {

    @PostMapping("/dictionary/data/list")
    @ApiOperation(value = "字段数据列表", httpMethod = "POST")
    AjaxResult getDictionaryDataListByCondition(@RequestBody DictionaryDataListConditionBo condition);

    /**
     * 海报分组列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/30 10:46
     */
    @PostMapping("/poster/group/list")
    @ApiOperation(value = "海报分组列表", httpMethod = "POST")
    AjaxResult getPosterGroupListByCondition(@RequestBody DictionaryDataListConditionBo condition);

    /**
     * 新增海报分组
     * 
     * <AUTHOR>
     * @date 2022-12-30 10:07:25
     */
    @PostMapping("dictionary/data/add")
    public AjaxResult addDictionaryData(@RequestBody DictionaryDataBo dictionaryDataBo);

    /**
     * 修改字典数据
     * 
     * @param dictionaryDataBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-30 10:07:25
     */
    @PostMapping("dictionary/data/update")
    public AjaxResult updateDictionaryData(@RequestBody DictionaryDataBo dictionaryDataBo);

    /**
     * 交换顺序
     *
     * @param firstId, secondId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/10 10:34
     */
    @GetMapping("dictionary/exchange")
    public AjaxResult exchange(@RequestParam("firstId") Long firstId, @RequestParam("secondId") Long secondId);

    /**
     * 根据id顺序更新对应的顺序
     *
     * @param idList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/10 10:34
     */
    @PostMapping("dictionary/update-sort")
    public AjaxResult updateGroupSortByIdList(@RequestBody List<Long> idList);
}
