package com.fh.cloud.screen.service.device.entity.bo;

import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 展示设备表，例如云屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ShowDeviceListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long showDeviceId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 激活状态
     */
    private String deviceActivation;

    /**
     * 设备类型：1云屏。设备绑定的时候更新
     */
    private Integer deviceType;

    /**
     * 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    private Integer devicePattern;

    /**
     * 是否全屏类型：1全屏，2不是全屏
     */
    @NotNull(message = "是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 设备MAC地址。设备绑定的时候更新
     */
    private String deviceMacAddress;

    /**
     * 开关机状态：1开机，2关机，3异常
     */
    private Integer deviceStatus;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 地点组id
     */
    private Long spaceGroupId;

    /**
     * 地点组使用类型:1行政教室，2非行政教室 {@link SpaceGroupUserType}
     */
    private Integer spaceGroupUseType;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 出货号
     */
    private String shipmentNo;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 设备品牌类型
     */
    @ApiModelProperty("设备品牌类型")
    private Integer deviceBrand;

    /**
     * 设备型号
     */
    @ApiModelProperty("设备型号")
    private String deviceModel;

    /**
     * 产品序列号
     */
    @ApiModelProperty("产品序列号")
    private String productSerialNumber;

    /**
     * 客户端版本
     */
    @ApiModelProperty("客户端版本")
    private String clientVersion;

    /**
     * 系统主动推送海报：1接受；2不接受
     */
    @ApiModelProperty("系统主动推送海报：1接受；2不接受")
    private Integer pushType;

    /**
     * 虹软人脸识别激活码
     */
    private String arcsoftFaceCode;

    /**
     * 检索班级时，检索任教范围 null or 1 检索任教班级和任教科目，2：只检索任教(班主任的)班级，3 任教科目班级
     */
    private Integer teachingScope;

    /**
     * 地点组ids
     */
    private List<Long> spaceGroupIds;

    /**
     * 地点ids查询过滤数据使用
     */
    private List<Long> spaceInfoIds;

    /**
     * 巡查备注
     */
    private String patrolRemark;

    /**
     * 设备巡查状态：
     *  1: '未排查',
     *  2: '正常',
     *  3: '硬件问题',
     *  4: '实施问题',
     *  5: '网络问题',
     *  6: '软件问题（安卓）',
     *  7: '软件问题（前端）',
     *  8: '软件问题（后端）',
     *  9: '使用问题（操作）',
     *  10: '使用问题（不想开）',
     */
    private Integer patrolType;

    /**
     * 云屏皮肤主题类型，默认1默认主题，2红色主题
     */
    @ApiModelProperty("云屏皮肤主题类型，默认1默认主题，2红色主题")
    private Integer deviceThemeType;

    @ApiModelProperty("组织ids")
    private List<Long> organizationIds;

    @ApiModelProperty("地点id")
    private Long spaceInfoId;

    @ApiModelProperty("设备号列表")
    private List<String> showDeviceNumbers;

    @ApiModelProperty("监管教育局id")
    private Long parentOrganizationId;

    @ApiModelProperty("地点")
    private List<SpaceInfoBo> spaceInfoBos;

    /**
     * 该设备上的人脸库建模类型（只用于过滤设备查询人脸库的数据）：1全校人脸库建模，2班级人脸库建模
     * {@link com.fh.cloud.screen.service.enums.FaceModType}
     */
    private Integer faceModType;

    /**
     * 该设备的监管状态 1-监管 2-未监管
     */
    private Integer superviseState;

    /**
     * 查询是否是放学设备   默认false 不查询
     */
    private Boolean queryLeaveSchoolDevice = false;
}
