package com.fh.cloud.screen.service.device.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.api.ShowDeviceCaptureApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 设备抓图表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
@FeignClient(contextId = "showDeviceCaptureApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ShowDeviceCaptureApiService.ShowDeviceCaptureApiFallbackFactory.class)
@Component
public interface ShowDeviceCaptureApiService extends ShowDeviceCaptureApi {

    @Component
    class ShowDeviceCaptureApiFallbackFactory implements FallbackFactory<ShowDeviceCaptureApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ShowDeviceCaptureApiFallbackFactory.class);

        @Override
        public ShowDeviceCaptureApiService create(Throwable cause) {
            ShowDeviceCaptureApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ShowDeviceCaptureApiService() {
                public AjaxResult getShowDeviceCapturePageListByCondition(ShowDeviceCaptureConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getShowDeviceCaptureListByCondition(ShowDeviceCaptureConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addShowDeviceCapture(ShowDeviceCaptureBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateShowDeviceCapture(ShowDeviceCaptureBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<ShowDeviceCaptureVo> getDetailByNumber(String deviceNumber) {
                    return AjaxResult.fail("getDetailByNumber失败");
                }

                @Override
                public AjaxResult uploadShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo) {
                    return AjaxResult.fail("uploadShowDeviceCapture失败");
                }

                @Override
                public AjaxResult launchShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo) {
                    return AjaxResult.fail("launchShowDeviceCapture失败");
                }
            };
        }
    }
}