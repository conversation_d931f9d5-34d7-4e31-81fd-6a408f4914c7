package com.fh.cloud.screen.service.cs.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.cs.api.CsContactApi;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * Cultural-Station文化小站联系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
@FeignClient(contextId = "csContactApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CsContactApiService.CsContactApiFallbackFactory.class)
@Component
public interface CsContactApiService extends CsContactApi {

    @Component
    class CsContactApiFallbackFactory implements FallbackFactory<CsContactApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CsContactApiFallbackFactory.class);
        @Override
        public CsContactApiService create(Throwable cause) {
            CsContactApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new CsContactApiService() {
                public AjaxResult getCsContactPageListByCondition(CsContactConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCsContactListByCondition(CsContactConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCsContact(CsContactBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCsContact(CsContactBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}