package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenSceneApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenSceneApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenSceneApiService.screenSceneApiFallbackFactory.class)
@Component
public interface ScreenSceneApiService extends ScreenSceneApi {
    @Component
    class screenSceneApiFallbackFactory implements FallbackFactory<ScreenSceneApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenSceneApiService.screenSceneApiFallbackFactory.class);

        @Override
        public ScreenSceneApiService create(Throwable cause) {
            ScreenSceneApiService.screenSceneApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ScreenSceneApiService() {

                @Override
                public AjaxResult getScreenSceneListByCondition(ScreenSceneListConditionBo condition) {
                    return AjaxResult.fail("云屏获取场景列表失败");
                }

                @Override
                public AjaxResult saveOrUpdateScreenScene(ScreenSceneBo screenSceneBo) {
                    return AjaxResult.fail("云屏保存或者修改场景失败");
                }

                @Override
                public AjaxResult delete(Long screenSceneId, Long organizationId, Long parentOrganizationId) {
                    return AjaxResult.fail("云屏删除场景失败");
                }

                @Override
                public AjaxResult getDetail(Long screenSceneId) {
                    return AjaxResult.fail("云屏获取场景详情失败");
                }

                @Override
                public AjaxResult saveOrUpdateScreenSceneBath(List<ScreenSceneBo> screenSceneBos) {
                    return AjaxResult.fail("云屏批量保存或者修改失败");
                }
            };
        }
    }
}
