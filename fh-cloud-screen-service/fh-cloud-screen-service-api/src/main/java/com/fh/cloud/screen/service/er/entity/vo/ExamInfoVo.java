package com.fh.cloud.screen.service.er.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考场_考试计划里面一次考试信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 考场号（名称）
     */
    @ApiModelProperty("考场号（名称）")
    private String examRoomName;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 地点id
     */
    @ApiModelProperty("地点id")
    private Long spaceInfoId;

    /**
     * 地点名称
     */
    @ApiModelProperty("地点名称")
    private String spaceInfoName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 科目名称合集-列表展示使用
     */
    private List<String> subjectNames;

    /**
     * 地点组id
     */
    private Long spaceGroupId;

    /**
     * 地点组名称
     */
    private String spaceGroupName;

    /**
     * 科目对象合集-详情展示使用
     */
    private List<ExamInfoSubjectVo> examInfoSubjectVos;
}
