package com.fh.cloud.screen.service.rest.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间年级活动课设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestGradeActivityBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long workRestGradeActivityId;

    /**
     * FK作息时间年级表主键
     */
    @NotNull(message = "FK作息时间年级表主键不能为空")
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    @NotNull(message = "FK作息时间主表主键id不能为空")
    private Long workRestId;

    /**
     * 活动课名称
     */
    @NotBlank(message = "活动课名称不能为空")
    private String activityName;

    /**
     * 活动课节次：1，2，3...
     */
    @NotNull(message = "活动课节次：1，2，3...不能为空")
    private Integer activityPosition;

    /**
     * 活动课节次类型：1在sort节次之前，2在sort节次之后
     */
    @NotNull(message = "活动课节次类型：1在sort节次之前，2在sort节次之后不能为空")
    private Integer activitySortType;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

}
