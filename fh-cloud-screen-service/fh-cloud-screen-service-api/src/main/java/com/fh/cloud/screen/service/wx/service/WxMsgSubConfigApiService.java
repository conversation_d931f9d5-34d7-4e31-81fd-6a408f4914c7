package com.fh.cloud.screen.service.wx.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubConfigVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.wx.api.WxMsgSubConfigApi;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubConfigBo;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubConfigConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;


/**
 * 微信消息订阅用户配置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-04-02 14:54:41
 */
@FeignClient(contextId = "wxMsgSubConfigApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = WxMsgSubConfigApiService.WxMsgSubConfigApiFallbackFactory.class)
@Component
public interface WxMsgSubConfigApiService extends WxMsgSubConfigApi {

    @Component
    class WxMsgSubConfigApiFallbackFactory implements FallbackFactory<WxMsgSubConfigApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(WxMsgSubConfigApiFallbackFactory.class);
        @Override
        public WxMsgSubConfigApiService create(Throwable cause) {
            WxMsgSubConfigApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new WxMsgSubConfigApiService() {
                public AjaxResult getWxMsgSubConfigPageListByCondition(WxMsgSubConfigConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getWxMsgSubConfigListByCondition(WxMsgSubConfigConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addWxMsgSubConfig(WxMsgSubConfigBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateWxMsgSubConfig(WxMsgSubConfigBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<WxMsgSubConfigVo> getDetailByUserOid(String userOid) {
                    return AjaxResult.fail("查询详情失败");
                }

                @Override
                public AjaxResult saveWxMsgSubConfig(WxMsgSubConfigBo wxMsgSubConfigBo) {
                    return AjaxResult.fail("保存失败");
                }
            };
        }
    }
}