package com.fh.cloud.screen.service.screen.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏模块表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenModuleDataVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long screenModuleDataId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * FK模块库表id，学校自定义模块的时候，该值为0。注意模块库id是固定值，可以用于定制业务判断，例如考勤。
     */
    private Long screenModuleLibraryId;

    /**
     * 模块来源类型：1预置模块，2自定义模块
     */
    private Integer moduleSource;

    /**
     * 自定义模块名称
     */
    private String customModuleName;

    /**
     * 自定义模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private Long customModuleGroupType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 模块下的内容列表
     */
    private List<ScreenContentVo> screenContentVos;

    /**
     * 模块名称（可以是模块库的模块名称或者自定义的模块名称）-云屏首页接口使用
     */
    private String moduleName;

    /**
     * 模块分组类型（可以包含自定义模块的数据，展示时候使用）
     */
    private Long moduleGroupType;

    /**
     * 场景id
     */
    private Long screenSceneId;

    /**
     * 是否海报：1是，2否
     */
    private Integer isPoster;

    /**
     * 海报多选id，使用逗号分割
     */
    private String screenModuleLibrarySelIds;
}
