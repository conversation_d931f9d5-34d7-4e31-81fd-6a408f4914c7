package com.fh.cloud.screen.service.wx.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
/**
 * 学校微信推送模板配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-09-24 17:12:03
 */
@Data
public class OrganizationWxMsgTemplateConfigVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 模板id
     */
    @ApiModelProperty("模板id")
    private String templateId;

    /**
     * 跳转地址
     */
    @ApiModelProperty("跳转地址")
    private String url;

    /**
     * 业务类型 1-放学推送（放学中） 2-放学推送（已放学）
     */
    @ApiModelProperty("业务类型 1-放学推送（放学中） 2-放学推送（已放学）")
    private Integer type;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public OrganizationWxMsgTemplateConfigVo returnOwn() {
        return this;
    }

}
