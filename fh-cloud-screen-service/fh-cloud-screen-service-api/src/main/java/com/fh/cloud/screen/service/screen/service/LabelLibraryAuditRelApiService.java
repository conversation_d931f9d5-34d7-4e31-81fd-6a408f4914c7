package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.LabelLibraryAuditRelApi;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 标签海报关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
@FeignClient(contextId = "labelLibraryAuditRelApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LabelLibraryAuditRelApiService.LabelLibraryAuditRelApiFallbackFactory.class)
@Component
public interface LabelLibraryAuditRelApiService extends LabelLibraryAuditRelApi {

    @Component
    class LabelLibraryAuditRelApiFallbackFactory implements FallbackFactory<LabelLibraryAuditRelApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LabelLibraryAuditRelApiFallbackFactory.class);
        @Override
        public LabelLibraryAuditRelApiService create(Throwable cause) {
            LabelLibraryAuditRelApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LabelLibraryAuditRelApiService() {
                public AjaxResult getLabelLibraryAuditRelPageListByCondition(LabelLibraryAuditRelConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLabelLibraryAuditRelListByCondition(LabelLibraryAuditRelConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLabelLibraryAuditRel(LabelLibraryAuditRelBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLabelLibraryAuditRel(LabelLibraryAuditRelBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}