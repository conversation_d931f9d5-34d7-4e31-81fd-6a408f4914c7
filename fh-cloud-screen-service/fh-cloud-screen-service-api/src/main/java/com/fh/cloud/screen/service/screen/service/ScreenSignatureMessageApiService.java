package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenSignatureMessageApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 电子签名寄语资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
@FeignClient(contextId = "screenSignatureMessageApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenSignatureMessageApiService.ScreenSignatureMessageApiFallbackFactory.class)
@Component
public interface ScreenSignatureMessageApiService extends ScreenSignatureMessageApi {

    @Component
    class ScreenSignatureMessageApiFallbackFactory implements FallbackFactory<ScreenSignatureMessageApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenSignatureMessageApiFallbackFactory.class);
        @Override
        public ScreenSignatureMessageApiService create(Throwable cause) {
            ScreenSignatureMessageApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ScreenSignatureMessageApiService() {
                public AjaxResult getScreenSignatureMessagePageListByCondition(ScreenSignatureMessageConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenSignatureMessageListByCondition(ScreenSignatureMessageConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenSignatureMessage(ScreenSignatureMessageBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenSignatureMessage(ScreenSignatureMessageBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}