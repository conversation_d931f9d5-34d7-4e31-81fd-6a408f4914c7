package com.fh.cloud.screen.service.slow.api;

import java.util.List;

import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

/**
 * 专门用于open服务的开放服务
 *
 * <AUTHOR>
 * @since 2023 /3/21
 */
public interface OpenSlowScreenApi {

    public final String prefix = "open-slow-screen";

    /**
     * 查询分页列表 ，不自带isDelete参数
     * 
     * <AUTHOR>
     * @date 2023-03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/space/queryPageList")
    AjaxResult<PageInfo<SpaceInfoVo>> querySpacePageList(@RequestBody SpaceInfoListConditionBo condition);

    /**
     * 查询列表 ，不自带isDelete参数
     *
     * @param condition the condition
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/space/queryList")
    AjaxResult<List<SpaceInfoVo>> querySpaceList(@RequestBody SpaceInfoListConditionBo condition);

    /**
     * 查询分页列表 ，不自带isDelete参数
     *
     * @param condition the condition
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/space/queryThirdScenePageList")
    AjaxResult<PageInfo<ScreenSceneThirdVo>>
        queryThirdScenePageList(@RequestBody ScreenSceneThirdConditionBo condition);

    /**
     * 查询列表 ，不自带isDelete参数
     *
     * @param condition the condition
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/space/queryThirdSceneList")
    AjaxResult<List<ScreenSceneThirdVo>> queryThirdSceneList(@RequestBody ScreenSceneThirdConditionBo condition);

    /**
     * 同步人脸信息-学生
     *
     * @param condition the condition
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/face/sync-student")
    AjaxResult<String> syncFaceRecordsStudent(@RequestBody List<FaceRecordStudentBo> faceRecordStudentBoList);

    /**
     * 同步人脸信息-教师
     *
     * @param condition the condition
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/face/sync-teacher")
    AjaxResult<String> syncFaceRecordsTeacher(@RequestBody List<FaceRecordTeacherBo> faceRecordTeacherBoList);

    /**
     * 同步卡号信息
     *
     * @param userCardBos the user card bos
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/card/sync")
    AjaxResult<String> syncCards(@RequestBody List<UserCardBo> userCardBos);

    /**
     * 同步课表信息
     *
     * @param syllabusInfoBos the syllabus info bos
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -03-29 14:01:33
     */
    @PostMapping(OpenSlowScreenApi.prefix + "/syllabus/sync")
    AjaxResult<String> syncSyllabus(@RequestBody List<SyllabusInfoBo> syllabusInfoBos);
}
