package com.fh.cloud.screen.service.grade.api;

import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

public interface GradeScreenApi {

    /**
     * 根据组织机构ID获取所有年级
     */
    @GetMapping("grade/org-section")
    AjaxResult getBySectionOrgId(@RequestParam("orgId") Long orgId);

    /**
     * 根据组织机构ID获取所有年级-按照学段分组
     */
    @GetMapping("/grade/section-group")
    AjaxResult getBySectionOrgIdGroup(@RequestParam("orgId") Long orgId);

    /**
     * 根据组织id获取学段列表
     *
     * @return
     */
    @GetMapping("/section/list")
    AjaxResult getSectionByOrgId(@RequestParam("orgId") Long orgId);

    /**
     * 当前学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @RequestMapping(value = "/year/current", method = RequestMethod.GET)
    AjaxResult currentYear();

    /**
     * 分页查询班级
     *
     * @param clazzConditionBo
     * @return
     */
    @RequestMapping(value = "/classes", method = RequestMethod.POST)
    AjaxResult getClassesListByCondition(@RequestBody ClazzConditionBoExt clazzConditionBo);

    /**
     * 获取年级的时候同时获取班级列表和学生列表
     *
     * @param orgId the org id
     * @return ajax result
     */
    @GetMapping(value = "/grade-student")
    AjaxResult listGradesWithClassesAndStudents(@RequestParam("orgId") Long orgId);
}
