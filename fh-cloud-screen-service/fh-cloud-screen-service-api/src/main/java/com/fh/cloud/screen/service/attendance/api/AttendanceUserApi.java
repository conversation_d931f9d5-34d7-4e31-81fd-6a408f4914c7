package com.fh.cloud.screen.service.attendance.api;

import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserResultVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface AttendanceUserApi {

    /**
     * 分页条件查询 考勤记录
     *
     * @param condition
     * @return
     */
    @PostMapping("/attendance-user/page-list")
    AjaxResult pageList(@RequestBody AttendanceUserListConditionBo condition);

    /**
     * 条件查询考勤记录
     *
     * @param condition
     * @return
     */
    @PostMapping("/attendance-user/list")
    AjaxResult<List<AttendanceUserVo>> list(@RequestBody AttendanceUserListConditionBo condition);

    /**
     * 日期条件查询考勤记录列表（日期必传）
     *
     * @param condition
     * @return
     */
    @PostMapping("/attendance-user/list-date")
    AjaxResult<AttendanceUserCensusVo> getListByDateCondition(@RequestBody AttendanceUserListConditionBo condition);

    /**
     * 日期条件查询考勤记录列表（日期必传）-导出信息查询
     *
     * @param condition
     * @return
     */
    @PostMapping("/attendance-user/list-date/export")
    AjaxResult<AttendanceUserResultVo> getListExportByDateCondition(@RequestBody AttendanceUserListConditionBo condition);


    /**
     * 考勤新增
     *
     * @param attendanceUserBo
     * @return
     */
    @PostMapping("/attendance-user/add")
    AjaxResult addAttendanceUser(@Validated @RequestBody AttendanceUserBo attendanceUserBo);

    /**
     * 考勤修改
     *
     * @param attendanceUserBo
     * @return
     */
    @PostMapping("/attendance-user/update")
    AjaxResult updateAttendanceUser(@Validated @RequestBody AttendanceUserBo attendanceUserBo);

    /**
     * 考勤记录详情
     *
     * @param attendanceUserId
     * @return
     */
    @GetMapping("/attendance-user/detail")
    AjaxResult getDetail(@NotNull(message = "请选择数据") Long attendanceUserId);

    /**
     * 考勤记录删除
     *
     * @param attendanceUserId
     * @return
     */
    @GetMapping("/attendance-user/delete")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long attendanceUserId);

    /**
     * 考勤记录更改状态 （实则逻辑 插入一条考勤记录数据 ： 无记录为异常）
     *
     * @param bo
     * @return
     */
    @PostMapping("/attendance-user/changeState")
    AjaxResult changeState(@RequestBody AttendanceUserBo bo);
}
