package com.fh.cloud.screen.service.enums;

/**
 * 科目类型
 *
 * <AUTHOR>
 * @date 2022/5/6 17:09
 */
public enum SubjectType {

    TYPE_COUNTRY(1L, "国家级"),
    TYPE_ORGANIZATION(2L, "局级"),
    TYPE_SCHOOL(3L, "校级"),
    TYPE_ALL(4L, "不分页所有（当前组织及上级及预置科目）"),
    TYPE_ALL_PAGE(5L, "分页所有");

    private final Long code;
    private final String value;

    SubjectType(Long code, String value) {
        this.code = code;
        this.value = value;
    }

    public Long getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static SubjectType getSubjectType(Long type) {

        for (SubjectType subjectType : SubjectType.values()) {

            if (subjectType.getCode().equals(type)) {
                return subjectType;
            }
        }
        return null;
    }
}