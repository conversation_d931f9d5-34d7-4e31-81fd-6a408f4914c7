package com.fh.cloud.screen.service.er.api;


import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 考场_考试计划里面一次考试的老师
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamInfoTeacherApi {

    /**
     * 查询考场_考试计划里面一次考试的老师分页列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/teach/page/list")
    public AjaxResult<PageInfo<ExamInfoTeacherVo>> getExamInfoTeacherPageListByCondition(@RequestBody ExamInfoTeacherConditionBo condition);

    /**
     * 查询考场_考试计划里面一次考试的老师列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/teach/list")
    public AjaxResult<List<ExamInfoTeacherVo>> getExamInfoTeacherListByCondition(@RequestBody ExamInfoTeacherConditionBo condition);


    /**
     * 新增考场_考试计划里面一次考试的老师
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/teach/add")
    public AjaxResult addExamInfoTeacher(@Validated @RequestBody ExamInfoTeacherBo examInfoTeacherBo);

    /**
     * 修改考场_考试计划里面一次考试的老师
     * @param examInfoTeacherBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/teach/update")
    public AjaxResult updateExamInfoTeacher(@Validated @RequestBody ExamInfoTeacherBo examInfoTeacherBo);

    /**
     * 查询考场_考试计划里面一次考试的老师详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/info/teach/detail")
    public AjaxResult<ExamInfoTeacherVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除考场_考试计划里面一次考试的老师
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/info/teach/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
