package com.fh.cloud.screen.service.label.api;


import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 标签节日关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
public interface LabelFestivalRelApi {

    /**
     * 查询标签节日关联表分页列表
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/festival/rel/page/list")
    public AjaxResult<PageInfo<LabelFestivalRelVo>> getLabelFestivalRelPageListByCondition(@RequestBody LabelFestivalRelConditionBo condition);

    /**
     * 查询标签节日关联表列表
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/festival/rel/list")
    public AjaxResult<List<LabelFestivalRelVo>> getLabelFestivalRelListByCondition(@RequestBody LabelFestivalRelConditionBo condition);


    /**
     * 新增标签节日关联表
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/festival/rel/add")
    public AjaxResult addLabelFestivalRel(@Validated @RequestBody LabelFestivalRelBo labelFestivalRelBo);

    /**
     * 修改标签节日关联表
     * @param labelFestivalRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/festival/rel/update")
    public AjaxResult updateLabelFestivalRel(@Validated @RequestBody LabelFestivalRelBo labelFestivalRelBo);

    /**
     * 查询标签节日关联表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @GetMapping("label/festival/rel/detail")
    public AjaxResult<LabelFestivalRelVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除标签节日关联表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @GetMapping("label/festival/rel/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
