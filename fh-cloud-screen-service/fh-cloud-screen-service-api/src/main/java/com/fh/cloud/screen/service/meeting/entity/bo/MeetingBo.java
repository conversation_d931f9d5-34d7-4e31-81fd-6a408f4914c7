package com.fh.cloud.screen.service.meeting.entity.bo;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 会议表
 * 
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Data
public class MeetingBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long meetingId;

    /**
     * FK所属组织ID
     */
    @NotNull(message = "组织ID不能为空")
    @ApiModelProperty("FK所属组织ID")
    private Long organizationId;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 会议室地点id
     */
    @NotNull(message = "会议室地点id不能为空")
    @ApiModelProperty("会议室地点id")
    private Long spaceInfoId;

    /**
     * 申请人user_oid
     */
    @NotNull(message = "申请人user_oid不能为空")
    @ApiModelProperty("申请人user_oid")
    private String userOid;

    /**
     * 会议主题
     */
    @NotNull(message = "会议主题不能为空")
    @ApiModelProperty("会议主题")
    private String title;

    /**
     * 会议日期:yyyy-MM-dd,前端提交的格式为：2023-09-14 00:00:00
     */
    @NotNull(message = "会议日期不能为空")
    @ApiModelProperty("会议日期:yyyy-MM-dd ")
    private Date meetingDate;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty("开始时间")
    private Time meetingStartTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty("结束时间")
    private Time meetingEndTime;

    /**
     * 是否签到（1：是，2：否）
     */
    @NotNull(message = "是否签到不能为空")
    @ApiModelProperty("是否签到（1：是，2：否）")
    private Integer isSignIn;

    /**
     * 会议内容
     */
    @ApiModelProperty("会议内容")
    private String content;

    /**
     * 会议备注
     */
    @ApiModelProperty("会议备注")
    private String note;

    /**
     * 会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）
     */
    @ApiModelProperty("会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）")
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 与会人员
     */
    @ApiModelProperty("与会人员")
    List<MeetingUserBo> meetingUserBos;

    /**
     * 会议人员类型：1：教师，2：学生
     */
    @ApiModelProperty("会议人员类型：1：教师，2：学生")
    private Integer meetingUserType;

    /**
     * 正常签到时间
     */
    @ApiModelProperty("正常签到时间")
    private Time normalSignInTime;

    /**
     * 会议uuid
     */
    @ApiModelProperty("会议uuid")
    private String meetingUuid;

    /**
     * 会议日期列表，前端提交的格式为：2023-09-14 00:00:00
     */
    @ApiModelProperty("会议日期列表")
    private List<Date> meetingDateList;

    /**
     * 是否定时器执行，定时器执行的时候不校验和长期预约规则的冲突
     */
    private boolean triggerExecute = false;
}
