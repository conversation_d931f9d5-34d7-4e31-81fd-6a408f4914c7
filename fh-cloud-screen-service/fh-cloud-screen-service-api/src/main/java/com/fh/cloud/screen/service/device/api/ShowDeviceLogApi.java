package com.fh.cloud.screen.service.device.api;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设备日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
public interface ShowDeviceLogApi {

    /**
     * 查询设备日志表分页列表
     * 
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @PostMapping("/show/device/log/page/list")
    public AjaxResult<PageInfo<ShowDeviceLogVo>>
        getShowDeviceLogPageListByCondition(@RequestBody ShowDeviceLogConditionBo condition);

    /**
     * 查询设备日志表列表
     * 
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @PostMapping("/show/device/log/list")
    public AjaxResult<List<ShowDeviceLogVo>>
        getShowDeviceLogListByCondition(@RequestBody ShowDeviceLogConditionBo condition);

    /**
     * 新增设备日志表
     * 
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @PostMapping("/show/device/log/add")
    public AjaxResult addShowDeviceLog(@Validated @RequestBody ShowDeviceLogBo showDeviceLogBo);

    /**
     * 修改设备日志表
     * 
     * @param showDeviceLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @PostMapping("/show/device/log/update")
    public AjaxResult updateShowDeviceLog(@Validated @RequestBody ShowDeviceLogBo showDeviceLogBo);

    /**
     * 查询设备日志表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @GetMapping("/show/device/log/detail")
    public AjaxResult<ShowDeviceLogVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除设备日志表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-10 15:08:02
     */
    @GetMapping("/show/device/log/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 查询设备日志表详情-根据deviceNumber。如果数据库有多条则选取最后一条
     *
     * @param deviceNumber the device number
     * @return detail by number
     * <AUTHOR>
     * @date 2022 -12-20 15:05:05
     * @returnType AjaxResult
     */
    @GetMapping("show/device/log/detail-number")
    public AjaxResult<ShowDeviceLogVo> getDetailByNumber(@RequestParam("deviceNumber") String deviceNumber);

    /**
     * 云屏设备提交日志数据，会更新日志数据
     *
     * @param showDeviceLogBo the show device log bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -12-20 15:05:05
     */
    @PostMapping("show/device/log/upload")
    public AjaxResult uploadShowDeviceLog(@Validated @RequestBody ShowDeviceLogBo showDeviceLogBo);

    /**
     * web发起日志拉取请求，会新增一条日志数据（由云屏提交）
     *
     * @param showDeviceLogBo the show device log bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -12-21 10:51:11
     */
    @PostMapping("show/device/log/launch")
    public AjaxResult launchShowDeviceLog(@RequestBody ShowDeviceLogBo showDeviceLogBo);

}
