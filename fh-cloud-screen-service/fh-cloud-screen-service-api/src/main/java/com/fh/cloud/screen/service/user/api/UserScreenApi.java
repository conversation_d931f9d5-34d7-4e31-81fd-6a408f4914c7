package com.fh.cloud.screen.service.user.api;

import com.light.core.entity.AjaxResult;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.vo.TeacherVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/9 11:48 上午 @description：
 */
public interface UserScreenApi {

    /**
     * 根据 用户姓名 组织机构ID 获取老师列表
     *
     * @param realName
     * @param orgId
     * @return
     */
    @GetMapping("/user/teacher/getByRealName")
    AjaxResult getTeacherListByRealName(@RequestParam("realName") String realName, @RequestParam("orgId") Long orgId);

    /**
     * 根据 班级ID 获取学生列表
     *
     * @param classesId
     * @return
     */
    @GetMapping("/user/student/getByClassesId")
    AjaxResult<List<StudentVo>> getStudentListByClassesId(@RequestParam("classesId") Long classesId);

    /**
     * 查询学校教师列表
     * 
     * @param organizationId
     * @return
     */
    @GetMapping("/user/teacher/list")
    AjaxResult<List<TeacherVo>> getTeacherListByOrganizationId(@RequestParam("organizationId") Long organizationId);
}
