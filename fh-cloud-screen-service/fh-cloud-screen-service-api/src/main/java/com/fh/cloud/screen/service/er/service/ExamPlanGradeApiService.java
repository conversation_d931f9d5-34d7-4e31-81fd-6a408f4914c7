package com.fh.cloud.screen.service.er.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.er.api.ExamPlanGradeApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 考场_考试计划涉及的年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@FeignClient(contextId = "examPlanGradeApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ExamPlanGradeApiService.ExamPlanGradeApiFallbackFactory.class)
@Component
public interface ExamPlanGradeApiService extends ExamPlanGradeApi {

    @Component
    class ExamPlanGradeApiFallbackFactory implements FallbackFactory<ExamPlanGradeApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ExamPlanGradeApiFallbackFactory.class);
        @Override
        public ExamPlanGradeApiService create(Throwable cause) {
            ExamPlanGradeApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ExamPlanGradeApiService() {
                public AjaxResult getExamPlanGradePageListByCondition(ExamPlanGradeConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getExamPlanGradeListByCondition(ExamPlanGradeConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addExamPlanGrade(ExamPlanGradeBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateExamPlanGrade(ExamPlanGradeBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}