package com.fh.cloud.screen.service.calendar.service;

import com.fh.cloud.screen.service.calendar.api.SchoolCalendarWeekApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekSaveUpdateConditionBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "schoolCalendarWeekApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = SchoolCalendarWeekApiService.SchoolCalendarWeekApiFallbackFactory.class)
@Component
public interface SchoolCalendarWeekApiService extends SchoolCalendarWeekApi {
    @Component
    class SchoolCalendarWeekApiFallbackFactory implements FallbackFactory<SchoolCalendarWeekApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(SchoolCalendarWeekApiFallbackFactory.class);

        @Override
        public SchoolCalendarWeekApiService create(Throwable cause) {
            SchoolCalendarWeekApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new SchoolCalendarWeekApiService() {

                @Override
                public AjaxResult getSchoolCalendarWeekListByCondition(SchoolCalendarWeekListConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                @Override
                public AjaxResult saveOrUpdateSchoolCalendarWeek(SchoolCalendarWeekSaveUpdateConditionBo conditionBo) {
                    return AjaxResult.fail("新增或修改失败");
                }
            };
        }
    }
}
