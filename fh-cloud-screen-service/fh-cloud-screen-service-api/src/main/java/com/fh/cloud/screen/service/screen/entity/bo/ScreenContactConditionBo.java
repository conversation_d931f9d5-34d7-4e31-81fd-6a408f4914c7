package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 云屏产品咨询收集联系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
@Data
public class ScreenContactConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 学校名称
	 */
	@ApiModelProperty("学校名称")
	private String schoolName;

	/**
	 * 联系方式电话
	 */
	@ApiModelProperty("联系方式电话")
	private String contactPhone;

	/**
	 * 咨询意向
	 */
	@ApiModelProperty("咨询意向")
	private String intention;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 模糊查询关键字
	 */
	private String searchKey;
}
