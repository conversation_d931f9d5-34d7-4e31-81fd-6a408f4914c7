package com.fh.cloud.screen.service.label.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 标签表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
@Data
public class LabelConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long labelId;

    /**
     * 父级id
     */
    @ApiModelProperty("父级id")
    private Long parentLabelId;

    /**
     * 层级：1：标签组别，2：标签
     */
    @ApiModelProperty("层级：1：标签组别，2：标签")
    private Integer level;

    /**
     * 标签类别，1：海报标签
     */
    @ApiModelProperty("标签类别，1：海报标签")
    private Integer type;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签名称")
    private String labelName;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签排序")
    private Integer labelSort;

    /**
     * 自定义节点对应学校，默认0：通用所有学校
     */
    @ApiModelProperty("自定义节点对应学校，默认0：通用所有学校")
    private Long organizationId;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 海报标签类别：1默认海报标签，2节假日海报标签
     */
    @ApiModelProperty("海报标签类别：1默认海报标签，2节假日海报标签")
    private Integer posterType;

    /**
     * 是否查询标签关联的海报主题ids,1:是
     */
    private Integer queryLibraryId;

    /**
     * 班级id
     */
    @ApiModelProperty("班级id")
    private Long classesId;

}
