package com.fh.cloud.screen.service.crm.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * CRM商讯联系人表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@Data
public class CrmContactConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long crmContactId;

	/**
	 * CRM商讯表id
	 */
	@ApiModelProperty("CRM商讯表id")
	private Long crmInfoId;

	/**
	 * 联系人姓名
	 */
	@ApiModelProperty("联系人姓名")
	private String contactName;

	/**
	 * 联系方式
	 */
	@ApiModelProperty("联系方式")
	private String contactWay;

	/**
	 * 联系人职务类型：1校长，2副校长，3书记，4副书记，5教研室主任，6总务主任，7教导主任，8信息主任，9信息老师
	 */
	@ApiModelProperty("联系人职务类型：1校长，2副校长，3书记，4副书记，5教研室主任，6总务主任，7教导主任，8信息主任，9信息老师")
	private Integer contactDutiesType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * CRM商讯表ids
	 */
	private List<Long> crmInfoIds;

}
