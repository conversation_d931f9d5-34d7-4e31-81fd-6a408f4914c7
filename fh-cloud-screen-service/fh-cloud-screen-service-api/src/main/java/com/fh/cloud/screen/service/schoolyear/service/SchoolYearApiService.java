package com.fh.cloud.screen.service.schoolyear.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.schoolyear.api.SchoolYearApi;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.bo.OrganizationTermBo;
import com.light.user.organization.entity.bo.OrganizationTermDelSaveBo;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = SchoolYearApiService.SchoolYearApiFallbackFactory.class)
@Component
public interface SchoolYearApiService extends SchoolYearApi {
    @Component
    class SchoolYearApiFallbackFactory implements FallbackFactory<SchoolYearApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(SchoolYearApiFallbackFactory.class);

        @Override
        public SchoolYearApiService create(Throwable cause) {
            SchoolYearApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new SchoolYearApiService() {

                @Override
                public AjaxResult listSchoolYears() {
                    return AjaxResult.fail("listSchoolYears失败");
                }

                @Override
                public AjaxResult currentYear() {
                    return AjaxResult.fail("currentYear失败");
                }

                @Override
                public AjaxResult getYear(String grade, String schoolYear) {
                    return AjaxResult.fail("getYear失败");
                }

                @Override
                public AjaxResult getMonthDay() {
                    return AjaxResult.fail("getMonthDay失败");
                }

                @Override
                public AjaxResult saveTerm(OrganizationTermDelSaveBo organizationTermDelSaveBo) {
                    return AjaxResult.fail("saveTerm失败");
                }

                @Override
                public AjaxResult listTerm(OrganizationTermBo organizationTermBo) {
                    return AjaxResult.fail("listTerm失败");
                }

                @Override
                public AjaxResult getSchoolTeacherWeekByMonth(Long organizationId, String month) {
                    return AjaxResult.fail("get教学周失败");
                }
            };
        }
    }
}
