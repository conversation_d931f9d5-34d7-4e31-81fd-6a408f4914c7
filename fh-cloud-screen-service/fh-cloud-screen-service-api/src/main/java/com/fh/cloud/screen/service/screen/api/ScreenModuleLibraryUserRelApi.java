package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryUserRelVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 模块用户关系表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
public interface ScreenModuleLibraryUserRelApi {

    /**
     * 查询模块用户关系表分页列表
     * 
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @PostMapping("/screen/module/library/user/rel/page/list")
    public AjaxResult<PageInfo<ScreenModuleLibraryUserRelVo>>
        getScreenModuleLibraryUserRelPageListByCondition(@RequestBody ScreenModuleLibraryUserRelConditionBo condition);

    /**
     * 查询模块用户关系表列表
     * 
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @PostMapping("/screen/module/library/user/rel/list")
    public AjaxResult<List<ScreenModuleLibraryUserRelVo>>
        getScreenModuleLibraryUserRelListByCondition(@RequestBody ScreenModuleLibraryUserRelConditionBo condition);

    /**
     * 新增模块用户关系表
     * 
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @PostMapping("/screen/module/library/user/rel/add")
    public AjaxResult addScreenModuleLibraryUserRel(
        @Validated @RequestBody ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo);

    /**
     * 修改模块用户关系表
     * 
     * @param screenModuleLibraryUserRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @PostMapping("/screen/module/library/user/rel/update")
    public AjaxResult updateScreenModuleLibraryUserRel(
        @Validated @RequestBody ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo);

    /**
     * 查询模块用户关系表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @GetMapping("/screen/module/library/user/rel/detail")
    public AjaxResult<ScreenModuleLibraryUserRelVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除模块用户关系表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @GetMapping("/screen/module/library/user/rel/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
