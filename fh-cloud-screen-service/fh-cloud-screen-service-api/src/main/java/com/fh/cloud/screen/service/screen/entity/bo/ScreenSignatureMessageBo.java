package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 电子签名寄语资源表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
@Data
public class ScreenSignatureMessageBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenSignatureMessageId;

	/**
	 * 所属组织id
	 */
	@ApiModelProperty("所属组织id")
	private Long organizationId;

	/**
	 * 电子签名寄语内容
	 */
	@ApiModelProperty("电子签名寄语内容")
	private String screenSignatureMessageContent;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
