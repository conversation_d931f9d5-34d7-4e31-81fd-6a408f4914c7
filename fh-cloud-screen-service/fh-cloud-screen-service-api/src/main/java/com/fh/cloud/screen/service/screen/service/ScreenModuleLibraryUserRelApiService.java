package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryUserRelApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 模块用户关系表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
@FeignClient(contextId = "screenModuleLibraryUserRelApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenModuleLibraryUserRelApiService.ScreenModuleLibraryUserRelApiFallbackFactory.class)
@Component
public interface ScreenModuleLibraryUserRelApiService extends ScreenModuleLibraryUserRelApi {

    @Component
    class ScreenModuleLibraryUserRelApiFallbackFactory
        implements FallbackFactory<ScreenModuleLibraryUserRelApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenModuleLibraryUserRelApiFallbackFactory.class);

        @Override
        public ScreenModuleLibraryUserRelApiService create(Throwable cause) {
            ScreenModuleLibraryUserRelApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenModuleLibraryUserRelApiService() {
                public AjaxResult
                    getScreenModuleLibraryUserRelPageListByCondition(ScreenModuleLibraryUserRelConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult
                    getScreenModuleLibraryUserRelListByCondition(ScreenModuleLibraryUserRelConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenModuleLibraryUserRel(ScreenModuleLibraryUserRelBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenModuleLibraryUserRel(ScreenModuleLibraryUserRelBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}