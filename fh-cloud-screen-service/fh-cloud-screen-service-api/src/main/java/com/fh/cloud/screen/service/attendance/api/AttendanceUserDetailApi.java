package com.fh.cloud.screen.service.attendance.api;

import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

public interface AttendanceUserDetailApi {

    /**
     * 根据 考勤记录获取 考勤详情列表
     * 
     * @param attendanceUserId
     * @return
     */
    @PostMapping("/attendance-user-detail/listByAttendanceUserId/{attendanceUserId}")
    AjaxResult<List<AttendanceUserDetailVo>>
        getListByAttendanceUserId(@PathVariable("attendanceUserId") Long attendanceUserId);
}
