package com.fh.cloud.screen.service.rest.entity.bo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 作息时间年级表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestGradeBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    @NotNull(message = "FK作息时间主表主键id不能为空")
    private Long workRestId;

    /**
     * grade 的code值，年级一致的情况这个值为默认值
     */
    @NotBlank(message = "grade 的code值，年级一致的情况这个值为默认值不能为空")
    private String grade;

    /**
     * 一周作息时间是否一致：1一致，2不一致
     */
    @NotNull(message = "一周作息时间是否一致：1一致，2不一致不能为空")
    private Integer weekSameType;

    /**
     * 上午课节数：0，1，2，3...
     */
    @NotNull(message = "上午课节数：0，1，2，3...不能为空")
    private Integer courseNumAm;

    /**
     * 下午课节数：0，1，2，3...
     */
    @NotNull(message = "下午课节数：0，1，2，3...不能为空")
    private Integer courseNumPm;

    /**
     * 晚上课节数：0，1，2，3...
     */
    @NotNull(message = "晚上课节数：0，1，2，3...不能为空")
    private Integer courseNumNt;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 作息时间天列表(1,2, ----------- list)
     */
    private Map<Integer, List<WorkRestDayBo>> weekMap;

}
