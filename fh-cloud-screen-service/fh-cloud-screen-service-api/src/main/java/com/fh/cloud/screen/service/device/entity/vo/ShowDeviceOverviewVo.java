package com.fh.cloud.screen.service.device.entity.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 设备总览vo
 * 
 * <AUTHOR>
 * @date 2023/5/31 18:10
 */
@Data
public class ShowDeviceOverviewVo implements Serializable {

    /**
     * 设备总数
     */
    private Long showDeviceTotalNum = 0L;
    /**
     * 设备激活数量
     */
    private Long showDeviceActiveNum = 0L;
    /**
     * 设备未激活数量
     */
    private Long showDeviceNotActiveNum = 0L;
    /**
     * 入住单位数量
     */
    private Long useOrganizationNum = 0L;
    /**
     * 开机数量
     */
    private Long showDeviceOnNum = 0L;
    /**
     * 关机数量
     */
    private Long showDeviceOffNum = 0L;
    /**
     * 离线数量
     */
    private Long showDeviceErrorNum = 0L;
    /**
     * 客户端版本统计列表
     */
    private List<ShowDeviceVo> clientVersionList;
}
