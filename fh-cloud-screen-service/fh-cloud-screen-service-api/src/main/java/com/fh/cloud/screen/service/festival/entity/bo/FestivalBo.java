package com.fh.cloud.screen.service.festival.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 节日表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
@Data
public class FestivalBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long festivalId;

	/**
	 * 类型：1传统节日、2国际节日、 3二十四节气、4自定义节点、5一般节日
	 */
	@ApiModelProperty("类型：1传统节日、2国际节日、 3二十四节气、4自定义节点、5一般节日")
	private Integer type;

	/**
	 * 年份
	 */
	@ApiModelProperty("年份")
	private Integer year;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String name;

	/**
	 * 用于外部关联key
	 */
	@ApiModelProperty("用于外部关联key")
	private String festivalCode;

	/**
	 * 节日当天（type属于1、2、3）
	 */
	@ApiModelProperty("节日当天")
	private Date festivalDay;

	/**
	 * 开始天（海报启用）
	 */
	@ApiModelProperty("开始天")
	private Date startDay;

	/**
	 * 结束天（海报启用）
	 */
	@ApiModelProperty("结束天")
	private Date endDay;

	/**
	 * 预置类型：1预置，2自定义
	 */
	@ApiModelProperty("预置类型：1预置，2自定义")
	private Integer presetType;

	/**
	 * 自定义节点对应学校，默认0：通用所有学校
	 */
	@ApiModelProperty("自定义节点对应学校，默认0：通用所有学校")
	private Long organizationId;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
