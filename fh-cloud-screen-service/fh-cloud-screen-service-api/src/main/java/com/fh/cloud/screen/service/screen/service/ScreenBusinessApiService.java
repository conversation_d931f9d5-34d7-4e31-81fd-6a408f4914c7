package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenBusinessApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenBusinessApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenBusinessApiService.ScreenBusinessApiFallbackFactory.class)
@Component
public interface ScreenBusinessApiService extends ScreenBusinessApi {
    @Component
    class ScreenBusinessApiFallbackFactory implements FallbackFactory<ScreenBusinessApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenBusinessApiService.ScreenBusinessApiFallbackFactory.class);

        @Override
        public ScreenBusinessApiService create(Throwable cause) {
            ScreenBusinessApiService.ScreenBusinessApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ScreenBusinessApiService() {
                @Override
                public AjaxResult screenIndex(ScreenBusinessBo screenBusinessBo) {
                    return AjaxResult.fail("查询云屏首页数据失败");
                }

                @Override
                public AjaxResult orgInfo(ScreenBusinessBo screenBusinessBo) {
                    return AjaxResult.fail("查询学校介绍数据失败");
                }

                @Override
                public AjaxResult orgInfoWithCache(ScreenBusinessBo screenBusinessBo) {
                    return AjaxResult.fail("查询学校介绍数据缓存失败");
                }

                @Override
                public AjaxResult clazzInfo(ScreenBusinessBo screenBusinessBo) {
                    return AjaxResult.fail("查询班级介绍数据失败");
                }

                @Override
                public AjaxResult clazzInfoWithCache(ScreenBusinessBo screenBusinessBo) {
                    return AjaxResult.fail("查询班级介绍数据缓存失败");
                }
            };
        }
    }
}
