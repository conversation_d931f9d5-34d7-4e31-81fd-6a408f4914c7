package com.fh.cloud.screen.service.er.api;


import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 考场_考试计划里面一次考试的学生
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamInfoStudentApi {

    /**
     * 查询考场_考试计划里面一次考试的学生分页列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/student/page/list")
    public AjaxResult<PageInfo<ExamInfoStudentVo>> getExamInfoStudentPageListByCondition(@RequestBody ExamInfoStudentConditionBo condition);

    /**
     * 查询考场_考试计划里面一次考试的学生列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/student/list")
    public AjaxResult<List<ExamInfoStudentVo>> getExamInfoStudentListByCondition(@RequestBody ExamInfoStudentConditionBo condition);


    /**
     * 新增考场_考试计划里面一次考试的学生
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/student/add")
    public AjaxResult addExamInfoStudent(@Validated @RequestBody ExamInfoStudentBo examInfoStudentBo);

    /**
     * 修改考场_考试计划里面一次考试的学生
     * @param examInfoStudentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/student/update")
    public AjaxResult updateExamInfoStudent(@Validated @RequestBody ExamInfoStudentBo examInfoStudentBo);

    /**
     * 查询考场_考试计划里面一次考试的学生详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/info/student/detail")
    public AjaxResult<ExamInfoStudentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除考场_考试计划里面一次考试的学生
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/info/student/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
    * 考试签到
    *
    * @param examInfoStudentBo attendanceTime,subjectId
    * @return
    * <AUTHOR>
    * @date 2022/11/29 14:41
    */
    @PostMapping("/exam/info/student/sign")
    public AjaxResult examSign(@RequestBody ExamInfoStudentBo examInfoStudentBo);
}
