package com.fh.cloud.screen.service.rest.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间天设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestDayBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long workRestDayId;

    /**
     * FK作息时间年级表主键
     */
    @NotNull(message = "FK作息时间年级表主键不能为空")
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    @NotNull(message = "FK作息时间主表主键id不能为空")
    private Long workRestId;

    /**
     * 课程节次：1，2，3...
     */
    @NotNull(message = "课程节次：1，2，3...不能为空")
    private Integer courseClassesPosition;

    /**
     * 课程节次名称，例如第1节
     */
    @NotBlank(message = "课程节次名称，例如第1节不能为空")
    private String courseClassesName;

    /**
     * 课程节次类型：1普通课，2活动课
     */
    @NotNull(message = "课程节次类型：1普通课，2活动课不能为空")
    private Integer courseClassesType;

    /**
     * 课程节次顺序（含活动课）：1，2，3...
     */
    @NotNull(message = "课程节次顺序（含活动课）：1，2，3...不能为空")
    private Integer courseClassesIndex;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    @NotNull(message = "星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值不能为空")
    private Integer week;

    /**
     * 上午1、下午2、晚上3
     */
    @NotNull(message = "上午1、下午2、晚上3不能为空")
    private Integer dayType;

    /**
     * 起始时间
     */
    @NotNull(message = "起始时间不能为空")
    private Date startTime;

    /**
     * 截止时间
     */
    @NotNull(message = "截止时间不能为空")
    private Date endTime;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

}
