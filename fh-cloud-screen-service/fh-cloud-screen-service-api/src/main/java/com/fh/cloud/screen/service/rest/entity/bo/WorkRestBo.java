package com.fh.cloud.screen.service.rest.entity.bo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 作息时间主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long workRestId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @NotNull(message = "所属校区ID不能为空")
    private Long campusId;

    /**
     * 作息时间名称
     */
    @NotBlank(message = "作息时间名称不能为空")
    private String name;

    /**
     * 禁用状态，0禁用，1正常
     */
    @NotNull(message = "禁用状态，0禁用，1正常不能为空")
    private Integer status;

    /**
     * 年级作息时间是否一致：1一致，2不一致
     */
    @NotNull(message = "年级作息时间是否一致：1一致，2不一致不能为空")
    private Integer gradeSameType;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 年级不一致列表dto。如果年级一致，则只有一条数据
     */
    private List<WorkRestGradeBo> workRestGradeBoList;

}
