package com.fh.cloud.screen.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.GetMapping;

@Getter
@AllArgsConstructor
public enum LabelEnums {
    /**
     * 海报类型1：海报标签
     */
    POSTER(1),

    /**
     * 标签层级：1：标签组别，2：标签
     */
    LEVEL_ONW(1), LEVEL_TWO(2),

    /**
     * 海报标签类别：1默认海报标签，2节假日海报标签
     */
    POSTER_DEFAULT(1), POSTER_FESTIVAL(2);

    private Integer code;
}
