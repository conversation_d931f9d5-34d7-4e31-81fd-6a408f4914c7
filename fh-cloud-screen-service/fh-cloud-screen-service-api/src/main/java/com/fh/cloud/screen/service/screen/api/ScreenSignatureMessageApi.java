package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 电子签名寄语资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
public interface ScreenSignatureMessageApi {

    /**
     * 查询电子签名寄语资源表分页列表
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @PostMapping("/screen/signature/message/page/list")
    public AjaxResult<PageInfo<ScreenSignatureMessageVo>> getScreenSignatureMessagePageListByCondition(@RequestBody ScreenSignatureMessageConditionBo condition);

    /**
     * 查询电子签名寄语资源表列表
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @PostMapping("/screen/signature/message/list")
    public AjaxResult<List<ScreenSignatureMessageVo>> getScreenSignatureMessageListByCondition(@RequestBody ScreenSignatureMessageConditionBo condition);


    /**
     * 新增电子签名寄语资源表
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @PostMapping("/screen/signature/message/add")
    public AjaxResult addScreenSignatureMessage(@Validated @RequestBody ScreenSignatureMessageBo screenSignatureMessageBo);

    /**
     * 修改电子签名寄语资源表
     * @param screenSignatureMessageBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @PostMapping("/screen/signature/message/update")
    public AjaxResult updateScreenSignatureMessage(@Validated @RequestBody ScreenSignatureMessageBo screenSignatureMessageBo);

    /**
     * 查询电子签名寄语资源表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @GetMapping("/screen/signature/message/detail")
    public AjaxResult<ScreenSignatureMessageVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除电子签名寄语资源表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @GetMapping("/screen/signature/message/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
