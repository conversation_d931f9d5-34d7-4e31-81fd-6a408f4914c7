package com.fh.cloud.screen.service.role.service;

import com.fh.app.role.service.role.entity.bo.RoleAppRelConditionBo;
import com.fh.app.role.service.role.entity.bo.RoleDataAuthorityConditionBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.role.api.UserRolePermissionApi;
import com.fh.sso.service.index.entity.bo.IndexListConditionBo;
import com.light.core.entity.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = UserRolePermissionApiService.UserRolePermissionApiFallbackFactory.class)
@Component
public interface UserRolePermissionApiService extends UserRolePermissionApi {
    @Component
    class UserRolePermissionApiFallbackFactory implements FallbackFactory<UserRolePermissionApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(UserRolePermissionApiFallbackFactory.class);

        @Override
        public UserRolePermissionApiService create(Throwable cause) {
            UserRolePermissionApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new UserRolePermissionApiService() {

                @Override
                public AjaxResult getUserPermissionAuthority(RoleAppRelConditionBo condition) {
                    return AjaxResult.fail("获取用户功能权限失败");
                }

                @Override
                public AjaxResult getUserMaxDataAuthority(RoleDataAuthorityConditionBo conditionBo) {
                    return AjaxResult.fail("获取用户角色的最大数据权限失败");
                }

                @Override
                public AjaxResult getUserRoleCodes() {
                    return AjaxResult.fail("获取用户角色标识失败");
                }

                @Override
                public AjaxResult getUserAppInfo(IndexListConditionBo conditionBo) {
                    return AjaxResult.fail("获取用户应用信息失败");
                }
            };
        }
    }
}
