package com.fh.cloud.screen.service.label.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.label.api.LabelFestivalRelApi;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 标签节日关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@FeignClient(contextId = "labelFestivalRelApiService",  value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
        configuration = FeignClientInterceptor.class, fallbackFactory = LabelFestivalRelApiService.LabelFestivalRelApiFallbackFactory.class)
@Component
public interface LabelFestivalRelApiService extends LabelFestivalRelApi {

    @Component
    class LabelFestivalRelApiFallbackFactory implements FallbackFactory<LabelFestivalRelApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LabelFestivalRelApiFallbackFactory.class);
        @Override
        public LabelFestivalRelApiService create(Throwable cause) {
            LabelFestivalRelApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LabelFestivalRelApiService() {
                public AjaxResult getLabelFestivalRelPageListByCondition(LabelFestivalRelConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLabelFestivalRelListByCondition(LabelFestivalRelConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLabelFestivalRel(LabelFestivalRelBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLabelFestivalRel(LabelFestivalRelBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}