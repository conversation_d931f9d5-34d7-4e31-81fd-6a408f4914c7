package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 云屏业务处理封装api
 */
public interface ScreenBusinessApi {

    /**
     * 查询云屏内容表列表
     *
     * @param screenBusinessBo the screen business bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     */
    @PostMapping("/screen/business/index")
    @ApiOperation(value = "查询云屏内容表列表", httpMethod = "POST")
    AjaxResult screenIndex(@RequestBody ScreenBusinessBo screenBusinessBo);

    /**
     * 查询学校介绍信息
     *
     * @param screenBusinessBo the screen business bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     */
    @PostMapping("/screen/business/org-info")
    @ApiOperation(value = "查询学校介绍信息", httpMethod = "POST")
    AjaxResult orgInfo(@RequestBody ScreenBusinessBo screenBusinessBo);

    /**
     * 查询学校介绍信息-带缓存实现（invalidedFromDB的模式）
     *
     * @param screenBusinessBo the screen business bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     */
    @PostMapping("/screen/business/org-info-cache")
    @ApiOperation(value = "查询学校介绍信息", httpMethod = "POST")
    AjaxResult orgInfoWithCache(@RequestBody ScreenBusinessBo screenBusinessBo);

    /**
     * 查询班级介绍信息
     *
     * @param screenBusinessBo the screen business bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     */
    @PostMapping("/screen/business/clazz-info")
    @ApiOperation(value = "查询班级介绍信息", httpMethod = "POST")
    AjaxResult clazzInfo(@RequestBody ScreenBusinessBo screenBusinessBo);

    /**
     * 查询班级介绍信息-带缓存实现（invalidedFromDB的模式）
     *
     * @param screenBusinessBo the screen business bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     */
    @PostMapping("/screen/business/clazz-info-cache")
    @ApiOperation(value = "查询班级介绍信息带缓存", httpMethod = "POST")
    AjaxResult clazzInfoWithCache(@RequestBody ScreenBusinessBo screenBusinessBo);

}
