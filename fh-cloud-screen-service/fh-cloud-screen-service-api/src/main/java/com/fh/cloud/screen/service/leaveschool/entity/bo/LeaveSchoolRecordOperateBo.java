package com.fh.cloud.screen.service.leaveschool.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 放学记录操作表id
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-09-13 10:26:18
 */
@Data
public class LeaveSchoolRecordOperateBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学记录操作表id
	 */
	@ApiModelProperty("放学记录操作表id")
	private Long leaveSchoolRecordOperateId;

	/**
	 * 放学记录表id
	 */
	@ApiModelProperty("放学记录表id")
	private Long leaveSchoolRecordId;

	/**
	 * 放学状态 1-未放学 2-放学中 3-已放学
	 */
	@ApiModelProperty("放学状态 1-未放学 2-放学中 3-已放学")
	private Integer leaveSchoolType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
