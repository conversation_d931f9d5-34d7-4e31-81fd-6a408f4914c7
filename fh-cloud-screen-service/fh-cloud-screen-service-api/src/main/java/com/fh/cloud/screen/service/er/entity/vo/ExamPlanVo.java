package com.fh.cloud.screen.service.er.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考场_考试计划
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamPlanVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examPlanId;

    /**
     * 考场计划名称
     */
    @ApiModelProperty("考场计划名称")
    private String examPlanName;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 考试计划开始日期
     */
    @ApiModelProperty("考试计划开始日期")
    private Date examPlanStartTime;

    /**
     * 考试计划结束日期
     */
    @ApiModelProperty("考试计划结束日期")
    private Date examPlanEndTime;

    /**
     * 考场计划说明
     */
    @ApiModelProperty("考场计划说明")
    private String examPlanRemark;

    /**
     * 考场计划类型：1未发布，2已发布
     */
    @ApiModelProperty("考场计划类型：1未发布，2已发布")
    private Integer examPlanType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 是否签到，0：否，1：是
     */
    @ApiModelProperty("是否签到，0：否，1：是")
    private Integer isSignIn;

    /**
     * 学年，格式为：xxxx-yyyy
     */
    private String schoolYear;
    /**
     * 学期名称
     */
    private String termName;

    /**
     * 考试年级列表
     */
    private List<String> grades;
}
