package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenPoetryLikesApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 共话诗词点赞记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
@FeignClient(contextId = "screenPoetryLikesApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenPoetryLikesApiService.ScreenPoetryLikesApiFallbackFactory.class)
@Component
public interface ScreenPoetryLikesApiService extends ScreenPoetryLikesApi {

    @Component
    class ScreenPoetryLikesApiFallbackFactory implements FallbackFactory<ScreenPoetryLikesApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenPoetryLikesApiFallbackFactory.class);
        @Override
        public ScreenPoetryLikesApiService create(Throwable cause) {
            ScreenPoetryLikesApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenPoetryLikesApiService() {
                public AjaxResult getScreenPoetryLikesPageListByCondition(ScreenPoetryLikesConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenPoetryLikesListByCondition(ScreenPoetryLikesConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenPoetryLikes(ScreenPoetryLikesBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenPoetryLikes(ScreenPoetryLikesBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult addScreenPoetryLikesNum(Long screenPoetryContentId) {
                    return AjaxResult.fail("点赞失败");
                }

            };
        }
    }
}