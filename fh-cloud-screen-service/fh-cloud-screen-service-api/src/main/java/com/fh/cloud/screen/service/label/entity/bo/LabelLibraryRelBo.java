package com.fh.cloud.screen.service.label.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.awt.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 标签海报关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@Data
public class LabelLibraryRelBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 海报模块表主键
     */
    @ApiModelProperty("海报模块表主键")
    private Long screenModuleLibraryId;

    /**
     * 标签表主键
     */
    @ApiModelProperty("标签表主键")
    private Long labelId;

    /**
     * 添加时 需要添加的标签列表
     */
    List<LabelBo> labelBos;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 主题id集合,逗号分隔
     */
    private String screenModuleLibraryIds;

    /**
     * 节日code 节日与标签一对一，且并存
     */
    private String festivalCode;
}
