package com.fh.cloud.screen.service.device.service;

import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.api.ShowDeviceLogApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 设备日志表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
@FeignClient(contextId = "showDeviceLogApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ShowDeviceLogApiService.ShowDeviceLogApiFallbackFactory.class)
@Component
public interface ShowDeviceLogApiService extends ShowDeviceLogApi {

    @Component
    class ShowDeviceLogApiFallbackFactory implements FallbackFactory<ShowDeviceLogApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ShowDeviceLogApiFallbackFactory.class);

        @Override
        public ShowDeviceLogApiService create(Throwable cause) {
            ShowDeviceLogApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ShowDeviceLogApiService() {
                public AjaxResult getShowDeviceLogPageListByCondition(ShowDeviceLogConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getShowDeviceLogListByCondition(ShowDeviceLogConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addShowDeviceLog(ShowDeviceLogBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateShowDeviceLog(ShowDeviceLogBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult<ShowDeviceLogVo> getDetailByNumber(String deviceNumber) {
                    return AjaxResult.fail("getDetailByNumber失败");
                }

                @Override
                public AjaxResult uploadShowDeviceLog(ShowDeviceLogBo showDeviceLogBo) {
                    return AjaxResult.fail("uploadShowDeviceLog失败");
                }

                @Override
                public AjaxResult launchShowDeviceLog(ShowDeviceLogBo showDeviceLogBo) {
                    return AjaxResult.fail("launchShowDeviceLog失败");
                }
            };
        }
    }
}