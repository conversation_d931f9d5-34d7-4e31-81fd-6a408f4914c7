package com.fh.cloud.screen.service.meeting.api;


import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongUserVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 长期预约表人员表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
public interface MeetingLongUserApi {

    /**
     * 查询长期预约表人员表分页列表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/user/page/list")
    public AjaxResult<PageInfo<MeetingLongUserVo>> getMeetingLongUserPageListByCondition(@RequestBody MeetingLongUserConditionBo condition);

    /**
     * 查询长期预约表人员表列表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/user/list")
    public AjaxResult<List<MeetingLongUserVo>> getMeetingLongUserListByCondition(@RequestBody MeetingLongUserConditionBo condition);


    /**
     * 新增长期预约表人员表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/user/add")
    public AjaxResult addMeetingLongUser(@Validated @RequestBody MeetingLongUserBo meetingLongUserBo);

    /**
     * 修改长期预约表人员表
     * @param meetingLongUserBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @PostMapping("/meeting/long/user/update")
    public AjaxResult updateMeetingLongUser(@Validated @RequestBody MeetingLongUserBo meetingLongUserBo);

    /**
     * 查询长期预约表人员表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @GetMapping("/meeting/long/user/detail")
    public AjaxResult<MeetingLongUserVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除长期预约表人员表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @GetMapping("/meeting/long/user/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
