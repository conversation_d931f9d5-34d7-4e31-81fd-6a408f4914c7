package com.fh.cloud.screen.service.rest.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 作息时间主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long workRestId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 所属校区ID
     */
    private Long campusId;

    /**
     * 作息时间名称
     */
    private String name;

    /**
     * 禁用状态，0禁用，1正常
     */
    private Integer status;

    /**
     * 年级作息时间是否一致：1一致，2不一致
     */
    private Integer gradeSameType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
    * 作息年级列表
    */
    private List<WorkRestGradeVo> workRestGradeVoList;

}
