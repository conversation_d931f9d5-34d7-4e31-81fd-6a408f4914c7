package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ScreenContentApi {

    /**
     * 查询云屏内容表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/content/list")
    @ApiOperation(value = "查询云屏内容表列表", httpMethod = "POST")
    AjaxResult getScreenContentListByCondition(@RequestBody ScreenContentListConditionBo condition);

    /**
     * 新增云屏内容表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/content/add")
    @ApiOperation(value = "新增云屏内容表", httpMethod = "POST")
    AjaxResult addScreenContent(@RequestBody ScreenContentBo screenContentBo);

    /**
     * 修改云屏内容表
     *
     * @param screenContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/content/update")
    @ApiOperation(value = "修改云屏内容表", httpMethod = "POST")
    AjaxResult updateScreenContent(@RequestBody ScreenContentBo screenContentBo);

    /**
     * 查询云屏内容表详情
     *
     * @param screenContentId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/screen/content/detail")
    @ApiOperation(value = "查询云屏内容表详情", httpMethod = "GET")
    AjaxResult getDetail(@RequestParam("screenContentId") Long screenContentId);

    /**
     * 删除云屏内容表
     *
     * @param screenContentId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/screen/content/delete")
    @ApiOperation(value = "删除云屏内容表", httpMethod = "GET")
    AjaxResult delete(@RequestParam("screenContentId") Long screenContentId);

    /**
     * 保存云屏内容表
     *
     * @param screenContentBo the screen content bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:09
     * @returnType AjaxResult
     */
    @PostMapping("/screen/content/save")
    @ApiOperation(value = "保存云屏内容表", httpMethod = "POST")
    AjaxResult saveScreenContent(@RequestBody ScreenContentBo screenContentBo);

    /**
     * 发布-撤回
     */
    @PostMapping("/screen/content/cancel")
    @ApiOperation(value = "发布撤回", httpMethod = "POST")
    AjaxResult cancelSubmit(@RequestParam("screenContentId") Long screenContentId);

    /**
     * 菜单(通知公告，新闻资讯，风采展示)
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/22 17:29
     */
    @PostMapping("/screen/content/menu")
    @ApiOperation(value = "首页查询菜单", httpMethod = "POST")
    AjaxResult getMenuByCondition(@RequestBody ScreenContentListConditionBo condition);

    /**
     * 菜单(通知公告，新闻资讯，风采展示)-带缓存的数据
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/22 17:29
     */
    @PostMapping("/screen/content/menu-cache")
    @ApiOperation(value = "首页查询菜单", httpMethod = "POST")
    AjaxResult getMenuByConditionWithCache(@RequestBody ScreenContentListConditionBo condition);
}
