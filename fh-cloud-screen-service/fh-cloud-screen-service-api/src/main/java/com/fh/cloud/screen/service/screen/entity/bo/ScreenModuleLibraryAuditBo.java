package com.fh.cloud.screen.service.screen.entity.bo;

import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 模块库审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
@Data
public class ScreenModuleLibraryAuditBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenModuleLibraryAuditId;

	/**
	 * 模块名称
	 */
	@ApiModelProperty("模块名称")
	private String moduleName;

	/**
	 * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
	 */
	@ApiModelProperty("模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容")
	private Long moduleGroupType;

	/**
	 * 预置类型：1预置，2不预置
	 */
	@ApiModelProperty("预置类型：1预置，2不预置")
	private Integer presetType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 父模块库id
	 */
	@ApiModelProperty("父模块库id")
	private Long parentScreenModuleLibraryId;

	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Long librarySort;

	/**
	 * 是否海报模块：1是，2否
	 */
	@ApiModelProperty("是否海报模块：1是，2否")
	private Long isPoster;

	/**
	 * 审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交
	 */
	@ApiModelProperty("审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交")
	private Integer auditType;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty("驳回原因")
	private String reason;

	/**
	 * 审核人
	 */
	@ApiModelProperty("审核人")
	private String auditUser;

	/**
	 * 审核时间
	 */
	@ApiModelProperty("审核时间")
	private Date auditTime;

	/**
	 * 发布状态 1-未发布 2-已发布
	 */
	@ApiModelProperty("发布状态 1-未发布 2-已发布")
	private Integer releaseType;

	/**
	 * 图片版式：1横屏，2竖屏
	 */
	@ApiModelProperty("图片版式：1横屏，2竖屏")
	private Integer devicePattern;

	/**
	 * 模块库表id
	 */
	@ApiModelProperty("模块库表id")
	private Long screenModuleLibraryId;

	/**
	 * 资源列表
	 */
	@ApiModelProperty("资源列表")
	private List<ScreenModuleLibraryMediaAuditBo> mediaAuditList;


	/**
	 * 主题关联标签列表
	 */
	private List<LabelBo> labelBos;

	/**
	 * ids
	 */
	@ApiModelProperty("ids")
	private List<Long> screenModuleLibraryAuditIds;

	/**
	 * 更新时间
	 */
	@NotNull(message = "更新时间不能为空")
	private Date createTime;

	/**
	 * 创建人
	 */
	@NotBlank(message = "创建人不能为空")
	private String createBy;

	/**
	 * 创建时间
	 */
	@NotNull(message = "创建时间不能为空")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@NotBlank(message = "更新人不能为空")
	private String updateBy;

	/**
	 * 机构id
	 */
	private Long organizationId;
}
