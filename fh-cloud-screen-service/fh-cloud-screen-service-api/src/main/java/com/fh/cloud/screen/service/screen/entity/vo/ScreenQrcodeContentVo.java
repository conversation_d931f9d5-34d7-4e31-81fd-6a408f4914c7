package com.fh.cloud.screen.service.screen.entity.vo;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import lombok.Data;

import java.io.Serializable;

/**
 * qr内容对象
 * 
 * <AUTHOR>
 * @date 2022/12/15 10:39
 */
@Data
public class ScreenQrcodeContentVo implements Serializable {
    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 空间或者classesId
     */
    private Long spaceInfoId;

    /**
     * 区域分组使用类型：行政或者非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 设备id
     */
    private Long showDeviceId;
    /**
     * 横竖屏
     */
    private Integer devicePattern;

    /**
     * 扫描结果状态：扫描业务状态，1成功，2失败-无权限
     */
    private Integer scanResultType;

    /**
     * 将对象转换为本对象
     * 
     * @return
     */
    public static ScreenQrcodeContentVo trans2ScreenQrcodeContentVo(ScreenBusinessBo screenBusinessBo) {
        ScreenQrcodeContentVo screenQrcodeContentVo = new ScreenQrcodeContentVo();
        screenQrcodeContentVo.setCampusId(screenBusinessBo.getCampusId());
        screenQrcodeContentVo.setDeviceNumber(screenBusinessBo.getDeviceNumber());
        screenQrcodeContentVo.setOrganizationId(screenBusinessBo.getOrganizationId());
        screenQrcodeContentVo.setSpaceInfoId(screenBusinessBo.getSpaceInfoId());
        screenQrcodeContentVo.setSpaceGroupUseType(screenBusinessBo.getSpaceGroupUseType());
        return screenQrcodeContentVo;
    }
}
