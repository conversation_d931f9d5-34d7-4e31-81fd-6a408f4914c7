package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 共话诗词点赞记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
public interface ScreenPoetryLikesApi {

    /**
     * 查询共话诗词点赞记录表分页列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @PostMapping("/screen/poetry/likes/page/list")
    public AjaxResult<PageInfo<ScreenPoetryLikesVo>> getScreenPoetryLikesPageListByCondition(@RequestBody ScreenPoetryLikesConditionBo condition);

    /**
     * 查询共话诗词点赞记录表列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @PostMapping("/screen/poetry/likes/list")
    public AjaxResult<List<ScreenPoetryLikesVo>> getScreenPoetryLikesListByCondition(@RequestBody ScreenPoetryLikesConditionBo condition);


    /**
     * 新增共话诗词点赞记录表
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @PostMapping("/screen/poetry/likes/add")
    public AjaxResult addScreenPoetryLikes(@Validated @RequestBody ScreenPoetryLikesBo screenPoetryLikesBo);

    /**
     * 修改共话诗词点赞记录表
     * @param screenPoetryLikesBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @PostMapping("/screen/poetry/likes/update")
    public AjaxResult updateScreenPoetryLikes(@Validated @RequestBody ScreenPoetryLikesBo screenPoetryLikesBo);

    /**
     * 查询共话诗词点赞记录表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @GetMapping("/screen/poetry/likes/detail")
    public AjaxResult<ScreenPoetryLikesVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除共话诗词点赞记录表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:38
     */
    @GetMapping("/screen/poetry/likes/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 点赞
     *
     * @param screenPoetryContentId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/6/27 9:26
     **/
    @GetMapping("/screen/poetry/likes/addLikesNum")
    public AjaxResult addScreenPoetryLikesNum(@RequestParam("screenPoetryContentId") Long screenPoetryContentId);

}
