package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryMediaAuditApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 云屏模块库媒体资源审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
@FeignClient(contextId = "screenModuleLibraryMediaAuditApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenModuleLibraryMediaAuditApiService.ScreenModuleLibraryMediaAuditApiFallbackFactory.class)
@Component
public interface ScreenModuleLibraryMediaAuditApiService extends ScreenModuleLibraryMediaAuditApi {

    @Component
    class ScreenModuleLibraryMediaAuditApiFallbackFactory implements FallbackFactory<ScreenModuleLibraryMediaAuditApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenModuleLibraryMediaAuditApiFallbackFactory.class);
        @Override
        public ScreenModuleLibraryMediaAuditApiService create(Throwable cause) {
            ScreenModuleLibraryMediaAuditApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenModuleLibraryMediaAuditApiService() {
                public AjaxResult getScreenModuleLibraryMediaAuditPageListByCondition(ScreenModuleLibraryMediaAuditConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenModuleLibraryMediaAuditListByCondition(ScreenModuleLibraryMediaAuditConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenModuleLibraryMediaAudit(ScreenModuleLibraryMediaAuditBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenModuleLibraryMediaAudit(ScreenModuleLibraryMediaAuditBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}