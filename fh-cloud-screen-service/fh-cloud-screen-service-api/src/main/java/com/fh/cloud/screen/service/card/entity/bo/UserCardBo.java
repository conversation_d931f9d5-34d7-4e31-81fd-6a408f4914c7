package com.fh.cloud.screen.service.card.entity.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户卡表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class UserCardBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long userCardId;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡类型：1学生卡，2教师卡
     */
    private Integer cardType;

    /**
     * 卡设备型号：1默认卡
     */
    private Integer cardDeviceModel;

    /**
     * 卡所属人user_oid
     */
    private String userOid;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 校区id
     */
    private Long campusId;

    /**
     * 学段
     */
    private String section;

    /**
     * 年级
     */
    private String grade;

    /**
     * 班级id
     */
    private Long classesId;

    private List<Long> classesIds;

    /**
     * 组织机构id
     */
    private Long organizationId;

    private List<StudentCardImportBo> studentCardImportList;
}
