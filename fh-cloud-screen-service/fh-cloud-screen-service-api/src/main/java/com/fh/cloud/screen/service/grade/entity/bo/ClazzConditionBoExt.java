package com.fh.cloud.screen.service.grade.entity.bo;

import com.light.user.clazz.entity.bo.ClazzConditionBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/12 11:38
 */
@Data
public class ClazzConditionBoExt extends ClazzConditionBo implements Serializable {

    @ApiModelProperty("学年")
    private String schoolYear;

    /**
     * 检索类型 1：app
     */
    private Integer queryType;

    /**
     * 应用id,过滤出属于该应用的角色的最大数据权限
     */
    private Long appId;

    /**
     * 检索班级时，检索任教范围 null or 1 检索任教班级和任教科目，2：只检索任教(班主任的)班级，3 任教科目班级
     */
    private Integer teachingScope;
}
