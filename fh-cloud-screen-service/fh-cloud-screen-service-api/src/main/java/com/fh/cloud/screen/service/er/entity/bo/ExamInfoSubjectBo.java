package com.fh.cloud.screen.service.er.entity.bo;

import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 考场_考试计划里面一次考试科目信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@Data
public class ExamInfoSubjectBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoSubjectId;

    /**
     * 考试id
     */
    @ApiModelProperty("考试id")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 学科code
     */
    @ApiModelProperty("学科code")
    private String subjectCode;

    /**
     * 学科名称
     */
    @ApiModelProperty("学科名称")
    private String subjectName;

    /**
     * 准考证开始
     */
    @ApiModelProperty("准考证开始")
    private String atNoStart;

    /**
     * 准考证结束
     */
    @ApiModelProperty("准考证结束")
    private String atNoEnd;

    /**
     * 该场考试开始时间
     */
    @ApiModelProperty("该场考试开始时间")
    private Date examStartTime;

    /**
     * 该场考试结束时间
     */
    @ApiModelProperty("该场考试结束时间")
    private Date examEndTime;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 本场考试学生信息-新增编辑使用
     */
    private List<ExamInfoStudentBo> examInfoStudentBos;

    /**
     * 本场考试监考老师信息-新增编辑使用
     */
    private List<ExamInfoTeacherBo> examInfoTeacherBos;

    /**
     * 设置唯一oid用于后续判断时间冲突，排除自身
     */
    private String uuid;

    /**
     * 科目的typeId：1全，2局，3校
     */
    private Long typeId;
}
