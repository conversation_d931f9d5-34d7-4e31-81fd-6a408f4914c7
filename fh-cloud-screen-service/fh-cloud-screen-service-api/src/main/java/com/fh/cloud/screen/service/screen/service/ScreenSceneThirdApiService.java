package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenSceneThirdApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 第三方对接云屏场景信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
@FeignClient(contextId = "screenSceneThirdApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenSceneThirdApiService.ScreenSceneThirdApiFallbackFactory.class)
@Component
public interface ScreenSceneThirdApiService extends ScreenSceneThirdApi {

    @Component
    class ScreenSceneThirdApiFallbackFactory implements FallbackFactory<ScreenSceneThirdApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenSceneThirdApiFallbackFactory.class);

        @Override
        public ScreenSceneThirdApiService create(Throwable cause) {
            ScreenSceneThirdApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ScreenSceneThirdApiService() {
                public AjaxResult getScreenSceneThirdPageListByCondition(ScreenSceneThirdConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenSceneThirdListByCondition(ScreenSceneThirdConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenSceneThird(ScreenSceneThirdBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenSceneThird(ScreenSceneThirdBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult deleteByAppCode(Long screenSceneThirdId, String appCode) {
                    return AjaxResult.fail("根据appCode删除失败");
                }
            };
        }
    }
}