package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 标签海报关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
@Data
public class LabelLibraryAuditRelBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 海报模块审核表主键
	 */
	@ApiModelProperty("海报模块审核表主键")
	private Long screenModuleLibraryAuditId;

	/**
	 * 标签表主键
	 */
	@ApiModelProperty("标签表主键")
	private Long labelId;

	/**
	 * 更新时间
	 */
	@NotNull(message = "更新时间不能为空")
	private Date createTime;

	/**
	 * 创建人
	 */
	@NotBlank(message = "创建人不能为空")
	private String createBy;

	/**
	 * 创建时间
	 */
	@NotNull(message = "创建时间不能为空")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@NotBlank(message = "更新人不能为空")
	private String updateBy;


	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
