package com.fh.cloud.screen.service.leaveschool.entity.vo;

import com.light.user.clazz.entity.vo.ClazzVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-08-23 20:14
 */
@Data
public class LeaveSchoolClazzVo extends ClazzVo {

    /**
     * 放学状态 1-未放学 2-放学中 3-已放学
     */
    @ApiModelProperty("放学状态 1-未放学 2-放学中 3-已放学")
    private Integer leaveSchoolType;

    /**
     * 年级label
     */
    private String gradeName;

    /**
     * 用于展示的班级名称，例如：一年级2班
     */
    private String classesNameShow;

    /**
     * 播报文件oid
     */
    @ApiModelProperty("播报文件oid")
    private String broadcastId;

    /**
     * 播报文件url
     */
    @ApiModelProperty("播报文件url")
    private String broadcastUrl;

    /**
     * 播放次数
     */
    private Integer playTimes;
}
