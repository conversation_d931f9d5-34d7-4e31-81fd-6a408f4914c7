package com.fh.cloud.screen.service.syllabus.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 课表信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:16:07
 */
@Data
public class SyllabusInfoBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键自动增长
	 */
	@ApiModelProperty("主键自动增长")
	private Long syllabusId;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private Long organizationId;

	/**
	 * 星期几:1-7,注意周一是1
	 */
	@ApiModelProperty("星期几:1-7,注意周一是1")
	private Long weekId;

	/**
	 * 排序0,1,2，3,4,5,6,7,8...... 特别说明0可能指早读，注意顺序需要与作息时间对应上
	 */
	@ApiModelProperty("排序0,1,2，3,4,5,6,7,8...... 特别说明0可能指早读，注意顺序需要与作息时间对应上")
	private Long sort;

	/**
	 * 1表示启用 0表示禁用
	 */
	@ApiModelProperty("1表示启用 0表示禁用")
	private Integer status;

	/**
	 * 课表来源：1同步，2导入，3排课插入
	 */
	@ApiModelProperty("课表来源：1同步，2导入，3排课插入")
	private Integer source;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;

	/**
	 * 班级名称
	 */
	@ApiModelProperty("班级名称")
	private String classesName;

	/**
	 * 科目的code
	 */
	@ApiModelProperty("科目的code")
	private String subjectCode;

	/**
	 * 科目的名称
	 */
	@ApiModelProperty("科目的名称")
	private String subjectName;

	/**
	 * 授课老师
	 */
	@ApiModelProperty("授课老师")
	private String teacherName;

	/**
	 * 双周科目的code
	 */
	@ApiModelProperty("双周科目的code")
	private String doubleSubjectCode;

	/**
	 * 双周科目的名称
	 */
	@ApiModelProperty("双周科目的名称")
	private String doubleSubjectName;

	/**
	 * 授课老师
	 */
	@ApiModelProperty("授课老师")
	private String doubleTeacherName;

	/**
	 * 更新时间
	 */
	@NotNull(message = "更新时间不能为空")
	private Date createTime;

	/**
	 * 创建人
	 */
	@NotBlank(message = "创建人不能为空")
	private String createBy;

	/**
	 * 创建时间
	 */
	@NotNull(message = "创建时间不能为空")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@NotBlank(message = "更新人不能为空")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@NotNull(message = "是否删除，0：否，1：是不能为空")
	private Integer isDelete;
}
