package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤用户详情表，需要日终计算
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceUserDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceUserDetailId;

    /**
     * FK考勤用户表id
     */
    private Long attendanceUserId;

    /**
     * 考勤一组顺序：1，2，3...
     */
    private Integer attendanceRuleDayIndex;

    /**
     * 考签到考勤设备号，例如云屏设备的设备号
     */
    private String signInDeviceNumber;

    /**
     * 签退考勤设备号，例如云屏设备的设备号
     */
    private String signOutDeviceNumber;

    /**
     * 签到地点
     */
    private String signInAddress;

    /**
     * 签退地点
     */
    private String signOutAddress;

    /**
     * 签到时间
     */
    private Date signInTime;

    /**
     * 签退时间
     */
    private Date signOutTime;

    /**
     * 一组考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，6缺卡
     */
    private Integer signInRecordType;

    /**
     * 一组考勤记录状态：1正常，2异常（保留状态），3迟到，4早退,6缺卡
     */
    private Integer signOutRecordType;

    /**
     * 匹配到的规则：1只签到，2只签退
     */
    private Integer onlySign;

    /**
     * 签到考勤方式：1实体卡，2人脸识别
     */
    private Integer signInAttendanceMethod;

    /**
     * 签退考勤方式：1实体卡，2人脸识别
     */
    private Integer signOutAttendanceMethod;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
