package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模块库表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenModuleLibraryListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long screenModuleLibraryId;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    private Long moduleGroupType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 排序
     */
    private Integer librarySort;

    /**
     * 预置类型：1预置，2不预置
     */
    private Integer presetType;

    /**
     * 父模块库id
     */
    private Long parentScreenModuleLibraryId;

    /**
     * 是否海报：1是，2否
     */
    private Integer isPoster;

    // /**
    // * 海报模块版式：1横屏，2竖屏
    // */
    // private Integer libraryPattern;
    //
    // /**
    // * 海报模块类型：1系统上传，2用户上传
    // */
    // private Integer posterSource;

    /**
     * 获取海报列表检索，dictionary_data organizationId 标识主题和校本海报
     */
    private Long dictionaryDataOrganizationId;

    /**
     * 获取海报列表检索，dictionary_data classesId 标识是否是班级海报
     */
    private Long dictionaryDataClassesId;

    /**
     * labelIds
     */
    private List<Long> labelIds;

    /**
     * 模块id列表，不要通过该字段传值，传值请通过screenModuleLibrarySelIds传递。
     */
    private List<Long> screenModuleLibraryIds;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 海报图片 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    private Integer devicePattern;

    /**
     * 海报主题id的拼接
     */
    private String screenModuleLibrarySelIds;

    /**
     * 是否查询收藏的海报并设置标识
     */
    private boolean queryCollect = false;

    /**
     * 设备号
     */
    private String deviceNumber;

    /**
     * 查询是否配置标签查询 1:未配置标签列表，2配置标签列表
     */
    private Integer queryLabelConfig;

    /**
     * 海报查询不包含的主题ids
     */
    private List<Long> notInScreenModuleLibraryIds;

    /**
     * 是否同步查询主题海报图片
     */
    private boolean queryPosterMedia = true;

    /**
     * 查询的是且还是或。这个接口特殊，默认是且。其他地方均默认或。即：如果是多个标签id查询，则这多个标签id是怎么作为条件的。
     * true是且，false是或
     */
    private boolean queryLabelJust = true;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    private Integer librarySource;

    /**
     * 模块来源id
     */
    private String thirdId;

    /**
     * 学校ids
     */
    private List<Long> organizationIds;
}
