package com.fh.cloud.screen.service.er.entity.bo;

import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 考场_考试计划里面一次考试信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamInfoBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 考场号（名称）
     */
    @ApiModelProperty("考场号（名称）")
    private String examRoomName;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 地点id
     */
    @ApiModelProperty("地点id")
    private Long spaceInfoId;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 科目对象合集-添加的时候使用
     */
    private List<ExamInfoSubjectBo> examInfoSubjectBos;

    /**
     * 地点名称（导入转换使用，不做db操作）
     */
    private String spaceInfoName;

    /**
     * 地点组id
     */
    private Long spaceGroupId;

    /**
     * 地点组名称
     */
    private String spaceGroupName;

    /**
     * 科目名称
     */
    private String subjectName;
}
