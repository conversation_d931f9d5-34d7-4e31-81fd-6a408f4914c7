package com.fh.cloud.screen.service.leaveschool.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolStudentSignRecordApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordSaveBatchBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 学生进出记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:58:52
 */
@FeignClient(contextId = "leaveSchoolStudentSignRecordApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LeaveSchoolStudentSignRecordApiService.LeaveSchoolStudentSignRecordApiFallbackFactory.class)
@Component
public interface LeaveSchoolStudentSignRecordApiService extends LeaveSchoolStudentSignRecordApi {

    @Component
    class LeaveSchoolStudentSignRecordApiFallbackFactory implements FallbackFactory<LeaveSchoolStudentSignRecordApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LeaveSchoolStudentSignRecordApiFallbackFactory.class);
        @Override
        public LeaveSchoolStudentSignRecordApiService create(Throwable cause) {
            LeaveSchoolStudentSignRecordApiFallbackFactory.LOGGER.error("服务调用失败:{}", cause.getMessage());
            return new LeaveSchoolStudentSignRecordApiService() {
                public AjaxResult getLeaveSchoolStudentSignRecordPageListByCondition(LeaveSchoolStudentSignRecordConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLeaveSchoolStudentSignRecordListByCondition(LeaveSchoolStudentSignRecordConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLeaveSchoolStudentSignRecord(LeaveSchoolStudentSignRecordBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLeaveSchoolStudentSignRecord(LeaveSchoolStudentSignRecordBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult addBatch(LeaveSchoolStudentSignRecordSaveBatchBo leaveSchoolStudentSignRecordSaveBatchBo) {
                    return AjaxResult.fail("批量新增学生进出记录失败");
                }

            };
        }
    }
}