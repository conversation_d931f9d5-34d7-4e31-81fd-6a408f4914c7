package com.fh.cloud.screen.service.device.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.api.ShowDeviceSwitchApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBatchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.light.core.entity.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = ShowDeviceSwitchApiService.ShowDeviceSwitchApiFallbackFactory.class)
@Component
public interface ShowDeviceSwitchApiService extends ShowDeviceSwitchApi {

    @Component
    class ShowDeviceSwitchApiFallbackFactory implements FallbackFactory<ShowDeviceSwitchApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ShowDeviceSwitchApiFallbackFactory.class);

        @Override
        public ShowDeviceSwitchApiService create(Throwable cause) {
            ShowDeviceSwitchApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ShowDeviceSwitchApiService() {

                @Override
                public AjaxResult<ShowDeviceVo> getShowDeviceSwitchListByCondition(ShowDeviceSwitchListConditionBo bo) {
                    return AjaxResult.fail("查询开关机设置列表失败");
                }

                @Override
                public AjaxResult<ShowDeviceVo> addShowDeviceSwitch(ShowDeviceSwitchBo showDeviceSwitchBo) {
                    return AjaxResult.fail("新增开关机设置失败");
                }

                @Override
                public AjaxResult updateShowDeviceSwitch(ShowDeviceSwitchBo showDeviceSwitchBo) {
                    return AjaxResult.fail("修改开关机设置失败");
                }

                @Override
                public AjaxResult saveShowDeviceSwitch(@RequestBody ShowDeviceSwitchBatchBo showDeviceSwitchBatchBo) {
                    return AjaxResult.fail("保存开关机设置失败");
                }

            };
        }
    }
}
