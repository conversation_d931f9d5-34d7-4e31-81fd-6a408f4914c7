package com.fh.cloud.screen.service.user.service;

import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.user.api.UserScreenApi;
import com.light.core.entity.AjaxResult;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.vo.TeacherVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = UserScreenApiService.UserApiFallbackFactory.class)
@Component
public interface UserScreenApiService extends UserScreenApi {
    @Component
    class UserApiFallbackFactory implements FallbackFactory<UserScreenApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(UserApiFallbackFactory.class);

        @Override
        public UserScreenApiService create(Throwable cause) {
            UserApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new UserScreenApiService() {

                @Override
                public AjaxResult getTeacherListByRealName(String realName, Long orgId) {
                    return AjaxResult.fail("获取失败");
                }

                @Override
                public AjaxResult<List<StudentVo>> getStudentListByClassesId(Long classesId) {
                    return AjaxResult.fail("获取失败");
                }

                @Override
                public AjaxResult<List<TeacherVo>> getTeacherListByOrganizationId(Long organizationId) {
                    return AjaxResult.fail("获取失败");
                }
            };
        }
    }
}
