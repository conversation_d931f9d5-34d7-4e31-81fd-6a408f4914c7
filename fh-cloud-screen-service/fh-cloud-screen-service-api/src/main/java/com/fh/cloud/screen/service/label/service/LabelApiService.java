package com.fh.cloud.screen.service.label.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.label.api.LabelApi;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
@FeignClient(contextId = "labelApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class, fallbackFactory = LabelApiService.LabelApiFallbackFactory.class)
@Component
public interface LabelApiService extends LabelApi {

    @Component
    class LabelApiFallbackFactory implements FallbackFactory<LabelApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LabelApiFallbackFactory.class);

        @Override
        public LabelApiService create(Throwable cause) {
            LabelApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LabelApiService() {
                public AjaxResult getLabelPageListByCondition(LabelConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLabelListByCondition(LabelConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLabel(LabelBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLabel(LabelBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long labelId) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long labelId) {
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getLabelTreeByCondition(LabelConditionBo conditionBo) {
                    return AjaxResult.fail("查询二级标签树失败");
                }

                @Override
                public AjaxResult updateLabelSortByIdList(List<Long> idList) {
                    return AjaxResult.fail("标签更新顺序失败");
                }

                @Override
                public AjaxResult exchangeLabelSort(Long preLabelId, Long nextLabelId, Long organizationId) {
                    return AjaxResult.fail("标签交换顺序失败");
                }
            };
        }
    }
}