package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏场景表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ScreenSceneBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long screenSceneId;

    /**
     * FK所属组织ID
     */
    @NotNull(message = "FK所属组织ID不能为空")
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    @NotNull(message = "FK所属校区ID不能为空")
    private Long campusId;

    /**
     * 场景名称
     */
    @NotBlank(message = "场景名称不能为空")
    private String screenSceneName;

    /**
     * 云屏场景布局，透传前端数据
     */
    @NotBlank(message = "云屏场景布局，透传前端数据不能为空")
    private String screenSceneLayout;

    /**
     * 地点组id
     */
    @NotNull(message = "地点组id不能为空")
    private Long spaceGroupId;

    /**
     * 场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景
     */
    @NotNull(message = "场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景不能为空")
    private Long screenSceneType;

    /**
     * 设备模式：1横屏，2竖屏。
     */
    @NotNull(message = "设备模式：1横屏，2竖屏。不能为空")
    private Integer screenDevicePattern;

    /**
     * 场景顺序：1，2，3...
     */
    @NotNull(message = "场景顺序：1，2，3...不能为空")
    private Long screenIndex;

    /**
     * 同一个场景内轮播的场景名称
     */
    @NotBlank(message = "同一个场景内轮播的场景名称不能为空")
    private String screenPlayName;

    /**
     * 同一个场景内的轮播的场景顺序：1，2，3...
     */
    @NotNull(message = "同一个场景内的轮播的场景顺序：1，2，3...不能为空")
    private Long screenPlayIndex;

    /**
     * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
     */
    @NotNull(message = "场景时间-开始时间，多个轮播场景的时候，场景时间相同不能为空")
    private Date startTime;

    /**
     * 场景时间-结束时间，多个轮播场景的时候，场景时间相同
     */
    @NotNull(message = "场景时间-结束时间，多个轮播场景的时候，场景时间相同不能为空")
    private Date endTime;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 空间id或者班级id
     */
    private Long spaceInfoId;

    /**
     * 空间群组使用类型：1行政，2非行政
     */
    private Integer spaceGroupUseType;

    /**
     * 设备id
     */
    private Long showDeviceId;

    /**
     * 模块数据id集合 ---> 改为对象集合
     */
    private List<ScreenModuleDataBo> screenModuleDatas;

    /**
     * 发布方式：1统一发布，2点位发布
     */
    @ApiModelProperty(value = "发布方式：1统一发布，2点位发布")
    private Integer publishType;

    /**
     * 同步修改场景模块关联表 1:同步，2：不同步
     */
    private Integer syncUpdateModule;

    /**
     * 自定义场景是否全屏类型：1全屏，2不是全屏
     */
    @ApiModelProperty(value = "自定义场景是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date startDate;

    /**
     * 结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效")
    private Date endDate;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效
     */
    @ApiModelProperty(value = "星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效")
    private String weeks;

    /**
     * 监管教育局id
     */
    @ApiModelProperty("监管教育局id")
    private Long parentOrganizationId;

    /**
     * 是否跳过审核校验 true：是
     */
    @ApiModelProperty("是否跳过审核校验 true：是")
    private boolean skipAudit = false;
}
