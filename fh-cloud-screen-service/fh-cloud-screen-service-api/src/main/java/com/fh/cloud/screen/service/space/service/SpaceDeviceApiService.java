package com.fh.cloud.screen.service.space.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.space.api.SpaceDeviceApi;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;
import com.light.core.entity.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = SpaceDeviceApiService.SpaceDeviceApiFallbackFactory.class)
@Component
public interface SpaceDeviceApiService extends SpaceDeviceApi {

    @Component
    class SpaceDeviceApiFallbackFactory implements FallbackFactory<SpaceDeviceApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(SpaceDeviceApiFallbackFactory.class);

        @Override
        public SpaceDeviceApiService create(Throwable cause) {
            SpaceDeviceApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new SpaceDeviceApiService() {

                @Override
                public AjaxResult saveSpaceDeviceRel(SpaceDeviceRelBo spaceDeviceRelBo) {
                    return AjaxResult.fail("保存失败");
                }

                @Override
                public AjaxResult<SpaceDeviceRelVo> getByDeviceId(Long deviceId) {
                    return AjaxResult.fail("根据设备号获取绑定空间信息失败");
                }

                @Override
                public AjaxResult updateSpaceDeviceRel(SpaceDeviceRelBo spaceDeviceRelBo) {
                    return AjaxResult.fail("更新失败");
                }
            };
        }
    }
}
