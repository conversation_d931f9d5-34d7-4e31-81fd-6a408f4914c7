package com.fh.cloud.screen.service.screen.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 云屏首页信息vo。不同级别的场景都封装为这种类型给云屏
 *
 * <AUTHOR>
 * @date 2022/6/7 10:58
 */
@Data
public class ScreenIndexVo implements Serializable {
    /**
     * 场景优先级从高到底：1 -> 3
     */
    private Integer screenPriority;

    /**
     * 场景列表
     */
    private List<ScreenSceneVo> screenSceneVos;

    /**
     * 当前场景（不为空则表示当前场景）
     */
    private ScreenSceneVo currentScreenSceneVo;

}
