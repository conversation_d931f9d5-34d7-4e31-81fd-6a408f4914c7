package com.fh.cloud.screen.service.device.entity.bo;

import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 展示设备表，例如云屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class ShowDeviceBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long showDeviceId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 设备号
     */
    @NotBlank(message = "设备号不能为空")
    private String deviceNumber;

    /**
     * 激活状态
     */
    private String deviceActivation;

    /**
     * 设备类型：1云屏。设备绑定的时候更新
     */
    @NotNull(message = "设备类型：1云屏。设备绑定的时候更新不能为空")
    private Integer deviceType;

    /**
     * 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    @NotNull(message = "设备模式：1横屏，2竖屏。设备绑定的时候更新不能为空")
    private Integer devicePattern;

    /**
     * 是否全屏类型：1全屏，2不是全屏
     */
    @NotNull(message = "是否全屏类型：1全屏，2不是全屏")
    private Integer deviceFullType;

    /**
     * 设备MAC地址。设备绑定的时候更新
     */
    @NotBlank(message = "设备MAC地址。设备绑定的时候更新不能为空")
    private String deviceMacAddress;

    /**
     * 开关机状态：1开机，2关机，3异常
     */
    @NotNull(message = "开关机状态：1开机，2关机，3异常不能为空")
    private Integer deviceStatus;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 出货号
     */
    private String shipmentNo;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备品牌类型
     */
    private Integer deviceBrand;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 产品序列号
     */
    private String productSerialNumber;

    /**
     * 客户端版本
     */
    private String clientVersion;

    /**
     * 系统主动推送海报：1接受；2不接受
     */
    @ApiModelProperty("系统主动推送海报：1接受；2不接受")
    private Integer pushType;

    /**
     * 设备海报播放间隔时长（秒）
     */
    @ApiModelProperty("设备海报播放间隔时长（秒）")
    private Integer devicePosterDuration;

    /**
     * 海报绑定的标签列表
     */
    @ApiModelProperty("海报绑定的标签列表")
    private List<LabelBo> labelBos;

    /**
     * 虹软人脸识别激活码
     */
    private String arcsoftFaceCode;

    /**
     * 巡查备注
     */
    private String patrolRemark;

    /**
     * 设备巡查状态：1未巡查，2已巡查
     */
    private Integer patrolType;

    /**
     * 云屏皮肤主题类型，默认1默认主题，2红色主题
     */
    @ApiModelProperty("云屏皮肤主题类型，默认1默认主题，2红色主题")
    private Integer deviceThemeType;

    /**
     * 所属校区ID
     */
    private Long campusId;

    /**
     * 该设备上的人脸库建模类型（只用于过滤设备查询人脸库的数据）：1全校人脸库建模，2班级人脸库建模
     * {@link com.fh.cloud.screen.service.enums.FaceModType}
     */
    private Integer faceModType;

    /**
     * 该设备的监管状态 1-监管 2-未监管
     */
    private Integer superviseState;

    /**
     * 设备空间组列表
     */
    @ApiModelProperty("设备空间组列表")
    private List<SpaceDeviceRelBo> spaceDeviceRelList;
}
