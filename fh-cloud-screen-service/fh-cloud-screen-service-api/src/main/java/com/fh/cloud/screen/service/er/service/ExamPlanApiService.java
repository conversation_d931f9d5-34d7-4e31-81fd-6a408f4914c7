package com.fh.cloud.screen.service.er.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.er.api.ExamPlanApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 考场_考试计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@FeignClient(contextId = "examPlanApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class, fallbackFactory = ExamPlanApiService.ExamPlanApiFallbackFactory.class)
@Component
public interface ExamPlanApiService extends ExamPlanApi {

    @Component
    class ExamPlanApiFallbackFactory implements FallbackFactory<ExamPlanApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ExamPlanApiFallbackFactory.class);

        @Override
        public ExamPlanApiService create(Throwable cause) {
            ExamPlanApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ExamPlanApiService() {
                public AjaxResult getExamPlanPageListByCondition(ExamPlanConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getExamPlanListByCondition(ExamPlanConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addExamPlan(ExamPlanBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateExamPlan(ExamPlanBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult pubsubExamPlan(ExamPlanBo Bo) {
                    return AjaxResult.fail("发布/取消失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }
            };
        }
    }
}