package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 电子签名寄语资源表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
@Data
public class ScreenSignatureMessageVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenSignatureMessageId;

    /**
     * 所属组织id
     */
    @ApiModelProperty("所属组织id")
    private Long organizationId;

    /**
     * 电子签名寄语内容
     */
    @ApiModelProperty("电子签名寄语内容")
    private String screenSignatureMessageContent;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ScreenSignatureMessageVo returnOwn() {
        return this;
    }

}
