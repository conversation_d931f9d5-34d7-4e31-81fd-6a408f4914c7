package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 云屏模块库媒体资源审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
public interface ScreenModuleLibraryMediaAuditApi {

    /**
     * 查询云屏模块库媒体资源审核表分页列表
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @PostMapping("/screen/module/library/media/audit/page/list")
    public AjaxResult<PageInfo<ScreenModuleLibraryMediaAuditVo>> getScreenModuleLibraryMediaAuditPageListByCondition(@RequestBody ScreenModuleLibraryMediaAuditConditionBo condition);

    /**
     * 查询云屏模块库媒体资源审核表列表
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @PostMapping("/screen/module/library/media/audit/list")
    public AjaxResult<List<ScreenModuleLibraryMediaAuditVo>> getScreenModuleLibraryMediaAuditListByCondition(@RequestBody ScreenModuleLibraryMediaAuditConditionBo condition);


    /**
     * 新增云屏模块库媒体资源审核表
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @PostMapping("/screen/module/library/media/audit/add")
    public AjaxResult addScreenModuleLibraryMediaAudit(@Validated @RequestBody ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo);

    /**
     * 修改云屏模块库媒体资源审核表
     * @param screenModuleLibraryMediaAuditBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @PostMapping("/screen/module/library/media/audit/update")
    public AjaxResult updateScreenModuleLibraryMediaAudit(@Validated @RequestBody ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo);

    /**
     * 查询云屏模块库媒体资源审核表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @GetMapping("/screen/module/library/media/audit/detail")
    public AjaxResult<ScreenModuleLibraryMediaAuditVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除云屏模块库媒体资源审核表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @GetMapping("/screen/module/library/media/audit/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
