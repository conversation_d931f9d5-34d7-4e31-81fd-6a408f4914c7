package com.fh.cloud.screen.service.screen.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long screenContentId;

    /**
     * FK所属组织ID
     */
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    private Long campusId;

    /**
     * FK所属班级ID
     */
    private Long classesId;

    /**
     * 内容所属范围：1校级，2班级
     */
    private Integer scopeType;

    /**
     * FK云屏模块表id
     */
    private Long screenModuleDataId;

    /**
     * 发布状态：1未发布，2已发布
     */
    private Integer screenContentStatus;

    /**
     * 模块内容类型：默认0，1网页地址，2富文本，3图片，4视频，5欢迎图
     */
    private Integer screenContentType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 有效时间-开始时间
     */
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    private Date endTime;

    /**
     * 内容详情
     */
    private List<ScreenContentDetailVo> screenContentDetailVos;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 内容数据来源
     */
    private Integer screenContentSource;

    /**
     * 通知nice值，越小优越在前面（用于实现置顶功能）
     */
    private Integer contentNice;
}
