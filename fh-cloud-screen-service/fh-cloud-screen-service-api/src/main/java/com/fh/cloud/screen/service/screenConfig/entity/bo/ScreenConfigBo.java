package com.fh.cloud.screen.service.screenConfig.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 云屏配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
@Data
public class ScreenConfigBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenConfigId;

	/**
	 * 所属组织ID
	 */
	@ApiModelProperty("所属组织ID")
	private Long organizationId;

	/**
	 * 配置信息
	 */
	@ApiModelProperty("配置信息")
	private String configValue;

	/**
	 * 品牌配置信息
	 */
	@ApiModelProperty("品牌配置信息")
	private String brandConfigValue;

	/**
	 * 描述
	 */
	@ApiModelProperty("描述")
	private String remark;

	/**
	 * 配置类型 1-偏好配置
	 */
	@ApiModelProperty("配置类型 1-偏好配置")
	private Integer type;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
