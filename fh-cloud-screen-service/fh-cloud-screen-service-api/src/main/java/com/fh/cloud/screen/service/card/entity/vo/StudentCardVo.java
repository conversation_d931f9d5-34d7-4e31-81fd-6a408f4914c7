package com.fh.cloud.screen.service.card.entity.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class StudentCardVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户卡id
     */
    private Long userCardId;

    /**
     * 用户oid
     */
    private String userOid;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 卡类型：1学生卡，2教师卡
     */
    private Integer cardType;

    /**
     * 校区名称
     */
    private String campusName;

    /**
     * 年级
     */
    private String grade;

    /**
     * 班级名称
     */
    private String classesName;

    /**
     * 班级Id
     */
    private Long classesId;
}
