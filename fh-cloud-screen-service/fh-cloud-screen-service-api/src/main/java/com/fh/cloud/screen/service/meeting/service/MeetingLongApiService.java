package com.fh.cloud.screen.service.meeting.service;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.meeting.api.MeetingLongApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;


/**
 * 长期预约表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@FeignClient(contextId = "meetingLongApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = MeetingLongApiService.MeetingLongApiFallbackFactory.class)
@Component
public interface MeetingLongApiService extends MeetingLongApi {

    @Component
    class MeetingLongApiFallbackFactory implements FallbackFactory<MeetingLongApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(MeetingLongApiFallbackFactory.class);
        @Override
        public MeetingLongApiService create(Throwable cause) {
            MeetingLongApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new MeetingLongApiService() {
                public AjaxResult getMeetingLongPageListByCondition(MeetingLongConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getMeetingLongListByCondition(MeetingLongConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addMeetingLong(MeetingLongBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateMeetingLong(MeetingLongBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}