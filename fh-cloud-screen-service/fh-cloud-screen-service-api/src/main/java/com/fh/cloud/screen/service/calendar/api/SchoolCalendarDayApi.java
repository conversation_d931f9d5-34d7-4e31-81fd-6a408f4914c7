package com.fh.cloud.screen.service.calendar.api;

import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface SchoolCalendarDayApi {

    /**
     * 查询校历上课日日期表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/calendar/day/list")
    @ApiOperation(value = "查询校历上课日日期表列表", httpMethod = "POST")
    AjaxResult getSchoolCalendarDayListByCondition(@RequestBody SchoolCalendarDayListConditionBo condition);

    /**
     * 保存或修改校历上课日日期表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/calendar/day/save-or-update")
    @ApiOperation(value = "保存或修改校历上课日日期表", httpMethod = "POST")
    AjaxResult saveOrUpdateSchoolCalendarDay(@Validated @RequestBody SchoolCalendarDayBo schoolCalendarDayBo);
}
