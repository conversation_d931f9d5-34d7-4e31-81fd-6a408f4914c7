package com.fh.cloud.screen.service.grade.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.grade.api.GradeScreenApi;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(contextId = "slow-1", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = GradeScreenApiService.GradeScreenApiFallbackFactory.class)
@Component
public interface GradeScreenApiService extends GradeScreenApi {
    @Component
    class GradeScreenApiFallbackFactory implements FallbackFactory<GradeScreenApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(GradeScreenApiFallbackFactory.class);

        @Override
        public GradeScreenApiService create(Throwable cause) {
            GradeScreenApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new GradeScreenApiService() {

                @Override
                public AjaxResult getBySectionOrgId(Long orgId) {
                    return AjaxResult.fail("年级获取失败");
                }

                @Override
                public AjaxResult currentYear() {
                    return AjaxResult.fail("获取学年接口失败");
                }

                @Override
                public AjaxResult getBySectionOrgIdGroup(Long orgId) {
                    return AjaxResult.fail("获取年级分组接口失败");
                }

                @Override
                public AjaxResult getSectionByOrgId(Long orgId) {
                    return AjaxResult.fail("获取学段接口失败");
                }

                @Override
                public AjaxResult getClassesListByCondition(ClazzConditionBoExt clazzConditionBo) {
                    return AjaxResult.fail("获取班级列表接口失败");
                }

                @Override
                public AjaxResult listGradesWithClassesAndStudents(Long orgId) {
                    return AjaxResult.fail("获取年级班级学生接口失败");
                }
            };
        }
    }
}
