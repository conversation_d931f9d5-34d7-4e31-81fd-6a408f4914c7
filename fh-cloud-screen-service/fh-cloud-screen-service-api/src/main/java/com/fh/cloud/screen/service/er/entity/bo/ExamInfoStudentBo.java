package com.fh.cloud.screen.service.er.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 考场_考试计划里面一次考试的学生
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamInfoStudentBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long examInfoStudentId;

	/**
	 * 考试科目表id
	 */
	@ApiModelProperty("考试科目表id")
	private Long examInfoSubjectId;

	/**
	 * 考试id
	 */
	@ApiModelProperty("考试id")
	private Long examInfoId;

	/**
	 * 考试计划id
	 */
	@ApiModelProperty("考试计划id")
	private Long examPlanId;

	/**
	 * 学生的user_oid
	 */
	@ApiModelProperty("学生的user_oid")
	private String userOid;

	/**
	 * 学生的姓名
	 */
	@ApiModelProperty("学生的姓名")
	private String realName;

	/**
	 * 用户来源类型：1校内，2校外
	 */
	@ApiModelProperty("用户来源类型：1校内，2校外")
	private Integer userFromType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 打卡时间
	 */
	@ApiModelProperty("打卡时间")
	private Date attendanceTime;

	/**
	 * 打卡状态，1：未签到，2：已签到[正常]，3已签到[迟到]
	 */
	@ApiModelProperty("打卡状态，1：未签到，2：已签到[正常]，3已签到[迟到]")
	private Integer attendanceStatus;

	/**
	 * 班级id
	 */
	@ApiModelProperty("班级id")
	private Long classesId;

	/**
	 * 年级
	 */
	@ApiModelProperty("年级")
	private String grade;

	/**
	 * 准考证号
	 */
	@ApiModelProperty("准考证号")
	private String registrationNumber;

	/**
	 * 座位号
	 */
	@ApiModelProperty("座位号")
	private String seatNumber;
}
