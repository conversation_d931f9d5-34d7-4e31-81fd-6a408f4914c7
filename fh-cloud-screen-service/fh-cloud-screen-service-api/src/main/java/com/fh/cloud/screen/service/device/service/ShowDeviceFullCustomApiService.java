package com.fh.cloud.screen.service.device.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.api.ShowDeviceFullCustomApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;

/**
 * 云屏全屏非全屏设置自定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
@FeignClient(contextId = "showDeviceFullCustomApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ShowDeviceFullCustomApiService.ShowDeviceFullCustomApiFallbackFactory.class)
@Component
public interface ShowDeviceFullCustomApiService extends ShowDeviceFullCustomApi {

    @Component
    class ShowDeviceFullCustomApiFallbackFactory implements FallbackFactory<ShowDeviceFullCustomApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ShowDeviceFullCustomApiFallbackFactory.class);

        @Override
        public ShowDeviceFullCustomApiService create(Throwable cause) {
            ShowDeviceFullCustomApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ShowDeviceFullCustomApiService() {
                public AjaxResult
                    getShowDeviceFullCustomPageListByCondition(ShowDeviceFullCustomConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getShowDeviceFullCustomListByCondition(ShowDeviceFullCustomConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addShowDeviceFullCustom(ShowDeviceFullCustomBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateShowDeviceFullCustom(ShowDeviceFullCustomBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}