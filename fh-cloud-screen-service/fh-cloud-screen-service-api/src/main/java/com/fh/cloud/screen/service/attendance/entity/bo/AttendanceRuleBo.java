package com.fh.cloud.screen.service.attendance.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 考勤规则表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceRuleBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long attendanceRuleId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @NotNull(message = "考勤类型：1教师考勤，2学生考勤不能为空")
    private Integer attendanceType;

    /**
     * 考勤方一天几次：1，2，3，4...
     */
    @NotNull(message = "考勤方一天几次：1，2，3，4...不能为空")
    private Integer attendanceModeNum;

    /**
     * 年级考勤是否一致：1一致，2不一致
     */
    @NotNull(message = "年级考勤是否一致：1一致，2不一致不能为空")
    private Integer gradeSameType;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * grade 的code值，年级一致的情况这个值为默认值
     */
    private String grade;

    /**
     * 日期时间
     */
    private Date nowDate;
}
