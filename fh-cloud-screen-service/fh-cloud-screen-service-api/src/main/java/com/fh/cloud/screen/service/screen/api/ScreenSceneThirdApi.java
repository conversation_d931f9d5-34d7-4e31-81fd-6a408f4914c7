package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 第三方对接云屏场景信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
public interface ScreenSceneThirdApi {

    /**
     * 查询第三方对接云屏场景信息表分页列表
     * 
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @PostMapping("screen/scene/third/page/list")
    public AjaxResult<PageInfo<ScreenSceneThirdVo>>
        getScreenSceneThirdPageListByCondition(@RequestBody ScreenSceneThirdConditionBo condition);

    /**
     * 查询第三方对接云屏场景信息表列表
     * 
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @PostMapping("screen/scene/third/list")
    public AjaxResult<List<ScreenSceneThirdVo>>
        getScreenSceneThirdListByCondition(@RequestBody ScreenSceneThirdConditionBo condition);

    /**
     * 新增第三方对接云屏场景信息表
     * 
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @PostMapping("screen/scene/third/add")
    public AjaxResult addScreenSceneThird(@Validated @RequestBody ScreenSceneThirdBo screenSceneThirdBo);

    /**
     * 修改第三方对接云屏场景信息表
     * 
     * @param screenSceneThirdBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @PostMapping("screen/scene/third/update")
    public AjaxResult updateScreenSceneThird(@Validated @RequestBody ScreenSceneThirdBo screenSceneThirdBo);

    /**
     * 查询第三方对接云屏场景信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @GetMapping("screen/scene/third/detail")
    public AjaxResult<ScreenSceneThirdVo>
        getDetail(@NotNull(message = "请选择数据") @RequestParam("screenSceneThirdId") Long screenSceneThirdId);

    /**
     * 删除第三方对接云屏场景信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @GetMapping("screen/scene/third/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("screenSceneThirdId") Long screenSceneThirdId);

    /**
     * 删除第三方对接云屏场景信息表-根据appCode删除
     *
     * @param screenSceneThirdId the screen scene third id
     * @param appCode the app code
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -04-06 17:50:34
     * @returnType AjaxResult
     */
    @GetMapping("screen/scene/third/delete-byappcode")
    public AjaxResult deleteByAppCode(
        @NotNull(message = "请选择数据") @RequestParam("screenSceneThirdId") Long screenSceneThirdId,
        @RequestParam("appCode") String appCode);

}
