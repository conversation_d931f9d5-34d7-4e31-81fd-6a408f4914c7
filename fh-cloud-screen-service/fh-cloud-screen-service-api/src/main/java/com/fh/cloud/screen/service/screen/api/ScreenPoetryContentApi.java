package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 共话诗词表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
public interface ScreenPoetryContentApi {

    /**
     * 查询共话诗词表分页列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @PostMapping("/screen/poetry/content/page/list")
    public AjaxResult<PageInfo<ScreenPoetryContentVo>> getScreenPoetryContentPageListByCondition(@RequestBody ScreenPoetryContentConditionBo condition);

    /**
     * 查询共话诗词表列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @PostMapping("/screen/poetry/content/list")
    public AjaxResult<List<ScreenPoetryContentVo>> getScreenPoetryContentListByCondition(@RequestBody ScreenPoetryContentConditionBo condition);


    /**
     * 新增共话诗词表
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @PostMapping("/screen/poetry/content/add")
    public AjaxResult addScreenPoetryContent(@Validated @RequestBody ScreenPoetryContentBo screenPoetryContentBo);

    /**
     * 修改共话诗词表
     * @param screenPoetryContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @PostMapping("/screen/poetry/content/update")
    public AjaxResult updateScreenPoetryContent(@Validated @RequestBody ScreenPoetryContentBo screenPoetryContentBo);

    /**
     * 查询共话诗词表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @GetMapping("/screen/poetry/content/detail")
    public AjaxResult<ScreenPoetryContentVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除共话诗词表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:47
     */
    @GetMapping("/screen/poetry/content/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
