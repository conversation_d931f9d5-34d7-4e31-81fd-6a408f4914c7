package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤规则表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceRuleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceRuleId;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    private Integer attendanceType;

    /**
     * 考勤方一天几次：1，2，3，4...
     */
    private Integer attendanceModeNum;

    /**
     * 年级考勤是否一致：1一致，2不一致
     */
    private Integer gradeSameType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
