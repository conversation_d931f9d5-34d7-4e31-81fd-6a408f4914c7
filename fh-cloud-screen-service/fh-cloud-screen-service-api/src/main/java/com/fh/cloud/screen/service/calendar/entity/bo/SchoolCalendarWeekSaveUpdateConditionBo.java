package com.fh.cloud.screen.service.calendar.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/12
 */
@Data
public class SchoolCalendarWeekSaveUpdateConditionBo implements Serializable {
    /**
     * FK校历主表主键id
     */
    private Long schoolCalendarId;

    /**
     * 所属组织ID
     */
    @NotNull(message = "所属组织ID不能为空")
    private Long organizationId;

    /**
     * 校历星期列表
     */
    @NotNull
    private List<SchoolCalendarWeekBo> schoolCalendarWeekBos;
}
