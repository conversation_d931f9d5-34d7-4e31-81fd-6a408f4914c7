package com.fh.cloud.screen.service.rest.service;

import com.fh.cloud.screen.service.rest.entity.bo.WorkRestBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestListConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.rest.api.WorkRestApi;
import com.light.core.entity.AjaxResult;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = WorkRestApiService.WorkRestApiFallbackFactory.class)
@Component
public interface WorkRestApiService extends WorkRestApi {
    @Component
    class WorkRestApiFallbackFactory implements FallbackFactory<WorkRestApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(WorkRestApiFallbackFactory.class);

        @Override
        public WorkRestApiService create(Throwable cause) {
            WorkRestApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new WorkRestApiService() {
                @Override
                public AjaxResult getWorkRestListByCondition(WorkRestListConditionBo workRestListConditionBo) {
                    return AjaxResult.fail("作息时间列表查询失败");
                }

                @Override
                public AjaxResult addWorkRest(WorkRestBo workRestBo) {
                    return AjaxResult.fail("添加作息时间失败");
                }

                @Override
                public AjaxResult updateWorkRest(WorkRestBo workRestBo) {
                    return AjaxResult.fail("更新作息时间失败");
                }

                @Override
                public AjaxResult getDetail(Long workRestId) {
                    return AjaxResult.fail("查看作息时间失败");
                }

                @Override
                public AjaxResult delete(Long workRestId) {
                    return AjaxResult.fail("删除作息时间失败");
                }

                @Override
                public AjaxResult saveWorkRestWithDetail(WorkRestBo workRestBo) {
                    return AjaxResult.fail("保存作息时间失败");
                }

                @Override
                public AjaxResult changeStatus(WorkRestBo workRestBo) {
                    return AjaxResult.fail("启用禁用作息时间失败");
                }
            };
        }
    }
}
