package com.fh.cloud.screen.service.screen.entity.vo;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 云屏紧急发布内容-地点组关系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentSpecialSpaceGroupRelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键,无实际意义
     */
    private Long id;

    /**
     * FK云屏紧急发布内容表主键
     */
    private Long screenContentSpecialId;

    /**
     * FK地点分组表主键id
     */
    private Long spaceGroupId;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
