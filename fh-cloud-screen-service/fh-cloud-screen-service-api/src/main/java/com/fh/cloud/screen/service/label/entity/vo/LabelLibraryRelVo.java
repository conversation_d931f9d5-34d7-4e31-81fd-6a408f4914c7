package com.fh.cloud.screen.service.label.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 标签海报关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@Data
public class LabelLibraryRelVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 海报模块表主键
     */
    @ApiModelProperty("海报模块表主键")
    private Long screenModuleLibraryId;

    /**
     * 标签表主键
     */
    @ApiModelProperty("标签表主键")
    private Long labelId;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 该云屏模块对应的节日类型拼接（数据库查询使用，切勿直接给前端使用）
     */
    private String festivalTypesConcat;

    /**
     * festivalTypesConcat转换而成的list。海报的节日类型列表
     */
    private List<Integer> festivalTypes;

    /**
     * 海报对应的节日id拼接（仅数据库查询使用）
     */
    private String festivalIdConcat;

    /**
     * festivalIdConcat转换而成的list。海报的节日id集合
     */
    private List<Long> festivalIds;

    /**
     * 标签id的拼接（数据库查询使用，切勿直接给前端使用）
     */
    private String labelIdConcat;

    /**
     * labelIdConcat转换成labelIds。海报的标签列表
     */
    private List<Long> labelIds;

    /**
     * 关联标签名称
     */
    private String labelName;

}
