package com.fh.cloud.screen.service.schoolyear.api;

import com.fh.app.role.service.role.entity.bo.RoleAppRelConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.bo.OrganizationTermBo;
import com.light.user.organization.entity.bo.OrganizationTermDelSaveBo;
import com.light.user.organization.entity.vo.OrganizationTermVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> The interface School year api.
 */
public interface SchoolYearApi {

    /**
     * years当前学年和下一学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:44:04
     */
    @ApiOperation(value = "查询当前学年和下一学年", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/school-year/years", method = RequestMethod.GET)
    public AjaxResult listSchoolYears();

    /**
     * 当前学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查询当前学年", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/school-year/current", method = RequestMethod.GET)
    public AjaxResult currentYear();

    /**
     * 根据年级和学年获取入学年份
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "根据年级和学年获取入学年份", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/school-year/year", method = RequestMethod.GET)
    public AjaxResult getYear(@RequestParam(value = "grade") String grade,
        @RequestParam(value = "schoolYear") String schoolYear);

    /**
     * 获取学年月日
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "学年开始月日", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/school-year/month-day", method = RequestMethod.GET)
    public AjaxResult getMonthDay();

    /**
     * 保存学期设置
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "保存学期设置", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/school-year/term-save", method = RequestMethod.POST)
    public AjaxResult saveTerm(@RequestBody OrganizationTermDelSaveBo organizationTermDelSaveBo);

    /**
     * 查看学期列表
     *
     * @param organizationTermBo the organization term bo
     * @return the ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查看学期列表", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/school-year/term-list", method = RequestMethod.POST)
    public AjaxResult listTerm(@RequestBody OrganizationTermBo organizationTermBo);

    /**
     * 查看当前月份的教学周
     *
     * @param organizationId 组织id
     * @param month 要查看的月份
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查看教学周", httpMethod = "POST")
    @RequestMapping(value = "/school-year/week-list", method = RequestMethod.GET)
    public AjaxResult getSchoolTeacherWeekByMonth(@RequestParam("organizationId") Long organizationId,
                                                                     @RequestParam("month") String month) throws ParseException;

}
