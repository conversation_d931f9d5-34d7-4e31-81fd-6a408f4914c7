package com.fh.cloud.screen.service.screen.api;


import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 电子签名图片资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
public interface ScreenSignaturePictureApi {

    /**
     * 查询电子签名图片资源表分页列表
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/picture/page/list")
    public AjaxResult<PageInfo<ScreenSignaturePictureVo>> getScreenSignaturePicturePageListByCondition(@RequestBody ScreenSignaturePictureConditionBo condition);

    /**
     * 查询电子签名图片资源表列表
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/picture/list")
    public AjaxResult<List<ScreenSignaturePictureVo>> getScreenSignaturePictureListByCondition(@RequestBody ScreenSignaturePictureConditionBo condition);


    /**
     * 新增电子签名图片资源表
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/picture/add")
    public AjaxResult addScreenSignaturePicture(@Validated @RequestBody ScreenSignaturePictureBo screenSignaturePictureBo);

    /**
     * 修改电子签名图片资源表
     * @param screenSignaturePictureBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @PostMapping("/screen/signature/picture/update")
    public AjaxResult updateScreenSignaturePicture(@Validated @RequestBody ScreenSignaturePictureBo screenSignaturePictureBo);

    /**
     * 查询电子签名图片资源表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @GetMapping("/screen/signature/picture/detail")
    public AjaxResult<ScreenSignaturePictureVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除电子签名图片资源表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @GetMapping("/screen/signature/picture/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
