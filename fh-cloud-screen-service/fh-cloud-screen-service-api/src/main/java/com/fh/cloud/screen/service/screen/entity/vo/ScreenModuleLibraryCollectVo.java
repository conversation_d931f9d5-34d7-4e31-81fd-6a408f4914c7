package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 海报收藏表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@Data
public class ScreenModuleLibraryCollectVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * FK模块库表id
     */
    @ApiModelProperty("FK模块库表id")
    private Long screenModuleLibraryId;

    /**
     * 收藏人id
     */
    @ApiModelProperty("收藏人id")
    private String userOid;

    /**
     * 收藏人组织id
     */
    @ApiModelProperty("收藏人组织id")
    private Long organizationId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 是否是校本海报：1 是，2否
     */
    @ApiModelProperty("是否是校本海报：1 是，2否")
    private Integer isSchoolPoster;
}
