package com.fh.cloud.screen.service.er.api;

import com.fh.cloud.screen.service.er.entity.bo.ExamInfoConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoBo;
import com.fh.cloud.screen.service.er.entity.bo.ImportExamInfoModel;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStatisticsVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;

import com.light.core.entity.AjaxResult;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 考场_考试计划里面一次考试信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamInfoApi {

    /**
     * 查询考场_考试计划里面一次考试信息分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/page/list")
    public AjaxResult<PageInfo<ExamInfoVo>> getExamInfoPageListByCondition(@RequestBody ExamInfoConditionBo condition);

    /**
     * 查询考场_考试计划里面一次考试信息列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/list")
    public AjaxResult<List<ExamInfoVo>> getExamInfoListByCondition(@RequestBody ExamInfoConditionBo condition);

    /**
     * 新增考场_考试计划里面一次考试信息
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/add")
    public AjaxResult addExamInfo(@Validated @RequestBody ExamInfoBo examInfoBo);

    /**
     * 修改考场_考试计划里面一次考试信息
     * 
     * @param examInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/info/update")
    public AjaxResult updateExamInfo(@Validated @RequestBody ExamInfoBo examInfoBo);

    /**
     * 查询考场_考试计划里面一次考试信息详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/info/detail")
    public AjaxResult<ExamInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除考场_考试计划里面一次考试信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/info/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 考试导入
     *
     * @param file, examPlanId, organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/17 9:58
     */
    @PostMapping(value = "/exam/info/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public AjaxResult importExamInfo(ImportExamInfoModel importExamInfoModel);

    /**
     * 根据考场号查询所有的考试科目信息
     */
    @GetMapping("/exam/info/subject/list")
    public AjaxResult<List<ExamInfoSubjectVo>> listSubjectByExamInfoId(@RequestParam("examInfoId") Long examInfoId);
    /**
     * 根据选择的考试科目关系表id查询本场考试的所有学生信息（包含签到和未签到）,参数：examInfoSubjectId,attendanceStatus
     */
    @PostMapping("/exam/info/attendance-member")
    public AjaxResult<List<ExamInfoStudentVo>> attendanceMember(@RequestBody ExamInfoConditionBo examInfoConditionBo);

    /**
     * 本次科目考试的总览信息（不根据用户筛选条件变化）,参数：examInfoSubjectId,attendanceStatus
     */
    @PostMapping("/exam/info/attendance-statistics")
    public AjaxResult<ExamInfoStatisticsVo> attendanceStatistics(@RequestBody ExamInfoConditionBo examInfoConditionBo);
}
