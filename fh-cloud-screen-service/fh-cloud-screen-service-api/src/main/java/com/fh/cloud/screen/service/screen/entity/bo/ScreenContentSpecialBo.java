package com.fh.cloud.screen.service.screen.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 云屏紧急发布内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentSpecialBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long screenContentSpecialId;

    /**
     * FK所属组织ID
     */
    @NotNull(message = "FK所属组织ID不能为空")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @NotNull(message = "所属校区ID不能为空")
    private Long campusId;

    /**
     * 使用模板类型：1通知公告、2图片、3标语、4倒计时、5视频、6网页URL、7富文本
     */
    @NotNull(message = "使用模板类型：1通知公告、2图片、3标语、4倒计时、5视频、6网页URL、7富文本不能为空")
    private Integer screenTemplateType;

    /**
     * 云屏内容-标题
     */
    @NotBlank(message = "云屏内容-标题不能为空")
    private String screenContentTitle;

    /**
     * 云屏内容-文本
     */
    @NotBlank(message = "云屏内容-文本不能为空")
    private String screenContentTxt;

    /**
     * 云屏内容-url
     */
    @NotBlank(message = "云屏内容-url不能为空")
    private String screenContentUrl;

    /**
     * 云屏图片或者视频媒体地址
     */
    @NotBlank(message = "云屏图片或者视频媒体地址不能为空")
    private String screenContentMediaUrl;

    /**
     * 云屏图片或者视频媒体地址-压缩后
     */
    @NotBlank(message = "云屏图片或者视频媒体地址-压缩后不能为空")
    private String screenContentMediaUrlCompress;

    /**
     * 云屏图片或者视频媒体地址-封面
     */
    @NotBlank(message = "云屏图片或者视频媒体地址-封面不能为空")
    private String screenContentMediaUrlCover;

    /**
     * 云屏图片或者视频媒体名称（不包含后缀）
     */
    @NotBlank(message = "云屏图片或者视频媒体名称（不包含后缀）不能为空")
    private String screenContentMediaName;

    /**
     * 云屏图片或者视频原始媒体名称（包含后缀）
     */
    @NotBlank(message = "云屏图片或者视频原始媒体名称（包含后缀）不能为空")
    private String screenContentMediaNameOri;

    /**
     * 有效时间-开始时间
     */
    @NotNull(message = "有效时间-开始时间不能为空")
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    @NotNull(message = "有效时间-结束时间不能为空")
    private Date endTime;

    /**
     * 发布状态：1未发布，2已发布
     */
    @NotNull(message = "发布状态：1未发布，2已发布")
    private Integer screenContentStatus;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

    /**
     * 云屏图片或者视频媒体fileoid
     */
    private String screenContentMediaId;

    /**
     * 云屏图片或者视频媒体fileoid-压缩后
     */
    private String screenContentMediaIdCompress;

    /**
     * 紧急发布到的地点组id集合
     */
    private List<Long> spaceGroupIds;

    /**
     * 称呼内容
     */
    private String callContent;

    /**
     * 落款内容
     */
    private String signContent;

    /**
     * 内容数据来源
     */
    private Integer screenContentSource;
}
