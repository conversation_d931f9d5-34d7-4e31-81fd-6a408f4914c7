package com.fh.cloud.screen.service.leaveschool.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 放学配置表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
@Data
public class LeaveSchoolConfigConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 放学配置表id
	 */
	@ApiModelProperty("放学配置表id")
	private Long leaveSchoolConfigId;

	/**
	 * 组织id
	 */
	@ApiModelProperty("组织id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@ApiModelProperty("校区id")
	private Long campusId;

	/**
	 * 各年级放学时间 1-一致 2-不一致
	 */
	@ApiModelProperty("各年级放学时间 1-一致 2-不一致")
	private Integer leaveSchoolGradeType;

	/**
	 * 一周内放学时间 1-一周一致 2-一周不一致
	 */
	@ApiModelProperty("一周内放学时间 1-一周一致 2-一周不一致")
	private Integer leaveSchoolWeekType;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 放学自动确认时间（单位：分钟）
	 */
	@ApiModelProperty("放学自动确认时间（单位：分钟）")
	private Integer autoConfirmTime;

}
