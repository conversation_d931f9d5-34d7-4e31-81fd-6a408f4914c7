package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.screen.api.ScreenPwdApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo;
import com.light.core.entity.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = ScreenPwdApiService.ScreenPwdApiFallbackFactory.class)
@Component
public interface ScreenPwdApiService extends ScreenPwdApi {

    @Component
    class ScreenPwdApiFallbackFactory implements FallbackFactory<ScreenPwdApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenPwdApiFallbackFactory.class);

        @Override
        public ScreenPwdApiService create(Throwable cause) {
            ScreenPwdApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new ScreenPwdApiService() {

                @Override
                public AjaxResult<ScreenPwdVo> getByOrgId(Long orgId) {
                    return AjaxResult.fail("云屏密码信息获取失败");
                }

                @Override
                public AjaxResult getScreenPwdListByCondition(ScreenPwdListConditionBo condition) {
                    return AjaxResult.fail("获取云屏密码列表失败");
                }

                @Override
                public AjaxResult addScreenPwd(ScreenPwdBo screenPwdBo) {
                    return AjaxResult.fail("新增云屏密码失败");
                }

                @Override
                public AjaxResult updateScreenPwd(ScreenPwdBo screenPwdBo) {
                    return AjaxResult.fail("修改云屏密码失败");
                }
            };
        }
    }
}
