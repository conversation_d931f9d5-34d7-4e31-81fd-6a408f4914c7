package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenPoetryContentApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 共话诗词表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
@FeignClient(contextId = "screenPoetryContentApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenPoetryContentApiService.ScreenPoetryContentApiFallbackFactory.class)
@Component
public interface ScreenPoetryContentApiService extends ScreenPoetryContentApi {

    @Component
    class ScreenPoetryContentApiFallbackFactory implements FallbackFactory<ScreenPoetryContentApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenPoetryContentApiFallbackFactory.class);
        @Override
        public ScreenPoetryContentApiService create(Throwable cause) {
            ScreenPoetryContentApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenPoetryContentApiService() {
                public AjaxResult getScreenPoetryContentPageListByCondition(ScreenPoetryContentConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenPoetryContentListByCondition(ScreenPoetryContentConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenPoetryContent(ScreenPoetryContentBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenPoetryContent(ScreenPoetryContentBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}