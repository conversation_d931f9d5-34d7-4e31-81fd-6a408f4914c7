package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * 考勤规则天表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceRuleDayVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long attendanceRuleDayId;

    /**
     * 考勤规则表ID
     */
    private Long attendanceRuleId;

    /**
     * grade 的code值，年级一致的情况这个值为默认值
     */
    private String grade;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    private Integer week;

    /**
     * 签到时间
     */
    private Time signInTime;

    /**
     * 签退时间
     */
    private Time signOutTime;

    /**
     * 考勤一组顺序：1，2，3...
     */
    private Integer attendanceRuleDayIndex;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 打卡规则左侧扩充时间分钟
     */
    private Integer signLeftMinute;

    /**
     * 打卡规则左侧扩充时间分钟
     */
    private Integer signRightMinute;

    /**
     * 考勤记录的唯一key，前端用这个匹配考勤规则
     */
    private String signInKey;

    /**
     * 考勤记录的唯一key，前端用这个匹配考勤规则
     */
    private String signOutKey;
}
