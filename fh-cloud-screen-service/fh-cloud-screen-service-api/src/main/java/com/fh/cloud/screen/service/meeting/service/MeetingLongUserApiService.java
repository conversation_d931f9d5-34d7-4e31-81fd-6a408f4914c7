package com.fh.cloud.screen.service.meeting.service;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.meeting.api.MeetingLongUserApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;


/**
 * 长期预约表人员表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@FeignClient(contextId = "meetingLongUserApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = MeetingLongUserApiService.MeetingLongUserApiFallbackFactory.class)
@Component
public interface MeetingLongUserApiService extends MeetingLongUserApi {

    @Component
    class MeetingLongUserApiFallbackFactory implements FallbackFactory<MeetingLongUserApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(MeetingLongUserApiFallbackFactory.class);
        @Override
        public MeetingLongUserApiService create(Throwable cause) {
            MeetingLongUserApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new MeetingLongUserApiService() {
                public AjaxResult getMeetingLongUserPageListByCondition(MeetingLongUserConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getMeetingLongUserListByCondition(MeetingLongUserConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addMeetingLongUser(MeetingLongUserBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateMeetingLongUser(MeetingLongUserBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}