package com.fh.cloud.screen.service.attendance.service;

import com.fh.cloud.screen.service.attendance.api.AttendanceUserDetailApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "attendanceUserDetailApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = AttendanceUserDetailApiService.AttendanceUserDetailApiFallbackFactory.class)
@Component
public interface AttendanceUserDetailApiService extends AttendanceUserDetailApi {
    @Component
    class AttendanceUserDetailApiFallbackFactory implements FallbackFactory<AttendanceUserDetailApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(AttendanceUserDetailApiFallbackFactory.class);

        @Override
        public AttendanceUserDetailApiService create(Throwable cause) {
            AttendanceUserDetailApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new AttendanceUserDetailApiService() {

                @Override
                public AjaxResult<List<AttendanceUserDetailVo>> getListByAttendanceUserId(Long attendanceUserId) {
                    return AjaxResult.fail("查询失败");
                }
            };
        }
    }
}
