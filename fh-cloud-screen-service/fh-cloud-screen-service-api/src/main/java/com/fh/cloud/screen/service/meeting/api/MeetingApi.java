package com.fh.cloud.screen.service.meeting.api;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingStudentImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingTeacherImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.light.core.entity.AjaxResult;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

/**
 * 会议表
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
public interface MeetingApi {

    /**
     * 查询会议表分页列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/page/list")
    public AjaxResult getMeetingPageListByCondition(@RequestBody MeetingConditionBo condition);

    /**
     * 查询我参与的会议列表
     *
     * <AUTHOR>
     * @date 2022/8/22 9:29
     */
    @PostMapping("meeting/my/list")
    public AjaxResult getMyMeetingPageListByCondition(@RequestBody MeetingConditionBo condition);

    /**
     * 按日期查看会议室列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/list")
    public AjaxResult getMeetingListByDate(@Validated @RequestBody MeetingConditionBo condition);

    /**
     * 新增或修改会议表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/add")
    public AjaxResult addMeeting(@Validated @RequestBody MeetingBo meetingBo);

    /**
     * 新增或修改会议表-批量
     *
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/add-batch")
    public AjaxResult addMeetingBatch(@RequestBody MeetingBo meetingBo);

    /**
     * 修改会议表
     * 
     * @param meetingBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/update")
    public AjaxResult updateMeeting(@RequestBody MeetingBo meetingBo);

    /**
     * 查询会议表详情
     * 
     * @param meetingId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @GetMapping("meeting/detail")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") @RequestParam("meetingId") Long meetingId);

    /**
     * 删除会议表
     * 
     * @param meetingBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("meeting/delete")
    public AjaxResult delete(@RequestBody MeetingBo meetingBo);

    /**
     * 会议签到接口
     *
     * @param meetingUserBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/8/23 9:59
     */
    @PostMapping("meeting/sign")
    public AjaxResult signIn(@RequestBody MeetingUserBo meetingUserBo);

    /**
     * 获取教师列表
     *
     * @param teacherConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/29 11:29
     */
    @PostMapping("meeting/teacher-list")
    public AjaxResult getTeacherListByCondition(@RequestBody TeacherConditionBo teacherConditionBo);

    /**
     * 获取当前及下一个会议（当天)
     *
     * @param meetingConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/20 15:17
     */
    @PostMapping("meeting/now-meeting")
    public AjaxResult getNowAndNextMeeting(@RequestBody MeetingConditionBo meetingConditionBo);

    /**
     * 获取会议及与会人员详情-缓存
     *
     * @param meetingId 会议主键
     * @return 会议-缓存
     * <AUTHOR>
     * @date 2023/4/10 16:44
     */
    @GetMapping("meeting/detail-cache")
    public AjaxResult getDetailCacheByMeetingId(@RequestParam("meetingId") Long meetingId);

    /**
     * 获取教师导入校验结果
     *
     * @param meetingImportBo the meeting import bo
     * @return teacher import check result
     * <AUTHOR>
     * @date 2024 -06-25 17:21:00
     */
    @PostMapping("meeting/teacher-import")
    public AjaxResult getTeacherImportCheckResult(@RequestBody MeetingImportBo<MeetingTeacherImportBo> meetingImportBo);

    /**
     * 获取学生导入校验结果
     *
     * @param meetingImportBo the meeting import bo
     * @return student import check result
     * <AUTHOR>
     * @date 2024 -06-25 17:20:58
     */
    @PostMapping("meeting/student-import")
    public AjaxResult getStudentImportCheckResult(@RequestBody MeetingImportBo<MeetingStudentImportBo> meetingImportBo);
}
