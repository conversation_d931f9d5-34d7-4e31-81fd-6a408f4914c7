package com.fh.cloud.screen.service.crm.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.crm.api.CrmContactApi;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * CRM商讯联系人表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@FeignClient(contextId = "crmContactApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = CrmContactApiService.CrmContactApiFallbackFactory.class)
@Component
public interface CrmContactApiService extends CrmContactApi {

    @Component
    class CrmContactApiFallbackFactory implements FallbackFactory<CrmContactApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CrmContactApiFallbackFactory.class);
        @Override
        public CrmContactApiService create(Throwable cause) {
            CrmContactApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new CrmContactApiService() {
                public AjaxResult getCrmContactPageListByCondition(CrmContactConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getCrmContactListByCondition(CrmContactConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addCrmContact(CrmContactBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateCrmContact(CrmContactBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

            };
        }
    }
}