package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 标签海报关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
@Data
public class LabelLibraryAuditRelConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 海报模块审核表主键
	 */
	@ApiModelProperty("海报模块审核表主键")
	private Long screenModuleLibraryAuditId;

	/**
	 * 标签表主键
	 */
	@ApiModelProperty("标签表主键")
	private Long labelId;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交
	 */
	@ApiModelProperty("审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交")
	private Integer auditType;

	/**
	 * 审核状态
	 */
	@ApiModelProperty("审核状态")
	private List<Integer> auditTypes;

	/**
	 * 审核主键ids
	 */
	private List<Long> screenModuleLibraryAuditIds;
}
