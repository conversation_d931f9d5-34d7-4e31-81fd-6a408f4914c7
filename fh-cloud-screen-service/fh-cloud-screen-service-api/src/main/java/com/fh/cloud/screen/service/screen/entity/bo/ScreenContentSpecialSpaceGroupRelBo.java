package com.fh.cloud.screen.service.screen.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 云屏紧急发布内容-地点组关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentSpecialSpaceGroupRelBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键,无实际意义
     */
    @NotNull(message = "主键,无实际意义不能为空")
    private Long id;

    /**
     * FK云屏紧急发布内容表主键
     */
    @NotNull(message = "FK云屏紧急发布内容表主键不能为空")
    private Long screenContentSpecialId;

    /**
     * FK地点分组表主键id
     */
    @NotNull(message = "FK地点分组表主键id不能为空")
    private Long spaceGroupId;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

}
