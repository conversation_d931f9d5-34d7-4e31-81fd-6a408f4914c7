package com.fh.cloud.screen.service.leaveschool.api;


import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 放学配置设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
public interface LeaveSchoolConfigDeviceApi {

    /**
     * 查询放学配置设备表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @PostMapping("/leave/school/config/device/page/list")
    public AjaxResult<PageInfo<LeaveSchoolConfigDeviceVo>> getLeaveSchoolConfigDevicePageListByCondition(@RequestBody LeaveSchoolConfigDeviceConditionBo condition);

    /**
     * 查询放学配置设备表列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @PostMapping("/leave/school/config/device/list")
    public AjaxResult<List<LeaveSchoolConfigDeviceVo>> getLeaveSchoolConfigDeviceListByCondition(@RequestBody LeaveSchoolConfigDeviceConditionBo condition);


    /**
     * 新增放学配置设备表
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @PostMapping("/leave/school/config/device/add")
    public AjaxResult addLeaveSchoolConfigDevice(@Validated @RequestBody LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo);

    /**
     * 修改放学配置设备表
     * @param leaveSchoolConfigDeviceBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @PostMapping("/leave/school/config/device/update")
    public AjaxResult updateLeaveSchoolConfigDevice(@Validated @RequestBody LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo);

    /**
     * 查询放学配置设备表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @GetMapping("/leave/school/config/device/detail")
    public AjaxResult<LeaveSchoolConfigDeviceVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除放学配置设备表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @GetMapping("/leave/school/config/device/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
