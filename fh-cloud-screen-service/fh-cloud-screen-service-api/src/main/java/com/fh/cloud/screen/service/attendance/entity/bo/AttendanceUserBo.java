package com.fh.cloud.screen.service.attendance.entity.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceUserBo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空")
    private Long attendanceUserId;

    /**
     * 该用户当天考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，5迟到早退，6缺卡
     */
    @NotNull(message = "该用户当天考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，5迟到早退，6缺卡不能为空")
    private Integer attendanceRecordType;

    /**
     * 考勤规则id，当天考勤使用的考勤规则
     */
    @NotNull(message = "考勤规则id，当天考勤使用的考勤规则不能为空")
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @NotNull(message = "考勤类型：1教师考勤，2学生考勤不能为空")
    private Integer attendanceType;

    /**
     * 考勤日期:yyyy-MM-dd
     */
    @NotNull(message = "考勤日期:yyyy-MM-dd不能为空")
    private Date attendanceDate;

    /**
     * 考勤 日 yyyy-MM-dd
     */
    private String attendanceDay;

    /**
     * 考勤 月 yyyy-MM
     */
    private String attendanceMonth;

    /**
     * 用户oid
     */
    @NotBlank(message = "用户oid不能为空")
    private String userOid;

    /**
     * 更新时间
     */
    @NotNull(message = "更新时间不能为空")
    private Date createTime;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    /**
     * 创建时间
     */
    @NotNull(message = "创建时间不能为空")
    private Date updateTime;

    /**
     * 更新人
     */
    @NotBlank(message = "更新人不能为空")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @NotNull(message = "是否删除，0：否，1：是不能为空")
    private Integer isDelete;

}
