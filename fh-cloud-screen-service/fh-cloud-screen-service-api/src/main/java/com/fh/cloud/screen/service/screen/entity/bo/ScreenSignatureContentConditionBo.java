package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 电子签名表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Data
public class ScreenSignatureContentConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenSignatureContentId;

    /**
     * FK云屏模块表id
     */
    @ApiModelProperty("FK云屏模块表id")
    private Long screenModuleDataId;

    /**
     * 电子签名标题
     */
    @ApiModelProperty("电子签名标题")
    private String screenSignatureContentTitle;

    /**
     * 电子签名文本内容
     */
    @ApiModelProperty("电子签名文本内容")
    private String screenSignatureContentTxt;

    /**
     * FK所属班级ID
     */
    @ApiModelProperty("FK所属班级ID")
    private Long classesId;

    /**
     * 【冗余】创建人姓名
     */
    @ApiModelProperty("【冗余】创建人姓名")
    private String createUserName;

    /**
     * 【冗余】创建人班级名称
     */
    @ApiModelProperty("【冗余】创建人班级名称")
    private String createUserClassesName;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 学校id
     */
    private Long organizationId;

}
