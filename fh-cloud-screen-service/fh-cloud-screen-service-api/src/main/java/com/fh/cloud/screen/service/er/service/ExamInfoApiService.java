package com.fh.cloud.screen.service.er.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.er.api.ExamInfoApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ImportExamInfoModel;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStatisticsVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 考场_考试计划里面一次考试信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@FeignClient(contextId = "slow-1", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class, fallbackFactory = ExamInfoApiService.ExamInfoApiFallbackFactory.class)
@Component
public interface ExamInfoApiService extends ExamInfoApi {

    @Component
    class ExamInfoApiFallbackFactory implements FallbackFactory<ExamInfoApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ExamInfoApiFallbackFactory.class);

        @Override
        public ExamInfoApiService create(Throwable cause) {
            ExamInfoApiFallbackFactory.LOGGER.error("云屏服务服务调用失败:{}", cause.getMessage());
            return new ExamInfoApiService() {
                public AjaxResult getExamInfoPageListByCondition(ExamInfoConditionBo condition) {
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getExamInfoListByCondition(ExamInfoConditionBo condition) {
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addExamInfo(ExamInfoBo Bo) {
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateExamInfo(ExamInfoBo Bo) {
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id) {
                    return AjaxResult.fail("查询详情失败");
                }

                public AjaxResult delete(Long id) {
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult importExamInfo(ImportExamInfoModel importExamInfoModel) {
                    return AjaxResult.fail("导入失败");
                }

                @Override
                public AjaxResult<List<ExamInfoSubjectVo>> listSubjectByExamInfoId(Long examInfoId) {
                    return AjaxResult.fail("查询考试科目失败");
                }

                @Override
                public AjaxResult<List<ExamInfoStudentVo>> attendanceMember(ExamInfoConditionBo examInfoConditionBo) {
                    return AjaxResult.fail("查询考试学生失败");
                }

                @Override
                public AjaxResult<ExamInfoStatisticsVo> attendanceStatistics(ExamInfoConditionBo examInfoConditionBo) {
                    return AjaxResult.fail("查询考试统计信息失败");
                }
            };
        }
    }
}