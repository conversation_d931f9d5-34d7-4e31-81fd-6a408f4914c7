package com.fh.cloud.screen.service.er.entity.bo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/10
 */
@Data
public class ExamInfoImportChildrenModel implements Serializable {
    /**
     * 考试科目
     */
    @Excel(name = "*考试科目", isImportField = "true")
    private String subjectName;

    /**
     * 准考证起止
     */
    @Excel(name = "准考证号起止", isImportField = "true")
    private String atNo;

    /**
     * 考试时间
     */
    @Excel(name = "*考试时间", isImportField = "true")
    private String examTime;

    /**
     * 教师的姓名
     */
    @Excel(name = "本校监考老师", isImportField = "true")
    private String schoolTeacherName;

    /**
     * 教师的姓名
     */
    @Excel(name = "校外监考教师", isImportField = "true")
    private String outTeacherName;

    /**
     * 学生的姓名
     */
    @Excel(name = "本校考生", isImportField = "true")
    private String schoolStudentName;

    /**
     * 学生的姓名
     */
    @Excel(name = "校外考生", isImportField = "true")
    private String outStudentName;
}
