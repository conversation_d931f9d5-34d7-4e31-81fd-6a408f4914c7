package com.fh.cloud.screen.service.leaveschool.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 放学配置表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
@FeignClient(contextId = "leaveSchoolConfigApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = LeaveSchoolConfigApiService.LeaveSchoolConfigApiFallbackFactory.class)
@Component
public interface LeaveSchoolConfigApiService extends LeaveSchoolConfigApi {

    @Component
    class LeaveSchoolConfigApiFallbackFactory implements FallbackFactory<LeaveSchoolConfigApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(LeaveSchoolConfigApiFallbackFactory.class);
        @Override
        public LeaveSchoolConfigApiService create(Throwable cause) {
            LeaveSchoolConfigApiFallbackFactory.LOGGER.error("${feignServiceName}服务调用失败:{}", cause.getMessage());
            return new LeaveSchoolConfigApiService() {
                public AjaxResult getLeaveSchoolConfigPageListByCondition(LeaveSchoolConfigConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getLeaveSchoolConfigListByCondition(LeaveSchoolConfigConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addLeaveSchoolConfig(LeaveSchoolConfigBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateLeaveSchoolConfig(LeaveSchoolConfigBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                public AjaxResult getLeaveSchoolConfig(LeaveSchoolConfigConditionBo condition) {
                    return AjaxResult.fail("获取放学配置失败");
                }

                public AjaxResult getTeacherClasses() {
                    return AjaxResult.fail("获取班主任班级列表失败");
                }

                public AjaxResult getClazzList(ClazzConditionBoExt clazzConditionBoExt) {
                    return AjaxResult.fail("获取班级列表失败");
                }

                @Override
                public AjaxResult getLeaveSchoolOrganizationList() {
                    return AjaxResult.fail("获取开了放学的组织列表");
                }
            };
        }
    }
}