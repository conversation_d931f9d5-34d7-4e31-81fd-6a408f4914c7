package com.fh.cloud.screen.service.space.api;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.light.core.entity.AjaxResult;

public interface SpaceInfoApi {

    /**
     * 查询区域信息表列表
     */
    @PostMapping("/space-info/list")
    AjaxResult getSpaceInfoListByCondition(@RequestBody SpaceInfoListConditionBo condition);

    /**
     * 查询区域信息
     *
     * @param condition
     * @return
     */
    @PostMapping("/space-info/query")
    public AjaxResult queryList(@RequestBody SpaceInfoListConditionBo condition);

    /**
     * 新增区域信息表
     */
    @PostMapping("/space-info/add")
    AjaxResult addSpaceInfo(@RequestBody SpaceInfoBo spaceInfoBo);

    /**
     * 修改区域信息表
     */
    @PostMapping("/space-info/update")
    AjaxResult updateSpaceInfo(@RequestBody SpaceInfoBo spaceInfoBo);

    /**
     * 根据ID获取地址信息
     *
     * @param spaceInfoId
     * @return
     */
    @GetMapping("/space-info/id/{spaceInfoId}")
    AjaxResult<SpaceInfoVo> getById(@PathVariable("spaceInfoId") Long spaceInfoId);

    /**
     * 删除区域信息表
     */
    @GetMapping("/space-info/delete")
    AjaxResult delete(@RequestParam("spaceInfoId") Long spaceInfoId);

    /**
     * 查询校区表列表
     */
    @GetMapping("/campus/list")
    AjaxResult getCampusListByCondition(@RequestParam("organizationId") Long organizationId);

    /**
     * 获取地点信息（可以获取行政班级或者非行政班级的地点信息）
     *
     * @param screenBusinessBo the screen business bo
     * @return space info
     */
    @PostMapping("/space-info/detail")
    AjaxResult getSpaceInfo(@RequestBody ScreenBusinessBo screenBusinessBo);

    /**
     * 查询区域设备数
     */
    @PostMapping("/space-info/device-count")
    AjaxResult getSpaceInfoDeviceCountByCondition(@RequestBody SpaceInfoListConditionBo condition);

    /**
     * 查询空间设备列表（不包含没有设备的地点）  根据监管教育局id批量查询多校地点设备
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/7/31 15:50
     **/
    @PostMapping("/space-info/device-list")
    AjaxResult getSpaceDeviceListByCondition(@RequestBody SpaceInfoListConditionBo condition);

    /**
     * 查询教育局监管学校设备地点
     *
     * @param parentOrganizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/2 9:52
     **/
    @GetMapping("/supervise-space/by-parentId")
    public AjaxResult getSuperviseSpaceByParentId(@RequestParam("parentOrganizationId") Long parentOrganizationId);
}
