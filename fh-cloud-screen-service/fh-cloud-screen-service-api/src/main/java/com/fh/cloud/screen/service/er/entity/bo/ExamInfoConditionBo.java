package com.fh.cloud.screen.service.er.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 考场_考试计划里面一次考试信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
public class ExamInfoConditionBo extends PageLimitBo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long examInfoId;

    /**
     * 考试计划id
     */
    @ApiModelProperty("考试计划id")
    private Long examPlanId;

    /**
     * 考场号（名称）
     */
    @ApiModelProperty("考场号（名称）")
    private String examRoomName;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 地点id
     */
    @ApiModelProperty("地点id")
    private Long spaceInfoId;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 考场号（名称）
     */
    @ApiModelProperty("考场号（名称）")
    private List<String> examRoomNames;

    /**
     * 考试科目code-查询
     */
    @ApiModelProperty("考试科目code-查询")
    private String subjectCode;

    /**
     * 地点组id
     */
    private Long spaceGroupId;

    /**
     * 地点组名称
     */
    private String spaceGroupName;

    /**
     * 科目名称
     */
    private String subjectName;
    /**
     * 考场_考试计划里面一次考试科目信息Id，包含了考试、地点科目信息，用于查询本次考试的学生信息
     */
    private Long examInfoSubjectId;

    /**
     * 打卡状态，空查询全部，1：未签到(ExamEnums.SIGN_ATTENDANCE_NOT)，2(ExamEnums.SIGN_ATTENDANCE_YES)：已签到（注意底层查询需要转换成2或3来查询）---> 转换成attendanceStatus
     */
    private Integer attendanceSignStatus;
}
