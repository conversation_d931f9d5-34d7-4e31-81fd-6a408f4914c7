package com.fh.cloud.screen.service.er.api;


import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 考场_考试计划涉及的年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamPlanGradeApi {

    /**
     * 查询考场_考试计划涉及的年级分页列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/grade/page/list")
    public AjaxResult<PageInfo<ExamPlanGradeVo>> getExamPlanGradePageListByCondition(@RequestBody ExamPlanGradeConditionBo condition);

    /**
     * 查询考场_考试计划涉及的年级列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/grade/list")
    public AjaxResult<List<ExamPlanGradeVo>> getExamPlanGradeListByCondition(@RequestBody ExamPlanGradeConditionBo condition);


    /**
     * 新增考场_考试计划涉及的年级
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/grade/add")
    public AjaxResult addExamPlanGrade(@Validated @RequestBody ExamPlanGradeBo examPlanGradeBo);

    /**
     * 修改考场_考试计划涉及的年级
     * @param examPlanGradeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/exam/plan/grade/update")
    public AjaxResult updateExamPlanGrade(@Validated @RequestBody ExamPlanGradeBo examPlanGradeBo);

    /**
     * 查询考场_考试计划涉及的年级详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/plan/grade/detail")
    public AjaxResult<ExamPlanGradeVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除考场_考试计划涉及的年级
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/exam/plan/grade/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);
}
