package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏内容详情表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
public class ScreenContentDetailListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long screenContentDetailId;

    /**
     * FK云屏内容表
     */
    private Long screenContentId;

    /**
     * 顺序
     */
    private Long screenContentIndex;

    /**
     * 云屏内容-标题
     */
    private String screenContentTitle;

    /**
     * 云屏内容-文本
     */
    private String screenContentTxt;

    /**
     * 云屏内容-url
     */
    private String screenContentUrl;

    /**
     * 云屏图片或者视频媒体地址
     */
    private String screenContentMediaUrl;

    /**
     * 云屏图片或者视频媒体地址-压缩后
     */
    private String screenContentMediaUrlCompress;

    /**
     * 云屏图片或者视频媒体地址-封面
     */
    private String screenContentMediaUrlCover;

    /**
     * 云屏图片或者视频媒体名称（不包含后缀）
     */
    private String screenContentMediaName;

    /**
     * 云屏图片或者视频原始媒体名称（包含后缀）
     */
    private String screenContentMediaNameOri;

    /**
     * 有效时间-开始时间
     */
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    private Date endTime;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 称呼内容
     */
    private String callContent;

    /**
     * 落款内容
     */
    private String signContent;

    /**
     * 云屏图片或者视频媒体fileoid
     */
    private String screenContentMediaId;

    /**
     * 云屏图片或者视频媒体fileoid-压缩后
     */
    private String screenContentMediaIdCompress;

    /**
     * 适用设备模式：1横屏，2竖屏。
     */
    private Integer screenDevicePattern;

    /**
     * 云屏图片或者视频媒体md5
     */
    @ApiModelProperty("云屏图片或者视频媒体md5")
    private String screenContentMediaMd5;

    /**
     * 云屏图片或者视频媒体md5-压缩后
     */
    @ApiModelProperty("云屏图片或者视频媒体md5-压缩后")
    private String screenContentMediaMd5Compress;
}
