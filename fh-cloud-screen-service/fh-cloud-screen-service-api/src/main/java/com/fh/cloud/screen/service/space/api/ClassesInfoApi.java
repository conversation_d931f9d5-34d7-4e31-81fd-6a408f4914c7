package com.fh.cloud.screen.service.space.api;

import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoBo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

public interface ClassesInfoApi {

    /**
     * 新增行政区域内容扩展信息表（班级扩展信息）
     *
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @PostMapping("/space/classes-info/add")
    public AjaxResult addClassesInfo(@RequestBody ClassesInfoBo classesInfoBo);

    /**
     * 修改行政区域内容扩展信息表（班级扩展信息）
     *
     * @param classesInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @PostMapping("/space/classes-info/update")
    public AjaxResult updateClassesInfo(@RequestBody ClassesInfoBo classesInfoBo);

    /**
     * 查询行政区域内容扩展信息表（班级扩展信息）详情
     *
     * @param classesInfoId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @GetMapping("/space/classes-info/detail")
    public AjaxResult getDetail(@RequestParam("classesInfoId") Long classesInfoId);

    /**
     * 删除行政区域内容扩展信息表（班级扩展信息）
     *
     * @param classesInfoId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @GetMapping("/space/classes-info/delete")
    public AjaxResult delete(@RequestParam("classesInfoId") Long classesInfoId);

    /**
     * 根据ID获取地址信息
     * 
     * @param classInfoId
     * @return
     */
    @GetMapping("/space/classes-info/id/{classInfoId}")
    AjaxResult<ClassesInfoVo> getById(@PathVariable("classInfoId") Long classInfoId);

    /**
     * 根据班级ID获取 地址信息
     * 
     * @param classesId
     * @return
     */
    @GetMapping("/space/classes-info/classesId/{classesId}")
    AjaxResult<ClassesInfoVo> getByClassesId(@PathVariable("classesId") Long classesId);
}
