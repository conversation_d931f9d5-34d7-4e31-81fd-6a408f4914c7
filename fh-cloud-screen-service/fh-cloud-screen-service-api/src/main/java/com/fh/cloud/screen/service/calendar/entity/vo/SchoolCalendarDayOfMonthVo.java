package com.fh.cloud.screen.service.calendar.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 学校 校历 每天上课状态VO
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Data
public class SchoolCalendarDayOfMonthVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所属组织ID
     */
    private Long organizationId;

    /**
     * 日 yyyy-MM-dd
     */
    private String dayDate;

    /**
     * 日
     */
    private Integer day;

    /**
     * 周几
     */
    private Integer week;

    /**
     * 上课状态： 1 上课 2 不上课
     */
    private Integer status;

}
