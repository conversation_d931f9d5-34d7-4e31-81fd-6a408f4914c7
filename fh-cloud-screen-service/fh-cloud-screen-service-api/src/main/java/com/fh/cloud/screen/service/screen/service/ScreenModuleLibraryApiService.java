package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25 16:08
 */
@FeignClient(contextId = "screenModuleLibraryApiService", value = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    configuration = FeignClientInterceptor.class,
    fallbackFactory = ScreenModuleLibraryApiService.ScreenModuleLibraryApiFallbackFactory.class)
@Component
public interface ScreenModuleLibraryApiService extends ScreenModuleLibraryApi {
    @Component
    class ScreenModuleLibraryApiFallbackFactory implements FallbackFactory<ScreenModuleLibraryApiService> {
        private static final Logger LOGGER =
            LoggerFactory.getLogger(ScreenModuleLibraryApiService.ScreenModuleLibraryApiFallbackFactory.class);

        @Override
        public ScreenModuleLibraryApiService create(Throwable cause) {
            ScreenModuleLibraryApiService.ScreenModuleLibraryApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}",
                cause.getMessage());

            return new ScreenModuleLibraryApiService() {
                public AjaxResult getScreenModuleLibraryListByCondition(ScreenModuleLibraryListConditionBo condition) {
                    return AjaxResult.fail("查询云屏模块库列表失败");
                }

                public AjaxResult
                    getScreenModuleLibraryGroupMapByCondition(ScreenModuleLibraryListConditionBo condition) {
                    return AjaxResult.fail("查询云屏模块库分组显示失败");
                }

                public AjaxResult addScreenModuleLibrary(ScreenModuleLibraryBo screenModuleLibraryBo) {
                    return AjaxResult.fail("新增云屏模块库失败");
                }

                public AjaxResult updateScreenModuleLibrary(ScreenModuleLibraryBo screenModuleLibraryBo) {
                    return AjaxResult.fail("更新云屏模块库失败");
                }

                public AjaxResult getDetail(Long screenModuleLibraryId) {
                    return AjaxResult.fail("查询云屏模块库详情失败");
                }

                public AjaxResult delete(Long screenModuleLibraryId) {
                    return AjaxResult.fail("删除云屏模块库失败");
                }

                public AjaxResult exchange(Long firstId, Long secondId) {
                    return AjaxResult.fail("交换顺序失败");
                }

                @Override
                public AjaxResult updateLibrarySortByIdList(List<Long> idList) {
                    return AjaxResult.fail("更新顺序失败");
                }

                @Override
                public AjaxResult getPosterList(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("获取海报条件查询主题列表失败");
                }

                @Override
                public AjaxResult getDevicePosterListByDeviceNumber(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("获取设备关联标签关联海报主题列表失败");
                }

                @Override
                public AjaxResult getLabelPosterListByCondition(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("获取标签海报列表失败");
                }

                @Override
                public AjaxResult getLabelPosterStatistics(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("获取标签海报统计信息失败");
                }

                @Override
                public AjaxResult updateLibrary(ScreenModuleLibraryBo screenModuleLibraryBo) {
                    return AjaxResult.fail("新增、编辑海报主题失败");
                }

                @Override
                public AjaxResult getPersonPosters(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("获取我创建的海报主题列表失败");
                }

                @Override
                public AjaxResult getPosterListSel(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("海报条件和分页查询主题列表根据已选择主题查询失败");
                }

                @Override
                public AjaxResult getPosterSchoolNum(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("查询校本海报数量失败");
                }

                @Override
                public AjaxResult getPosterMediaSchoolNum(ScreenModuleLibraryListConditionBo conditionBo) {
                    return AjaxResult.fail("查询校本海报库海报数量失败");
                }
            };
        }
    }
}
