package com.fh.cloud.screen.service.leaveschool.api;


import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordVo;

import com.light.core.entity.AjaxResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.github.pagehelper.PageInfo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
/**
 * 放学记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
public interface LeaveSchoolRecordApi {

    /**
     * 查询放学记录表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @PostMapping("/leave/school/record/page/list")
    public AjaxResult<PageInfo<LeaveSchoolRecordVo>> getLeaveSchoolRecordPageListByCondition(@RequestBody LeaveSchoolRecordConditionBo condition);

    /**
     * 查询放学记录表列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @PostMapping("/leave/school/record/list")
    public AjaxResult<List<LeaveSchoolRecordVo>> getLeaveSchoolRecordListByCondition(@RequestBody LeaveSchoolRecordConditionBo condition);


    /**
     * 新增放学记录表
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @PostMapping("/leave/school/record/add")
    public AjaxResult addLeaveSchoolRecord(@Validated @RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo);

    /**
     * 修改放学记录表
     * @param leaveSchoolRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @PostMapping("/leave/school/record/update")
    public AjaxResult updateLeaveSchoolRecord(@Validated @RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo);

    /**
     * 查询放学记录表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @GetMapping("/leave/school/record/detail")
    public AjaxResult<LeaveSchoolRecordVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);


    /**
     * 删除放学记录表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @GetMapping("/leave/school/record/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 获取放学记录
     *
     * @param leaveSchoolRecordBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 19:44
     **/
    @PostMapping("/leave/school/record/get-record")
    public AjaxResult getLeaveSchoolRecord(@RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo);

}
