package com.fh.cloud.screen.service.leaveschool.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 放学播报信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
@Data
public class LeaveSchoolBroadcastInfoVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 放学播报信息表id
     */
    @ApiModelProperty("放学播报信息表id")
    private Long broadcastInfoId;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 校区id
     */
    @ApiModelProperty("校区id")
    private Long campusId;

    /**
     * 播报文字内容
     */
    @ApiModelProperty("播报文字内容")
    private String broadcastContent;

    /**
     * 播报文件oid
     */
    @ApiModelProperty("播报文件oid")
    private String broadcastId;

    /**
     * 播报文件url
     */
    @ApiModelProperty("播报文件url")
    private String broadcastUrl;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public LeaveSchoolBroadcastInfoVo returnOwn() {
        return this;
    }

    /**
     * 播放次数
     */
    @ApiModelProperty("播放次数")
    private Integer playTimes;
}
