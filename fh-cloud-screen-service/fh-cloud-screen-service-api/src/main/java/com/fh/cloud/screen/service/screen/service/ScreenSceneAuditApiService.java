package com.fh.cloud.screen.service.screen.service;


import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.fh.cloud.screen.service.screen.api.ScreenSceneAuditApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditConditionBo;
import com.light.core.constants.ServiceNameConstants;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 云屏场景审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
@FeignClient(contextId = "screenSceneAuditApiService", value= ConstServiceName.FH_CLOUD_SCREEN_SERVICE, configuration = FeignClientInterceptor.class, fallbackFactory = ScreenSceneAuditApiService.ScreenSceneAuditApiFallbackFactory.class)
@Component
public interface ScreenSceneAuditApiService extends ScreenSceneAuditApi {

    @Component
    class ScreenSceneAuditApiFallbackFactory implements FallbackFactory<ScreenSceneAuditApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(ScreenSceneAuditApiFallbackFactory.class);
        @Override
        public ScreenSceneAuditApiService create(Throwable cause) {
            ScreenSceneAuditApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());
            return new ScreenSceneAuditApiService() {
                public AjaxResult getScreenSceneAuditPageListByCondition(ScreenSceneAuditConditionBo condition){
                    return AjaxResult.fail("查询分页列表失败");
                }

                public AjaxResult getScreenSceneAuditListByCondition(ScreenSceneAuditConditionBo condition){
                    return AjaxResult.fail("查询列表失败");
                }

                public AjaxResult addScreenSceneAudit(ScreenSceneAuditBo Bo){
                    return AjaxResult.fail("新增失败");
                }

                public AjaxResult updateScreenSceneAudit(ScreenSceneAuditBo Bo){
                    return AjaxResult.fail("更新失败");
                }

                public AjaxResult getDetail(Long id){
                    return AjaxResult.fail("查询详情失败");
                }


                public AjaxResult delete(Long id){
                    return AjaxResult.fail("删除失败");
                }

                @Override
                public AjaxResult screenSceneAudit(ScreenSceneAuditBo screenSceneAuditBo) {
                    return AjaxResult.fail("审核失败");
                }

                @Override
                public AjaxResult screenSceneAuditBatch(ScreenSceneAuditBo screenSceneAuditBo) {
                    return AjaxResult.fail("批量审核失败");
                }

                @Override
                public AjaxResult screenToAudit(Long screenSceneAuditId) {
                    return AjaxResult.fail("获取待发布云屏数据失败");
                }

                @Override
                public AjaxResult getScreenSceneAuditCount(ScreenSceneAuditConditionBo condition) {
                    return AjaxResult.fail("获取审核数量失败");
                }

            };
        }
    }
}