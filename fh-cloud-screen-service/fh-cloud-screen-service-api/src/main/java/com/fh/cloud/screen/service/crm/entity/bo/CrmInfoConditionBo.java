package com.fh.cloud.screen.service.crm.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * CRM商讯管理表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@Data
public class CrmInfoConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long crmInfoId;

	/**
	 * 登记人员姓名
	 */
	@ApiModelProperty("登记人员姓名")
	private String saleName;

	/**
	 * 学校名称
	 */
	@ApiModelProperty("学校名称")
	private String schoolName;

	/**
	 * 省id
	 */
	@ApiModelProperty("省id")
	private Long provinceId;

	/**
	 * 省名称
	 */
	@ApiModelProperty("省名称")
	private String provinceName;

	/**
	 * 市id
	 */
	@ApiModelProperty("市id")
	private Long cityId;

	/**
	 * 市名称
	 */
	@ApiModelProperty("市名称")
	private String cityName;

	/**
	 * 县区id
	 */
	@ApiModelProperty("县区id")
	private Long areaId;

	/**
	 * 县区名称
	 */
	@ApiModelProperty("县区名称")
	private String areaName;

	/**
	 * 地址
	 */
	@ApiModelProperty("地址")
	private String address;

	/**
	 * 学校对文化重视程度:1重视，2一般，3不重视
	 */
	@ApiModelProperty("学校对文化重视程度:1重视，2一般，3不重视")
	private Integer schoolCulturalFocus;

	/**
	 * 文化已经做了的工作
	 */
	@ApiModelProperty("文化已经做了的工作")
	private String culturalWorked;

	/**
	 * 是否有校园文化建设投入：1有，2无
	 */
	@ApiModelProperty("是否有校园文化建设投入：1有，2无")
	private Integer culturalInputType;

	/**
	 * 学校领导对文化重视程度:1重视，2一般，3不重视
	 */
	@ApiModelProperty("学校领导对文化重视程度:1重视，2一般，3不重视")
	private Integer leaderCulturalFocus;

	/**
	 * 评估是否有商业机会：1是，2否
	 */
	@ApiModelProperty("评估是否有商业机会：1是，2否")
	private Integer opportunityType;

	/**
	 * 评估是否有商业机会的依据
	 */
	@ApiModelProperty("评估是否有商业机会的依据")
	private String opportunityReason;

	/**
	 * 班牌运行情况：1正常使用，2偶尔使用，3常闭
	 */
	@ApiModelProperty("班牌运行情况：1正常使用，2偶尔使用，3常闭")
	private Integer brandWorkingType;

	/**
	 * 班牌是否适配云屏应用：1是，2否
	 */
	@ApiModelProperty("班牌是否适配云屏应用：1是，2否")
	private Integer brandAdapterType;

	/**
	 * 班牌不适配云屏应用的原因
	 */
	@ApiModelProperty("班牌不适配云屏应用的原因")
	private String brandNotAdapterReason;

	/**
	 * 学校班牌型号
	 */
	@ApiModelProperty("学校班牌型号")
	private String brandModel;

	/**
	 * 学校班牌数量
	 */
	@ApiModelProperty("学校班牌数量")
	private String brandNumber;

	/**
	 * 学校类型（即教育级别类型）：1学前，2小学，3初中，4高中，5九年一贯制，6十二年一贯制，7完中，8职高，9大学
	 */
	@ApiModelProperty("学校类型（即教育级别类型）：1学前，2小学，3初中，4高中，5九年一贯制，6十二年一贯制，7完中，8职高，9大学")
	private Integer schoolLevelType;

	/**
	 * 学校排名类型：1优，2良，3差
	 */
	@ApiModelProperty("学校排名类型：1优，2良，3差")
	private Integer schoolRankingType;

	/**
	 * 学校规模
	 */
	@ApiModelProperty("学校规模")
	private String schoolScale;

	/**
	 * 学校跟踪项目，多个使用英文逗号分割
	 */
	@ApiModelProperty("学校跟踪项目，多个使用英文逗号分割")
	private String schoolTrackProject;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
