package com.fh.cloud.screen.service.device.api;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

/**
 * 云屏全屏非全屏设置自定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
public interface ShowDeviceFullCustomApi {

    /**
     * 查询云屏全屏非全屏设置自定义分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @PostMapping("/show/device/full/custom/page/list")
    public AjaxResult<PageInfo<ShowDeviceFullCustomVo>>
        getShowDeviceFullCustomPageListByCondition(@RequestBody ShowDeviceFullCustomConditionBo condition);

    /**
     * 查询云屏全屏非全屏设置自定义列表
     * 
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @PostMapping("/show/device/full/custom/list")
    public AjaxResult<List<ShowDeviceFullCustomVo>>
        getShowDeviceFullCustomListByCondition(@RequestBody ShowDeviceFullCustomConditionBo condition);

    /**
     * 新增云屏全屏非全屏设置自定义
     * 
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @PostMapping("/show/device/full/custom/add")
    public AjaxResult addShowDeviceFullCustom(@Validated @RequestBody ShowDeviceFullCustomBo showDeviceFullCustomBo);

    /**
     * 修改云屏全屏非全屏设置自定义
     * 
     * @param showDeviceFullCustomBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @PostMapping("/show/device/full/custom/update")
    public AjaxResult updateShowDeviceFullCustom(@Validated @RequestBody ShowDeviceFullCustomBo showDeviceFullCustomBo);

    /**
     * 查询云屏全屏非全屏设置自定义详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @GetMapping("/show/device/full/custom/detail")
    public AjaxResult<ShowDeviceFullCustomVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

    /**
     * 删除云屏全屏非全屏设置自定义
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @GetMapping("/show/device/full/custom/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("id") Long id);

}
