package com.fh.cloud.screen.service.leaveschool.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 放学记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
@Data
public class LeaveSchoolRecordVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 放学记录表id
     */
    @ApiModelProperty("放学记录表id")
    private Long leaveSchoolRecordId;

    /**
     * 组织id
     */
    @ApiModelProperty("组织id")
    private Long organizationId;

    /**
     * 校区id
     */
    @ApiModelProperty("校区id")
    private Long campusId;

    /**
     * 区域id或者classesId
     */
    @ApiModelProperty("区域id或者classesId")
    private Long spaceInfoId;

    /**
     * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
     */
    @ApiModelProperty("区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室")
    private Integer spaceGroupUseType;

    /**
     * 放学状态 1-未放学 2-放学中 3-已放学
     */
    @ApiModelProperty("放学状态 1-未放学 2-放学中 3-已放学")
    private Integer leaveSchoolType;

    /**
     * 放学日期
     */
    @ApiModelProperty("放学日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date leaveSchoolDay;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /**
     * 操作记录
     */
    @ApiModelProperty("操作记录")
    private List<LeaveSchoolRecordOperateVo> operateList;

    @ApiModelProperty("放学自动确认时间（单位：分钟）")
    private Integer autoConfirmTime;

    /*
     * 方便steam流存入自身
     * */
    public LeaveSchoolRecordVo returnOwn() {
        return this;
    }

}
