package com.fh.cloud.screen.service.attendance.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 一天三次考勤的用户信息展示vo
 * 
 * <AUTHOR>
 * @date 2023/9/15 13:47
 */
@Data
public class AttendanceUserResultVo implements Serializable {
    /**
     * 考勤规则id
     */
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    private Integer attendanceType;

    /**
     * 考勤一组顺序：1，2，3...
     */
    private Integer attendanceRuleDayIndex;

    /**
     * 考勤 日
     */
    private String attendanceDay;

    /**
     * 一天所有的考勤记录
     */
    private List<AttendanceUserAllDayVo> attendanceUserAllDayVos;
}
