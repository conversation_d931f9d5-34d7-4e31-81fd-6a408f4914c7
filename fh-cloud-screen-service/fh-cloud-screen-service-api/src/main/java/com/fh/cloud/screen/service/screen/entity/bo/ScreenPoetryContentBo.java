package com.fh.cloud.screen.service.screen.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 共话诗词表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
@Data
public class ScreenPoetryContentBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenPoetryContentId;

	/**
	 * FK云屏模块表id
	 */
	@ApiModelProperty("FK云屏模块表id")
	private Long screenModuleDataId;

	/**
	 * 共话诗词标题
	 */
	@ApiModelProperty("共话诗词标题")
	private String screenPoetryContentTitle;

	/**
	 * 共话诗词文本内容
	 */
	@ApiModelProperty("共话诗词文本内容")
	private String screenPoetryContentTxt;

	/**
	 * FK所属班级ID
	 */
	@ApiModelProperty("FK所属班级ID")
	private Long classesId;

	/**
	 * 创建人oid
	 */
	@ApiModelProperty("创建人oid")
	private String createBy;

	/**
	 * 【冗余】创建人姓名
	 */
	@ApiModelProperty("【冗余】创建人姓名")
	private String createUserName;

	/**
	 * 【冗余】创建人班级名称
	 */
	@ApiModelProperty("【冗余】创建人班级名称")
	private String createUserClassesName;



	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
