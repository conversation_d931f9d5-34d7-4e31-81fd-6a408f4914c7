package com.fh.cloud.screen.service.screen.entity.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 云屏移动端H5首页vo
 * 
 * <AUTHOR>
 * @date 2023/5/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ScreenModuleLibraryGroupVo extends ScreenModuleLibraryMediaVo implements Serializable {
    /**
     * 海报分组下属的主题ids
     */
    private String stringLibraryIds;

    /**
     * 海报关联的标签id
     */
    private Long labelId;

    /**
     * 海报关联的标签名称
     */
    private String labelName;

    /**
     * 移动端首页需每个标签分组下需要展示的海报ids
     */
    private List<Long> topNScreenModuleLibraryIds;

    /**
     * 海报map的list，用于返回到h5首页
     */
    private List<LinkedHashMap> screenModuleLibraryMapList;
}
