package com.fh.cloud.screen.service.screen.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 第三方对接云屏场景信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
@Data
public class ScreenSceneThirdConditionBo extends PageLimitBo{

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long screenSceneThirdId;

	/**
	 * 名称
	 */
	@ApiModelProperty("名称")
	private String screenSceneThirdName;

	/**
	 * 第三方场景类型：1课后服务
	 */
	@ApiModelProperty("第三方场景类型：1课后服务")
	private Integer screenSceneThirdType;

	/**
	 * 第三方场景开始时间
	 */
	@ApiModelProperty("第三方场景开始时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date screenSceneThirdStartTime;

	/**
	 * 第三方场景结束时间
	 */
	@ApiModelProperty("第三方场景结束时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date screenSceneThirdEndTime;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private Long organizationId;

	/**
	 * FK所属校区ID
	 */
	@ApiModelProperty("FK所属校区ID")
	private Long campusId;

	/**
	 * FK空间ID或者班级id
	 */
	@ApiModelProperty("FK空间ID或者班级id")
	private Long spaceInfoId;

	/**
	 * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
	 */
	@ApiModelProperty("区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室")
	private Integer spaceGroupUseType;

	/**
	 * FK设备id
	 */
	@ApiModelProperty("FK设备id")
	private Long showDeviceId;

	/**
	 * 第三方场景展示内容页面url
	 */
	@ApiModelProperty("第三方场景展示内容页面url")
	private String screenSceneThirdShowUrl;

	/**
	 * 第三方场景提交数据url
	 */
	@ApiModelProperty("第三方场景提交数据url")
	private String screenSceneThirdPostUrl;





	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 应用code
	 */
	@ApiModelProperty("应用code")
	private String appCode;

	@ApiModelProperty("开始更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startUpdateTime;

	@ApiModelProperty("结束更新时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endUpdateTime;

	/**
	 * 场景提前N分钟在云屏上面展示，默认提前30分钟
	 */
	@ApiModelProperty("场景提前N分钟在云屏上面展示，默认提前30分钟")
	private Integer screenSceneThirdDelayMinute;

	/**
	 * 冗余的关联该场景的第三方业务id
	 */
	@ApiModelProperty("场景提前N分钟在云屏上面展示，默认提前30分钟")
	private String thirdBizId;
}
