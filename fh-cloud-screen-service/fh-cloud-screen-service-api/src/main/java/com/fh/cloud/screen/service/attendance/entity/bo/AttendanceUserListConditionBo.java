package com.fh.cloud.screen.service.attendance.entity.bo;

import com.light.core.entity.PageLimitBo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
public class AttendanceUserListConditionBo extends PageLimitBo implements Serializable {

    /**
     * 主键
     */
    private Long attendanceUserId;

    /**
     * 该用户当天考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，5迟到早退，6缺卡
     */
    private Integer attendanceRecordType;

    /**
     * 考勤规则id，当天考勤使用的考勤规则
     */
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    private Integer attendanceType;

    /**
     * 考勤日期:yyyy-MM-dd
     */
    private String attendanceDate;

    /**
     * 考勤 日
     */
    private String attendanceDay;

    /**
     * 考勤 月
     */
    private String attendanceMonth;

    /**
     * 用户oid
     */
    private String userOid;

    /**
     * 用户OID列表
     */
    private List<String> userOids;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 组织机构ID
     */
    private Long organizationId;

    /**
     * 年级
     */
    private String grade;

    /**
     * 班级
     */
    private Long classesId;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 考勤顺序
     */
    private Integer attendanceRuleDayIndex;
}
