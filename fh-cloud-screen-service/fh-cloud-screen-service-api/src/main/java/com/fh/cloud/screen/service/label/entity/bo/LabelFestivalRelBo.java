package com.fh.cloud.screen.service.label.entity.bo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 标签节日关联表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@Data
public class LabelFestivalRelBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * 节日表关联key
	 */
	@ApiModelProperty("节日表关联key")
	private String festivalCode;

	/**
	 * 标签表主键
	 */
	@ApiModelProperty("标签表主键")
	private Long labelId;

	/**
	 * 添加时 需要添加的标签列表
	 */
	List<LabelBo> labelBos;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
