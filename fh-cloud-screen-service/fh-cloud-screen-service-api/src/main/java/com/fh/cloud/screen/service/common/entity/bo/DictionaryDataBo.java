package com.fh.cloud.screen.service.common.entity.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/30 17:04
 */
@Data
public class DictionaryDataBo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典值
     */
    private String dictValue;

    /**
     * 字典名称
     */
    private String dictLabel;

    /**
     * 字典排序
     */
    private Integer dictSort;

    /**
     * 字典值备注
     */
    private String dictDataRemark;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 组织id
     */
    private Long organizationId;

    /**
     * 类型：1非海报，2海报展示分组，3海报不展示分组
     */
    private Integer type;
}
