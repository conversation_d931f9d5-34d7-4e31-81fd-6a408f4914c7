package com.fh.cloud.screen.service.screenConfig.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 云屏配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
@Data
public class ScreenConfigVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long screenConfigId;

    /**
     * 所属组织ID
     */
    @ApiModelProperty("所属组织ID")
    private Long organizationId;

    /**
     * 配置信息
     */
    @ApiModelProperty("配置信息")
    private String configValue;

    /**
     * 品牌配置信息
     */
    @ApiModelProperty("品牌配置信息")
    private String brandConfigValue;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String remark;

    /**
     * 配置类型 1-偏好配置
     */
    @ApiModelProperty("配置类型 1-偏好配置")
    private Integer type;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ScreenConfigVo returnOwn() {
        return this;
    }

}
