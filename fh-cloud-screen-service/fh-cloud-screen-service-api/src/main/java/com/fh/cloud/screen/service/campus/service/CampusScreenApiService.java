package com.fh.cloud.screen.service.campus.service;

import com.fh.cloud.screen.service.campus.api.CampusScreenApi;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.consts.ConstServiceName;
import com.light.core.entity.AjaxResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

@FeignClient(name = ConstServiceName.FH_CLOUD_SCREEN_SERVICE,
    fallbackFactory = CampusScreenApiService.CampusApiFallbackFactory.class)
@Component
public interface CampusScreenApiService extends CampusScreenApi {
    @Component
    class CampusApiFallbackFactory implements FallbackFactory<CampusScreenApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(CampusApiFallbackFactory.class);

        @Override
        public CampusScreenApiService create(Throwable cause) {
            CampusApiFallbackFactory.LOGGER.error("云屏服务调用失败:{}", cause.getMessage());

            return new CampusScreenApiService() {

                @Override
                public AjaxResult getCampusByCondition(CampusListConditionBo condition) {
                    return AjaxResult.fail("校区查询失败");
                }
            };
        }
    }
}
