package com.fh.cloud.screen.service.rest.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间年级活动课设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
public class WorkRestGradeActivityVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long workRestGradeActivityId;

    /**
     * FK作息时间年级表主键
     */
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    private Long workRestId;

    /**
     * 活动课名称
     */
    private String activityName;

    /**
     * 活动课节次：1，2，3...
     */
    private Integer activityPosition;

    /**
     * 活动课节次类型：1在sort节次之前，2在sort节次之后
     */
    private Integer activitySortType;

    /**
     * 更新时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
