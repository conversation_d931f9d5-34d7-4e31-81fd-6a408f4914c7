package com.fh.cloud.screen.service.screen.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 云屏产品咨询收集联系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
@Data
public class ScreenContactVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String schoolName;

    /**
     * 联系方式电话
     */
    @ApiModelProperty("联系方式电话")
    private String contactPhone;

    /**
     * 咨询意向
     */
    @ApiModelProperty("咨询意向")
    private String intention;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @ApiModelProperty("是否删除，0：否，1：是")
    private Integer isDelete;

    /*
     * 方便steam流存入自身
     * */
    public ScreenContactVo returnOwn() {
        return this;
    }

}
