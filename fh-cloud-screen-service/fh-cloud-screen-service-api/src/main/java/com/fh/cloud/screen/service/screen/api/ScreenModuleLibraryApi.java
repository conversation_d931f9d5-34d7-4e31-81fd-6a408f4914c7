package com.fh.cloud.screen.service.screen.api;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface ScreenModuleLibraryApi {

    /**
     * 查询云屏模块库列表
     *
     * <AUTHOR>
     * @date 2022-05-26 11:17:09
     */
    @PostMapping("/screen/module-library/list")
    @ApiOperation(value = "查询模块库表列表", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryListByCondition(@RequestBody ScreenModuleLibraryListConditionBo condition);

    /**
     * 查询云屏模块库(分组显示)
     *
     * <AUTHOR>
     * @date 2022-05-26 11:17:09
     */
    @PostMapping("/screen/module-library/group")
    @ApiOperation(value = "查询模块库表分组显示", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryGroupMapByCondition(@RequestBody ScreenModuleLibraryListConditionBo condition);

    /**
     * 新增模块库表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/screen/module-library/add")
    @ApiOperation(value = "新增模块库表", httpMethod = "POST")
    AjaxResult addScreenModuleLibrary(@RequestBody ScreenModuleLibraryBo screenModuleLibraryBo);

    /**
     * 修改模块库表
     *
     * @param screenModuleLibraryBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改模块库表", httpMethod = "POST")
    AjaxResult updateScreenModuleLibrary(@RequestBody ScreenModuleLibraryBo screenModuleLibraryBo);

    /**
     * 查询模块库表详情
     *
     * @param screenModuleLibraryId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询模块库表详情", httpMethod = "GET")
    AjaxResult getDetail(@RequestParam("screenModuleLibraryId") Long screenModuleLibraryId);

    /**
     * 删除模块库表
     *
     * @param screenModuleLibraryId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除模块库表", httpMethod = "GET")
    AjaxResult delete(@RequestParam("screenModuleLibraryId") Long screenModuleLibraryId);

    /**
     * 交换顺序
     *
     * @param firstId
     * @param secondId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/13 14:07
     */
    @GetMapping("/exchange")
    public AjaxResult exchange(@RequestParam("firstId") Long firstId, @RequestParam("secondId") Long secondId);

    /**
     * 按照id顺序批量更新顺序
     *
     * @param idList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 14:27
     */
    @PostMapping("/update-sort")
    public AjaxResult updateLibrarySortByIdList(@RequestBody List<Long> idList);

    /**
     * 海报条件和分页查询主题列表，【只查询关联了标签的】。
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 16:43
     */
    @PostMapping("/poster-list")
    @ApiOperation(value = "海报条件和分页查询主题列表", httpMethod = "GET")
    public AjaxResult getPosterList(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 海报条件和分页查询主题列表-只根据选择的主题id进行查询，【忽略是否关联标签】
     *
     * @param conditionBo the condition bo
     * @return the poster list sel
     * <AUTHOR>
     * @date 2023 -06-05 14:39:26
     */
    @PostMapping("/poster-list-sel")
    @ApiOperation(value = "海报条件和分页查询主题列表根据已选择主题查询", httpMethod = "GET")
    public AjaxResult getPosterListSel(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 设备关联标签关联海报主题列表(订阅海报列表)
     *
     * @param conditionBo the deviceNumber
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 9:38
     */
    @PostMapping("/device-poster-list")
    @ApiOperation(value = "设备关联标签关联海报主题列表", httpMethod = "POST")
    public AjaxResult getDevicePosterListByDeviceNumber(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 运营海报库列表（未配置标签、已配置标签）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 13:53
     */
    @PostMapping("/label-poster-list")
    @ApiOperation(value = "获取标签海报列表", httpMethod = "POST")
    public AjaxResult getLabelPosterListByCondition(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 获取标签海报统计信息（未配置标签、已配置标签）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 13:53
     */
    @PostMapping("/label-poster-statistics")
    @ApiOperation(value = "获取标签海报统计信息", httpMethod = "POST")
    public AjaxResult getLabelPosterStatistics(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 新增或编辑主题海报
     *
     * @param screenModuleLibraryBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/9 14:04
     */
    @PostMapping("/update-library")
    @ApiModelProperty(value = "新增或编辑主题海报")
    public AjaxResult updateLibrary(@RequestBody ScreenModuleLibraryBo screenModuleLibraryBo);

    /**
     * 我创建的海报列表
     *
     * @param conditionBo
     * @return AjaxResult
     * <AUTHOR>
     * @date 2023/3/31 11:24
     */
    @PostMapping("/poster-person")
    public AjaxResult getPersonPosters(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 根据organizationIds查询校本海报数量
     *
     * @param conditionBo the condition bo
     * @return poster num
     * <AUTHOR>
     * @date 2024 -07-01 15:13:55
     */
    @PostMapping("/poster-school-num")
    public AjaxResult<List<ScreenModuleLibraryNumVo>> getPosterSchoolNum(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 查询校本海报海报数
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult<java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo>>
     * <AUTHOR>
     * @date 2024/8/2 14:28
     **/
    @PostMapping("/poster-media-school-num")
    public AjaxResult<List<ScreenModuleLibraryNumVo>> getPosterMediaSchoolNum(@RequestBody ScreenModuleLibraryListConditionBo conditionBo);
}
