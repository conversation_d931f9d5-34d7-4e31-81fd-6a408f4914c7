package com.fh.cloud.screen.service.screen.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignaturePictureDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenSignaturePictureMapper;
import com.fh.cloud.screen.service.screen.service.IScreenSignaturePictureService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 电子签名图片资源表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Service
public class ScreenSignaturePictureServiceImpl extends
    ServiceImpl<ScreenSignaturePictureMapper, ScreenSignaturePictureDto> implements IScreenSignaturePictureService {

    @Resource
    private ScreenSignaturePictureMapper screenSignaturePictureMapper;

    @Override
    public List<ScreenSignaturePictureVo>
        getScreenSignaturePictureListByCondition(ScreenSignaturePictureConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenSignaturePictureMapper.getScreenSignaturePictureListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenSignaturePicture(ScreenSignaturePictureBo screenSignaturePictureBo) {
        ScreenSignaturePictureDto screenSignaturePicture = new ScreenSignaturePictureDto();
        BeanUtils.copyProperties(screenSignaturePictureBo, screenSignaturePicture);
        screenSignaturePicture.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenSignaturePicture)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenSignaturePicture(ScreenSignaturePictureBo screenSignaturePictureBo) {
        ScreenSignaturePictureDto screenSignaturePicture = new ScreenSignaturePictureDto();
        BeanUtils.copyProperties(screenSignaturePictureBo, screenSignaturePicture);
        if (updateById(screenSignaturePicture)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenSignaturePictureVo getScreenSignaturePictureByCondition(ScreenSignaturePictureConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ScreenSignaturePictureVo vo = screenSignaturePictureMapper.getScreenSignaturePictureByCondition(condition);
        return vo;
    }

}