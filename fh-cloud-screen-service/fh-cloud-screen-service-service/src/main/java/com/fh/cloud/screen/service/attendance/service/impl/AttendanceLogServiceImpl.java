package com.fh.cloud.screen.service.attendance.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.attendance.entity.bo.*;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceLog;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.attendance.mapper.AttendanceLogMapper;
import com.fh.cloud.screen.service.attendance.service.IAttendanceLogService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserService;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarWeekService;
import com.fh.cloud.screen.service.consts.AttendanceConstants;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.AttendanceLogRedisKeyEnum;
import com.fh.cloud.screen.service.enums.AttendanceRuleEnums;
import com.fh.cloud.screen.service.enums.AttendanceUserTypeEnums;
import com.fh.cloud.screen.service.enums.SchoolCalendarEnum;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.StringKit;
import com.google.common.collect.Lists;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.vo.TeacherVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤流水表，不用于业务查询接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Service
public class AttendanceLogServiceImpl extends ServiceImpl<AttendanceLogMapper, AttendanceLog>
    implements IAttendanceLogService {

    @Resource
    private AttendanceLogMapper attendanceLogMapper;

    @Resource
    private IAttendanceRuleService attendanceRuleService;

    @Resource
    private BaseDataService baseDataService;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private ISchoolCalendarWeekService schoolCalendarWeekService;

    @Resource
    private IAttendanceUserService iAttendanceUserService;

    @Override
    public List<AttendanceLogVo> getAttendanceLogListByCondition(AttendanceLogListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return attendanceLogMapper.getAttendanceLogListByCondition(condition);
    }

    @Override
    public List<AttendanceLogVo> getTodayAttendanceLogList(AttendanceLogListConditionBo condition) {
        // 获取当天实时的考勤数据

        return null;
    }

    @Override
    public AjaxResult addAttendanceLog(AttendanceLogBo attendanceLogBo) {
        // 1.参数校验
        if (AttendanceRuleEnums.RULE_STUDENT.getCode().equals(attendanceLogBo.getAttendanceType())) {
            if (null == attendanceLogBo.getClassesId() || null == attendanceLogBo.getGrade()) {
                return AjaxResult.fail("当前学生没有班级，打卡失败");
            }
        }
        // 2.考勤规则存在性校验
        AttendanceRuleAddBo ruleBo = attendanceRuleService.getAttendanceRuleByOrganizationIdAndType(
            attendanceLogBo.getOrganizationId(), attendanceLogBo.getAttendanceType());
        if (null == ruleBo) {
            return AjaxResult.fail("考勤规则不存在");
        }
        attendanceLogBo.setAttendanceRuleId(ruleBo.getAttendanceRuleId());

        // 3.结合校历校验，当天是否上课日
        AjaxResult ajaxResult =
            getSchoolCalendarDaysByOrganization(ruleBo.getOrganizationId(), attendanceLogBo.getAttendanceTime());
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        // 根据考勤规则，过滤当前日的规则
        Integer week = (Integer)ajaxResult.getData();
        List<AttendanceRuleDayBo> attendanceRuleDays = ruleBo.getAttendanceRuleDays();
        final Integer finalWeek = week;
        // ruleBoDays是最终结合校历过滤后的实际有效的规则
        List<AttendanceRuleDayBo> ruleBoDays =
            attendanceRuleDays.stream().filter(days -> days.getWeek().equals(finalWeek)).collect(Collectors.toList());
        // 学生考勤年级不一致时，再次过滤出对应年级的考勤
        if (AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(ruleBo.getGradeSameType())) {
            ruleBoDays = ruleBoDays.stream().filter(days -> days.getGrade().equals(attendanceLogBo.getGrade()))
                .collect(Collectors.toList());
        }

        // 4.根据规则判断当前签到结果，根据规则判断具体是签到、签退、或者迟到早退
        // 成功匹配到的规则
        AttendanceRuleDayBo attendanceRuleDayBo = null;
        // 多个考勤规则循环与当前考勤时间比对，设置signType
        for (int i = 0; i < ruleBoDays.size(); i++) {
            AttendanceRuleDayBo ruleDayBo = ruleBoDays.get(i);
            if (ruleDayBo.getSignInTime() == null || ruleDayBo.getSignOutTime() == null) {
                // 单组规则只有配置了签到或签退的处理（结果存放attendanceLogBo）
                calculateAttendanceByStartAndEndDateSingle(attendanceLogBo, ruleBoDays, i,
                    ruleBo.getAttendanceModeNum());
            } else {
                // 一组完整签到签退规则处理（结果存放attendanceLogBo）
                calculateAttendanceByStartAndEndDate(attendanceLogBo, ruleBoDays, i, ruleBo.getAttendanceModeNum());
            }
            // 签到或签退有效，记录有效规则，开始后面流程的处理
            if (null != attendanceLogBo.getSignType()) {
                attendanceRuleDayBo = ruleDayBo;
                break;
            }
        }

        // 5.保存并返回打卡结果
        return saveAndGetSignResult(attendanceLogBo, ruleBo, attendanceRuleDayBo);
    }

    /**
     * 根据用户打卡结果，返回给前端和存入db和redis
     *
     * @param attendanceLogBo 用户打卡数据
     * @param ruleBo 考勤规则主表记录
     * @param attendanceRuleDayBo 有效考勤规则
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/11/21 16:44
     */
    private AjaxResult saveAndGetSignResult(AttendanceLogBo attendanceLogBo, AttendanceRuleAddBo ruleBo,
        AttendanceRuleDayBo attendanceRuleDayBo) {
        // 在实时间范围内 有效打卡
        if (null != attendanceLogBo.getSignType()) {
            // 缓存流水key-教师和学生分开
            String cacheKey = "";
            if (AttendanceRuleEnums.RULE_STUDENT.getCode().equals(ruleBo.getAttendanceType())) {
                cacheKey = AttendanceLogRedisKeyEnum.ATTENDANCE_LOG_CLASS_STUDENT_DAY_KEY.getValue() + ConstString.ywmh
                    + attendanceLogBo.getClassesId() + ConstString.ywmh;
            } else if (AttendanceRuleEnums.RULE_TEACHER.getCode().equals(attendanceLogBo.getAttendanceType())) {
                cacheKey = AttendanceLogRedisKeyEnum.ATTENDANCE_LOG_ORG_TEACHER_DAY_KEY.getValue() + ConstString.ywmh
                    + ruleBo.getOrganizationId() + ConstString.ywmh;
            }
            // 缓存流水时间：格式为20230807140000
            String ruleSignInStr = DateKit
                .date2String(DateKit.transferYMD2CurrentDay(attendanceRuleDayBo.getSignInTime()), "yyyyMMddHHmmss");
            String ruleSignOutStr = DateKit
                .date2String(DateKit.transferYMD2CurrentDay(attendanceRuleDayBo.getSignOutTime()), "yyyyMMddHHmmss");
            // 缓存流水key，格式为：attendance:log:student:classesId:day:323:20230807140000
            String signInKey = cacheKey.concat(ruleSignInStr);
            String signOutKey = cacheKey.concat(ruleSignOutStr);
            // sunqbtodo 需要抽取一个方法专门用来获取signKey：签到类型，签到状态，签到的规则时间
            String signKey = "";
            if (AttendanceRuleEnums.SIGN_TYPE_IN.getCode().equals(attendanceLogBo.getSignType())) {
                signKey = signInKey;
            } else if (AttendanceRuleEnums.SIGN_TYPE_OUT.getCode().equals(attendanceLogBo.getSignType())) {
                signKey = signOutKey;
            } else if (AttendanceRuleEnums.SIGN_MIDDLE.getCode().equals(attendanceLogBo.getSignType())) {
                signKey = signInKey;
            } else if (AttendanceRuleEnums.SIGN_LATER.getCode().equals(attendanceLogBo.getSignType())) {
                signKey = signInKey;
                attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_IN.getCode());
            } else if (AttendanceRuleEnums.SIGN_EARLY.getCode().equals(attendanceLogBo.getSignType())) {
                signKey = signOutKey;
                attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_OUT.getCode());
            }

            // 获取用户在此规则缓存流水列表中的位置，及该条记录
            Map<String, Object> userMap = getUserCacheLogByUserOidAndRedisKey(attendanceLogBo.getUserOid(), signKey);
            Integer index = (Integer)userMap.get("index");
            AttendanceLogVo attendanceLogVoCache = (AttendanceLogVo)userMap.get("cacheLog");

            // 一组完整规则迟到或早退，需要判断是否有签到记录才能确认是迟到或早退
            if (AttendanceRuleEnums.SIGN_MIDDLE.getCode().equals(attendanceLogBo.getSignType())) {
                if (null != attendanceLogVoCache) {
                    // 有签到记录，本次为早退
                    attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_OUT.getCode());
                    // 早退判断是否覆盖缓存里之前该用户数据
                    userMap = getUserCacheLogByUserOidAndRedisKey(attendanceLogBo.getUserOid(), signOutKey);
                    index = (Integer)userMap.get("index");
                    attendanceLogVoCache = (AttendanceLogVo)userMap.get("cacheLog");
                    return saveAttendanceByRepeat(attendanceLogBo, attendanceLogVoCache, signOutKey, index);
                } else {
                    // 无签到记录，本次为迟到
                    attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_IN.getCode());
                    return saveAttendanceByRepeat(attendanceLogBo, null, signInKey, null);
                }
            }
            return saveAttendanceByRepeat(attendanceLogBo, attendanceLogVoCache, signKey, index);
        }
        log.error("不在考勤时间范围内:signType为空，attendanceLogBo=" + JSONObject.toJSONString(attendanceLogBo));
        return AjaxResult.fail("不在考勤时间范围内");
    }

    /**
     * 根据缓存中的此次规则中，用户是否存在记录，然后插入或覆盖用户缓存记录，并插入db和返回前端
     *
     * @param attendanceLogBo
     * @param cacheLogVo
     * @param signKey
     * @param index
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/11/21 16:50
     */
    private AjaxResult saveAttendanceByRepeat(AttendanceLogBo attendanceLogBo, AttendanceLogVo cacheLogVo,
        String signKey, Integer index) {
        Map<String, Object> map = new HashMap<>();
        // 返回前端数据封装
        map.put("isRepeat", false);
        map.put("attendanceTime", attendanceLogBo.getAttendanceTime());
        map.put("user", attendanceLogBo.getUserOid());
        map.put("signKey", signKey);
        map.put("signType", attendanceLogBo.getSignType());
        map.put("attendanceResultTime", attendanceLogBo.getAttendanceTime());
        // 缓存有值。覆盖
        if (null != cacheLogVo) {
            if (attendanceLogBo.getSignType().equals(AttendanceRuleEnums.SIGN_TYPE_IN.getCode())) {
                // 签到不允许重复，这里只插入数据库
                map.put("isRepeat", true);
                map.put("attendanceTime", attendanceLogBo.getAttendanceTime());
                map.put("attendanceResultTime", cacheLogVo.getAttendanceTime());
                map.put("user", cacheLogVo.getUserOid());
                insertDBAndRedis(null, index, null, attendanceLogBo);
            } else {
                // 签退可以覆盖缓存值，并插入数据库
                insertDBAndRedis(signKey, index, null, attendanceLogBo);
            }
        } else {
            // 缓存无值。插入
            insertDBAndRedis(null, null, signKey, attendanceLogBo);
        }
        return AjaxResult.success(map);
    }

    /**
     * 根据用户oid 和考勤流水缓存key,获取用户在此此规则key对应流水列表中的位置
     *
     * @param userOid 用户oid
     * @param signKey 考勤规则对应的key
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2022/11/21 16:46
     */
    private Map<String, Object> getUserCacheLogByUserOidAndRedisKey(String userOid, String signKey) {
        Map<String, Object> map = new HashMap<>();
        List<AttendanceLogVo> attendanceLogVoList = getCacheAttendanceLogListByKey(signKey);
        for (int i = 0; i < attendanceLogVoList.size(); i++) {
            if (attendanceLogVoList.get(i).getUserOid().equals(userOid)) {
                map.put("index", i);
                map.put("cacheLog", attendanceLogVoList.get(i));
                break;
            }
        }
        return map;
    }

    /**
     * 用户打卡数据，插入数据库及插入或更新redis（todo双写一致性）
     *
     * @param updateKey 更新的key
     * @param updateIndex 更新key的位置
     * @param rPushKey 插入的key
     * @param attendanceLogBo 用户考勤记录
     * @return void
     * <AUTHOR>
     * @update by sunqb :新增缓存key失效时间，不会永久缓存用户的签到签退记录。因此不允许出现跨天考勤
     * @date 2022/11/21 16:52
     */
    private void insertDBAndRedis(String updateKey, Integer updateIndex, String rPushKey,
        AttendanceLogBo attendanceLogBo) {
        boolean saveAttendanceLog = saveAttendanceLog(attendanceLogBo);
        if (null != updateKey) {
            redisComponent.lUpdateIndex(updateKey, updateIndex, JSONUtil.toJsonStr(attendanceLogBo));
            redisComponent.expire(updateKey, DateKit.getNextDayMidnightSeconds());
        } else if (null != rPushKey) {
            redisComponent.rPush(rPushKey, JSONUtil.toJsonStr(attendanceLogBo));
            redisComponent.expire(rPushKey, DateKit.getNextDayMidnightSeconds());
        }

    }

    /**
     * 根据组织id和日期判断当前是否是上课日
     *
     * @param organization 组织id
     * @param date 日期
     * @return AjaxResult success 上课日，周几week。fail 不是上课日
     * <AUTHOR>
     */
    private AjaxResult getSchoolCalendarDaysByOrganization(Long organization, Date date) {
        // 联合校历，判断当前日期是否属于上课日
        Map<String, Object> schoolCalendarMap =
            schoolCalendarWeekService.getCacheWeekListAndDayListByOrganizationId(organization);
        List<SchoolCalendarWeekVo> weekVos = (List<SchoolCalendarWeekVo>)schoolCalendarMap.get("weekVos");
        List<SchoolCalendarDay> dayVos = (List<SchoolCalendarDay>)schoolCalendarMap.get("dayVos");
        if (CollectionUtils.isEmpty(weekVos)) {
            return AjaxResult.fail("校历不存在");
        }
        // 不上课标志
        boolean notWorkFlag = false;
        Integer week = DateKit.getWeekIdByDate(date);
        Integer firstWeek = week;
        SchoolCalendarWeekVo schoolCalendarWeekVo =
            weekVos.stream().filter(weekVo -> weekVo.getWeek().equals(firstWeek)).findFirst().get();
        // 上课周不上课，true
        if (SchoolCalendarEnum.TYPE_IS.getValue() != schoolCalendarWeekVo.getType()) {
            notWorkFlag = true;
        }
        if (CollectionUtils.isNotEmpty(dayVos)) {
            for (SchoolCalendarDay dayVo : dayVos) {
                // 考勤当天
                if (DateKit.checkOneDay(dayVo.getDay(), date)) {
                    // 不上课 true
                    if (SchoolCalendarEnum.TYPE_IS.getValue() != dayVo.getType()) {
                        notWorkFlag = true;
                    } else {
                        notWorkFlag = false;
                        week = dayVo.getWeek();
                    }
                    break;
                }
            }
        }
        if (notWorkFlag) {
            log.error("不在考勤时间范围内:打卡考勤判断是非工作日,organizationId=" + organization + ";date=" + date);
            return AjaxResult.fail("不在考勤时间范围内");
        }
        return AjaxResult.success(week);
    }

    /**
     * 一组完整考勤规则根据时间范围，计算当前打卡结果
     *
     * @param attendanceLogBo 用户打卡数据
     * @param ruleDayBos 当天考勤规则列表
     * @param ruleDayBosIndex 当前一组单个签到或签退规则，在规则列表中的位置
     * @param attendanceModeNum 当前考勤规则一天几组的标识
     * @return void
     * <AUTHOR>
     * @update sunqingbiao at FH4_2023_46
     * @date 2022/11/21 10:26
     */
    private void calculateAttendanceByStartAndEndDate(AttendanceLogBo attendanceLogBo,
        List<AttendanceRuleDayBo> ruleDayBos, Integer ruleDayBosIndex, Integer attendanceModeNum) {
        AttendanceRuleDayBo attendanceRuleDayBo = ruleDayBos.get(ruleDayBosIndex);
        Integer signLeftMinute = attendanceRuleDayBo.getSignLeftMinute();
        Integer signRightMinute = attendanceRuleDayBo.getSignRightMinute();

        long beforeTime = 0L;
        long afterTime = 0L;
        // 第一组考勤
        if (ruleDayBosIndex == 0) {
            // 一天一组考勤，前后间隔时间两小时
            if (AttendanceRuleEnums.ATTENDANCE_MODE_NUM_1.getCode().equals(attendanceModeNum)) {
                beforeTime = AttendanceConstants.MAX_SECOND;
                afterTime = AttendanceConstants.MAX_SECOND;
            } else {
                beforeTime = AttendanceConstants.MAX_SECOND;
                afterTime = AttendanceConstants.MIN_SECOND;
            }
        }
        // 最后一组考勤
        else if (ruleDayBosIndex == ruleDayBos.size() - 1) {
            beforeTime = AttendanceConstants.MIN_SECOND;
            afterTime = AttendanceConstants.MAX_SECOND;
        }
        // 中间段考勤
        else {
            beforeTime = AttendanceConstants.MIN_SECOND;
            afterTime = AttendanceConstants.MIN_SECOND;
        }

        // update by sunqb：新增自定义时间规则扩展，如果有值则直接覆盖
        if (signLeftMinute != null) {
            beforeTime = beforeTime * 60;
        }
        if (signRightMinute != null) {
            afterTime = afterTime * 60;
        }
        // 计算
        calculateAttendanceResult(attendanceLogBo, attendanceRuleDayBo, beforeTime, afterTime);
    }

    /**
     * 判断用户打卡时间，是否在考勤规则时间范围内
     *
     * @param attendanceLogBo, attendanceRuleDayBo, beforeTime, afterTime
     * <AUTHOR>
     * @date 2022/6/13 16:52
     */
    private void calculateAttendanceResult(AttendanceLogBo attendanceLogBo, AttendanceRuleDayBo attendanceRuleDayBo,
        long beforeTime, long afterTime) {
        // 规则签到时间拼接当前日期
        Date ruleSignInDate =
            DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), attendanceLogBo.getAttendanceTime());
        Date ruleSignOutDate =
            DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), attendanceLogBo.getAttendanceTime());
        // 签到截止时间00+59s
        Date ruleSignInDate59 = DateKit.getAfterSeconds(AttendanceConstants.FIFTY_NINE, ruleSignInDate);
        Date attendanceTime = attendanceLogBo.getAttendanceTime();
        // 签到从59秒结束，判断间隔时间，添加59秒
        beforeTime = beforeTime + AttendanceConstants.FIFTY_NINE;
        long diffSignInMin = DateKit.getDiffSecDate(attendanceTime, ruleSignInDate59);
        if (0 <= diffSignInMin && diffSignInMin <= beforeTime) {
            attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_IN.getCode());
        }
        long diffSignOutMin = DateKit.getDiffSecDate(ruleSignOutDate, attendanceTime);
        if (0 <= diffSignOutMin && diffSignOutMin <= afterTime) {
            attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_OUT.getCode());
        }
        // 迟到，早退
        if (attendanceTime.before(ruleSignOutDate) && attendanceTime.after(ruleSignInDate59)) {
            attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_MIDDLE.getCode());
        }
    }

    /**
     * 设置一组单个签到或签退的考勤规则的开始及结束时间范围(并调用方法校验考勤。一天一组单个签到或签退，时间无范围限制，多个以,后一个前30分钟，为第一个的打卡结束时间)
     *
     * @param attendanceLogBo 用户打卡数据
     * @param ruleDayBos 当天考勤规则列表
     * @param ruleDayBosIndex 当前一组单个签到或签退规则，在规则列表中的位置
     * @return void
     * <AUTHOR>
     * @date 2022/11/18 9:58
     */
    private void checkOneRuleSignTimeByZero(AttendanceLogBo attendanceLogBo, List<AttendanceRuleDayBo> ruleDayBos,
        Integer ruleDayBosIndex) {
        Date signTime = attendanceLogBo.getAttendanceTime();
        AttendanceRuleDayBo attendanceRuleDayBo = ruleDayBos.get(ruleDayBosIndex);
        Date endDate = null;
        Date startDate = null;

        // list最后一个、后面时间范围：本次规则单个,则无时间限制，否则按正常后两个小时范围，前为本次的第一个打卡节点的30分钟
        if (ruleDayBosIndex == ruleDayBos.size() - 1) {
            // 单组考勤规则唯一，不考虑时间
            if (ruleDayBos.size() == 1) {
                // 开始日期为当天零点
                startDate = DateKit.getDayZore(signTime);
                // 结束日期为第二天零点
                endDate = DateKit.getZeroDate(1, signTime);
                calculateAttendanceResult(attendanceLogBo, attendanceRuleDayBo, startDate, endDate);
            }
            // 单组考勤规则在最后位置
            else {
                if (attendanceRuleDayBo.getSignOutTime() != null) {
                    startDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), signTime);
                } else if (attendanceRuleDayBo.getSignInTime() != null) {
                    startDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), signTime);
                }
                startDate = DateKit.addMinute(startDate, -AttendanceConstants.MIN_MINUTES);
                // 结束日期为第二天零点
                endDate = DateKit.getZeroDate(1, signTime);
                calculateAttendanceResult(attendanceLogBo, attendanceRuleDayBo, startDate, endDate);
            }
        }

        // 取下一个，打卡节点的前30分钟，为本次打卡的结束范围，（开始范围起点为：本次打卡的前30分钟，本次是第一次，不限时间）
        // 单组考勤规则在1~[end-1]
        AttendanceRuleDayBo nextRuleDayBo = ruleDayBos.get(ruleDayBosIndex + 1);
        if (nextRuleDayBo.getSignInTime() != null) {
            endDate = DateKit.transferYMD2Day(nextRuleDayBo.getSignInTime(), signTime);
        } else {
            endDate = DateKit.transferYMD2Day(nextRuleDayBo.getSignOutTime(), signTime);
        }
        endDate = DateKit.addMinute(endDate, -AttendanceConstants.MIN_MINUTES);
        // 开始范围起点为：本次打卡的前30分钟，本次是第一次，不限时间
        if (ruleDayBosIndex != 0) {
            if (attendanceRuleDayBo.getSignInTime() == null) {
                startDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), signTime);
            }
            if (attendanceRuleDayBo.getSignOutTime() == null) {
                startDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), signTime);
            }

        } else {
            startDate = DateKit.getDayZore(attendanceLogBo.getAttendanceTime());
        }
        // 根据开始范围和结束范围，判断用户签到状态
        calculateAttendanceResult(attendanceLogBo, attendanceRuleDayBo, startDate, endDate);
    }

    /**
     * 设置一组单个签到或签退考勤规则的开始及结束时间范围，按照完整考勤时间范围前后2小时，中间30分钟计算
     *
     * @param attendanceLogBo 用户打卡数据
     * @param ruleDayBos 当天考勤规则列表
     * @param ruleDayBosIndex 当前一组单个签到或签退规则，在规则列表中的位置
     * @param attendanceModeNum 当前考勤规则一天几组的标识
     * @return void
     * <AUTHOR>
     * @update sunqingbiao at FH4_2023_46
     * @date 2022/11/18 9:56
     */
    private void calculateAttendanceByStartAndEndDateSingle(AttendanceLogBo attendanceLogBo,
        List<AttendanceRuleDayBo> ruleDayBos, Integer ruleDayBosIndex, Integer attendanceModeNum) {
        Date signTime = attendanceLogBo.getAttendanceTime();
        AttendanceRuleDayBo attendanceRuleDayBo = ruleDayBos.get(ruleDayBosIndex);
        Integer signLeftMinute = attendanceRuleDayBo.getSignLeftMinute();
        Integer signRightMinute = attendanceRuleDayBo.getSignRightMinute();
        Date endDate;
        Date startDate;
        Date ruleDate = null;
        if (attendanceRuleDayBo.getSignInTime() != null) {
            ruleDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), signTime);
        }
        if (attendanceRuleDayBo.getSignOutTime() != null) {
            ruleDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), signTime);
        }
        // 第一组考勤规则组
        if (ruleDayBosIndex == 0) {
            if (AttendanceRuleEnums.ATTENDANCE_MODE_NUM_1.getCode().equals(attendanceModeNum)) {
                startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MAX_MINUTES);
                endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MAX_MINUTES);
            } else {
                startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MAX_MINUTES);
                endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MIN_MINUTES);
            }
        }
        // 最后一组考勤规则组
        else if (ruleDayBosIndex == ruleDayBos.size() - 1) {
            startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MIN_MINUTES);
            endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MAX_MINUTES);
        }
        // 中间段的考勤规则组
        else {
            startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MIN_MINUTES);
            endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MIN_MINUTES);
        }
        // update by sunqb：数据库如果配置了自定义左右扩张时间，则用数据库的时间作为规则
        if (signLeftMinute != null && signRightMinute != ConstantsInteger.NUM_0) {
            startDate = DateKit.addMinute(ruleDate, -signLeftMinute);
        }
        if (signRightMinute != null && signRightMinute != ConstantsInteger.NUM_0) {
            endDate = DateKit.addMinute(ruleDate, signRightMinute);
        }
        calculateAttendanceResult(attendanceLogBo, attendanceRuleDayBo, startDate, endDate);
    }

    /**
     * 用户打卡数据与一组单个签到或签退规则比对，给attendanceLogBo对象设置signType并返回
     *
     * @param attendanceLogBo 用户打卡数据
     * @param attendanceRuleDayBo 一组单个签到或签退规则
     * @param beforeDate 规则开始范围时间
     * @param endDate 规则结束时间范围
     * @return void
     * <AUTHOR>
     * @date 2022/11/18 10:06
     */
    private void calculateAttendanceResult(AttendanceLogBo attendanceLogBo, AttendanceRuleDayBo attendanceRuleDayBo,
        Date beforeDate, Date endDate) {
        // 规则签到时间拼接当前日期
        Date signTime = attendanceLogBo.getAttendanceTime();
        Date ruleSignInDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), signTime);
        Date ruleSignOutDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), signTime);
        // 签到截止时间=00+59s
        Date ruleSignInDate59 = DateKit.getAfterSeconds(AttendanceConstants.FIFTY_NINE, ruleSignInDate);

        // 单组签退规则
        if (attendanceRuleDayBo.getSignInTime() == null) {
            // 早退
            if (signTime.before(ruleSignOutDate) && signTime.after(beforeDate)) {
                attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_EARLY.getCode());
            }
            // 正常签退
            if ((signTime.after(ruleSignOutDate) || signTime == beforeDate) && signTime.before(endDate)) {
                attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_OUT.getCode());
            }
        }
        // 单组签到规则
        if (attendanceRuleDayBo.getSignOutTime() == null) {
            // 正常签到
            if ((signTime.before(ruleSignInDate59) || signTime == ruleSignInDate59) && signTime.after(beforeDate)) {
                attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_TYPE_IN.getCode());
            }
            // 迟到
            if ((signTime.after(ruleSignInDate59)) && signTime.before(endDate)) {
                attendanceLogBo.setSignType(AttendanceRuleEnums.SIGN_LATER.getCode());
            }
        }
    }

    @Override
    public boolean updateAttendanceLog(AttendanceLogBo attendanceLogBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long attendanceLogId) {
        return null;
    }

    @Override
    public List<AttendanceLogVo> getStudentLogListByClassesIdAndDateTime(Long classesId, String dateTime) {

        // 处理日期 yyyyMMdd HH:mm:ss -> update by sunqb变更为yyyyMMddHH:mm:ss
        final String date = DateKit.formatDate(dateTime, "yyyyMMddHH:mm:ss");

        List<AttendanceLogVo> attendanceLogVos = Lists.newArrayList();

        // 缓存取值
        final String key = AttendanceLogRedisKeyEnum.ATTENDANCE_LOG_CLASS_STUDENT_DAY_KEY.getValue() + ConstString.ywmh
            + classesId + ConstString.ywmh + date;
        attendanceLogVos = getCacheAttendanceLogListByKey(key);
        if (CollectionUtil.isNotEmpty(attendanceLogVos)) {
            return attendanceLogVos;
        }

        // // 获取班级成员
        // final List<StudentVo> studentVos = this.baseDataService.getStudentVoListByClassesId(classesId);
        // final List<String> userOids = studentVos.stream().map(StudentVo::getUserOid).collect(Collectors.toList());
        //
        // // 数据库查询考勤数据列表
        // attendanceLogVos = this.getByUserOidsAndDay(userOids, day);

        return attendanceLogVos;
    }

    /**
     * 根据班级、日期获取学生打卡记录信息
     *
     * @param classesId the classesId 班级ID
     * @param date the dateTime 日期 yyyy-MM-dd
     * @return
     */
    @Override
    public AttendanceLogCensusVo getStudentCensusByClassesId(Long classesId, String date) {

        AttendanceLogCensusVo vo = new AttendanceLogCensusVo();
        // 获取所有学生
        final List<StudentVo> studentVoList = this.baseDataService.getStudentVoListByClassesId(classesId);
        if (CollectionUtils.isEmpty(studentVoList)) {
            return vo;
        }
        int allStudentSize = studentVoList.size();
        vo.setCount(allStudentSize);
        // 转map key user Oid value: realName
        final Map<String, String> userOidRealNameMap = studentVoList.stream().collect(Collectors
            .toMap(StudentVo::getUserOid, x -> x.getUserVo().getRealName(), (k1, k2) -> k2, LinkedHashMap::new));
        final Set<String> userOids = userOidRealNameMap.keySet();

        // 处理已打卡学生
        List<AttendanceLogVo> attendDanceVo = this.clockList(userOids, date, userOidRealNameMap);
        attendDanceVo = CollectionUtil.reverse(attendDanceVo);
        vo.setClockNum(attendDanceVo.size());
        vo.setClockLog(attendDanceVo);

        // 未打卡数量
        vo.setUnClockNum(vo.getCount() - vo.getClockNum());
        // 未打卡用户信息
        final List<AttendanceLogVo> unClockList = this.buildUnClockList(userOidRealNameMap, attendDanceVo);
        vo.setUnClockLog(unClockList);

        // 计算百分比
        this.calcPercent(vo);

        return vo;
    }

    /**
     * 处理已打卡数据
     * 
     * @param userOids the user oid list 学生用户OID 集合
     * @param date the clock date 打卡日期
     * @param userOidRealNameMap the user oid real name map 用户OID对应用户名成MAP
     * @return
     */
    private List<AttendanceLogVo> clockList(Set<String> userOids, String date, Map<String, String> userOidRealNameMap) {
        List<AttendanceUserVo> attendanceUserVos =
            this.iAttendanceUserService.getSignInListByUserOidsAndDateDay(userOids, date);
        return attendanceUserVos.stream().filter(x -> userOidRealNameMap.get(x.getUserOid()) != null).map(x -> {
            AttendanceLogVo attendanceLogVo = new AttendanceLogVo();
            attendanceLogVo.setUserOid(x.getUserOid());
            attendanceLogVo.setRealName(userOidRealNameMap.get(x.getUserOid()));
            return attendanceLogVo;
        }).collect(Collectors.toList());
    }

    /**
     * 处理未打卡用户信息
     *
     * @param userOidRealNameMap the user oid real name map key:userOid value: realName 用户OID 姓名map
     * @param attendDanceVo the attendanceVos 已打卡考勤数据包含用户OID
     * @return
     */
    private List<AttendanceLogVo> buildUnClockList(Map<String, String> userOidRealNameMap,
        List<AttendanceLogVo> attendDanceVo) {
        final List<String> userOids =
            attendDanceVo.parallelStream().map(AttendanceLogVo::getUserOid).collect(Collectors.toList());
        return userOidRealNameMap.entrySet().stream().filter(x -> !userOids.contains(x.getKey())).map(x -> {
            AttendanceLogVo attendanceLogVo = new AttendanceLogVo();
            attendanceLogVo.setRealName(x.getValue());
            return attendanceLogVo;
        }).collect(Collectors.toList());
    }

    @Override
    public AttendanceLogCensusVo getClockTeacherCensusByOrgId(Long orgId, String cacheKey) {
        AttendanceLogCensusVo vo = new AttendanceLogCensusVo();

        // 获取所有老师
        final List<TeacherVo> teacherVos = this.baseDataService.getTeacherVoByOrgId(orgId);
        vo.setCount(teacherVos.size());
        // 转map key user Oid value: realName
        final Map<String, String> userOidRealNameMap = teacherVos.stream().collect(Collectors
            .toMap(TeacherVo::getUserOid, x -> x.getUserVo().getRealName(), (k1, k2) -> k2, LinkedHashMap::new));

        // 获取已打卡老师
        List<AttendanceLogVo> attendDanceVo = this.getCacheAttendanceLogListByKey(cacheKey);
        attendDanceVo = CollectionUtil.reverse(attendDanceVo);
        // 已打卡老师赋值名称
        attendDanceVo = buildAttendanceUserRealName(userOidRealNameMap, attendDanceVo);
        vo.setClockNum(attendDanceVo.size());
        vo.setClockLog(attendDanceVo);

        // 未打卡数量
        vo.setUnClockNum(vo.getCount() - vo.getClockNum());
        // 未打卡用户信息
        final List<AttendanceLogVo> unClockList = this.buildUnClockList(userOidRealNameMap, attendDanceVo);
        vo.setUnClockLog(unClockList);

        // 计算百分比
        this.calcPercent(vo);

        return vo;
    }

    /**
     * 计算百分比 ： 打卡数量 / 总数量 * 100 保留1位小数
     *
     * @param vo
     */
    private void calcPercent(AttendanceLogCensusVo vo) {
        // 计算百分比
        if (vo.getCount() != 0) {
            final double percent = NumberUtil.mul(NumberUtil.div(vo.getClockNum(), vo.getCount()), 100);
            vo.setPercent(NumberUtil.round(percent, 1));
        }
    }

    /**
     * 处理考勤用户 姓名
     *
     * @param userOidRealNameMap the userOid realName map key: userOid value:realName 用户OID对应姓名Map
     * @param attendDanceVo 考勤记录
     * @return
     */
    private List<AttendanceLogVo> buildAttendanceUserRealName(Map<String, String> userOidRealNameMap,
        List<AttendanceLogVo> attendDanceVo) {
        attendDanceVo = attendDanceVo.stream().filter(x -> userOidRealNameMap.get(x.getUserOid()) != null).map(x -> {
            x.setRealName(userOidRealNameMap.get(x.getUserOid()));
            return x;
        }).collect(Collectors.toList());
        return attendDanceVo;
    }

    /**
     * 根据学校、日期 获取老师考勤记录
     *
     * @param orgId the orgId 学校ID
     * @param dateTime the date time 日期 yyyy-MM-dd HH:mm:ss
     * @return
     */
    @Override
    public List<AttendanceLogVo> getTeacherLogListByOrgIdAndDateTime(Long orgId, String dateTime) {
        // 处理日期 yyyyMMdd HH:mm --> 变更为 yyyyMMddHHmmss
        final String date = DateKit.formatDate(dateTime, "yyyyMMddHHmmss");

        List<AttendanceLogVo> attendanceLogVos = Lists.newArrayList();

        // 缓存取值
        final String key = AttendanceLogRedisKeyEnum.ATTENDANCE_LOG_ORG_TEACHER_DAY_KEY.getValue() + ConstString.ywmh
            + orgId + ConstString.ywmh + date;
        attendanceLogVos = getCacheAttendanceLogListByKey(key);
        if (CollectionUtil.isNotEmpty(attendanceLogVos)) {
            return attendanceLogVos;
        }
        return attendanceLogVos;
    }

    /**
     * 根据班级ID获取学生当天的打卡统计信息
     * 
     * @param classesId the classes id 班级ID
     * @return
     */
    @Override
    public AttendanceDayCensusVo getClockStudentCensusByClassesId(Long organizationId, Long classesId) {
        // sunqbtodo 后续需要封装一个方法获取cachekey，可以根据时间判断。暂时可以直接获取列表给前端，前端判断
        AttendanceDayCensusVo resultVo = new AttendanceDayCensusVo();
        ClazzVo clazzVo = this.baseDataService.getByClazzId(classesId);
        String grade = "";
        if (clazzVo != null) {
            grade = clazzVo.getGrade();
        }
        // 获取所有学生
        final List<StudentVo> studentVoList = this.baseDataService.getStudentVoListByClassesId(classesId);
        if (CollectionUtils.isEmpty(studentVoList)) {
            return resultVo;
        }
        int allStudentSize = studentVoList.size();
        // 转map key user Oid value: realName
        final Map<String, String> userOidRealNameMap = studentVoList.stream().collect(Collectors
            .toMap(StudentVo::getUserOid, x -> x.getUserVo().getRealName(), (k1, k2) -> k2, LinkedHashMap::new));

        // 根据考勤规则塞数据
        Integer nowDayWeek = DateKit.getWeekIdByDate(new Date());
        AttendanceRuleAddBo attendanceRuleAddBo = attendanceRuleService
            .getAttendanceRuleByOrganizationIdAndType(organizationId, AttendanceUserTypeEnums.STUDENT.getCode());
        if (attendanceRuleAddBo == null) {
            return null;
        }
        Integer gradeSameType = attendanceRuleAddBo.getGradeSameType();
        resultVo.setAttendanceRuleId(attendanceRuleAddBo.getAttendanceRuleId());
        resultVo.setAttendanceType(attendanceRuleAddBo.getAttendanceType());
        List<AttendanceRuleDayBo> attendanceRuleDays = attendanceRuleAddBo.getAttendanceRuleDays();
        if (CollectionUtils.isEmpty(attendanceRuleDays)) {
            return null;
        }
        String signKeyPrefix = AttendanceLogRedisKeyEnum.ATTENDANCE_LOG_CLASS_STUDENT_DAY_KEY.getValue()
            + ConstString.ywmh + classesId + ConstString.ywmh;
        List<AttendanceLogCensusVo> attendanceLogCensusVos = Lists.newArrayList();
        for (AttendanceRuleDayBo attendanceRuleDayBo : attendanceRuleDays) {
            // 不是今天的考勤规则的直接跳过
            if (attendanceRuleDayBo.getWeek() == null || !attendanceRuleDayBo.getWeek().equals(nowDayWeek)) {
                continue;
            }
            // 如果是年级不一致，那么不是该班级所在年级的考勤跳过
            if (gradeSameType != null && gradeSameType.equals(AttendanceRuleEnums.GRADE_SAME_NOT.getCode())
                && StringUtils.isNotBlank(grade) && !grade.equals(attendanceRuleDayBo.getGrade())) {
                continue;
            }
            String signInKey = signKeyPrefix + DateKit
                .date2String(DateKit.transferYMD2CurrentDay(attendanceRuleDayBo.getSignInTime()), "yyyyMMddHHmmss");
            String signOutKey = signKeyPrefix + DateKit
                .date2String(DateKit.transferYMD2CurrentDay(attendanceRuleDayBo.getSignOutTime()), "yyyyMMddHHmmss");
            attendanceRuleDayBo.setSignInKey(signInKey);
            attendanceRuleDayBo.setSignOutKey(signOutKey);
            AttendanceLogCensusVo attendanceLogCensusVoIn =
                calculateOnceStudentForSign(userOidRealNameMap, allStudentSize, signInKey);
            AttendanceLogCensusVo attendanceLogCensusVoOut =
                calculateOnceStudentForSign(userOidRealNameMap, allStudentSize, signOutKey);
            attendanceLogCensusVos.add(attendanceLogCensusVoIn);
            attendanceLogCensusVos.add(attendanceLogCensusVoOut);
        }

        // 最终返回的数据过滤处理
        List<AttendanceRuleDayVo> attendanceRuleDayVos =
            attendanceRuleDays.stream().filter(attendanceRuleDayBo -> attendanceRuleDayBo.getWeek() != null
                && attendanceRuleDayBo.getWeek().equals(nowDayWeek)).map(attendanceRuleDayBo -> {
                    AttendanceRuleDayVo attendanceRuleDayVo = new AttendanceRuleDayVo();
                    BeanUtils.copyProperties(attendanceRuleDayBo, attendanceRuleDayVo);
                    return attendanceRuleDayVo;
                }).collect(Collectors.toList());
        // 年级不一致再次过滤
        String gradeFinal = grade;
        if (gradeSameType != null && AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(gradeSameType)) {
            attendanceRuleDayVos = attendanceRuleDayVos.stream().filter(attendanceRuleDayVo -> {
                if (StringUtils.isNotBlank(gradeFinal)) {
                    return gradeFinal.equals(attendanceRuleDayVo.getGrade());
                }
                return true;
            }).collect(Collectors.toList());
        }
        resultVo.setAttendanceRuleDayVos(attendanceRuleDayVos);
        resultVo.setAttendanceLogCensusVos(attendanceLogCensusVos);
        return resultVo;
    }

    /**
     * 统计学生一次考勤的（一个时间）的考勤数据
     *
     * @param userOidRealNameMap the user oid real name map
     * @param signInKey the sign in key
     * @return the attendance log census vo
     * <AUTHOR>
     * @date 2023 -08-27 11:01:53
     */
    private AttendanceLogCensusVo calculateOnceStudentForSign(Map<String, String> userOidRealNameMap,
        Integer allStudentSize, String signInKey) {
        AttendanceLogCensusVo attendanceLogCensusVo = new AttendanceLogCensusVo();
        attendanceLogCensusVo.setSignKey(signInKey);
        attendanceLogCensusVo.setCount(allStudentSize);
        // 处理已打卡学生
        List<AttendanceLogVo> attendDanceVo = this.getCacheAttendanceLogListByKey(signInKey);
        attendDanceVo = CollectionUtil.reverse(attendDanceVo);
        attendDanceVo = this.buildAttendanceUserRealName(userOidRealNameMap, attendDanceVo);
        attendanceLogCensusVo.setClockNum(attendDanceVo.size());
        attendanceLogCensusVo.setClockLog(attendDanceVo);
        // 未打卡数量
        attendanceLogCensusVo.setUnClockNum(attendanceLogCensusVo.getCount() - attendanceLogCensusVo.getClockNum());
        // 未打卡用户信息
        final List<AttendanceLogVo> unClockList = this.buildUnClockList(userOidRealNameMap, attendDanceVo);
        attendanceLogCensusVo.setUnClockLog(unClockList);
        // 计算百分比
        this.calcPercent(attendanceLogCensusVo);
        return attendanceLogCensusVo;
    }

    /**
     * 根据班级和日期预先插入班级用户当天考勤数据（全部正常签到）
     *
     * @param classesId
     * @param date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    @Override
    public AjaxResult preInsertAttendanceLogByClass(Long classesId, String date) {
        Date formatDate = DateKit.string2Date(date, null);
        final List<StudentVo> studentVoList = this.baseDataService.getStudentVoListByClassesId(classesId);
        if (CollectionUtils.isEmpty(studentVoList)) {
            return AjaxResult.fail("班级学生为空");
        }

        ClazzVo clazzVo = baseDataService.getByClazzId(classesId);
        if (null == clazzVo.getId()) {
            return AjaxResult.fail("班级不存在");
        }
        Long organizationId = clazzVo.getOrganizationId();
        String grade = clazzVo.getGrade();

        AttendanceRuleAddBo attendanceRuleByCache = attendanceRuleService
            .getAttendanceRuleByOrganizationIdAndType(organizationId, AttendanceRuleEnums.RULE_STUDENT.getCode());
        Long attendanceRuleId = attendanceRuleByCache.getAttendanceRuleId();

        // 判断考勤日，否则流水转换也会失败,联合校历，判断当前日期是否属于上课日
        AjaxResult ajaxResult =
            getSchoolCalendarDaysByOrganization(attendanceRuleByCache.getOrganizationId(), formatDate);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        Integer week = (Integer)ajaxResult.getData();

        // 根据考勤规则，过滤当前日的规则
        List<AttendanceRuleDayBo> attendanceRuleDays = attendanceRuleByCache.getAttendanceRuleDays();
        final Integer finalWeek = week;
        List<AttendanceRuleDayBo> ruleBoDays =
            attendanceRuleDays.stream().filter(days -> days.getWeek().equals(finalWeek)).collect(Collectors.toList());
        // 学生考勤年级不一致时，再次过滤出对应年级的考勤
        if (AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(attendanceRuleByCache.getGradeSameType())) {
            ruleBoDays = ruleBoDays.stream().filter(days -> days.getGrade().equals(grade)).collect(Collectors.toList());
        }
        // 生成学生考勤数据
        List<AttendanceLogBo> attendanceLogBos = new ArrayList<>();
        for (AttendanceRuleDayBo ruleDayBo : ruleBoDays) {
            // 一组考勤，要生成两个
            Date signDate;
            if (ruleDayBo.getSignInTime() != null) {
                signDate = DateKit.getDateAndTimeCompose(formatDate, ruleDayBo.getSignInTime());
                signDate = DateKit.getAfterMinute(-1, signDate);
                attendanceLogBos.addAll(
                    generateAttendanceLog(studentVoList, organizationId, attendanceRuleId, classesId, grade, signDate));
            }
            if (ruleDayBo.getSignOutTime() != null) {
                signDate = DateKit.getDateAndTimeCompose(formatDate, ruleDayBo.getSignOutTime());
                signDate = DateKit.getAfterMinute(1, signDate);
                attendanceLogBos.addAll(
                    generateAttendanceLog(studentVoList, organizationId, attendanceRuleId, classesId, grade, signDate));
            }
        }

        for (AttendanceLogBo attendanceLogBo : attendanceLogBos) {
            addAttendanceLog(attendanceLogBo);
        }
        return AjaxResult.success();
    }

    /**
     * 生成正常考勤的用户考勤参数
     *
     * @param studentVoList, orgId, attRuleId, classId, grade, signDate
     * @return java.util.List<com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogBo>
     * <AUTHOR>
     * @date 2023/1/18 11:31
     */
    private List<AttendanceLogBo> generateAttendanceLog(List<StudentVo> studentVoList, Long orgId, Long attRuleId,
        Long classId, String grade, Date signDate) {
        List<AttendanceLogBo> attendanceLogBos = new ArrayList<>();
        AttendanceLogBo attendanceLogBo;
        for (StudentVo studentVo : studentVoList) {
            attendanceLogBo = new AttendanceLogBo();
            attendanceLogBo.setUserOid(studentVo.getUserOid());
            attendanceLogBo.setAttendanceType(AttendanceRuleEnums.RULE_STUDENT.getCode());
            attendanceLogBo.setOrganizationId(orgId);
            attendanceLogBo.setAttendanceRuleId(attRuleId);
            attendanceLogBo.setClassesId(classId);
            attendanceLogBo.setGrade(grade);
            attendanceLogBo.setAttendanceTime(signDate);
            attendanceLogBos.add(attendanceLogBo);
        }
        return attendanceLogBos;
    }

    /**
     * 根据userOid和日期预先插入用户当天考勤数据（全部正常签到）
     *
     * @param organizationId, date
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/1/18 11:24
     */
    @Override
    public AjaxResult preInsertAttendanceLogByTeacher(Long organizationId, String date) {
        Date formatDate = DateKit.string2Date(date, null);
        List<TeacherVo> teacherVos = this.baseDataService.getTeacherVoByOrgId(organizationId);
        if (CollectionUtils.isEmpty(teacherVos)) {
            return AjaxResult.fail("教师为空");
        }

        AttendanceRuleAddBo attendanceRuleByCache = attendanceRuleService
            .getAttendanceRuleByOrganizationIdAndType(organizationId, AttendanceRuleEnums.RULE_TEACHER.getCode());
        Long attendanceRuleId = attendanceRuleByCache.getAttendanceRuleId();

        // 判断考勤日，否则流水转换也会失败,联合校历，判断当前日期是否属于上课日
        AjaxResult ajaxResult = getSchoolCalendarDaysByOrganization(organizationId, formatDate);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        Integer week = (Integer)ajaxResult.getData();

        // 根据考勤规则，过滤当前日的规则
        List<AttendanceRuleDayBo> attendanceRuleDays = attendanceRuleByCache.getAttendanceRuleDays();
        final Integer finalWeek = week;
        List<AttendanceRuleDayBo> ruleBoDays =
            attendanceRuleDays.stream().filter(days -> days.getWeek().equals(finalWeek)).collect(Collectors.toList());

        // 生成教师考勤数据
        List<AttendanceLogBo> attendanceLogBos = new ArrayList<>();
        for (AttendanceRuleDayBo ruleDayBo : ruleBoDays) {
            // 一组考勤，要生成两个
            Date signDate;
            if (ruleDayBo.getSignInTime() != null) {
                signDate = DateKit.getDateAndTimeCompose(formatDate, ruleDayBo.getSignInTime());
                signDate = DateKit.getAfterMinute(-1, signDate);
                AttendanceLogBo attendanceLogBo;
                for (TeacherVo teacherVo : teacherVos) {
                    attendanceLogBo = new AttendanceLogBo();
                    attendanceLogBo.setUserOid(teacherVo.getUserOid());
                    attendanceLogBo.setAttendanceType(AttendanceRuleEnums.RULE_TEACHER.getCode());
                    attendanceLogBo.setOrganizationId(organizationId);
                    attendanceLogBo.setAttendanceRuleId(attendanceRuleId);
                    attendanceLogBo.setAttendanceTime(signDate);
                    attendanceLogBos.add(attendanceLogBo);
                }
            }
            if (ruleDayBo.getSignOutTime() != null) {
                signDate = DateKit.getDateAndTimeCompose(formatDate, ruleDayBo.getSignOutTime());
                signDate = DateKit.getAfterMinute(1, signDate);
                AttendanceLogBo attendanceLogBo;
                for (TeacherVo teacherVo : teacherVos) {
                    attendanceLogBo = new AttendanceLogBo();
                    attendanceLogBo.setUserOid(teacherVo.getUserOid());
                    attendanceLogBo.setAttendanceType(AttendanceRuleEnums.RULE_TEACHER.getCode());
                    attendanceLogBo.setOrganizationId(organizationId);
                    attendanceLogBo.setAttendanceRuleId(attendanceRuleId);
                    attendanceLogBo.setAttendanceTime(signDate);
                    attendanceLogBos.add(attendanceLogBo);
                }
            }
        }

        for (AttendanceLogBo attendanceLogBo : attendanceLogBos) {
            addAttendanceLog(attendanceLogBo);
        }
        return AjaxResult.success();
    }

    /**
     * 根据 cache key 获取 考勤记录redis缓存
     *
     * @param key the redis key
     * @return
     */
    private List<AttendanceLogVo> getCacheAttendanceLogListByKey(String key) {

        List<AttendanceLogVo> attendanceLogVos = Lists.newArrayList();
        final Object logVoListObj = this.redisComponent.lGet(key, 0, -1);
        if (logVoListObj != null) {
            final String jsonArray = logVoListObj.toString();
            if (StrUtil.isNotEmpty(jsonArray)) {
                attendanceLogVos = JSONUtil.toList(JSONUtil.parseArray(jsonArray), AttendanceLogVo.class);
                // redisComponent.expire(key, 60 * 60 * 24);
            }
        }
        return attendanceLogVos;
    }

    /**
     * 根据用户OID列表、日期（yyyy-MM-dd） 获取考勤记录列表
     *
     * @param userOids the user oid list 用户OID列表
     * @param day the day yyyy-MM-dd 日期
     * @return the return com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo list
     */
    public List<AttendanceLogVo> getByUserOidsAndDay(Collection<String> userOids, String day) {
        // 数据库查询考勤数据列表
        AttendanceLogListConditionBo bo = new AttendanceLogListConditionBo();
        bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        bo.setAttendanceDay(day);
        bo.setUserOids(userOids);
        return this.getAttendanceLogListByCondition(bo);
    }

    private boolean saveAttendanceLog(AttendanceLogBo attendanceLogBo) {
        Date date = new Date();
        AttendanceLog attendanceLog = new AttendanceLog();
        attendanceLog.setCreateBy(attendanceLogBo.getUserOid());
        attendanceLog.setCreateTime(date);
        attendanceLog.setIsDelete(StatusEnum.NOTDELETE.getCode());
        BeanUtils.copyProperties(attendanceLogBo, attendanceLog);
        return save(attendanceLog);
    }

}