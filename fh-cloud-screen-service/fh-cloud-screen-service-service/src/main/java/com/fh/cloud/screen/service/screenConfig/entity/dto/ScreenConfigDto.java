package com.fh.cloud.screen.service.screenConfig.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云屏配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_config")
public class ScreenConfigDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_config_id", type = IdType.AUTO)
	private Long screenConfigId;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 配置信息
	 */
	@TableField("config_value")
	private String configValue;

	/**
	 * 品牌配置信息
	 */
	@TableField("brand_config_value")
	private String brandConfigValue;

	/**
	 * 描述
	 */
	@TableField("remark")
	private String remark;

	/**
	 * 配置类型 1-偏好配置
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
