package com.fh.cloud.screen.service;

import com.dtflys.forest.springboot.annotation.ForestScan;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.delayqueue.DelayService;
import com.fh.cloud.screen.service.delayqueue.DelayServiceLaunch;
import com.fh.cloud.screen.service.delayqueue.entity.TaskDelayed;
import com.fh.cloud.screen.service.delayqueue.impl.DelayServiceCaptureImpl;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.annotation.PostConstruct;

@EnableFeignClients(
    basePackages = {"com.fh.cloud.screen", "com.light", "com.fh.app.role", "com.fh.basic.third", "com.fh.sso"})
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = {"com.fh.cloud.screen.**.**.*", "com.light.**.**.*",
    "com.fh.app.role.**.**.*", "com.fh.basic.third.**.**.*", "com.fh.sso.**.**.*"})
@MapperScan({"com.fh.cloud.screen.**.mapper"})
@EnableSwagger2
@EnableKnife4j
@ForestScan(basePackages = {"com.fh.cloud.screen.service.tts.service"})
public class CloudScreenServiceServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CloudScreenServiceServiceApplication.class, args);
    }

    @Value("${school.year.month:8}")
    private int SCHOOL_YEAR_MONTH;
    @Value("${school.year.day:20}")
    private int SCHOOL_YEAR_DAY;
    @Autowired
    private DelayServiceLaunch delayServiceLaunch;

    @PostConstruct
    public void init() {
        initConfig();
        initDelayQueue();
    }

    public void initConfig() {
        ConstantsConfig.SCHOOL_YEAR_MONTH = this.SCHOOL_YEAR_MONTH;
        ConstantsConfig.SCHOOL_YEAR_DAY = this.SCHOOL_YEAR_DAY;
    }

    public void initDelayQueue() {
        delayServiceLaunch.setRunMode(DelayServiceLaunch.RunModeType.AUTO.getValue()).run();
    }
}
