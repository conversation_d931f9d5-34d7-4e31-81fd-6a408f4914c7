package com.fh.cloud.screen.service.attendance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRule;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRuleDay;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleAddVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo;
import com.fh.cloud.screen.service.attendance.mapper.AttendanceRuleMapper;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleDayService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.enums.AttendanceRuleEnums;
import com.fh.cloud.screen.service.enums.AttendanceRuleRedisKeyEnum;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考勤规则表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Service
public class AttendanceRuleServiceImpl extends ServiceImpl<AttendanceRuleMapper, AttendanceRule>
    implements IAttendanceRuleService {

    @Resource
    private AttendanceRuleMapper attendanceRuleMapper;

    @Resource
    private IAttendanceRuleDayService attendanceRuleDayService;

    @Resource
    private RedisComponent redisComponent;

    @Override
    public List<AttendanceRuleVo> getAttendanceRuleListByCondition(AttendanceRuleListConditionBo condition) {
        return attendanceRuleMapper.getAttendanceRuleListByCondition(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateAttendanceRule(AttendanceRuleAddBo attendanceRuleAddBo) {

        if (AttendanceRuleEnums.RULE_TEACHER.getCode().equals(attendanceRuleAddBo.getAttendanceType())) {
            attendanceRuleAddBo.setGradeSameType(AttendanceRuleEnums.GRADE_SAME_IS.getCode());
        }
        // 修改操作，更新子表
        if (null != attendanceRuleAddBo.getAttendanceRuleId()) {
            AttendanceRule attendanceRule = baseMapper.selectById(attendanceRuleAddBo.getAttendanceRuleId());
            if (null == attendanceRule) {
                return false;
            }
            if (AttendanceRuleEnums.RULE_TEACHER.getCode().equals(attendanceRuleAddBo.getAttendanceType())) {
                attendanceRuleDayService.remove(new LambdaQueryWrapper<AttendanceRuleDay>()
                    .eq(AttendanceRuleDay::getAttendanceRuleId, attendanceRuleAddBo.getAttendanceRuleId()));

            } else {
                // 之前年级不一致，现在年级不一致，覆盖同年级
                if (AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(attendanceRule.getGradeSameType())
                    && AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(attendanceRuleAddBo.getGradeSameType())
                    && null != attendanceRuleAddBo.getAttendanceRuleDays().get(0).getAttendanceRuleDayId()) {
                    attendanceRuleDayService.remove(new LambdaQueryWrapper<AttendanceRuleDay>()
                        .eq(AttendanceRuleDay::getAttendanceRuleId, attendanceRuleAddBo.getAttendanceRuleId())
                        .eq(AttendanceRuleDay::getGrade,
                            attendanceRuleAddBo.getAttendanceRuleDays().get(0).getGrade()));
                }
                // 之前年级一致，现在年级不一致。
                else if (AttendanceRuleEnums.GRADE_SAME_IS.getCode().equals(attendanceRule.getGradeSameType())
                    && AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(attendanceRuleAddBo.getGradeSameType())) {
                    attendanceRuleDayService.remove(new LambdaQueryWrapper<AttendanceRuleDay>()
                        .eq(AttendanceRuleDay::getAttendanceRuleId, attendanceRuleAddBo.getAttendanceRuleId()));
                }
                // 年级一致时，
                else if (attendanceRuleAddBo.getGradeSameType().equals(AttendanceRuleEnums.GRADE_SAME_IS.getCode())) {
                    attendanceRuleDayService.remove(new LambdaQueryWrapper<AttendanceRuleDay>()
                        .eq(AttendanceRuleDay::getAttendanceRuleId, attendanceRuleAddBo.getAttendanceRuleId()));
                }
            }
        } else {
            // 新增 校验唯一性
            AttendanceRuleVo infoByOrganizationId = getInfoByOrganizationId(attendanceRuleAddBo.getOrganizationId(),
                attendanceRuleAddBo.getAttendanceType());
            if (null != infoByOrganizationId.getAttendanceRuleId()) {
                return false;
            }
        }

        AttendanceRule attendanceRuleDto = new AttendanceRule();
        BeanUtils.copyProperties(attendanceRuleAddBo, attendanceRuleDto);
        attendanceRuleDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
        this.saveOrUpdate(attendanceRuleDto);

        List<AttendanceRuleDay> dayList = new ArrayList<>();
        for (AttendanceRuleDayBo attendanceRuleDayBo : attendanceRuleAddBo.getAttendanceRuleDays()) {
            AttendanceRuleDay day = new AttendanceRuleDay();
            BeanUtils.copyProperties(attendanceRuleDayBo, day);
            day.setAttendanceRuleId(attendanceRuleDto.getAttendanceRuleId());
            // 年级一致，年级字段赋默认值
            if (AttendanceRuleEnums.GRADE_SAME_IS.getCode().equals(attendanceRuleAddBo.getGradeSameType())) {
                day.setGrade("0");
            }
            day.setIsDelete(StatusEnum.NOTDELETE.getCode());
            dayList.add(day);
        }

        // redis存规则id+
        dayList =
            dayList.stream().sorted(Comparator.comparing(AttendanceRuleDay::getWeek)).collect(Collectors.toList());
        boolean saveBatchFlag = attendanceRuleDayService.saveBatch(dayList);
        if (saveBatchFlag) {
            // 存入考勤规则id
            // redisComponent.set(AttendanceRuleRedisKeyEnum.ATTENDANCE_RULE_ID_KEY.getValue()
            // .concat(attendanceRuleDto.getOrganizationId().toString())
            // .concat(attendanceRuleDto.getAttendanceType().toString()), attendanceRuleDto.getAttendanceRuleId());
        }
        return saveBatchFlag;
    }

    @Override
    public boolean updateAttendanceRule(AttendanceRuleBo attendanceRuleBo) {
        return false;
    }

    @Override
    public AttendanceRuleAddVo getDetail(AttendanceRuleBo attendanceRuleBo) {
        AttendanceRuleVo attendanceRuleVo =
            getInfoByOrganizationId(attendanceRuleBo.getOrganizationId(), attendanceRuleBo.getAttendanceType());
        if (null == attendanceRuleVo) {
            return null;
        }
        AttendanceRuleAddVo ruleAddVo = new AttendanceRuleAddVo();
        BeanUtils.copyProperties(attendanceRuleVo, ruleAddVo);

        LambdaQueryWrapper<AttendanceRuleDay> lqw = new LambdaQueryWrapper<>();
        lqw.eq(AttendanceRuleDay::getAttendanceRuleId, attendanceRuleVo.getAttendanceRuleId());
        if (AttendanceRuleEnums.RULE_STUDENT.getCode().equals(attendanceRuleVo.getAttendanceType())
            && StringUtils.isNotEmpty(attendanceRuleBo.getGrade())
            && AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(attendanceRuleVo.getGradeSameType())) {
            lqw.eq(AttendanceRuleDay::getGrade, attendanceRuleBo.getGrade());
        }
        List<AttendanceRuleDay> ruleDays = attendanceRuleDayService.list(lqw);
        if (CollectionUtils.isNotEmpty(ruleDays)) {
            final List<AttendanceRuleDayVo> dayVos = ruleDays.stream()
                .map(val -> BeanUtil.toBean(val, AttendanceRuleDayVo.class)).collect(Collectors.toList());
            ruleAddVo.setAttendanceRuleDays(dayVos);
        }

        return ruleAddVo;
    }

    @Override
    public AttendanceRuleVo getInfoByOrganizationId(Long organizationId, Integer attendanceType) {
        AttendanceRuleVo attendanceRuleVo = new AttendanceRuleVo();
        AttendanceRule attendanceRule = baseMapper
            .selectOne(new LambdaQueryWrapper<AttendanceRule>().eq(AttendanceRule::getOrganizationId, organizationId)
                .eq(AttendanceRule::getAttendanceType, attendanceType));
        if (null != attendanceRule) {
            BeanUtils.copyProperties(attendanceRule, attendanceRuleVo);
        }
        return attendanceRuleVo;
    }

    /**
     * 通过date 获取最近的考勤规则时间
     *
     * @param attendanceRuleBo
     * @return java.sql.Date yyyy-MM-dd HH:mm:ss
     * <AUTHOR>
     * @date 2022/6/16 15:24
     */
    @Override
    public String getRuleDataTimeByCondition(AttendanceRuleBo attendanceRuleBo) {
        // 从缓存获取考勤规则
        AttendanceRuleAddBo attendanceRuleByCache = getAttendanceRuleByOrganizationIdAndType(
            attendanceRuleBo.getOrganizationId(), attendanceRuleBo.getAttendanceType());
        if (null == attendanceRuleByCache) {
            return null;
        }

        List<AttendanceRuleDayBo> list = attendanceRuleByCache.getAttendanceRuleDays();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<AttendanceRuleDayBo> dayList =
            list.stream().filter(day -> DateKit.getWeekIdByDate(attendanceRuleBo.getNowDate()) == day.getWeek())
                .collect(Collectors.toList());
        if (null != attendanceRuleBo.getGrade()
            && AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(attendanceRuleByCache.getGradeSameType())) {
            dayList = list.stream().filter(day -> day.getGrade().equals(attendanceRuleBo.getGrade()))
                .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(dayList)) {
            return null;
        }
        // 最小间隔时间(分钟) 初始化
        Date signDate = null;
        long minSign = 60 * 24L;
        for (AttendanceRuleDayBo attendanceRuleDay : dayList) {
            Date ruleSignInDate = DateKit.transferYMD2CurrentDay(attendanceRuleDay.getSignInTime());
            Date ruleSignOutDate = DateKit.transferYMD2CurrentDay(attendanceRuleDay.getSignOutTime());
            long signInDiff = Math.abs(DateKit.getDatePoor(attendanceRuleBo.getNowDate(), ruleSignInDate));
            long signOutDiff = Math.abs(DateKit.getDatePoor(attendanceRuleBo.getNowDate(), ruleSignOutDate));
            if (signInDiff < minSign) {
                minSign = signInDiff;
                signDate = DateKit.transferYMD2CurrentDay(attendanceRuleDay.getSignInTime());
            }
            if (signOutDiff < minSign) {
                minSign = signOutDiff;
                signDate = DateKit.transferYMD2CurrentDay(attendanceRuleDay.getSignOutTime());
            }
        }
        if (null == signDate) {
            return null;
        }
        return DateKit.date2String(signDate, null);
    }

    @Override
    public AttendanceRuleAddBo getAttendanceRuleByOrganizationIdAndType(Long organizationId, Integer attendanceType) {
        if (organizationId == null || attendanceType == null) {
            return null;
        }
        String firstKey = AttendanceRuleRedisKeyEnum.ATTENDANCE_RULE_KEY.getValue();
        String secondKey =
            String.valueOf(organizationId).concat(ConstString.ywmh).concat(String.valueOf(attendanceType));
        Object obj = redisComponent.hget(firstKey, secondKey);
        AttendanceRuleAddBo ruleBo = null;
        if (null != obj) {
            String json = JSONObject.toJSONString(obj);
            ruleBo = JSONObject.parseObject(json, AttendanceRuleAddBo.class);
        }
        return ruleBo;
    }

    @Override
    public AttendanceRule getAttendanceRuleId(Long attendanceRuleId) {
        QueryWrapper<AttendanceRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AttendanceRule::getAttendanceRuleId, attendanceRuleId).eq(AttendanceRule::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        return this.attendanceRuleMapper.selectOne(queryWrapper);
    }
}