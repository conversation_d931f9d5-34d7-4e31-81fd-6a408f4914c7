package com.fh.cloud.screen.service.screen.service.impl;

import com.fh.cloud.screen.service.screen.entity.bo.*;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryLikesDto;
import com.fh.cloud.screen.service.screen.mapper.ScreenPoetryLikesMapper;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryContentDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo;
import com.fh.cloud.screen.service.screen.service.IScreenPoetryContentService;
import com.fh.cloud.screen.service.screen.mapper.ScreenPoetryContentMapper;
import com.light.core.entity.AjaxResult;
/**
 * 共话诗词表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
@Service
public class ScreenPoetryContentServiceImpl extends ServiceImpl<ScreenPoetryContentMapper, ScreenPoetryContentDto> implements IScreenPoetryContentService {

	@Resource
	private ScreenPoetryContentMapper screenPoetryContentMapper;

	@Resource
	private ScreenPoetryLikesMapper screenPoetryLikesMapper;
	
    @Override
	public List<ScreenPoetryContentVo> getScreenPoetryContentListByCondition(ScreenPoetryContentConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return screenPoetryContentMapper.getScreenPoetryContentListByCondition(condition);
	}

	@Override
	public AjaxResult addScreenPoetryContent(ScreenPoetryContentBo screenPoetryContentBo) {
		ScreenPoetryContentDto screenPoetryContent = new ScreenPoetryContentDto();
		BeanUtils.copyProperties(screenPoetryContentBo, screenPoetryContent);
		screenPoetryContent.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(screenPoetryContent)){
			// 生成点赞记录
			ScreenPoetryLikesDto likesDto = new ScreenPoetryLikesDto();
			likesDto.setScreenPoetryContentId(screenPoetryContent.getScreenPoetryContentId());
			screenPoetryLikesMapper.insert(likesDto);

			ScreenPoetryContentVo screenPoetryContentVo = new ScreenPoetryContentVo();
			BeanUtils.copyProperties(screenPoetryContent, screenPoetryContentVo);

			return AjaxResult.success(screenPoetryContentVo);
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScreenPoetryContent(ScreenPoetryContentBo screenPoetryContentBo) {
		ScreenPoetryContentDto screenPoetryContent = new ScreenPoetryContentDto();
		BeanUtils.copyProperties(screenPoetryContentBo, screenPoetryContent);
		if(updateById(screenPoetryContent)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ScreenPoetryContentVo getScreenPoetryContentByCondition(ScreenPoetryContentConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return screenPoetryContentMapper.getScreenPoetryContentByCondition(condition);
	}

}