package com.fh.cloud.screen.service.syllabus.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;
import com.fh.cloud.screen.service.rest.service.IWorkRestDayService;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.syllabus.api.SyllabusInfoApi;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoWithRestVo;
import com.fh.cloud.screen.service.syllabus.service.ISyllabusInfoService;
import com.light.core.constants.SystemConstants;
import com.light.core.enums.StatusEnum;
import com.light.user.clazz.entity.vo.ClazzVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课表信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:22:20
 */
@RestController
@Validated
public class SyllabusInfoController implements SyllabusInfoApi {

    @Autowired
    private ISyllabusInfoService syllabusInfoService;
    @Autowired
    private IWorkRestDayService workRestDayService;
    @Autowired
    private BaseDataService baseDataService;

    /**
     * 查询课表信息分页列表
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:22:20
     */
    @Override
    public AjaxResult<PageInfo<SyllabusInfoVo>>
        getSyllabusInfoPageListByCondition(@RequestBody SyllabusInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SyllabusInfoVo> pageInfo =
            new PageInfo<>(syllabusInfoService.getSyllabusInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询课表信息列表
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:22:20
     */
    @Override
    public AjaxResult<List<SyllabusInfoVo>>
        getSyllabusInfoListByCondition(@RequestBody SyllabusInfoConditionBo condition) {
        List<SyllabusInfoVo> list = syllabusInfoService.getSyllabusInfoListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增课表信息
     * 
     * <AUTHOR>
     * @date 2023-09-18 15:22:20
     */
    @Override
    public AjaxResult addSyllabusInfo(@Validated @RequestBody SyllabusInfoBo syllabusInfoBo) {
        return syllabusInfoService.addSyllabusInfo(syllabusInfoBo);
    }

    /**
     * 修改课表信息
     * 
     * @param syllabusInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-18 15:22:20
     */
    @Override
    public AjaxResult updateSyllabusInfo(@Validated @RequestBody SyllabusInfoBo syllabusInfoBo) {
        if (null == syllabusInfoBo.getSyllabusId()) {
            return AjaxResult.fail("课表信息id不能为空");
        }
        return syllabusInfoService.updateSyllabusInfo(syllabusInfoBo);
    }

    /**
     * 查询课表信息详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-18 15:22:20
     */
    @Override
    public AjaxResult<SyllabusInfoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("课表信息id不能为空");
        }
        SyllabusInfoConditionBo condition = new SyllabusInfoConditionBo();
        condition.setSyllabusId(id);
        SyllabusInfoVo vo = syllabusInfoService.getSyllabusInfoByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除课表信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-09-18 15:22:20
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        SyllabusInfoBo syllabusInfoBo = new SyllabusInfoBo();
        syllabusInfoBo.setSyllabusId(id);
        syllabusInfoBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        AjaxResult ajaxResult = syllabusInfoService.updateSyllabusInfo(syllabusInfoBo);
        if (ajaxResult.isSuccess()) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult<SyllabusInfoWithRestVo> getSyllabusInfoOfClasses(SyllabusInfoConditionBo syllabusInfoBo) {
        SyllabusInfoWithRestVo syllabusInfoWithRestVo = new SyllabusInfoWithRestVo();
        if (syllabusInfoBo.getOrganizationId() == null || syllabusInfoBo.getClassesId() == null) {
            return AjaxResult.success(syllabusInfoWithRestVo);
        }

        // 课表信息
        SyllabusInfoConditionBo condition = new SyllabusInfoConditionBo();
        condition.setOrganizationId(syllabusInfoBo.getOrganizationId());
        condition.setClassesId(syllabusInfoBo.getClassesId());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setStatus(StatusEnum.ENABLE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setOrderBy("week_id,sort");
        List<SyllabusInfoVo> list = syllabusInfoService.getSyllabusInfoListByCondition(condition);
        syllabusInfoWithRestVo.setSyllabusInfoVoList(list);

        // 作息时间
        ClazzVo clazzVo = baseDataService.getByClazzId(syllabusInfoBo.getClassesId());
        String grade = clazzVo == null ? null : clazzVo.getGrade();
        List<WorkRestDayVo> workRestDayVos =
            workRestDayService.getWorkRestDayListByGrade(syllabusInfoBo.getOrganizationId(), grade);
        syllabusInfoWithRestVo.setWorkRestDayVoList(workRestDayVos);
        return AjaxResult.success(syllabusInfoWithRestVo);
    }
}
