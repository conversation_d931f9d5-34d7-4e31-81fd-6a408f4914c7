package com.fh.cloud.screen.service.space.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.ClassesInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;

import java.util.List;

/**
 * 行政区域内容扩展信息表（班级扩展信息）Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 16:11:44
 */
public interface ClassesInfoMapper extends BaseMapper<ClassesInfo> {

    List<ClassesInfoVo> getClassesInfoListByCondition(ClassesInfoListConditionBo condition);

}
