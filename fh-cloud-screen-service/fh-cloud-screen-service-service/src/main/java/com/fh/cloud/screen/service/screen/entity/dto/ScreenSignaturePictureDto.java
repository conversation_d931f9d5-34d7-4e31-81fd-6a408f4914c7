package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 电子签名图片资源表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_signature_picture")
public class ScreenSignaturePictureDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_signature_picture_id", type = IdType.AUTO)
	private Long screenSignaturePictureId;

	/**
	 * 所属组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 电子签名图片url
	 */
	@TableField("screen_signature_picture_media_url")
	private String screenSignaturePictureMediaUrl;

	/**
	 * 电子签名图片名称（不包含后缀）
	 */
	@TableField("screen_signature_picture_media_name")
	private String screenSignaturePictureMediaName;

	/**
	 * 电子签名图片名称（包含后缀）
	 */
	@TableField("screen_signature_picture_media_name_ori")
	private String screenSignaturePictureMediaNameOri;

	/**
	 * 电子签名图片fileOid
	 */
	@TableField("screen_signature_picture_media_id")
	private String screenSignaturePictureMediaId;

	/**
	 * 文件md5
	 */
	@TableField("screen_signature_picture_media_md5")
	private String screenSignaturePictureMediaMd5;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
