package com.fh.cloud.screen.service.attendance.controller;

import com.fh.cloud.screen.service.attendance.api.AttendanceRuleDayApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleDayService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 考勤规则天表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@RestController
@Validated
@Api(value = "", tags = "考勤规则(天)接口")
@RequestMapping("/attendance-day")
public class AttendanceRuleDayController implements AttendanceRuleDayApi {

    @Autowired
    private IAttendanceRuleDayService attendanceRuleDayService;

    /**
     * 查询考勤规则天表列表
     * 
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询考勤规则天表列表", httpMethod = "POST")
    public AjaxResult getAttendanceRuleDayListByCondition(@RequestBody AttendanceRuleDayListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<AttendanceRuleDayVo> pageInfo =
            new PageInfo<>(attendanceRuleDayService.getAttendanceRuleDayListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("attendanceRuleDayList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增考勤规则天表
     * 
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增考勤规则天表", httpMethod = "POST")
    public AjaxResult addAttendanceRuleDay(@Validated @RequestBody AttendanceRuleDayBo attendanceRuleDayBo) {
        boolean save = attendanceRuleDayService.addAttendanceRuleDay(attendanceRuleDayBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改考勤规则天表
     * 
     * @param attendanceRuleDayBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改考勤规则天表", httpMethod = "POST")
    public AjaxResult updateAttendanceRuleDay(@Validated @RequestBody AttendanceRuleDayBo attendanceRuleDayBo) {
        if (null == attendanceRuleDayBo.getAttendanceRuleDayId()) {
            return AjaxResult.fail("考勤规则天表id不能为空");
        }
        boolean update = attendanceRuleDayService.updateAttendanceRuleDay(attendanceRuleDayBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询考勤规则天表详情
     * 
     * @param attendanceRuleDayId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询考勤规则天表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long attendanceRuleDayId) {
        Map<String, Object> map = attendanceRuleDayService.getDetail(attendanceRuleDayId);
        return AjaxResult.success(map);
    }

    /**
     * 删除考勤规则天表
     * 
     * @param attendanceRuleDayId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除考勤规则天表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long attendanceRuleDayId) {
        AttendanceRuleDayBo attendanceRuleDayBo = new AttendanceRuleDayBo();
        attendanceRuleDayBo.setAttendanceRuleDayId(attendanceRuleDayId);
        attendanceRuleDayBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = attendanceRuleDayService.updateAttendanceRuleDay(attendanceRuleDayBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
