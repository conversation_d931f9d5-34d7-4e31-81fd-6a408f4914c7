package com.fh.cloud.screen.service.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserResultVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.light.core.entity.AjaxResult;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface IAttendanceUserService extends IService<AttendanceUser> {

    /**
     * 条件筛选
     * 
     * @param condition
     * @return
     */
    List<AttendanceUserVo> getAttendanceUserListByCondition(AttendanceUserListConditionBo condition);

    /**
     * 根据 日期 及其他 条件筛选
     * 
     * @param condition
     * @return
     */
    AttendanceUserCensusVo getByDateCondition(AttendanceUserListConditionBo condition);

    /**
     * 查询导出的考勤数据-按每次考勤分组
     *
     * @param condition
     * @return
     */
    AttendanceUserResultVo getListExportByDateCondition(AttendanceUserListConditionBo condition);

    boolean addAttendanceUser(AttendanceUserBo attendanceUserBo);

    boolean updateAttendanceUser(AttendanceUserBo attendanceUserBo);

    Map<String, Object> getDetail(Long attendanceUserId);

    AjaxResult changeState(AttendanceUserBo bo);

    /**
     * 批量保存
     *
     * @param attendanceUserDtoList
     * @return void
     * <AUTHOR>
     * @date 2022/6/22 11:12
     */
    void saveBatchAndDetail(List<AttendanceUser> attendanceUserDtoList);

    /**
     *  根据日期、用户OID列表 获取已经签到的用户记录
     * @param userOids
     * @param date
     * @return
     */
    List<AttendanceUserVo> getSignInListByUserOidsAndDateDay(Collection<String> userOids, String date);

    /**
     * 根据ID 获取 考勤信息
     * @param attendanceUserId the attendance user id 考勤ID
     * @return
     */
    AttendanceUser getByAttendanceUserId(Long attendanceUserId);

}
