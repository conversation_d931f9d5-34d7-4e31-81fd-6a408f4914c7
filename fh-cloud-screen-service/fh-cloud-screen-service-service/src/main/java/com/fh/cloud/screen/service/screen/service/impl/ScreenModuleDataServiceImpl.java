package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.enums.ScreenModulePresetType;
import com.fh.cloud.screen.service.enums.ScreenModuleSourceType;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleData;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleDataMapper;
import com.fh.cloud.screen.service.screen.service.IScreenModuleDataService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云屏模块表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Service
public class ScreenModuleDataServiceImpl extends ServiceImpl<ScreenModuleDataMapper, ScreenModuleData>
    implements IScreenModuleDataService {

    @Resource
    private ScreenModuleDataMapper screenModuleDataMapper;

    @Lazy
    @Resource
    private IScreenModuleLibraryService screenModuleLibraryService;

    @Lazy
    @Resource
    private IScreenModuleDataService screenModuleDataService;

    @Override
    public List<ScreenModuleDataVo> getScreenModuleDataListByCondition(ScreenModuleDataListConditionBo condition) {
        List<ScreenModuleDataVo> screenModuleDataVos =
            screenModuleDataMapper.getScreenModuleDataListByCondition(condition);
        if (CollectionUtils.isEmpty(screenModuleDataVos)) {
            return Lists.newArrayList();
        }
        screenModuleDataVos.forEach(screenModuleDataVo -> {
            if (screenModuleDataVo.getModuleSource() != null
                && screenModuleDataVo.getModuleSource().equals(ScreenModuleSourceType.CUSTOM.getValue())) {
                screenModuleDataVo.setModuleGroupType(screenModuleDataVo.getCustomModuleGroupType());
                screenModuleDataVo.setModuleName(screenModuleDataVo.getCustomModuleName());
            }
        });
        return screenModuleDataVos;
    }

    @Override
    public boolean addScreenModuleData(ScreenModuleDataBo screenModuleDataBo) {
        ScreenModuleData screenModuleData = new ScreenModuleData();
        BeanUtils.copyProperties(screenModuleDataBo, screenModuleData);
        screenModuleData.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(screenModuleData);
    }

    @Override
    public boolean updateScreenModuleData(ScreenModuleDataBo screenModuleDataBo) {
        ScreenModuleData screenModuleData = new ScreenModuleData();
        BeanUtils.copyProperties(screenModuleDataBo, screenModuleData);
        return updateById(screenModuleData);
    }

    @Override
    public ScreenModuleDataVo getDetail(Long screenModuleDataId) {
        if (screenModuleDataId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenModuleData> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenModuleData::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenModuleData::getScreenModuleDataId, screenModuleDataId);
        ScreenModuleData screenModuleData = getOne(lqw);
        ScreenModuleDataVo screenModuleDataVo = new ScreenModuleDataVo();
        BeanUtils.copyProperties(screenModuleData, screenModuleDataVo);
        return screenModuleDataVo;
    }

    @Override
    public void initScreenModuleData(Long organizationId) {
        if (organizationId == null) {
            return;
        }
        ScreenModuleLibraryListConditionBo condition = new ScreenModuleLibraryListConditionBo();
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setPresetType(ScreenModulePresetType.PRESET.getValue());
        condition.getPresetType();
        List<ScreenModuleLibraryVo> screenModuleLibraryVos =
            screenModuleLibraryService.getScreenModuleLibraryListByCondition(condition);
        if (CollectionUtils.isEmpty(screenModuleLibraryVos)) {
            return;
        }

        List<ScreenModuleData> screenModuleDataList = screenModuleLibraryVos.stream().map(screenModuleLibraryVo -> {
            ScreenModuleData screenModuleData = new ScreenModuleData();
            screenModuleData.setModuleSource(ScreenModuleSourceType.PRESET.getValue());
            screenModuleData.setIsDelete(StatusEnum.NOTDELETE.getCode());
            screenModuleData.setScreenModuleLibraryId(screenModuleLibraryVo.getScreenModuleLibraryId());
            screenModuleData.setOrganizationId(organizationId);
            return screenModuleData;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(screenModuleDataList)) {
            saveBatch(screenModuleDataList);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void updatePresetModule(ScreenModuleDataBo screenModuleDataBo) {
        List<Long> selectModuleLibraryIds = screenModuleDataBo.getSelectModuleLibraryIds();
        if (CollectionUtils.isEmpty(selectModuleLibraryIds)) {
            return;
        }

        Long organizationId = screenModuleDataBo.getOrganizationId();
        ScreenModuleDataListConditionBo condition = new ScreenModuleDataListConditionBo();
        condition.setPageNo(SystemConstants.NO_PAGE);
        condition.setModuleSource(ScreenModuleSourceType.PRESET.getValue());
        condition.setOrganizationId(organizationId);
        List<ScreenModuleDataVo> screenModuleDataVos =
            screenModuleDataService.getScreenModuleDataListByCondition(condition);

        // 删除 -> 不删除(screenModuleDataIds)
        List<Long> screenModuleDataIdsDeleteYes2No = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(screenModuleDataVos) && CollectionUtils.isNotEmpty(selectModuleLibraryIds)) {
            screenModuleDataIdsDeleteYes2No = screenModuleDataVos.stream()
                .filter(screenModuleDataVo -> selectModuleLibraryIds
                    .contains(screenModuleDataVo.getScreenModuleLibraryId()))
                .filter(screenModuleDataVo -> screenModuleDataVo.getIsDelete().equals(StatusEnum.ISDELETE.getCode()))
                .map(ScreenModuleDataVo::getScreenModuleDataId).collect(Collectors.toList());
        }
        // 不删除 -> 删除(screenModuleDataIds)
        List<Long> screenModuleDataIdsDeleteNo2Yes = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(screenModuleDataVos)) {
            screenModuleDataIdsDeleteNo2Yes = screenModuleDataVos.stream().filter(
                screenModuleDataVo -> !selectModuleLibraryIds.contains(screenModuleDataVo.getScreenModuleLibraryId()))
                .filter(screenModuleDataVo -> screenModuleDataVo.getIsDelete().equals(StatusEnum.NOTDELETE.getCode()))
                .map(ScreenModuleDataVo::getScreenModuleDataId).collect(Collectors.toList());
        }
        // 需要新增(screenModuleLibraryIds)
        List<Long> screenModuleLibraryIdsAdd = Lists.newArrayList();
        List<Long> screenModuleLibraryIdsExist = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(screenModuleDataVos)) {
            screenModuleLibraryIdsExist = screenModuleDataVos.stream().map(ScreenModuleDataVo::getScreenModuleLibraryId)
                .collect(Collectors.toList());
        }
        List<Long> finalScreenModuleLibraryIdsExist = screenModuleLibraryIdsExist;
        if (CollectionUtils.isNotEmpty(selectModuleLibraryIds)) {
            screenModuleLibraryIdsAdd = selectModuleLibraryIds.stream()
                .filter(moduleLibraryId -> !finalScreenModuleLibraryIdsExist.contains(moduleLibraryId))
                .collect(Collectors.toList());
        }
        List<ScreenModuleData> screenModuleDataList = screenModuleLibraryIdsAdd.stream().map(screenModuleLibraryId -> {
            ScreenModuleData screenModuleData = new ScreenModuleData();
            screenModuleData.setModuleSource(ScreenModuleSourceType.PRESET.getValue());
            screenModuleData.setIsDelete(StatusEnum.NOTDELETE.getCode());
            screenModuleData.setScreenModuleLibraryId(screenModuleLibraryId);
            screenModuleData.setOrganizationId(organizationId);
            return screenModuleData;
        }).collect(Collectors.toList());

        // db操作
        screenModuleDataService.updateDelete(screenModuleDataIdsDeleteYes2No, StatusEnum.NOTDELETE.getCode());
        screenModuleDataService.updateDelete(screenModuleDataIdsDeleteNo2Yes, StatusEnum.ISDELETE.getCode());
        if (CollectionUtils.isNotEmpty(screenModuleDataList)) {
            screenModuleDataService.saveBatch(screenModuleDataList);
        }
    }

    @Override
    public void updateDelete(List<Long> screenModuleDataIds, Integer isDelete) {
        if (CollectionUtils.isEmpty(screenModuleDataIds)) {
            return;
        }

        UpdateWrapper<ScreenModuleData> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().in(true, ScreenModuleData::getScreenModuleDataId, screenModuleDataIds)
            .set(ScreenModuleData::getIsDelete, isDelete);
        this.baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<ScreenModuleDataVo> listByScreenSceneIds(List<Long> screenSceneIds) {
        if (CollectionUtils.isEmpty(screenSceneIds)) {
            return Lists.newArrayList();
        }
        List<ScreenModuleDataVo> screenModuleDataVos = screenModuleDataMapper.listByScreenSceneIds(screenSceneIds);
        if (CollectionUtils.isEmpty(screenModuleDataVos)) {
            return Lists.newArrayList();
        }
        screenModuleDataVos.forEach(screenModuleDataVo -> {
            if (screenModuleDataVo.getModuleSource() != null
                && screenModuleDataVo.getModuleSource().equals(ScreenModuleSourceType.CUSTOM.getValue())) {
                screenModuleDataVo.setModuleGroupType(screenModuleDataVo.getCustomModuleGroupType());
                screenModuleDataVo.setModuleName(screenModuleDataVo.getCustomModuleName());
            }
        });
        return screenModuleDataVos;
    }

    @Override
    public boolean checkCustomModuleNameExist(ScreenModuleDataBo screenModuleDataBo) {
        List<ScreenModuleDataVo> screenModuleDataVos =
            screenModuleDataMapper.listCustomModuleDataVosByName(screenModuleDataBo);
        return CollectionUtils.isNotEmpty(screenModuleDataVos) ? true : false;
    }

    @Override
    public Long getScreenModuleDataIdByScreenModuleLibraryId(Long screenModuleLibraryId, Long organizationId) {
        if (screenModuleLibraryId == null) {
            return null;
        }
        ScreenModuleDataListConditionBo conditionBo = new ScreenModuleDataListConditionBo();
        conditionBo.setScreenModuleLibraryId(screenModuleLibraryId);
        conditionBo.setOrganizationId(organizationId);
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleDataVo> screenModuleDataListByCondition =
            screenModuleDataService.getScreenModuleDataListByCondition(conditionBo);
        if (CollectionUtils.isEmpty(screenModuleDataListByCondition)) {
            return null;
        }
        ScreenModuleDataVo screenModuleDataVo = screenModuleDataListByCondition.get(0);
        return screenModuleDataVo.getScreenModuleDataId();
    }
}