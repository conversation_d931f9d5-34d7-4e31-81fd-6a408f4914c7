package com.fh.cloud.screen.service.er.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考场_考试计划里面一次考试的老师
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("er_exam_info_teacher")
public class ExamInfoTeacherDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "exam_info_teacher_id", type = IdType.AUTO)
	private Long examInfoTeacherId;

	/**
	 * 考试科目表id
	 */
	@TableField("exam_info_subject_id")
	private Long examInfoSubjectId;

	/**
	 * 考试id
	 */
	@TableField("exam_info_id")
	private Long examInfoId;

	/**
	 * 考试计划id
	 */
	@TableField("exam_plan_id")
	private Long examPlanId;

	/**
	 * 教师的user_oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 教师的姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 用户来源类型：1校内，2校外
	 */
	@TableField("user_from_type")
	private Integer userFromType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
