package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDetailDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 放学配置详情表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
public interface ILeaveSchoolConfigDetailService extends IService<LeaveSchoolConfigDetailDto> {

    List<LeaveSchoolConfigDetailVo> getLeaveSchoolConfigDetailListByCondition(LeaveSchoolConfigDetailConditionBo condition);

	AjaxResult addLeaveSchoolConfigDetail(LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo);

	AjaxResult updateLeaveSchoolConfigDetail(LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo);

	LeaveSchoolConfigDetailVo getLeaveSchoolConfigDetailByCondition(LeaveSchoolConfigDetailConditionBo condition);

	/**
	 * 删除后批量新增
	 *
	 * @param leaveSchoolConfigId 
	 * @param configDetailList 
	 * @return boolean 
	 * <AUTHOR>
	 * @date 2023/8/23 11:46
	 **/
	boolean deleteAndAddLeaveSchoolConfigDetailList(Long leaveSchoolConfigId, List<LeaveSchoolConfigDetailBo> configDetailList);

}

