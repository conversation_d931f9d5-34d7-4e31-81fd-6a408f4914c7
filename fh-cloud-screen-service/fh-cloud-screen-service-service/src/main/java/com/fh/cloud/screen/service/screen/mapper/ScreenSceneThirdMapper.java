package com.fh.cloud.screen.service.screen.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneThirdDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import org.apache.ibatis.annotations.Param;

/**
 * 第三方对接云屏场景信息表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
public interface ScreenSceneThirdMapper extends BaseMapper<ScreenSceneThirdDto> {

    List<ScreenSceneThirdVo> getScreenSceneThirdListByCondition(ScreenSceneThirdConditionBo condition);

    /**
     * 根据地点、设备、时间查询第三方对接的场景数据
     *
     * @param spaceGroupUseType the space group use type
     * @param spaceInfoId the space info id
     * @param showDeviceId the show device id
     * @param date the date
     * @return screen scene third vo list by space and device and date
     * <AUTHOR>
     * @date 2023 -04-11 17:42:53
     */
    List<ScreenSceneThirdVo> getScreenSceneThirdVoListBySpaceAndDeviceAndDate(
        @Param("spaceGroupUseType") Integer spaceGroupUseType, @Param("spaceInfoId") Long spaceInfoId,
        @Param("showDeviceId") Long showDeviceId, @Param("date") Date date);
}
