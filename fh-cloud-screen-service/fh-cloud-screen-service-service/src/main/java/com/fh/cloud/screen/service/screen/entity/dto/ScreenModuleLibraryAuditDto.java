package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 模块库审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_module_library_audit")
public class ScreenModuleLibraryAuditDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_module_library_audit_id", type = IdType.AUTO)
	private Long screenModuleLibraryAuditId;

	/**
	 * 模块名称
	 */
	@TableField("module_name")
	private String moduleName;

	/**
	 * 模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
	 */
	@TableField("module_group_type")
	private Long moduleGroupType;

	/**
	 * 预置类型：1预置，2不预置
	 */
	@TableField("preset_type")
	private Integer presetType;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 父模块库id
	 */
	@TableField("parent_screen_module_library_id")
	private Long parentScreenModuleLibraryId;

	/**
	 * 排序
	 */
	@TableField("library_sort")
	private Long librarySort;

	/**
	 * 是否海报模块：1是，2否
	 */
	@TableField("is_poster")
	private Long isPoster;

	/**
	 * 审核状态 1-待审核 2-审核通过 3-审核驳回 4-待提交
	 */
	@TableField("audit_type")
	private Integer auditType;

	/**
	 * 驳回原因
	 */
	@TableField("reason")
	private String reason;

	/**
	 * 审核人
	 */
	@TableField("audit_user")
	private String auditUser;

	/**
	 * 审核时间
	 */
	@TableField("audit_time")
	private Date auditTime;

	/**
	 * 发布状态 1-未发布 2-已发布
	 */
	@TableField("release_type")
	private Integer releaseType;

	/**
	 * 图片版式：1横屏，2竖屏
	 */
	@TableField("device_pattern")
	private Integer devicePattern;

	/**
	 * 模块库表id
	 */
	@TableField("screen_module_library_id")
	private Long screenModuleLibraryId;

}
