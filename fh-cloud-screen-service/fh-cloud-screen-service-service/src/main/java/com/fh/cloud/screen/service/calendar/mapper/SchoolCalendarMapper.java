package com.fh.cloud.screen.service.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendar;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;

import java.util.List;

/**
 * 校历主表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
public interface SchoolCalendarMapper extends BaseMapper<SchoolCalendar> {

    List<SchoolCalendarVo> getSchoolCalendarListByCondition(SchoolCalendarListConditionBo condition);

}
