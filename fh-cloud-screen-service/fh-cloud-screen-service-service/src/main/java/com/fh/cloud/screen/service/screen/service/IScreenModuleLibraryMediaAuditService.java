package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMediaAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 云屏模块库媒体资源审核表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
public interface IScreenModuleLibraryMediaAuditService extends IService<ScreenModuleLibraryMediaAuditDto> {

    List<ScreenModuleLibraryMediaAuditVo> getScreenModuleLibraryMediaAuditListByCondition(ScreenModuleLibraryMediaAuditConditionBo condition);

	AjaxResult addScreenModuleLibraryMediaAudit(ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo);

	AjaxResult updateScreenModuleLibraryMediaAudit(ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo);

	ScreenModuleLibraryMediaAuditVo getScreenModuleLibraryMediaAuditByCondition(ScreenModuleLibraryMediaAuditConditionBo condition);

	boolean updateMediaAuditList(Long screenModuleLibraryAuditId, List<ScreenModuleLibraryMediaAuditBo> mediaAuditBos);

}

