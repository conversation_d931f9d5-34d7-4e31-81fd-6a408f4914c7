package com.fh.cloud.screen.service.crm.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactConditionBo;
import com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo;
import com.fh.cloud.screen.service.crm.service.ICrmContactService;
import com.light.core.constants.SystemConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.crm.api.CrmInfoApi;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoConditionBo;
import com.fh.cloud.screen.service.crm.entity.dto.CrmInfoDto;
import com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo;
import com.fh.cloud.screen.service.crm.service.ICrmInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * CRM商讯管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@RestController
@Validated
public class CrmInfoController implements CrmInfoApi {

    @Autowired
    private ICrmInfoService crmInfoService;
    @Autowired
    private ICrmContactService crmContactService;

    /**
     * 查询CRM商讯管理表分页列表
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult<PageInfo<CrmInfoVo>> getCrmInfoPageListByCondition(@RequestBody CrmInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<CrmInfoVo> pageInfo = new PageInfo<>(crmInfoService.getCrmInfoListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询CRM商讯管理表列表
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult<List<CrmInfoVo>> getCrmInfoListByCondition(@RequestBody CrmInfoConditionBo condition) {
        List<CrmInfoVo> list = crmInfoService.getCrmInfoListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增CRM商讯管理表
     * 
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult addCrmInfo(@Validated @RequestBody CrmInfoBo crmInfoBo) {
        Long aLong = crmInfoService.addCrmInfo(crmInfoBo);
        if (aLong == null) {
            return AjaxResult.fail("保存失败");
        }
        return AjaxResult.success("保存成功");
    }

    /**
     * 修改CRM商讯管理表
     * 
     * @param crmInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult updateCrmInfo(@Validated @RequestBody CrmInfoBo crmInfoBo) {
        if (null == crmInfoBo.getCrmInfoId()) {
            return AjaxResult.fail("CRM商讯管理表id不能为空");
        }
        return crmInfoService.updateCrmInfo(crmInfoBo);
    }

    /**
     * 查询CRM商讯管理表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult<CrmInfoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("CRM商讯管理表id不能为空");
        }
        CrmInfoConditionBo condition = new CrmInfoConditionBo();
        condition.setCrmInfoId(id);
        CrmInfoVo vo = crmInfoService.getCrmInfoByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除CRM商讯管理表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        CrmInfoDto crmInfoDto = new CrmInfoDto();
        crmInfoDto.setCrmInfoId(id);
        crmInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (crmInfoService.updateById(crmInfoDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult addCrmInfoWithContact(CrmInfoBo crmInfoBo) {
        return crmInfoService.addCrmInfoWithContact(crmInfoBo);
    }

    @Override
    public AjaxResult<CrmInfoVo> getDetailWithContact(Long id) {
        if (null == id) {
            return AjaxResult.fail("CRM商讯管理表id不能为空");
        }
        CrmInfoConditionBo condition = new CrmInfoConditionBo();
        condition.setCrmInfoId(id);
        CrmInfoVo vo = crmInfoService.getCrmInfoByCondition(condition);
        List<CrmContactVo> crmContactVos = getCrmContactVos(id);
        if (CollectionUtils.isNotEmpty(crmContactVos)) {
            vo.setCrmContactVos(crmContactVos);
        }
        return AjaxResult.success(vo);
    }

    @Override
    public AjaxResult<PageInfo<CrmInfoVo>> getCrmInfoPageListByConditionWithContact(CrmInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<CrmInfoVo> crmInfoListByCondition = crmInfoService.getCrmInfoListByCondition(condition);
        if (CollectionUtils.isNotEmpty(crmInfoListByCondition)) {
            List<Long> ids = crmInfoListByCondition.stream().map(CrmInfoVo::getCrmInfoId).collect(Collectors.toList());
            List<CrmContactVo> crmContactVos = getCrmContactVos(ids);
            if (CollectionUtils.isNotEmpty(crmContactVos)) {
                Map<Long, List<CrmContactVo>> collect =
                    crmContactVos.stream().collect(Collectors.groupingBy(CrmContactVo::getCrmInfoId));
                crmInfoListByCondition.forEach(crmInfoVo -> {
                    List<CrmContactVo> crmContactVoList = collect.get(crmInfoVo.getCrmInfoId());
                    if (CollectionUtils.isNotEmpty(crmContactVoList)) {
                        crmInfoVo.setCrmContactVos(crmContactVoList);
                    }
                });
            }

        }
        PageInfo<CrmInfoVo> pageInfo = new PageInfo<>(crmInfoListByCondition);
        return AjaxResult.success(pageInfo);
    }

    @Override
    public AjaxResult<List<CrmInfoVo>> getCrmInfoListByConditionWithContact(CrmInfoConditionBo condition) {
        List<CrmInfoVo> list = crmInfoService.getCrmInfoListByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> ids = list.stream().map(CrmInfoVo::getCrmInfoId).collect(Collectors.toList());
            List<CrmContactVo> crmContactVos = getCrmContactVos(ids);
            if (CollectionUtils.isNotEmpty(crmContactVos)) {
                Map<Long, List<CrmContactVo>> collect =
                    crmContactVos.stream().collect(Collectors.groupingBy(CrmContactVo::getCrmInfoId));
                list.forEach(crmInfoVo -> {
                    List<CrmContactVo> crmContactVoList = collect.get(crmInfoVo.getCrmInfoId());
                    if (CollectionUtils.isNotEmpty(crmContactVoList)) {
                        crmInfoVo.setCrmContactVos(crmContactVoList);
                    }
                });
            }

        }
        return AjaxResult.success(list);
    }

    /**
     * 查询联系人信息
     *
     * @param id
     * @return
     */
    private List<CrmContactVo> getCrmContactVos(Long id) {
        // 顺带查询出联系人信息
        CrmContactConditionBo crmContactConditionBo = new CrmContactConditionBo();
        crmContactConditionBo.setPageNo(SystemConstants.NO_PAGE);
        crmContactConditionBo.setCrmInfoId(id);
        List<CrmContactVo> crmContactVos = crmContactService.getCrmContactListByCondition(crmContactConditionBo);
        return crmContactVos;
    }

    /**
     * 查询联系人信息
     *
     * @param id
     * @return
     */
    private List<CrmContactVo> getCrmContactVos(List<Long> ids) {
        // 顺带查询出联系人信息
        CrmContactConditionBo crmContactConditionBo = new CrmContactConditionBo();
        crmContactConditionBo.setPageNo(SystemConstants.NO_PAGE);
        crmContactConditionBo.setCrmInfoIds(ids);
        List<CrmContactVo> crmContactVos = crmContactService.getCrmContactListByCondition(crmContactConditionBo);
        return crmContactVos;
    }

}
