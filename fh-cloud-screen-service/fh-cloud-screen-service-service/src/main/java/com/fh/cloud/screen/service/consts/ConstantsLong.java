package com.fh.cloud.screen.service.consts;

/**
 * <AUTHOR>
 * @date 2022/4/12 15:11
 */
public interface ConstantsLong {

    /**
     * 合法
     */
    long STATUS_YES = 1L;
    /**
     * 非法
     */
    long STATUS_NO = 0L;

    long NUM_0 = 0L;
    long NUM_1 = 1L;
    long NUM_2 = 2L;
    long NUM_7 = 7L;
    long NUM_10 = 10L;

    long NUM_1000 = 1000L;

    /**
     * 基础服务关于角色权限的appId,2=校园云屏
     */
    Long APP_ID_SCREEN = 2L;
    Long APP_ID_BASIC = 3L;
    Long APP_ID_ATTENDANCE = 4L;

    /**
     * 截图服务延迟队列的延迟时间，单位s
     */
    Long DELAY_SERVICE_CAPTURE_EXPIRE_IN = 60L;
    /**
     * 抓取日志服务延迟队列的延迟时间，单位s
     */
    Long DELAY_SERVICE_LOG_EXPIRE_IN = 60L;

}
