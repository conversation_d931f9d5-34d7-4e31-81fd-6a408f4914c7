package com.fh.cloud.screen.service.leaveschool.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 放学配置表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("leave_school_config")
public class LeaveSchoolConfigDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学配置表id
	 */
	@TableId(value = "leave_school_config_id", type = IdType.AUTO)
	private Long leaveSchoolConfigId;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@TableField("campus_id")
	private Long campusId;

	/**
	 * 各年级放学时间 1-一致 2-不一致
	 */
	@TableField("leave_school_grade_type")
	private Integer leaveSchoolGradeType;

	/**
	 * 一周内放学时间 1-一周一致 2-一周不一致
	 */
	@TableField("leave_school_week_type")
	private Integer leaveSchoolWeekType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 放学自动确认时间（单位：分钟）
	 */
	@TableField("auto_confirm_time")
	private Integer autoConfirmTime;

}
