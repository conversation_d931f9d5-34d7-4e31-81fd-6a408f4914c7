package com.fh.cloud.screen.service.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 校历上课日日期表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
public interface SchoolCalendarDayMapper extends BaseMapper<SchoolCalendarDay> {

    List<SchoolCalendarDayVo> getSchoolCalendarDayListByCondition(SchoolCalendarDayListConditionBo condition);

    /**
     * 根据校历主表ID 和 月份 查询
     *
     * @param schoolCalendarId
     * @param month
     * @return
     */
    List<SchoolCalendarDay> getBySchoolCalendarIdAndMonth(@Param("schoolCalendarId") Long schoolCalendarId,
        @Param("month") String month);
}
