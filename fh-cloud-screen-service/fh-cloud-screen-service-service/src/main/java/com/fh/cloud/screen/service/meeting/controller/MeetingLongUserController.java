package com.fh.cloud.screen.service.meeting.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.meeting.api.MeetingLongUserApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongUserDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongUserVo;
import com.fh.cloud.screen.service.meeting.service.IMeetingLongUserService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 长期预约表人员表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@RestController
@Validated
public class MeetingLongUserController implements MeetingLongUserApi{
	
    @Autowired
    private IMeetingLongUserService meetingLongUserService;

    /**
     * 查询长期预约表人员表分页列表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
    @Override
    public AjaxResult<PageInfo<MeetingLongUserVo>> getMeetingLongUserPageListByCondition(@RequestBody MeetingLongUserConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<MeetingLongUserVo> pageInfo = new PageInfo<>(meetingLongUserService.getMeetingLongUserListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询长期预约表人员表列表
	 * <AUTHOR>
	 * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult<List<MeetingLongUserVo>> getMeetingLongUserListByCondition(@RequestBody MeetingLongUserConditionBo condition){
		List<MeetingLongUserVo> list = meetingLongUserService.getMeetingLongUserListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增长期预约表人员表
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
     */
	@Override
    public AjaxResult addMeetingLongUser(@Validated @RequestBody MeetingLongUserBo meetingLongUserBo){
		return meetingLongUserService.addMeetingLongUser(meetingLongUserBo);
    }

    /**
	 * 修改长期预约表人员表
	 * @param meetingLongUserBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult updateMeetingLongUser(@Validated @RequestBody MeetingLongUserBo meetingLongUserBo) {
		if(null == meetingLongUserBo.getMeetingLongUserId()) {
			return AjaxResult.fail("长期预约表人员表id不能为空");
		}
		return meetingLongUserService.updateMeetingLongUser(meetingLongUserBo);
	}

	/**
	 * 查询长期预约表人员表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult<MeetingLongUserVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("长期预约表人员表id不能为空");
		}
		MeetingLongUserConditionBo condition = new MeetingLongUserConditionBo();
		condition.setMeetingLongUserId(id);
		MeetingLongUserVo vo = meetingLongUserService.getMeetingLongUserByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除长期预约表人员表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-11 14:26:44
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		MeetingLongUserDto meetingLongUserDto = new MeetingLongUserDto();
		meetingLongUserDto.setMeetingLongUserId(id);
		meetingLongUserDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(meetingLongUserService.updateById(meetingLongUserDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
