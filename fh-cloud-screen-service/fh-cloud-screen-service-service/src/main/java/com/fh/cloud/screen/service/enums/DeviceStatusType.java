package com.fh.cloud.screen.service.enums;

/**
 * 区域分组使用类型（结合分组类型使用）
 *
 * <AUTHOR>
 */
public enum DeviceStatusType {
    /**
     * 开机
     */
    ON(1),
    /***
     * 关机
     */
    OFF(2),
    /**
     * 异常
     */
    ERROR(3),
    /**
     * 开机中
     */
    ON_ING(4),
    /**
     * 关机中
     */
    OFF_ING(5);

    private int value;

    DeviceStatusType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}