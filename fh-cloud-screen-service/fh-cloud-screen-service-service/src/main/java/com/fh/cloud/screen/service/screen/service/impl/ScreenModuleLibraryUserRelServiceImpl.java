package com.fh.cloud.screen.service.screen.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryUserRelDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryUserRelVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryUserRelService;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryUserRelMapper;
import com.light.core.entity.AjaxResult;

/**
 * 模块用户关系表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
@Service
public class ScreenModuleLibraryUserRelServiceImpl
    extends ServiceImpl<ScreenModuleLibraryUserRelMapper, ScreenModuleLibraryUserRelDto>
    implements IScreenModuleLibraryUserRelService {

    @Resource
    private ScreenModuleLibraryUserRelMapper screenModuleLibraryUserRelMapper;

    @Override
    public List<ScreenModuleLibraryUserRelVo>
        getScreenModuleLibraryUserRelListByCondition(ScreenModuleLibraryUserRelConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenModuleLibraryUserRelMapper.getScreenModuleLibraryUserRelListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenModuleLibraryUserRel(ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo) {
        ScreenModuleLibraryUserRelDto screenModuleLibraryUserRel = new ScreenModuleLibraryUserRelDto();
        BeanUtils.copyProperties(screenModuleLibraryUserRelBo, screenModuleLibraryUserRel);
        screenModuleLibraryUserRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenModuleLibraryUserRel)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenModuleLibraryUserRel(ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo) {
        ScreenModuleLibraryUserRelDto screenModuleLibraryUserRel = new ScreenModuleLibraryUserRelDto();
        BeanUtils.copyProperties(screenModuleLibraryUserRelBo, screenModuleLibraryUserRel);
        if (updateById(screenModuleLibraryUserRel)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenModuleLibraryUserRelVo getDetail(Long id) {
        ScreenModuleLibraryUserRelConditionBo condition = new ScreenModuleLibraryUserRelConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleLibraryUserRelVo> list =
            screenModuleLibraryUserRelMapper.getScreenModuleLibraryUserRelListByCondition(condition);
        ScreenModuleLibraryUserRelVo vo = new ScreenModuleLibraryUserRelVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}