package com.fh.cloud.screen.service.calendar.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekSaveUpdateConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarWeek;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;

import java.util.List;
import java.util.Map;

/**
 * 校历上课日星期表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
public interface ISchoolCalendarWeekService extends IService<SchoolCalendarWeek> {

    List<SchoolCalendarWeekVo> getSchoolCalendarWeekListByCondition(SchoolCalendarWeekListConditionBo condition);

    boolean addSchoolCalendarWeek(SchoolCalendarWeekBo schoolCalendarWeekBo);

    boolean saveOrUpdateSchoolCalendarWeeks(SchoolCalendarWeekSaveUpdateConditionBo conditionBo);

    Map<String, Object> getDetail(Long schoolCalendarWeekId);

    /**
     * 根据主表ID 获取信息
     *
     * @param schoolCalendarId
     * @return
     */
    List<SchoolCalendarWeek> getBySchoolCalendarId(Long schoolCalendarId);

    /**
     * 获取缓存中的校历
     */
    Map<String, Object> getCacheWeekListAndDayListByOrganizationId(Long organizationId);
}
