package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 电子签名表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_signature_content")
public class ScreenSignatureContentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_signature_content_id", type = IdType.AUTO)
	private Long screenSignatureContentId;

	/**
	 * FK云屏模块表id
	 */
	@TableField("screen_module_data_id")
	private Long screenModuleDataId;

	/**
	 * 电子签名标题
	 */
	@TableField("screen_signature_content_title")
	private String screenSignatureContentTitle;

	/**
	 * 电子签名文本内容
	 */
	@TableField("screen_signature_content_txt")
	private String screenSignatureContentTxt;

	/**
	 * FK所属班级ID
	 */
	@TableField("classes_id")
	private Long classesId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 【冗余】创建人姓名
	 */
	@TableField("create_user_name")
	private String createUserName;

	/**
	 * 【冗余】创建人班级名称
	 */
	@TableField("create_user_classes_name")
	private String createUserClassesName;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
