package com.fh.cloud.screen.service.device.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 展示设备表，例如云屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("show_device")
public class ShowDevice implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "show_device_id", type = IdType.AUTO)
    private Long showDeviceId;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 设备号
     */
    @TableField("device_number")
    private String deviceNumber;

    /**
     * 激活状态
     */
    @TableField("device_activation")
    private String deviceActivation;

    /**
     * 设备类型：1云屏。设备绑定的时候更新
     */
    @TableField("device_type")
    private Integer deviceType;

    /**
     * 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    @TableField("device_pattern")
    private Integer devicePattern;

    /**
     * 是否全屏类型：1全屏，2不是全屏
     */
    @TableField("device_full_type")
    private Integer deviceFullType;

    /**
     * 设备MAC地址。设备绑定的时候更新
     */
    @TableField("device_mac_address")
    private String deviceMacAddress;

    /**
     * 开关机状态：1开机，2关机，3异常
     */
    @TableField("device_status")
    private Integer deviceStatus;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 出货号
     */
    @TableField("shipment_no")
    private String shipmentNo;

    /**
     * 设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 设备品牌类型
     */
    @TableField("device_brand")
    private Integer deviceBrand;

    /**
     * 设备型号
     */
    @TableField("device_model")
    private String deviceModel;

    /**
     * 产品序列号
     */
    @TableField("product_serial_number")
    private String productSerialNumber;

    /**
     * 客户端版本
     */
    @TableField("client_version")
    private String clientVersion;

    /**
     * 系统主动推送海报：1接受；2不接受
     */
    @TableField("push_type")
    private Integer pushType;

    /**
     * 设备海报播放间隔时长（秒）
     */
    @TableField("device_poster_duration")
    private Integer devicePosterDuration;

    /**
     * 虹软人脸识别激活码
     */
    @TableField("arcsoft_face_code")
    private String arcsoftFaceCode;

    /**
     * 巡查备注
     */
    @TableField("patrol_remark")
    private String patrolRemark;

    /**
     * 设备巡查状态：1未巡查，2已巡查
     */
    @TableField("patrol_type")
    private Integer patrolType;

    /**
     * 云屏皮肤主题类型，默认1默认主题，2红色主题
     */
    @TableField("device_theme_type")
    private Integer deviceThemeType;

    /**
     * 该设备上的人脸库建模类型（只用于过滤设备查询人脸库的数据）：1全校人脸库建模，2班级人脸库建模
     * {@link com.fh.cloud.screen.service.enums.FaceModType}
     */
    @TableField("face_mod_type")
    private Integer faceModType;

    /**
     * 该设备的监管状态 1-监管 2-未监管
     */
    @TableField("supervise_state")
    private Integer superviseState;
}
