package com.fh.cloud.screen.service.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 设备订阅标签表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
public interface IShowDeviceLabelRelService extends IService<ShowDeviceLabelRelDto> {

    List<ShowDeviceLabelRelVo> getShowDeviceLabelRelListByCondition(ShowDeviceLabelRelConditionBo condition);

	AjaxResult addShowDeviceLabelRel(ShowDeviceLabelRelBo showDeviceLabelRelBo);

	AjaxResult updateShowDeviceLabelRel(ShowDeviceLabelRelBo showDeviceLabelRelBo);

	ShowDeviceLabelRelVo getDetail(Long id);

}

