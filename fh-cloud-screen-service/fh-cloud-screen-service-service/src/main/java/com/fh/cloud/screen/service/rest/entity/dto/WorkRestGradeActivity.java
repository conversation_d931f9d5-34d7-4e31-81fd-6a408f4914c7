package com.fh.cloud.screen.service.rest.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间年级活动课设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("work_rest_grade_activity")
public class WorkRestGradeActivity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "work_rest_grade_activity_id", type = IdType.AUTO)
    private Long workRestGradeActivityId;

    /**
     * FK作息时间年级表主键
     */
    @TableField("work_rest_grade_id")
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    @TableField("work_rest_id")
    private Long workRestId;

    /**
     * 活动课名称
     */
    @TableField("activity_name")
    private String activityName;

    /**
     * 活动课节次：1，2，3...
     */
    @TableField("activity_position")
    private Integer activityPosition;

    /**
     * 活动课节次类型：1在sort节次之前，2在sort节次之后
     */
    @TableField("activity_sort_type")
    private Integer activitySortType;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
