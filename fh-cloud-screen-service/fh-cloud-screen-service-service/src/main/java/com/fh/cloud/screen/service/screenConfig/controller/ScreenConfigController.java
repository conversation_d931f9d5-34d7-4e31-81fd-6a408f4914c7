package com.fh.cloud.screen.service.screenConfig.controller;

import com.fh.cloud.screen.service.screenConfig.api.ScreenConfigApi;
import com.fh.cloud.screen.service.screenConfig.entity.dto.ScreenConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;
import com.fh.cloud.screen.service.screenConfig.service.IScreenConfigService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 云屏配置表
 *
 * <AUTHOR>
 * @email
 * @date 2024-07-29 09:10:13
 */
@RestController
@Validated
public class ScreenConfigController implements ScreenConfigApi {

    @Autowired
    private IScreenConfigService screenConfigService;

    /**
     * 查询云屏配置表分页列表
     *
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @Override
    public AjaxResult<PageInfo<ScreenConfigVo>> getScreenConfigPageListByCondition(@RequestBody ScreenConfigConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenConfigVo> pageInfo = new PageInfo<>(screenConfigService.getScreenConfigListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询云屏配置表列表
     *
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @Override
    public AjaxResult<List<ScreenConfigVo>> getScreenConfigListByCondition(@RequestBody ScreenConfigConditionBo condition) {
        List<ScreenConfigVo> list = screenConfigService.getScreenConfigListByCondition(condition);
        return AjaxResult.success(list);
    }


    /**
     * 新增云屏配置表
     *
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @Override
    public AjaxResult addScreenConfig(@Validated @RequestBody ScreenConfigBo screenConfigBo) {
        return screenConfigService.addScreenConfig(screenConfigBo);
    }

    /**
     * 修改云屏配置表
     *
     * @param screenConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @Override
    public AjaxResult updateScreenConfig(@Validated @RequestBody ScreenConfigBo screenConfigBo) {
        if (null == screenConfigBo.getScreenConfigId()) {
            return AjaxResult.fail("云屏配置表id不能为空");
        }
        return screenConfigService.updateScreenConfig(screenConfigBo);
    }

    /**
     * 查询云屏配置表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @Override
    public AjaxResult<ScreenConfigVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("云屏配置表id不能为空");
        }
        ScreenConfigConditionBo condition = new ScreenConfigConditionBo();
        condition.setScreenConfigId(id);
        ScreenConfigVo vo = screenConfigService.getScreenConfigByCondition(condition);
        return AjaxResult.success(vo);
    }


    /**
     * 删除云屏配置表
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-29 09:10:13
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenConfigDto screenConfigDto = new ScreenConfigDto();
        screenConfigDto.setScreenConfigId(id);
        screenConfigDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenConfigService.updateById(screenConfigDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 保存云屏配置
     *
     * @param configBo
     * @return
     */
    @Override
    public AjaxResult saveScreenConfig(ScreenConfigBo configBo) {
        return screenConfigService.saveScreenConfig(configBo);
    }

    /**
     * 根据机构id和配置类型查询云屏配置信息
     *
     * @param condition
     * @return
     */
    @Override
    public AjaxResult<ScreenConfigVo> getByOrganizationIdAndType(ScreenConfigConditionBo condition) {
        if (condition.getOrganizationId() == null) {
            return AjaxResult.fail("查询机构id不能为空");
        }
        if (condition.getType() == null) {
            return AjaxResult.fail("查询配置类型不能为空");
        }
        ScreenConfigVo vo = screenConfigService.getScreenConfigByCondition(condition);
        return AjaxResult.success(vo);
    }


}
