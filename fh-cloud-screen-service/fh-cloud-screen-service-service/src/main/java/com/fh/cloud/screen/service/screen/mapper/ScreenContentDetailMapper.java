package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentDetail;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云屏内容详情表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface ScreenContentDetailMapper extends BaseMapper<ScreenContentDetail> {

    List<ScreenContentDetailVo> getScreenContentDetailListByCondition(ScreenContentDetailListConditionBo condition);

    /**
     * 通过内容id集合查询内容详情集合
     *
     * @param screenContentIds the screen content ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-07 16:31:10
     */
    List<ScreenContentDetailVo>
        listScreenContentDetailByScreenContentIds(@Param("screenContentIds") List<Long> screenContentIds);
}
