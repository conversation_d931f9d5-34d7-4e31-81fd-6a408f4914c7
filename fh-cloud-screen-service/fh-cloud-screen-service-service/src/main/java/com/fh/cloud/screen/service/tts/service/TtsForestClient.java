package com.fh.cloud.screen.service.tts.service;

import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.fh.cloud.screen.service.tts.dto.BuguResultDto;

import java.util.Map;

/**
 * tts的forest客户端服务
 * 
 * <AUTHOR>
 * @date 2023/11/2 14:57
 */
public interface TtsForestClient {
    /**
     * 布谷api：https://www.buguniaopeiyin.com/peiyin/
     */
    String buguApi = "https://user.api.hudunsoft.com/v1/alivoice/texttoaudio";

    /**
     * 文字转语音
     *
     * @param map the map
     * @return 语音地址 string
     * <AUTHOR>
     * @date 2023 -11-02 14:59:47
     */
    @Post(url = buguApi, headers = {"Content-Type: application/json;charset=UTF-8"})
    BuguResultDto textToVoiceUrl(@Body Map<String, Object> map);
}
