package com.fh.cloud.screen.service.meeting.entity.dto;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会议表
 * 
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("meeting")
public class MeetingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "meeting_id", type = IdType.AUTO)
    private Long meetingId;

    /**
     * FK所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 区域分组使用类型：1是行政教室，2不是行政教室
     */
    @TableField("space_group_use_type")
    private Integer spaceGroupUseType;

    /**
     * 会议室地点id
     */
    @TableField("space_info_id")
    private Long spaceInfoId;

    /**
     * 申请人user_oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 会议主题
     */
    @TableField("title")
    private String title;

    /**
     * 会议日期:yyyy-MM-dd
     */
    @TableField("meeting_date")
    private Date meetingDate;

    /**
     * 开始时间
     */
    @TableField("meeting_start_time")
    private Time meetingStartTime;

    /**
     * 结束时间
     */
    @TableField("meeting_end_time")
    private Time meetingEndTime;

    /**
     * 是否签到（1：是，2：否）
     */
    @TableField("is_sign_in")
    private Integer isSignIn;

    /**
     * 会议内容
     */
    @TableField("content")
    private String content;

    /**
     * 会议备注
     */
    @TableField("note")
    private String note;

    /**
     * 会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 会议用户类型：1：申请人，2：参会人
     */
    @TableField("meeting_user_type")
    private Integer meetingUserType;

    /**
     * 正常签到时间
     */
    @TableField("normal_sign_in_time")
    private Time normalSignInTime;

    /**
     * 会议uuid
     */
    @TableField("meeting_uuid")
    private String meetingUuid;
}
