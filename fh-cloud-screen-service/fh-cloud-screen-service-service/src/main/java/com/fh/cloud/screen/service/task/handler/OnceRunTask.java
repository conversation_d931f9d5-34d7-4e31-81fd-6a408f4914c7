package com.fh.cloud.screen.service.task.handler;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.device.api.ShowDeviceLogApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.wx.service.WxMpMsgService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.task.dto.DelayDemoDto;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.StringKit;
import com.google.common.collect.Lists;
import com.light.base.config.api.ConfigApi;
import com.light.base.config.entity.bo.ConfigBo;
import com.light.redis.component.RedisComponent;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * 手动执行一次的任务
 * 
 * <AUTHOR>
 * @date 2022/6/24 17:01
 */
@Component
@Slf4j
public class OnceRunTask {

    @Resource
    private MessageService messageService;
    @Autowired
    private RedisComponent redisComponent;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Resource
    private ConfigApi configApi;
    @Resource
    private ShowDeviceLogApi showDeviceLogApi;
    @Resource
    private WxMpMsgService wxMpMsgService;

    /**
     * 手动推送初始化云屏的接口
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-initScreen")
    public void initScreen() throws Exception {
        log.info("OnceRunTask-initScreen------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(param)) {
            // 设置任务结果
            XxlJobHelper.handleFail("参数不允许为空");
            return;
        }
        String[] methodParams = param.split(",");
        Long organizationId = Long.valueOf(methodParams[0]);
        List<String> deviceNumbers = Lists.newArrayList();
        if (methodParams.length > 1) {
            deviceNumbers = StringKit.splitString2List(methodParams[1], ConstString.jh);
        }

        try {
            MessageVo messageVo = new MessageVo();
            messageVo.setMessageBody(null);
            messageVo.setMessageType(MessageWsType.INIT.getValue());
            messageService.sendMessageWsBySchool(organizationId, deviceNumbers, messageVo);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask_initScreen------>error:" + e.getLocalizedMessage());
        }
        log.info("OnceRunTask-initScreen------>end");
    }

    /**
     * 手动创建一个课后延迟服务的场景-demo演示使用，参数为：学校id,开始时间(HH:mm),结束时间(HH:mm),序列号#序列号
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-createDelayDemo")
    public void createDelayDemo() throws Exception {
        log.info("OnceRunTask-createDelayDemo------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(param)) {
            // 设置任务结果
            XxlJobHelper.handleFail("参数不允许为空");
            return;
        }
        String[] methodParams = param.split(",");
        Long organizationId = Long.valueOf(methodParams[0]);
        String startTimeString = methodParams.length > 1 ? methodParams[1] : null;
        String endTimeString = methodParams.length > 2 ? methodParams[2] : null;
        String concatDeviceNumber = methodParams.length > 3 ? methodParams[3] : null;

        Date startTime = DateKit.transferYMD2CurrentDay(DateKit.string2Date(startTimeString, "HH:mm"));
        Date endTime = DateKit.transferYMD2CurrentDay(DateKit.string2Date(endTimeString, "HH:mm"));
        List<String> showDeviceNumbers = StringKit.splitString2List(concatDeviceNumber, ConstString.jh);
        try {
            // 场景数据生成放到缓存里面，有效期到截至时间
            String cacheKey = StringUtils.join(ConstantsRedis.DEMO_DELAY_CACHE_PREFIX, organizationId);
            DelayDemoDto delayDemoDto =
                DelayDemoDto.generateDelayDemoDto("课后服务演示场景数据", startTime, endTime, organizationId);
            String dataJson = JSONObject.toJSONString(delayDemoDto);
            redisComponent.set(cacheKey, dataJson, DateKit.getSecondsToTime(endTime));
            // 推送给云屏拉取场景数据
            MessageVo messageVo = new MessageVo();
            messageVo.setMessageBody(null);
            messageVo.setMessageType(MessageWsType.MODIFY_SCENE.getValue());
            messageService.sendMessageWsBySchool(organizationId, showDeviceNumbers, messageVo);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask_createDelayDemo------>error:" + e.getLocalizedMessage());
        }
        log.info("OnceRunTask-createDelayDemo------>end");
    }

    /**
     * 手动创建一个会议的场景-demo演示使用，参数为：学校id,开始时间(HH:mm),结束时间(HH:mm),序列号#序列号
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-createMeetingDemo")
    public void createMeetingDemo() throws Exception {
        log.info("OnceRunTask-createMeetingDemo------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(param)) {
            // 设置任务结果
            XxlJobHelper.handleFail("参数不允许为空");
            return;
        }
        String[] methodParams = param.split(",");
        Long organizationId = Long.valueOf(methodParams[0]);
        String startTimeString = methodParams.length > 1 ? methodParams[1] : null;
        String endTimeString = methodParams.length > 2 ? methodParams[2] : null;
        String concatDeviceNumber = methodParams.length > 3 ? methodParams[3] : null;

        Date startTime = DateKit.transferYMD2CurrentDay(DateKit.string2Date(startTimeString, "HH:mm"));
        Date endTime = DateKit.transferYMD2CurrentDay(DateKit.string2Date(endTimeString, "HH:mm"));
        List<String> showDeviceNumbers = StringKit.splitString2List(concatDeviceNumber, ConstString.jh);
        try {
            // 场景数据生成放到缓存里面，有效期到截至时间
            String cacheKey = StringUtils.join(ConstantsRedis.DEMO_MEETING_CACHE_PREFIX, organizationId);
            DelayDemoDto delayDemoDto =
                DelayDemoDto.generateDelayDemoDto("会议演示场景数据", startTime, endTime, organizationId);
            String dataJson = JSONObject.toJSONString(delayDemoDto);
            redisComponent.set(cacheKey, dataJson, DateKit.getSecondsToTime(endTime));
            // 推送给云屏拉取场景数据
            MessageVo messageVo = new MessageVo();
            messageVo.setMessageBody(null);
            messageVo.setMessageType(MessageWsType.MODIFY_SCENE.getValue());
            messageService.sendMessageWsBySchool(organizationId, showDeviceNumbers, messageVo);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask_createDelayDemo------>error:" + e.getLocalizedMessage());
        }
        log.info("OnceRunTask-createMeetingDemo------>end");
    }

    /**
     * 手动清除云屏首页缓存的数据：screen_index_cache_* 的所有数据。使用前缀匹配模糊删除所有匹配项
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-cleanScreenIndexCacheByPrefix")
    public void cleanScreenIndexCache() throws Exception {
        log.info("OnceRunTask-cleanScreenIndexCacheByPrefix------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        String[] methodParams = param.split(",");
        String cacheKeyPrefix = methodParams[0];
        try {
            redisComponent.deleteByPrex(cacheKeyPrefix);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask_cleanScreenIndexCacheByPrefix------>error:" + e.getLocalizedMessage());
        }
        log.info("OnceRunTask-cleanScreenIndexCacheByPrefix------>end");
    }

    /**
     * 发布h5包：1更新数据库的版本号和md5，2刷新缓存。共三个参数使用逗号分割：version,md5,id
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-postH5")
    public void postH5() throws Exception {
        log.info("OnceRunTask-postH5------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        String[] methodParams = param.split(",");
        try {
            String version = methodParams[0];
            String md5 = methodParams[1];
            String id = methodParams[2];
            ConfigBo configBo = new ConfigBo();
            configBo.setId(Long.valueOf(id));
            configBo.setConfigKey("H5_VERSION");
            configBo.setConfigValue(version);
            configBo.setConfigDescription(md5);
            configApi.updateConfig(configBo);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask-postH5------>error:" + e.getLocalizedMessage());
        }
        log.info("OnceRunTask-postH5------>end");
    }

    /**
     * 发起日志抓取请求
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-log")
    public void showDeviceLog() throws Exception {
        log.info("OnceRunTask-log------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        String[] methodParams = param.split(",");
        try {
            String deviceNumber = methodParams[0];
            ShowDeviceLogBo showDeviceLogBo = new ShowDeviceLogBo();
            showDeviceLogBo.setDeviceNumber(deviceNumber);
            showDeviceLogApi.launchShowDeviceLog(showDeviceLogBo);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask-log------>error:" + e.getLocalizedMessage());
        }
        log.info("OnceRunTask-log------>end");
    }

    /**
     * 测试方法调用
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("OnceRunTask-test")
    public void test() throws Exception {
        try {
            // WxMpTemplateData wxMpTemplateData1 = new WxMpTemplateData("character_string10", "20", "#FF0000");
            // WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("thing11", "地点地点地点地点地点地点地点地点地...", "#FF0000");
            // WxMpTemplateData wxMpTemplateData3 = new WxMpTemplateData("thing8", "设备设备设备设备设备设备设备设备设...", "#FF0000");
            // List<WxMpTemplateData> data = Lists.newArrayList(wxMpTemplateData1, wxMpTemplateData2,
            // wxMpTemplateData3);
            // wxMpMsgService.sendTemplateMsg("wxb5a60383807871d0", "http://www.baidu.com",
            // "o-OA56rF5uA76dwVkjoi3zj44q_w", data, null,
            // "Y9JuiJSK5hjGa38YOnBPvE8BZT0mIs6lQ2Bf1V7jn4A");

            // WxMpTemplateData wxMpTemplateData1 = new WxMpTemplateData("thing1", "张三", "#FF0000");
            // WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("time2", DateKit.date2String(new
            // Date(),"yyyy年MM月dd日"), "#FF0000");
            // List<WxMpTemplateData> data = Lists.newArrayList(wxMpTemplateData1, wxMpTemplateData2);
            // wxMpMsgService.sendTemplateMsg("wxb5a60383807871d0", "http://www.baidu.com",
            // "o-OA56rF5uA76dwVkjoi3zj44q_w",
            // data, null, "cmjULvzViJJjAi4ojssTfhanJjRJ-sd-7zuVjC5a3Bk");

            WxMpTemplateData wxMpTemplateData1 =
                new WxMpTemplateData("time3", DateKit.date2String(new Date(), "yyyy年MM月dd日 HH:mm"), "#FF0000");
            WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("const4", "通过", "#FF0000");
            WxMpTemplateData wxMpTemplateData3 = new WxMpTemplateData("thing5", "学校管理员", "#FF0000");
            List<WxMpTemplateData> data = Lists.newArrayList(wxMpTemplateData1, wxMpTemplateData2, wxMpTemplateData3);
            wxMpMsgService.sendTemplateMsg("wxb5a60383807871d0", "http://www.baidu.com", "o-OA56rF5uA76dwVkjoi3zj44q_w",
                data, null, "IkTz-gcHKEaqtF0xBnGOE11XAB5ep0BK9N0UEAHHLTY");
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("OnceRunTask-test------>error:" + e.getLocalizedMessage());
        }
    }
}
