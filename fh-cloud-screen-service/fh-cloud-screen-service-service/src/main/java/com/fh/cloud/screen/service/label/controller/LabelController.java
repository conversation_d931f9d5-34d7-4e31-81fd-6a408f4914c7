package com.fh.cloud.screen.service.label.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.LabelEnums;
import com.fh.cloud.screen.service.label.api.LabelApi;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.service.ILabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
@RestController
@Validated
public class LabelController implements LabelApi {

    @Autowired
    private ILabelService labelService;

    /**
     * 查询标签表分页列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @Override
    public AjaxResult<PageInfo<LabelVo>> getLabelPageListByCondition(@RequestBody LabelConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<LabelVo> pageInfo = new PageInfo<>(labelService.getLabelListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询标签表列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @Override
    public AjaxResult<List<LabelVo>> getLabelListByCondition(@RequestBody LabelConditionBo condition) {
        List<LabelVo> list = labelService.getLabelListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增标签表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @Override
    public AjaxResult addLabel(@Validated @RequestBody LabelBo labelBo) {
        return labelService.addLabel(labelBo);
    }

    /**
     * 修改标签表
     * 
     * @param labelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @Override
    public AjaxResult updateLabel(@Validated @RequestBody LabelBo labelBo) {
        if (null == labelBo.getLabelId()) {
            return AjaxResult.fail("标签表id不能为空");
        }
        return labelService.updateLabel(labelBo);
    }

    /**
     * 查询标签表详情
     * 
     * @param labelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @Override
    public AjaxResult<LabelVo> getDetail(@RequestParam("labelId") Long labelId) {
        if (null == labelId) {
            return AjaxResult.fail("标签表id不能为空");
        }
        LabelVo vo = labelService.getDetail(labelId);
        return AjaxResult.success(vo);
    }

    /**
     * 删除标签表
     * 
     * @param labelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:33
     */
    @Override
    public AjaxResult delete(@RequestParam("labelId") Long labelId) {
        return labelService.delete(labelId);
    }

    /**
     * 根据标签类型 获取二级目录结构标
     *
     * @param conditionBo 标签类型 默认1，海报标签
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 9:33
     */
    @Override
    public AjaxResult getLabelTreeByCondition(@RequestBody LabelConditionBo conditionBo) {
        // 默认标签类型
        if (null == conditionBo.getType()) {
            conditionBo.setType(LabelEnums.POSTER.getCode());
        }
        if (null == conditionBo.getOrganizationId()) {
            conditionBo.setOrganizationId(ConstantsLong.NUM_0);
        }
        List<LabelVo> list = labelService.getLabelListByCondition(conditionBo);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success();
        }
        // 一级目录
        List<LabelVo> oneLevels = list.stream().filter(x -> x.getParentLabelId() == 0L)
            .sorted(Comparator.comparing(LabelVo::getLabelSort)).collect(Collectors.toList());
        // 一级目录除外的map(二级map）
        Map<Long,
            List<LabelVo>> secondLevelMap = list.stream().filter(x -> x.getParentLabelId() != 0L)
                .sorted(Comparator.comparing(LabelVo::getLabelSort))
                .collect(Collectors.groupingBy(LabelVo::getParentLabelId, LinkedHashMap::new, Collectors.toList()));
        // 一级目录赋子目录
        oneLevels.forEach(x -> x.setChildrenLabels(secondLevelMap.get(x.getLabelId())));
        return AjaxResult.success(oneLevels);
    }

    /**
     * 按照id顺序批量更新顺序
     *
     * @param idList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/14 14:27
     */
    @Override
    public AjaxResult updateLabelSortByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return AjaxResult.fail("参数不能为空");
        }
        List<LabelDto> labelDtoList = new ArrayList<>();
        int i = 1;
        for (Long id : idList) {
            LabelDto labelDto = new LabelDto();
            labelDto.setLabelId(id);
            labelDto.setLabelSort(i++);
            labelDtoList.add(labelDto);
        }
        labelService.updateBatchById(labelDtoList);
        return AjaxResult.success();
    }

    @Override
    public AjaxResult exchangeLabelSort(Long preLabelId, Long nextLabelId, Long organizationId) {
        if (preLabelId == null || nextLabelId == null || organizationId == null) {
            return AjaxResult.fail("参数不能为空");
        }
        LabelDto preLabel = labelService.getById(preLabelId);
        LabelDto nxtLabel = labelService.getById(nextLabelId);
        if (preLabel == null || nxtLabel == null || !organizationId.equals(preLabel.getOrganizationId())
            || !organizationId.equals(nxtLabel.getOrganizationId())) {
            return AjaxResult.fail("标签不存在");
        }
        Integer preSort = preLabel.getLabelSort();
        Integer nxtSort = nxtLabel.getLabelSort();
        LambdaUpdateWrapper<LabelDto> preWrapper = new LambdaUpdateWrapper<>();
        preWrapper.eq(LabelDto::getLabelId, preLabelId);
        preWrapper.set(LabelDto::getLabelSort, nxtSort);
        preWrapper.set(LabelDto::getUpdateTime, new Date());
        labelService.update(preWrapper);
        LambdaUpdateWrapper<LabelDto> nxtWrapper = new LambdaUpdateWrapper<>();
        nxtWrapper.eq(LabelDto::getLabelId, nextLabelId);
        nxtWrapper.set(LabelDto::getLabelSort, preSort);
        nxtWrapper.set(LabelDto::getUpdateTime, new Date());
        labelService.update(nxtWrapper);
        return AjaxResult.success();
    }
}
