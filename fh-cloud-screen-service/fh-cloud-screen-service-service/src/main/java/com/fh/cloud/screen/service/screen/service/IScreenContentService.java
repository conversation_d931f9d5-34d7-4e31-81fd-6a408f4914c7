package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContent;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;

import java.util.Date;
import java.util.List;

/**
 * 云屏内容表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface IScreenContentService extends IService<ScreenContent> {

    List<ScreenContentVo> getScreenContentListByCondition(ScreenContentListConditionBo condition);

    boolean addScreenContent(ScreenContentBo screenContentBo);

    boolean updateScreenContent(ScreenContentBo screenContentBo);

    ScreenContentVo getDetail(Long screenContentId);

    /**
     * 查询并且带有内容数据
     *
     * @param condition the condition
     * @return screen content list by condition with detail
     */
    List<ScreenContentVo> getScreenContentListByConditionWithDetail(ScreenContentListConditionBo condition);

    /**
     * 查询内容详情以及子表的内容详情
     *
     * @param screenContentId the screen content id
     * @return detail with detail
     */
    ScreenContentVo getDetailWithDetail(Long screenContentId);

    /**
     * 保存或更新内容以及内容详情信息
     *
     * @param screenContentBo the screen content bo
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -05-09 14:39:04
     */
    Long saveOrUpdateWithDetail(ScreenContentBo screenContentBo);

    /**
     * 查询大于当前时间的第一条记录
     *
     * @param scopeType 必须 {@link com.fh.cloud.screen.service.enums.ScreenContentScopeType}
     * @param screenModuleDataId 必须
     * @param campusId 可选
     * @param classesId 可选
     * @param updateTime 上一次的查询时间
     * @return integer integer
     * <AUTHOR>
     * @date 2023 -01-16 14:34:55
     */
    Integer countFirstRecord(Integer scopeType, Long screenModuleDataId, Long campusId, Long classesId,
        Date updateTime);

    /**
     * 隐藏连接转截图返回对象里面的图片
     *
     * @param screenContentVos the screen content vos
     * <AUTHOR>
     * @date 2023 -01-18 14:30:30
     */
    void hideLinkImage(List<ScreenContentVo> screenContentVos);

    /**
     * 根据内容id产生本内容内所有的视频资源的转码消息
     * @return
     */
    boolean produceGenCoverMqMessage(Long screenContentId);
}
