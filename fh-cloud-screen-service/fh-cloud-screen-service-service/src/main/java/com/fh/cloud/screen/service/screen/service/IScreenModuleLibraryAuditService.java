package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 模块库审核表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
public interface IScreenModuleLibraryAuditService extends IService<ScreenModuleLibraryAuditDto> {

    List<ScreenModuleLibraryAuditVo>
        getScreenModuleLibraryAuditListByCondition(ScreenModuleLibraryAuditConditionBo condition);

    AjaxResult addScreenModuleLibraryAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    AjaxResult updateScreenModuleLibraryAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    ScreenModuleLibraryAuditVo getScreenModuleLibraryAuditByCondition(ScreenModuleLibraryAuditConditionBo condition);

    List<ScreenModuleLibraryAuditVo> getPosterAuditList(ScreenModuleLibraryAuditConditionBo condition);

    AjaxResult addPosterAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    AjaxResult updatePosterAudit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    AjaxResult<ScreenModuleLibraryAuditVo> getPosterAuditDetail(Long screenModuleLibraryAuditId);

    AjaxResult audit(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    AjaxResult releasePoster(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 批量更新海报（审核）主题标签
     * 
     * @param screenModuleLibraryAuditBo
     * @return
     */
    AjaxResult updatePosterAuditLabel(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);

    /**
     * 根据主键批量逻辑删除
     * 
     * @param screenModuleLibraryAuditIds
     */
    void deleteBatch(List<Long> screenModuleLibraryAuditIds);

    /**
     * 批量发布/取消发布
     * 
     * @param screenModuleLibraryAuditBo，参数为screenModuleLibraryAuditIds和releaseType
     */
    AjaxResult releasePosterBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo);
}
