package com.fh.cloud.screen.service.meeting.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;
import org.apache.ibatis.annotations.Param;

/**
 * 长期预约表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
public interface MeetingLongMapper extends BaseMapper<MeetingLongDto> {

    List<MeetingLongVo> getMeetingLongListByCondition(MeetingLongConditionBo condition);

    MeetingLongVo getMeetingLongByCondition(MeetingLongConditionBo condition);

    List<MeetingLongVo> getMeetingRelationList(MeetingLongConditionBo condition);

}
