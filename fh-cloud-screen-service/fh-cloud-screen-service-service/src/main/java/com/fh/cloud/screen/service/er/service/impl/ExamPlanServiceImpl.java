package com.fh.cloud.screen.service.er.service.impl;

import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo;
import com.fh.cloud.screen.service.er.service.IExamPlanGradeService;
import com.fh.cloud.screen.service.er.service.IExamPlanService;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.google.common.collect.Lists;
import com.light.core.utils.FuzzyQueryUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.er.entity.dto.ExamPlanDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo;
import com.fh.cloud.screen.service.er.mapper.ExamPlanMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 考场_考试计划接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Service
public class ExamPlanServiceImpl extends ServiceImpl<ExamPlanMapper, ExamPlanDto> implements IExamPlanService {

    @Resource
    private ExamPlanMapper examPlanMapper;
    @Autowired
    private IExamPlanGradeService examPlanGradeService;

    @Override
    public List<ExamPlanVo> getExamPlanListByCondition(ExamPlanConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<ExamPlanVo> list = examPlanMapper.getExamPlanListByCondition(condition);
        // 处理返回数据
        handleListResult(list);
        return list;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult addExamPlan(ExamPlanBo examPlanBo) {
        ExamPlanDto examPlan = new ExamPlanDto();
        BeanUtils.copyProperties(examPlanBo, examPlan);
        examPlan.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(examPlan)) {
            // 保存年级
            examPlanGradeService.batchDelAddExamPlanGrade(examPlan.getExamPlanId(), examPlanBo.getGrades());
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamPlan(ExamPlanBo examPlanBo) {
        ExamPlanDto examPlan = new ExamPlanDto();
        BeanUtils.copyProperties(examPlanBo, examPlan);
        if (updateById(examPlan)) {
            // 更新年级
            examPlanGradeService.batchDelAddExamPlanGrade(examPlan.getExamPlanId(), examPlanBo.getGrades());
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ExamPlanVo getDetail(Long id) {
        ExamPlanConditionBo condition = new ExamPlanConditionBo();
        condition.setExamPlanId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamPlanVo> list = examPlanMapper.getExamPlanListByCondition(condition);
        ExamPlanVo vo = new ExamPlanVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        List<ExamPlanVo> examPlanVos = Lists.newArrayList();
        examPlanVos.add(vo);
        handleListResult(examPlanVos);
        return vo;
    }

    /**
     * 处理列表返回的结果
     *
     * @param list the list
     * @autor sunqingbiao
     * @date 2022-09-30 14:35:17
     */
    private void handleListResult(List<ExamPlanVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> examPlanIds = list.stream().map(ExamPlanVo::getExamPlanId).collect(Collectors.toList());
        ExamPlanGradeConditionBo examPlanGradeConditionBo = new ExamPlanGradeConditionBo();
        examPlanGradeConditionBo.setExamPlanIds(examPlanIds);
        List<ExamPlanGradeVo> examPlanGradeList =
            examPlanGradeService.getExamPlanGradeListByCondition(examPlanGradeConditionBo);
        Map<Long, List<ExamPlanGradeVo>> examPlanGradeMap = examPlanGradeList.stream()
            .collect(Collectors.groupingBy(ExamPlanGradeVo::getExamPlanId, Collectors.toList()));

        list.forEach(examPlanVo -> {
            Date examPlanStartTime = examPlanVo.getExamPlanStartTime();
            examPlanVo.setSchoolYear(SchoolYearUtil.getSchoolYearByDate(examPlanStartTime));
            examPlanVo.setTermName(SchoolYearUtil.getTermNameByDate(examPlanStartTime));
            List<ExamPlanGradeVo> examPlanGradeVos = examPlanGradeMap.get(examPlanVo.getExamPlanId());
            if (CollectionUtils.isNotEmpty(examPlanGradeVos)) {
                examPlanVo
                    .setGrades(examPlanGradeVos.stream().map(ExamPlanGradeVo::getGrade).collect(Collectors.toList()));
            }
        });
    }

}