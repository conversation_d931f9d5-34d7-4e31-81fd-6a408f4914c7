package com.fh.cloud.screen.service.calendar.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayVo;

import java.util.List;
import java.util.Map;

/**
 * 校历上课日日期表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
public interface ISchoolCalendarDayService extends IService<SchoolCalendarDay> {

    List<SchoolCalendarDayVo> getSchoolCalendarDayListByCondition(SchoolCalendarDayListConditionBo condition);

    boolean saveOrUpdateSchoolCalendarDay(SchoolCalendarDayBo schoolCalendarDayBo);

    boolean updateSchoolCalendarDay(SchoolCalendarDayBo schoolCalendarDayBo);

    Map<String, Object> getDetail(Long schoolCalendarDayId);

    List<SchoolCalendarDay> getBySchoolCalendarId(Long schoolCalendarId);

    List<SchoolCalendarDay> getBySchoolCalendarIdAndMonth(Long schoolCalendarId, String month);
}
