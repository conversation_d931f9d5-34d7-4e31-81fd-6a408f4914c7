package com.fh.cloud.screen.service.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataBo;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.common.entity.dto.DictionaryData;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;
import com.fh.cloud.screen.service.common.mapper.ScreenDictionaryDataMapper;
import com.fh.cloud.screen.service.common.service.IScreenDictionaryDataService;
import com.fh.cloud.screen.service.enums.DictionaryType;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.google.common.collect.Maps;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The type Dictionary data service.
 *
 * <AUTHOR>
 * @date 2022 /5/30 17:12
 */
@Service
public class ScreenDictionaryDataServiceImpl extends ServiceImpl<ScreenDictionaryDataMapper, DictionaryData>
    implements IScreenDictionaryDataService {

    @Resource
    private ScreenDictionaryDataMapper dictionaryDataMapper;

    @Lazy
    @Resource
    private IScreenDictionaryDataService dictionaryDataService;

    @Resource
    private IScreenModuleLibraryService screenModuleLibraryService;
    @Resource
    private IScreenModuleLibraryMediaService screenModuleLibraryMediaService;

    @Override
    public List<DictionaryDataVo> getDictionaryDataListByCondition(DictionaryDataListConditionBo condition) {
        return dictionaryDataMapper.getDictionaryDataListByCondition(condition);
    }

    @Override
    public Map<String, String> getDictionaryMapByType(String dictType) {
        if (StringUtils.isBlank(dictType)) {
            return Maps.newHashMap();
        }
        DictionaryDataListConditionBo conditionBo = new DictionaryDataListConditionBo();
        conditionBo.setDictType(dictType);
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<DictionaryDataVo> dictionaryDataVos = dictionaryDataService.getDictionaryDataListByCondition(conditionBo);
        if (CollectionUtils.isEmpty(dictionaryDataVos)) {
            return Maps.newHashMap();
        }
        Map<String, String> resultMap = dictionaryDataVos.stream()
            .collect(Collectors.toMap(DictionaryDataVo::getDictValue, DictionaryDataVo::getDictLabel));
        return resultMap;
    }

    @Override
    public AjaxResult addDictionaryData(DictionaryDataBo dictionaryDataBo) {
        DictionaryData dictionaryData = new DictionaryData();
        BeanUtils.copyProperties(dictionaryDataBo, dictionaryData);
        dictionaryData.setIsDelete(StatusEnum.NOTDELETE.getCode());
        dictionaryData.setDictType(DictionaryType.MODULE_GROUP_TYPE.getValue());
        // 设置顺序及关联key dictValue
        String dictValue = "1";
        int dictSort = 0;
        List<DictionaryDataVo> dictionaryDataList = dictionaryDataMapper.getDictionaryDataListByCondition(null);
        if (CollectionUtils.isNotEmpty(dictionaryDataList)) {
            // 设置顺序
            List<DictionaryDataVo> dictSortList = dictionaryDataList.stream()
                .filter(x -> dictionaryDataBo.getOrganizationId().equals(x.getOrganizationId()))
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dictSortList)) {
                dictSort = dictSortList.get(0).getDictSort() - 1;
            }
            // 设置设置diceValue
            List<DictionaryDataVo> dictValueSortList = dictionaryDataList
                .stream().sorted(Comparator
                    .comparing(DictionaryDataVo::getDictValue, Comparator.comparingInt(Integer::parseInt)).reversed())
                .collect(Collectors.toList());

            dictValue = String.valueOf(Integer.parseInt(dictValueSortList.get(0).getDictValue()) + 1);
        }
        // 设置关联key diceValue和顺序sort
        dictionaryData.setDictValue(dictValue);
        dictionaryData.setDictSort(dictSort);
        if (save(dictionaryData)) {
            return AjaxResult.success(dictionaryData);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateDictionaryData(DictionaryDataBo dictionaryDataBo) {
        DictionaryData byId = getById(dictionaryDataBo.getId());
        if (null == byId) {
            return AjaxResult.fail("分类不存在");
        }
        // 更新
        DictionaryData dictionaryData = new DictionaryData();
        BeanUtils.copyProperties(dictionaryDataBo, dictionaryData);
        updateById(dictionaryData);
        if (!StatusEnum.ISDELETE.getCode().equals(dictionaryDataBo.getIsDelete())) {
            return AjaxResult.success("保存成功");
        }
        // 删除同步删除下属模块，否则收藏引用依旧有效
        Integer dictValue = Integer.valueOf(byId.getDictValue());
        LambdaQueryWrapper<ScreenModuleLibrary> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenModuleLibrary::getModuleGroupType, dictValue);
        lqw.eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleLibrary> libraryList = screenModuleLibraryService.list(lqw);
        // 分组下没有主题模块
        if (CollectionUtils.isEmpty(libraryList)) {
            return AjaxResult.success("删除成功");
        }
        // 同步删除主题模块
        libraryList.forEach(x -> x.setIsDelete(StatusEnum.ISDELETE.getCode()));
        screenModuleLibraryService.updateBatchById(libraryList);
        List<Long> libraryIds =
            libraryList.stream().map(ScreenModuleLibrary::getScreenModuleLibraryId).collect(Collectors.toList());
        // 同比删除海报图片
        LambdaUpdateWrapper<ScreenModuleLibraryMedia> luw = new LambdaUpdateWrapper<>();
        luw.in(ScreenModuleLibraryMedia::getScreenModuleLibraryId, libraryIds);
        luw.eq(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.NOTDELETE.getCode());
        luw.set(ScreenModuleLibraryMedia::getIsDelete, StatusEnum.ISDELETE.getCode());
        screenModuleLibraryMediaService.update(luw);
        return AjaxResult.success("删除成功");
    }

}
