package com.fh.cloud.screen.service.enums;

public enum AttendanceRuleRedisKeyEnum {

    /**
     * 考勤规则缓存key:hset结构，格式例如：key（attendance:rule:） key（organizationId:attendanceType） value(考勤规则明细)
     */
    ATTENDANCE_RULE_KEY("attendance:rule:"),
    ;

    private final String value;

    AttendanceRuleRedisKeyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

}
