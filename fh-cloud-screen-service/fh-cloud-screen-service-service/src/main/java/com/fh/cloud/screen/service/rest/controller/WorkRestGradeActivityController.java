package com.fh.cloud.screen.service.rest.controller;

import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityListConditionBo;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeActivityVo;
import com.fh.cloud.screen.service.rest.service.IWorkRestGradeActivityService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 作息时间年级活动课设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@RequestMapping("/rest/activity")
@Validated
public class WorkRestGradeActivityController {

    @Autowired
    private IWorkRestGradeActivityService workRestGradeActivityService;

    /**
     * 查询作息时间年级活动课设置表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询作息时间年级活动课设置表列表", httpMethod = "POST")
    public AjaxResult
        getWorkRestGradeActivityListByCondition(@RequestBody WorkRestGradeActivityListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<WorkRestGradeActivityVo> pageInfo =
            new PageInfo<>(workRestGradeActivityService.getWorkRestGradeActivityListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("workRestGradeActivityList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增作息时间年级活动课设置表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增作息时间年级活动课设置表", httpMethod = "POST")
    public AjaxResult addWorkRestGradeActivity(@RequestBody WorkRestGradeActivityBo workRestGradeActivityBo) {
        boolean save = workRestGradeActivityService.addWorkRestGradeActivity(workRestGradeActivityBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改作息时间年级活动课设置表
     * 
     * @param workRestGradeActivityBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改作息时间年级活动课设置表", httpMethod = "POST")
    public AjaxResult updateWorkRestGradeActivity(@RequestBody WorkRestGradeActivityBo workRestGradeActivityBo) {
        if (null == workRestGradeActivityBo.getWorkRestGradeActivityId()) {
            return AjaxResult.fail("作息时间年级活动课设置表id不能为空");
        }
        boolean update = workRestGradeActivityService.updateWorkRestGradeActivity(workRestGradeActivityBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询作息时间年级活动课设置表详情
     * 
     * @param workRestGradeActivityId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询作息时间年级活动课设置表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("workRestGradeActivityId") Long workRestGradeActivityId) {
        Map<String, Object> map = workRestGradeActivityService.getDetail(workRestGradeActivityId);
        return AjaxResult.success(map);
    }

    /**
     * 删除作息时间年级活动课设置表
     * 
     * @param workRestGradeActivityId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除作息时间年级活动课设置表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("workRestGradeActivityId") Long workRestGradeActivityId) {
        WorkRestGradeActivityBo workRestGradeActivityBo = new WorkRestGradeActivityBo();
        workRestGradeActivityBo.setWorkRestGradeActivityId(workRestGradeActivityId);
        workRestGradeActivityBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = workRestGradeActivityService.updateWorkRestGradeActivity(workRestGradeActivityBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
