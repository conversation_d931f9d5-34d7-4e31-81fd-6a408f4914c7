package com.fh.cloud.screen.service.face.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordTeacherDto;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import org.apache.ibatis.annotations.Param;

/**
 * 教师人脸库Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
public interface FaceRecordTeacherMapper extends BaseMapper<FaceRecordTeacherDto> {

	List<FaceRecordTeacherVo> getFaceRecordTeacherListByCondition(FaceRecordTeacherConditionBo condition);

	/**
	 * 根据userOid更新realName
	 *
	 * @param userOid the user oid
	 * @param realName the real name
	 * <AUTHOR>
	 * @date 2023 -06-14 18:07:14
	 */
	void updateRealNameByUserOid(@Param("userOid") String userOid,@Param("realName") String realName);

}
