package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.File;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 云屏模块库媒体资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("screen_module_library_media")
@ApiModel(value = "ScreenModuleLibraryMediaDto对象", description = "云屏模块库媒体资源表")
public class ScreenModuleLibraryMedia implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "screen_module_library_media_id", type = IdType.AUTO)
    private Long screenModuleLibraryMediaId;

    @ApiModelProperty(value = "FK模块库表")
    private Long screenModuleLibraryId;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址")
    private String screenModuleLibraryMediaUrl;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址-压缩后")
    private String screenModuleLibraryMediaUrlCompress;

    @ApiModelProperty(value = "云屏图片或者视频媒体地址-封面")
    private String screenModuleLibraryMediaUrlCover;

    @ApiModelProperty(value = "云屏图片或者视频媒体名称（不包含后缀）")
    private String screenModuleLibraryMediaName;

    @ApiModelProperty(value = "云屏图片或者视频原始媒体名称（包含后缀）")
    private String screenModuleLibraryMediaNameOri;

    @ApiModelProperty(value = "云屏图片或者视频媒体fileoid")
    private String screenContentMediaId;

    @ApiModelProperty(value = "云屏图片或者视频媒体fileoid-压缩后")
    private String screenContentMediaIdCompress;

    @ApiModelProperty(value = "更新时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否删除，0：否，1：是")
    private Integer isDelete;
    /**
     * 设备模式：1横屏，2竖屏。设备绑定的时候更新
     */
    @TableField("device_pattern")
    private Integer devicePattern;

    /**
     * 文件md5
     */
    @TableField("screen_content_media_md5")
    private String screenContentMediaMd5;

    /**
     * 海报文件（导入使用）
     */
    @TableField(exist = false)
    private File file;

    /**
     * 媒体图片排序
     */
    @TableField("media_sort")
    private Long mediaSort;

    /**
     * 模块来源 1-默认 2-资源中心发布
     */
    @TableField("media_source")
    private Integer mediaSource;

    /**
     * 模块来源id
     */
    @TableField("third_id")
    private String thirdId;
}
