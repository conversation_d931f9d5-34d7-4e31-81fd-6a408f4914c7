package com.fh.cloud.screen.service.leaveschool.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 放学记录操作表id
 * 
 * <AUTHOR>
 * @email 
 * @date 2024-09-13 10:26:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("leave_school_record_operate")
public class LeaveSchoolRecordOperateDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学记录操作表id
	 */
	@TableId(value = "leave_school_record_operate_id", type = IdType.AUTO)
	private Long leaveSchoolRecordOperateId;

	/**
	 * 放学记录表id
	 */
	@TableField("leave_school_record_id")
	private Long leaveSchoolRecordId;

	/**
	 * 放学状态 1-未放学 2-放学中 3-已放学
	 */
	@TableField("leave_school_type")
	private Integer leaveSchoolType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
