package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignaturePictureDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 电子签名图片资源表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
public interface IScreenSignaturePictureService extends IService<ScreenSignaturePictureDto> {

    List<ScreenSignaturePictureVo> getScreenSignaturePictureListByCondition(ScreenSignaturePictureConditionBo condition);

	AjaxResult addScreenSignaturePicture(ScreenSignaturePictureBo screenSignaturePictureBo);

	AjaxResult updateScreenSignaturePicture(ScreenSignaturePictureBo screenSignaturePictureBo);

	ScreenSignaturePictureVo getScreenSignaturePictureByCondition(ScreenSignaturePictureConditionBo condition);

}

