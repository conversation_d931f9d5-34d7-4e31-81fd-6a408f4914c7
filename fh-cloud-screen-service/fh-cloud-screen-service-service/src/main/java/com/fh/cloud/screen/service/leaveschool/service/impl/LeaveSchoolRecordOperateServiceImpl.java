package com.fh.cloud.screen.service.leaveschool.service.impl;

import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordOperateDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordOperateConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordOperateBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordOperateVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolRecordOperateService;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolRecordOperateMapper;
import com.light.core.entity.AjaxResult;
/**
 * 放学记录操作表id接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-09-13 10:26:18
 */
@Service
public class LeaveSchoolRecordOperateServiceImpl extends ServiceImpl<LeaveSchoolRecordOperateMapper, LeaveSchoolRecordOperateDto> implements ILeaveSchoolRecordOperateService {

	@Resource
	private LeaveSchoolRecordOperateMapper leaveSchoolRecordOperateMapper;
	
    @Override
	public List<LeaveSchoolRecordOperateVo> getLeaveSchoolRecordOperateListByCondition(LeaveSchoolRecordOperateConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolRecordOperateMapper.getLeaveSchoolRecordOperateListByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolRecordOperate(LeaveSchoolRecordOperateBo leaveSchoolRecordOperateBo) {
		LeaveSchoolRecordOperateDto leaveSchoolRecordOperate = new LeaveSchoolRecordOperateDto();
		BeanUtils.copyProperties(leaveSchoolRecordOperateBo, leaveSchoolRecordOperate);
		leaveSchoolRecordOperate.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(leaveSchoolRecordOperate)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateLeaveSchoolRecordOperate(LeaveSchoolRecordOperateBo leaveSchoolRecordOperateBo) {
		LeaveSchoolRecordOperateDto leaveSchoolRecordOperate = new LeaveSchoolRecordOperateDto();
		BeanUtils.copyProperties(leaveSchoolRecordOperateBo, leaveSchoolRecordOperate);
		if(updateById(leaveSchoolRecordOperate)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public LeaveSchoolRecordOperateVo getLeaveSchoolRecordOperateByCondition(LeaveSchoolRecordOperateConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return leaveSchoolRecordOperateMapper.getLeaveSchoolRecordOperateByCondition(condition);
	}

}