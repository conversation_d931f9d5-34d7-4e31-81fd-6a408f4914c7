package com.fh.cloud.screen.service.role.controller;

import com.fh.app.role.service.role.entity.bo.RoleAppRelConditionBo;
import com.fh.app.role.service.role.entity.bo.RoleDataAuthorityConditionBo;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.role.api.UserRolePermissionApi;
import com.fh.cloud.screen.service.role.servise.UserRoleService;
import com.fh.sso.service.index.api.IndexApi;
import com.fh.sso.service.index.entity.bo.IndexListConditionBo;
import com.light.core.entity.AjaxResult;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@RestController
@Validated
@Api(value = "", tags = "获取用户功能权限接口")
public class UserRolePermissionController implements UserRolePermissionApi {

    @Resource
    private UserRoleService userRoleService;
    @Resource
    private IndexApi indexApi;
    @Resource
    private CurrentUserService currentUserService;

    @ApiOperation(value = "获取用户功能权限", httpMethod = "GET")
    public AjaxResult getUserPermissionAuthority(@RequestBody RoleAppRelConditionBo condition) {
        return userRoleService.getRoleAppRelPageListByCondition(condition);
    }

    @ApiOperation("获取用户最大数据权限")
    public AjaxResult getUserMaxDataAuthority(@RequestBody RoleDataAuthorityConditionBo conditionBo) {
        Integer userMaxDataAuthority = userRoleService.getUserMaxDataAuthority(conditionBo.getAppId());
        if (null != userMaxDataAuthority) {
            return AjaxResult.success(userMaxDataAuthority);
        }
        return AjaxResult.success(ConstantsInteger.USER_DATA_AUTHORITY_PERSONAL);
    }

    @ApiOperation("获取当前用户的角色标识")
    public AjaxResult getUserRoleCodes() {
        return AjaxResult.success(userRoleService.getRoleFlag());
    }

    @ApiOperation("获取当前用户的应用信息")
    public AjaxResult getUserAppInfo(IndexListConditionBo conditionBo) {
        if (currentUserService.getCurrentUser() != null
            && currentUserService.getCurrentUser().getCurrentUser() != null) {
            if (conditionBo.getIdentityType() == null) {
                conditionBo.setIdentityType(currentUserService.getCurrentUser().getCurrentUser().getUserIdentityType());
            }
            if (StringUtils.isBlank(conditionBo.getUserOid())) {
                conditionBo.setUserOid(currentUserService.getCurrentUser().getCurrentUser().getUserOid());
            }
            if (conditionBo.getOrganizationId() == null) {
                conditionBo.setOrganizationId(
                    currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId());
            }
        }
        return indexApi.getIndexListByCondition(conditionBo);
    }
}
