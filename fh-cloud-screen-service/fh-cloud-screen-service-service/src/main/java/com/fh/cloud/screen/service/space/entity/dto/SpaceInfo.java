package com.fh.cloud.screen.service.space.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 区域信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("space_info")
public class SpaceInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "space_info_id", type = IdType.AUTO)
    private Long spaceInfoId;

    /**
     * FK区域组表主键
     */
    @TableField("space_group_id")
    private Long spaceGroupId;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @TableField("campus_id")
    private Long campusId;

    /**
     * 区域名称
     */
    @TableField("space_info_name")
    private String spaceInfoName;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否有电脑，0：否，1：是
     */
    @TableField("computer_use")
    private Integer computerUse;
    /**
     * 是否有网络，0：否，1：是
     */
    @TableField("network_use")
    private Integer networkUse;
    /**
     * 是否有投影，0：否，1：是
     */
    @TableField("shadow_use")
    private Integer shadowUse;

    /**
     * 是否有会议室，0：否，1：是
     */
    @TableField("meeting_use")
    private Integer meetingUse;
    /**
     * 容纳人数
     */
    @TableField("user_capacity")
    private Integer userCapacity;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

}
