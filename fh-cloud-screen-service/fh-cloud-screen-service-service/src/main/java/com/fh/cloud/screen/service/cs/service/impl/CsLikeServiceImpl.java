package com.fh.cloud.screen.service.cs.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeConditionBo;
import com.fh.cloud.screen.service.cs.entity.dto.CsLikeDto;
import com.fh.cloud.screen.service.cs.entity.vo.CsLikeVo;
import com.fh.cloud.screen.service.cs.mapper.CsLikeMapper;
import com.fh.cloud.screen.service.cs.service.ICsLikeService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * Cultural-Station文化小站喜欢记录表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
@Service
public class CsLikeServiceImpl extends ServiceImpl<CsLikeMapper, CsLikeDto> implements ICsLikeService {

	@Resource
	private CsLikeMapper csLikeMapper;
	
    @Override
	public List<CsLikeVo> getCsLikeListByCondition(CsLikeConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return csLikeMapper.getCsLikeListByCondition(condition);
	}

	@Override
	public AjaxResult addCsLike(CsLikeBo csLikeBo) {
		CsLikeDto csLike = new CsLikeDto();
		BeanUtils.copyProperties(csLikeBo, csLike);
		csLike.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(csLike)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateCsLike(CsLikeBo csLikeBo) {
		CsLikeDto csLike = new CsLikeDto();
		BeanUtils.copyProperties(csLikeBo, csLike);
		if(updateById(csLike)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public CsLikeVo getCsLikeByCondition(CsLikeConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		CsLikeVo vo = csLikeMapper.getCsLikeByCondition(condition);
		return vo;
	}

}