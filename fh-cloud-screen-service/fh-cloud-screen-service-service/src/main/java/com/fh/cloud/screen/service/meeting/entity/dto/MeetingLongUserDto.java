package com.fh.cloud.screen.service.meeting.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 长期预约表人员表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("meeting_long_user")
public class MeetingLongUserDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "meeting_long_user_id", type = IdType.AUTO)
	private Long meetingLongUserId;

	/**
	 * FK长期预约表ID
	 */
	@TableField("meeting_long_id")
	private Long meetingLongId;

	/**
	 * 参与人user_oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 签到时间
	 */
	@TableField("sign_time")
	private Date signTime;

	/**
	 * 与会状态，1：未签到，2：已签到[提前]，3：已签到[正常]，4已签到[迟到]
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * grade 的code值
	 */
	@TableField("grade")
	private String grade;

	/**
	 * 班级ID
	 */
	@TableField("classes_id")
	private Long classesId;

	/**
	 * 班级名称
	 */
	@TableField("classes_name")
	private String classesName;

}
