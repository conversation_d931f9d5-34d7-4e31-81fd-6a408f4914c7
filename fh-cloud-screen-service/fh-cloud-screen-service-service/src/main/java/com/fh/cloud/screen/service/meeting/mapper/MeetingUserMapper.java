package com.fh.cloud.screen.service.meeting.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;

/**
 * 会议人员表Mapper
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
public interface MeetingUserMapper extends BaseMapper<MeetingUserDto> {

    List<MeetingUserVo> getMeetingUserListByCondition(MeetingUserConditionBo condition);

}
