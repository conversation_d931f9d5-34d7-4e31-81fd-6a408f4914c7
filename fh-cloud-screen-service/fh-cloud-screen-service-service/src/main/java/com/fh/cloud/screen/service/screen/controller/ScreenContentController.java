package com.fh.cloud.screen.service.screen.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.chromedp.dto.ChromedpDto;
import com.fh.cloud.screen.service.chromedp.service.ChromedpService;
import com.fh.cloud.screen.service.consts.ConstantsMessage;
import com.fh.cloud.screen.service.consts.ConstantsRedisScreenIndex;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailBo;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.StringKit;
import com.fh.cloud.screen.service.verify.service.VerifyPermissionsService;
import com.fh.cp.codec.entity.dto.CodecProcessDto;
import com.fh.cp.codec.enums.CodecCmdType;
import com.fh.cp.codec.rabbitmq.constant.CodecRabbitConstant;
import com.google.common.collect.Lists;
import com.light.redis.component.RedisComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.ScreenContentScopeType;
import com.fh.cloud.screen.service.enums.ScreenContentStatusType;
import com.fh.cloud.screen.service.enums.ScreenContentType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.screen.api.ScreenContentApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleDataService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 云屏内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@RestController
@Api(value = "", tags = "云屏内容管理")
@Slf4j
public class ScreenContentController implements ScreenContentApi {

    @Autowired
    private IScreenContentService screenContentService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    IScreenModuleDataService screenModuleDataService;
    @Autowired
    private ChromedpService chromedpService;
    @Autowired
    private RedisComponent redisComponent;
    @Resource
    private VerifyPermissionsService verifyPermissionsService;
    @Resource
    private BaseDataService baseDataService;

    /**
     * 云屏link转截图开关开启标识：默认关闭，需要nacos配置才是开启.1开，0关
     */
    @Value("${screen.content.link.image.enable:0}")
    private String screenContentLinkImageEnable;

    /**
     * 查询云屏内容表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询云屏内容表列表", httpMethod = "POST")
    public AjaxResult getScreenContentListByCondition(@RequestBody ScreenContentListConditionBo condition) {
        if (StringUtils.isNotBlank(condition.getScreenContentTitle())) {
            condition.setScreenContentTitle(FuzzyQueryUtil.transferMean(condition.getScreenContentTitle()));
        }
        if (condition.getScreenContentStatus() != null) {
            // 2 && inDate
            if (condition.getScreenContentStatus().equals(ScreenContentStatusType.ON_SHOW.getValue())) {
                condition.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
                condition.setNowDate(new Date());
            }
            // overdueDate
            if (condition.getScreenContentStatus().equals(ScreenContentStatusType.OUT_DATE.getValue())) {
                condition.setScreenContentStatus(null);
                condition.setOverdueDate(new Date());
            }
        }
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // web内容列表查询的时候，空认为是全校分类，且只有校级查询走这个逻辑
        if (condition.getCampusId() == null && condition.getScopeType() != null
            && condition.getScopeType().equals(ScreenContentScopeType.SCHOOL.getValue())) {
            condition.setCampusId(ConstantsLong.NUM_0);
        }
        condition.setOrderBy("content_nice asc,update_time desc");

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", screenContentService.getScreenContentListByConditionWithDetail(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ScreenContentVo> pageInfo =
                new PageInfo<>(screenContentService.getScreenContentListByConditionWithDetail(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 新增云屏内容表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "新增云屏内容表", httpMethod = "POST")
    public AjaxResult addScreenContent(@RequestBody ScreenContentBo screenContentBo) {
        boolean save = screenContentService.addScreenContent(screenContentBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏内容表
     *
     * @param screenContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "修改云屏内容表", httpMethod = "POST")
    public AjaxResult updateScreenContent(@RequestBody ScreenContentBo screenContentBo) {
        if (null == screenContentBo.getScreenContentId()) {
            return AjaxResult.fail("云屏内容表id不能为空");
        }
        boolean update = screenContentService.updateScreenContent(screenContentBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏内容表详情
     *
     * @param screenContentId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询云屏内容表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenContentId") Long screenContentId) {
        ScreenContentVo screenContentVo = screenContentService.getDetailWithDetail(screenContentId);
        return AjaxResult.success(screenContentVo);
    }

    /**
     * 删除云屏内容表
     *
     * @param screenContentId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "删除云屏内容表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenContentId") Long screenContentId) {
        ScreenContentBo screenContentBo = new ScreenContentBo();
        screenContentBo.setScreenContentId(screenContentId);
        screenContentBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenContentService.updateScreenContent(screenContentBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 保存云屏内容表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "保存云屏内容表", httpMethod = "POST")
    public AjaxResult saveScreenContent(@RequestBody ScreenContentBo screenContentBo) {
        // 越权校验
        boolean verifyOrganization = verifyPermissionsService.verifyOrganization(screenContentBo.getOrganizationId());
        if (!verifyOrganization) {
            return AjaxResult.fail("失败：当前登陆用户与该组织不同");
        }
        // 内容审核
        if (baseDataService.getCheckSwitch()) {
            boolean check = checkScreenContentDetail(screenContentBo.getScreenContentType(),
                screenContentBo.getScreenContentDetailBos());
            if (!check) {
                return AjaxResult.fail(ConstantsMessage.WRONG_INSPECT);
            }
        }

        boolean save = false;
        Long screenContentId = null;
        try {
            if (screenContentBo.getScreenContentDetailBos().size() > ConstantsInteger.NUM_10) {
                AjaxResult.fail("内容数量超过最大限制");
            }
            if (screenContentBo.getScreenContentType().equals(ScreenContentType.CONTENT_IMAGE.getValue())
                || screenContentBo.getScreenContentType().equals(ScreenContentType.CONTENT_VIDEO.getValue())) {
                screenContentBo.setDeleteAndAddDetail(true);
            }
            // 默认发布状态
            if (null == screenContentBo.getScreenContentStatus()) {
                screenContentBo.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
            }
            screenContentId = screenContentService.saveOrUpdateWithDetail(screenContentBo);
            if (screenContentId != null) {
                save = true;
            }
        } catch (Exception e) {
            log.error("ScreenContentController saveScreenContent error:", e);
            save = false;
        }
        if (save) {
            // publish event
            applicationContext.publishEvent(PublishEvent.produceContentPublishEvent(
                MessageWsType.MODIFY_CONTENT.getValue(), screenContentBo.getOrganizationId(), screenContentId, null));
            // create screenshotTask
            if (CollectionUtils.isNotEmpty(screenContentBo.getScreenContentDetailBos())
                && screenContentBo.getScreenContentDetailBos().get(0) != null
                && StringUtils.isNotBlank(screenContentBo.getScreenContentDetailBos().get(0).getScreenContentUrl())) {
                chromedpService.produceChromedpTask(ChromedpDto.generateChromedpDto(String.valueOf(screenContentId),
                    ChromedpService.BusinessType.NORMAL.getValue(),
                    screenContentBo.getScreenContentDetailBos().get(0).getScreenContentUrl(), null, null, null));
            }
            // mq消息获取封面图片
            if (screenContentBo.getScreenContentType().equals(ScreenContentType.CONTENT_VIDEO.getValue())) {
                screenContentService.produceGenCoverMqMessage(screenContentId);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail();
        }
    }

    /**
     * 撤回云屏发布内容
     *
     * @param screenContentId
     * @return
     */
    @ApiOperation(value = "撤回云屏发布内容", httpMethod = "POST")
    @Override
    public AjaxResult cancelSubmit(Long screenContentId) {
        if (null == screenContentId) {
            return AjaxResult.fail("云屏发布内容表id不能为空");
        }

        ScreenContentBo screenContentBo = new ScreenContentBo();
        screenContentBo.setScreenContentId(screenContentId);
        screenContentBo.setScreenContentStatus(ScreenContentStatusType.NOT_PUBLISH.getValue());
        boolean update = screenContentService.updateScreenContent(screenContentBo);
        if (update) {
            // publish event
            applicationContext.publishEvent(PublishEvent.produceContentPublishEvent(
                MessageWsType.MODIFY_CONTENT.getValue(), screenContentBo.getOrganizationId(), screenContentId, null));
            return AjaxResult.success("撤销发布成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 菜单数据查询(通知公告，新闻资讯，风采展示)
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/22 17:29
     */
    @ApiOperation(value = "首页查询菜单数据", httpMethod = "POST")
    public AjaxResult getMenuByCondition(@RequestBody ScreenContentListConditionBo condition) {
        Long screenModuleDataId = screenModuleDataService.getScreenModuleDataIdByScreenModuleLibraryId(
            condition.getScreenModuleLibraryId(), condition.getOrganizationId());
        if (screenModuleDataId == null) {
            return AjaxResult.fail("菜单不存在");
        }

        condition.setScreenModuleDataId(screenModuleDataId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
        condition.setOrderBy("content_nice asc,update_time desc");
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<>();
            condition.setPageNo(SystemConstants.NO_PAGE);
            List<ScreenContentVo> screenContentListByConditionWithDetail =
                screenContentService.getScreenContentListByConditionWithDetail(condition);
            // 是否隐藏link转换的图片
            if (SystemConstants.NO.equals(screenContentLinkImageEnable)) {
                screenContentService.hideLinkImage(screenContentListByConditionWithDetail);
            }
            map.put("list", screenContentListByConditionWithDetail);
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            List<ScreenContentVo> screenContentListByConditionWithDetail =
                screenContentService.getScreenContentListByConditionWithDetail(condition);
            // 是否隐藏link转换的图片
            if (SystemConstants.NO.equals(screenContentLinkImageEnable)) {
                screenContentService.hideLinkImage(screenContentListByConditionWithDetail);
            }
            PageInfo<ScreenContentVo> pageInfo = new PageInfo<>(screenContentListByConditionWithDetail);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 菜单数据查询(通知公告，新闻资讯，风采展示)-优先查询缓存数据
     *
     * @param condition the condition
     * @return the menu by condition with cache
     * <AUTHOR>
     * @date 2023 -01-13 17:40:29
     */
    @ApiOperation(value = "首页查询菜单数据-优先查询缓存数据", httpMethod = "POST")
    public AjaxResult getMenuByConditionWithCache(ScreenContentListConditionBo condition) {
        // 0、带了分页参数且不是第一页，则直接查询db
        if (condition.getPageNo() != null && !condition.getPageNo().equals(ConstantsInteger.NUM_1)) {
            return getMenuByCondition(condition);
        }
        Long screenModuleDataId = screenModuleDataService.getScreenModuleDataIdByScreenModuleLibraryId(
            condition.getScreenModuleLibraryId(), condition.getOrganizationId());
        if (screenModuleDataId == null) {
            return AjaxResult.fail("菜单不存在");
        }
        String queryTimeCacheKey = StringKit.getMessage(ConstantsRedisScreenIndex.INDEX_MENU_QUERY_TIME,
            String.valueOf(condition.getOrganizationId()), String.valueOf(condition.getScopeType()),
            String.valueOf(screenModuleDataId), String.valueOf(condition.getCampusId()),
            String.valueOf(condition.getClassesId()));
        String cacheKey =
            StringKit.getMessage(ConstantsRedisScreenIndex.INDEX_MENU, String.valueOf(condition.getOrganizationId()),
                String.valueOf(condition.getScopeType()), String.valueOf(screenModuleDataId),
                String.valueOf(condition.getCampusId()), String.valueOf(condition.getClassesId()));
        if (!redisComponent.hasKey(queryTimeCacheKey) || redisComponent.get(queryTimeCacheKey) == null) {
            AjaxResult menuByCondition = getMenuByConditionAndSetCache(condition, queryTimeCacheKey, cacheKey);
            return menuByCondition;
        }

        // 1、判断是否有新数据：查询大于上次查询时间是否有数据
        Long queryTime = (Long)redisComponent.get(queryTimeCacheKey);
        Date queryTimeDate = DateKit.getDateFromMillis(queryTime);
        Integer newData = screenContentService.countFirstRecord(condition.getScopeType(), screenModuleDataId,
            condition.getCampusId(), condition.getClassesId(), queryTimeDate);
        // 2-1、返回redis内容
        if (newData == null) {
            if (redisComponent.hasKey(cacheKey) && redisComponent.get(cacheKey) != null) {
                Object cacheValue = redisComponent.get(cacheKey);
                Map<String, Object> cacheValueMap = JSONObject.parseObject((String)cacheValue, Map.class);
                cacheValueMap.put("list",
                    JSONObject.parseArray(JSONObject.toJSONString(cacheValueMap.get("list")), ScreenContentVo.class));
                redisComponent.set(queryTimeCacheKey, System.currentTimeMillis());
                return AjaxResult.success(cacheValueMap);
            }
        }
        // 2-2、查询db并返回最新内容，同时更新redis
        if (newData != null || (newData == null && !redisComponent.hasKey(cacheKey))
            || (newData == null && redisComponent.get(cacheKey) == null)) {
            AjaxResult menuByCondition = getMenuByConditionAndSetCache(condition, queryTimeCacheKey, cacheKey);
            return menuByCondition;
        }

        // 默认构造空对象返回
        return AjaxResult.success(null, 0L, condition.getPageNo(), condition.getPageSize());
    }

    /**
     * 从数据库查询云屏菜单数据，并缓存数据到redis
     *
     * @param condition the condition
     * @param queryTimeCacheKey the query time cache key
     * @param cacheKey the cache key
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -01-16 18:35:03
     */
    private AjaxResult getMenuByConditionAndSetCache(ScreenContentListConditionBo condition, String queryTimeCacheKey,
        String cacheKey) {
        AjaxResult menuByCondition = getMenuByCondition(condition);
        if (menuByCondition.isSuccess() && menuByCondition.getData() != null) {
            Object value = menuByCondition.getData();
            redisComponent.set(queryTimeCacheKey, System.currentTimeMillis());
            redisComponent.set(cacheKey, JSONObject.toJSONString(value),
                ConstantsRedisScreenIndex.INDEX_MENU_EXPIRE_IN);
            return menuByCondition;
        }
        return menuByCondition;
    }

    /**
     * 检查内容文本或图片是否合规
     *
     * @param screenContentDetailBos
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 14:31
     */
    private boolean checkScreenContentDetail(Integer screenContentType,
        List<ScreenContentDetailBo> screenContentDetailBos) {
        boolean check = true;
        if (CollectionUtils.isEmpty(screenContentDetailBos)) {
            return check;
        }
        for (ScreenContentDetailBo screenContentDetailBo : screenContentDetailBos) {
            // 标题
            if (StringUtils.isNotBlank(screenContentDetailBo.getScreenContentTitle())) {
                check = baseDataService.checkSingleText(screenContentDetailBo.getScreenContentTitle());
            }
            if (!check) {
                return false;
            }
            // 内容
            if (StringUtils.isNotBlank(screenContentDetailBo.getScreenContentTxt())) {
                check = baseDataService.checkRichText(screenContentDetailBo.getScreenContentTxt());
            }
            if (!check) {
                return false;
            }
            // 称呼
            if (StringUtils.isNotBlank(screenContentDetailBo.getCallContent())) {
                check = baseDataService.checkSingleText(screenContentDetailBo.getCallContent());
            }
            if (!check) {
                return false;
            }
            // 落款
            if (StringUtils.isNotBlank(screenContentDetailBo.getSignContent())) {
                check = baseDataService.checkSingleText(screenContentDetailBo.getSignContent());
            }
            if (!check) {
                return false;
            }

            // 图片
            if (StringUtils.isNotBlank(screenContentDetailBo.getScreenContentMediaUrl()) && screenContentType != null
                && screenContentType.equals(ScreenContentType.CONTENT_IMAGE.getValue())) {
                check = baseDataService.checkSingleImage(screenContentDetailBo.getScreenContentMediaUrl());
            }
        }
        return check;

    }

}
