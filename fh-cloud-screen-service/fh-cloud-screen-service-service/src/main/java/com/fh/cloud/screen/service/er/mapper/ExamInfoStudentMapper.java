package com.fh.cloud.screen.service.er.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoStudentDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import org.apache.ibatis.annotations.Param;

/**
 * 考场_考试计划里面一次考试的学生Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamInfoStudentMapper extends BaseMapper<ExamInfoStudentDto> {

    List<ExamInfoStudentVo> getExamInfoStudentListByCondition(ExamInfoStudentConditionBo condition);

    void addExamInfoStudentBatchByXML(@Param("examInfoStudentBos") List<ExamInfoStudentBo> examInfoStudentBos);

}
