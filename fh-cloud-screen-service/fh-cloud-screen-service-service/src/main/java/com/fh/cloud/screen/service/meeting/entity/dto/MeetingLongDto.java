package com.fh.cloud.screen.service.meeting.entity.dto;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 长期预约表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("meeting_long")
public class MeetingLongDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "meeting_long_id", type = IdType.AUTO)
	private Long meetingLongId;

	/**
	 * FK所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 区域分组使用类型：1是行政教室，2不是行政教室
	 */
	@TableField("space_group_use_type")
	private Integer spaceGroupUseType;

	/**
	 * 会议室地点id
	 */
	@TableField("space_info_id")
	private Long spaceInfoId;

	/**
	 * 申请人user_oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 会议主题
	 */
	@TableField("title")
	private String title;

	/**
	 * 长期会议开始日期:yyyy-MM-dd
	 */
	@TableField("meeting_start_date")
	private Date meetingStartDate;

	/**
	 * 长期会议结束日期:yyyy-MM-dd
	 */
	@TableField("meeting_end_date")
	private Date meetingEndDate;

	/**
	 * 开始时间
	 */
	@TableField("meeting_start_time")
	private Time meetingStartTime;

	/**
	 * 结束时间
	 */
	@TableField("meeting_end_time")
	private Time meetingEndTime;

	/**
	 * 正常签到时间
	 */
	@TableField("normal_sign_in_time")
	private Time normalSignInTime;

	/**
	 * 是否签到（1：是，2：否）
	 */
	@TableField("is_sign_in")
	private Integer isSignIn;

	/**
	 * 会议内容
	 */
	@TableField("content")
	private String content;

	/**
	 * 会议备注
	 */
	@TableField("note")
	private String note;

	/**
	 * 会议状态（1:未开始，2：进行中，3：已结束，4：提前结束）
	 */
	@TableField("status")
	private Integer status;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 会议用户类型（1：教师，2：学生）
	 */
	@TableField("meeting_user_type")
	private Integer meetingUserType;

	/**
	 * 批量会议uuid，埋点字段，后续用于批量取消
	 */
	@TableField("meeting_uuid")
	private String meetingUuid;

	/**
	 * 生效的星期，多个使用英文逗号分割。周一到周日对应：1-7
	 */
	@TableField("weeks")
	private String weeks;

}
