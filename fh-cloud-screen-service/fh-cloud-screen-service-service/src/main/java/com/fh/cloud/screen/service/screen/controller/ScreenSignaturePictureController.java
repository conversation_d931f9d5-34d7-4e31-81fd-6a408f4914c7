package com.fh.cloud.screen.service.screen.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenSignaturePictureApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignaturePictureDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo;
import com.fh.cloud.screen.service.screen.service.IScreenSignaturePictureService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 电子签名图片资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@RestController
@Validated
public class ScreenSignaturePictureController implements ScreenSignaturePictureApi {

    @Autowired
    private IScreenSignaturePictureService screenSignaturePictureService;

    /**
     * 查询电子签名图片资源表分页列表
     * 
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult<PageInfo<ScreenSignaturePictureVo>>
        getScreenSignaturePicturePageListByCondition(@RequestBody ScreenSignaturePictureConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenSignaturePictureVo> pageInfo =
            new PageInfo<>(screenSignaturePictureService.getScreenSignaturePictureListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询电子签名图片资源表列表
     * 
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult<List<ScreenSignaturePictureVo>>
        getScreenSignaturePictureListByCondition(@RequestBody ScreenSignaturePictureConditionBo condition) {
        List<ScreenSignaturePictureVo> list =
            screenSignaturePictureService.getScreenSignaturePictureListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增电子签名图片资源表
     * 
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult
        addScreenSignaturePicture(@Validated @RequestBody ScreenSignaturePictureBo screenSignaturePictureBo) {
        return screenSignaturePictureService.addScreenSignaturePicture(screenSignaturePictureBo);
    }

    /**
     * 修改电子签名图片资源表
     * 
     * @param screenSignaturePictureBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult
        updateScreenSignaturePicture(@Validated @RequestBody ScreenSignaturePictureBo screenSignaturePictureBo) {
        if (null == screenSignaturePictureBo.getScreenSignaturePictureId()) {
            return AjaxResult.fail("电子签名图片资源表id不能为空");
        }
        return screenSignaturePictureService.updateScreenSignaturePicture(screenSignaturePictureBo);
    }

    /**
     * 查询电子签名图片资源表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult<ScreenSignaturePictureVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("电子签名图片资源表id不能为空");
        }
        ScreenSignaturePictureConditionBo condition = new ScreenSignaturePictureConditionBo();
        condition.setScreenSignaturePictureId(id);
        ScreenSignaturePictureVo vo = screenSignaturePictureService.getScreenSignaturePictureByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除电子签名图片资源表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenSignaturePictureDto screenSignaturePictureDto = new ScreenSignaturePictureDto();
        screenSignaturePictureDto.setScreenSignaturePictureId(id);
        screenSignaturePictureDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenSignaturePictureService.updateById(screenSignaturePictureDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
