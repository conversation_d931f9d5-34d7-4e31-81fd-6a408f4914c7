package com.fh.cloud.screen.service.rest.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("work_rest")
public class WorkRest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "work_rest_id", type = IdType.AUTO)
    private Long workRestId;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @TableField("campus_id")
    private Long campusId;

    /**
     * 作息时间名称
     */
    @TableField("name")
    private String name;

    /**
     * 禁用状态，0禁用，1正常
     */
    @TableField("status")
    private Integer status;

    /**
     * 年级作息时间是否一致：1一致，2不一致
     */
    @TableField("grade_same_type")
    private Integer gradeSameType;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
