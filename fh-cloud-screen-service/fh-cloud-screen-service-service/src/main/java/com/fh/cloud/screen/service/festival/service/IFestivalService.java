package com.fh.cloud.screen.service.festival.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.enums.FestivalEnum;
import com.fh.cloud.screen.service.festival.entity.dto.FestivalDto;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalBo;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;
import com.light.core.entity.AjaxResult;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;

/**
 * 节日表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
public interface IFestivalService extends IService<FestivalDto> {

    List<FestivalVo> getFestivalListByCondition(FestivalConditionBo condition);

    AjaxResult addFestival(FestivalBo festivalBo);

    AjaxResult updateFestival(FestivalBo festivalBo);

    FestivalVo getDetail(Long festivalId);

    AjaxResult delete(Long festivalId);

    /**
     * 获取 {date}那一天的所在的节日(参见{@link FestivalEnum})集合
     * 
     * @param stringDate yyyy-MM-dd
     * @return
     */
    List<FestivalVo> getFestivalDurationListByDate(String stringDate);

}
