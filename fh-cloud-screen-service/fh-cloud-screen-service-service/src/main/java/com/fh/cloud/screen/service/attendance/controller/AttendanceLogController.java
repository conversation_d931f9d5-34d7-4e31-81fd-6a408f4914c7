package com.fh.cloud.screen.service.attendance.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fh.cloud.screen.service.attendance.api.AttendanceLogApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo;
import com.fh.cloud.screen.service.attendance.service.IAttendanceLogService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 考勤流水表，不用于业务查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@RestController
@Validated
@Api(value = "", tags = "考勤流水接口")
public class AttendanceLogController implements AttendanceLogApi {

    @Autowired
    private IAttendanceLogService attendanceLogService;

    /**
     * 查询考勤流水表，不用于业务查询列表
     * 
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "查询考勤流水表，不用于业务查询列表", httpMethod = "POST")
    public AjaxResult getAttendanceLogListByCondition(@RequestBody AttendanceLogListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<AttendanceLogVo> pageInfo =
            new PageInfo<>(attendanceLogService.getAttendanceLogListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("attendanceLogList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    @ApiOperation(value = "查询考勤流水表当天考勤记录", httpMethod = "POST")
    public AjaxResult getTodayAttendanceLogList(@RequestBody AttendanceLogListConditionBo condition) {
        return AjaxResult.success(attendanceLogService.getTodayAttendanceLogList(condition));
    }

    /**
     * 新增考勤流水表，不用于业务查询
     * 
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "新增考勤流水表，不用于业务查询", httpMethod = "POST")
    public AjaxResult addAttendanceLog(@Validated @RequestBody AttendanceLogBo attendanceLogBo) {
        if (StringUtil.isBlank(attendanceLogBo.getDeviceNumber()) || null == attendanceLogBo.getAttendanceType()
            || null == attendanceLogBo.getOrganizationId()) {
            return AjaxResult.fail("参数错误");
        }
        return attendanceLogService.addAttendanceLog(attendanceLogBo);
    }

    /**
     * 修改考勤流水表，不用于业务查询
     * 
     * @param attendanceLogBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "修改考勤流水表，不用于业务查询", httpMethod = "POST")
    public AjaxResult updateAttendanceLog(@Validated @RequestBody AttendanceLogBo attendanceLogBo) {
        if (null == attendanceLogBo.getAttendanceLogId()) {
            return AjaxResult.fail("考勤流水表，不用于业务查询id不能为空");
        }
        boolean update = attendanceLogService.updateAttendanceLog(attendanceLogBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询考勤流水表，不用于业务查询详情
     * 
     * @param attendanceLogId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "查询考勤流水表，不用于业务查询详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("attendanceLogId") Long attendanceLogId) {
        Map<String, Object> map = attendanceLogService.getDetail(attendanceLogId);
        return AjaxResult.success(map);
    }

    /**
     * 删除考勤流水表，不用于业务查询
     * 
     * @param attendanceLogId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "删除考勤流水表，不用于业务查询", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("attendanceLogId") Long attendanceLogId) {
        AttendanceLogBo attendanceLogBo = new AttendanceLogBo();
        attendanceLogBo.setAttendanceLogId(attendanceLogId);
        attendanceLogBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = attendanceLogService.updateAttendanceLog(attendanceLogBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 根据班级ID、日期 获取学生统计信息
     *
     * <AUTHOR>
     * @param classesId the classesId 班级ID
     * @param dateTime the dateTime yyyy-MM-dd HH:mm:ss 日期
     * @return
     */
    public AjaxResult getStudentCensusByClassesId(@RequestParam("classesId") Long classesId,
        @RequestParam("dateTime") String dateTime) {
        AttendanceLogCensusVo attendanceLogCensusVo =
            this.attendanceLogService.getStudentCensusByClassesId(classesId, dateTime);
        return AjaxResult.success(attendanceLogCensusVo);
    }

    /**
     * 根据学校ID、日期 获取老师统计信息
     *
     * <AUTHOR>
     * @param orgId the orgId 学校ID
     * @param cacheKey the clock teacher attendance key
     * @return
     */
    @Override
    public AjaxResult<AttendanceLogCensusVo> getClockTeacherCensusByOrgId(@RequestParam("orgId") Long orgId,
        @RequestParam("cacheKey") String cacheKey) {
        return AjaxResult.success(this.attendanceLogService.getClockTeacherCensusByOrgId(orgId, cacheKey));
    }

    /**
     * 根据班级ID、cache Key 获取考勤统计信息
     * 
     * @param classesId the classes id 班级ID
     * @param cacheKey attendance log list cache key 打卡记录缓存key
     * @return
     */
    @Override
    public AjaxResult<AttendanceDayCensusVo> getClockStudentCensusByClassesId(
        @RequestParam("organizationId") Long organizationId, @RequestParam("classesId") Long classesId) {
        return AjaxResult
            .success(this.attendanceLogService.getClockStudentCensusByClassesId(organizationId, classesId));
    }

    /**
     * 根据班级和日期预先插入班级用户当天考勤数据（全部正常签到）
     *
     * @param classesId
     * @param date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    @Override
    @GetMapping("/attendance-log/pre-insert-class")
    public AjaxResult preInsertAttendanceLogByClass(@RequestParam("classesId") Long classesId,
        @RequestParam("date") String date) {
        if (null == classesId || StringUtils.isBlank(date)) {
            return AjaxResult.fail("参数错误");
        }
        return attendanceLogService.preInsertAttendanceLogByClass(classesId, date);
    }

    /**
     * 根据组织id和日期预先插入全部教师当天考勤数据（全部正常签到）
     *
     * @param organizationId
     * @param date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    @Override
    @GetMapping("/attendance-log/pre-insert-teacher")
    public AjaxResult preInsertAttendanceLogByTeacher(@RequestParam("organizationId") Long organizationId,
        @RequestParam("date") String date) {
        if (StringUtils.isBlank(date) || StringUtils.isBlank(date)) {
            return AjaxResult.fail("参数错误");
        }
        return attendanceLogService.preInsertAttendanceLogByTeacher(organizationId, date);
    }
}
