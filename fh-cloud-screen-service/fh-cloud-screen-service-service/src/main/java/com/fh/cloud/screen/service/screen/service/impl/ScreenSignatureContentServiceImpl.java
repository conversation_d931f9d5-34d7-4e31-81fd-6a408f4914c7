package com.fh.cloud.screen.service.screen.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureContentDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenSignatureContentMapper;
import com.fh.cloud.screen.service.screen.service.IScreenSignatureContentService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 电子签名表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@Service
public class ScreenSignatureContentServiceImpl extends
    ServiceImpl<ScreenSignatureContentMapper, ScreenSignatureContentDto> implements IScreenSignatureContentService {

    @Resource
    private ScreenSignatureContentMapper screenSignatureContentMapper;

    @Override
    public List<ScreenSignatureContentVo>
        getScreenSignatureContentListByCondition(ScreenSignatureContentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenSignatureContentMapper.getScreenSignatureContentListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenSignatureContent(ScreenSignatureContentBo screenSignatureContentBo) {
        ScreenSignatureContentDto screenSignatureContent = new ScreenSignatureContentDto();
        BeanUtils.copyProperties(screenSignatureContentBo, screenSignatureContent);
        screenSignatureContent.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenSignatureContent)) {
            ScreenSignatureContentVo screenSignatureContentVo = new ScreenSignatureContentVo();
            BeanUtils.copyProperties(screenSignatureContent, screenSignatureContentVo);
            return AjaxResult.success(screenSignatureContentVo);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenSignatureContent(ScreenSignatureContentBo screenSignatureContentBo) {
        ScreenSignatureContentDto screenSignatureContent = new ScreenSignatureContentDto();
        BeanUtils.copyProperties(screenSignatureContentBo, screenSignatureContent);
        if (updateById(screenSignatureContent)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenSignatureContentVo getScreenSignatureContentByCondition(ScreenSignatureContentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ScreenSignatureContentVo vo = screenSignatureContentMapper.getScreenSignatureContentByCondition(condition);
        return vo;
    }

}