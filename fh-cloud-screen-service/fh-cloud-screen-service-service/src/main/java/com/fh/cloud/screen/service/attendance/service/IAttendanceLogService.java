package com.fh.cloud.screen.service.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceLog;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 考勤流水表，不用于业务查询接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface IAttendanceLogService extends IService<AttendanceLog> {

    List<AttendanceLogVo> getAttendanceLogListByCondition(AttendanceLogListConditionBo condition);

    List<AttendanceLogVo> getTodayAttendanceLogList(AttendanceLogListConditionBo condition);

    AjaxResult addAttendanceLog(AttendanceLogBo attendanceLogBo);

    boolean updateAttendanceLog(AttendanceLogBo attendanceLogBo);

    Map<String, Object> getDetail(Long attendanceLogId);

    /**
     * 根据班级ID 日期（yyyy-MM-dd） 获取学生考勤记录
     *
     * @param classesId
     * @param day
     * @return
     */
    List<AttendanceLogVo> getStudentLogListByClassesIdAndDateTime(Long classesId, String day);

    /**
     * 根据班级、日期获取学生打卡记录信息
     *
     * @param classesId the classesId 班级ID
     * @param dateTime the dateTime 日期 yyyy-MM-dd HH:mm:ss
     * @return
     */
    AttendanceLogCensusVo getStudentCensusByClassesId(Long classesId, String dateTime);

    /**
     * 根据学校ID、日期获取老师打卡记录信息
     *
     * @param orgId the orgId 学校ID
     * @param cacheKey the teacher clock attendance cache key
     * @return
     */
    AttendanceLogCensusVo getClockTeacherCensusByOrgId(Long orgId, String cacheKey);

    /**
     * 根据学校ID 日期（yyyy-MM-dd） 获取老师考勤记录
     *
     * @param orgId the orgId 学校ID
     * @param day the day yyyy-MM-dd HH:mm:ss 日期
     * @return
     */
    List<AttendanceLogVo> getTeacherLogListByOrgIdAndDateTime(Long orgId, String day);

    /**
     * 根据班级ID 缓存key获取统计信息
     *
     * @param classesId the classes id 班级ID
     * @return clock student census by classes id
     * <AUTHOR>
     * @date 2023 -08-27 09:51:58
     */
    AttendanceDayCensusVo getClockStudentCensusByClassesId(Long organizationId, Long classesId);

    /**
     * 根据班级和日期预先插入班级用户当天考勤数据（全部正常签到）
     *
     * @param classesId, date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    AjaxResult preInsertAttendanceLogByClass(Long classesId, String date);

    /**
     * 根据userOid和日期预先插入用户当天考勤数据（全部正常签到）
     *
     * @param organizationId, date
     * @return com.light.core.entity.AjaxResult<com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo>
     * <AUTHOR>
     * @date 2023/1/12 14:01
     */
    AjaxResult preInsertAttendanceLogByTeacher(Long organizationId, String date);
}
