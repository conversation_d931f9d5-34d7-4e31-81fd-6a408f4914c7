package com.fh.cloud.screen.service.meeting.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.meeting.service.IMeetingUserService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;
import com.fh.cloud.screen.service.meeting.mapper.MeetingUserMapper;
import com.light.core.entity.AjaxResult;

/**
 * 会议人员表接口实现类
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Service
public class MeetingUserServiceImpl extends ServiceImpl<MeetingUserMapper, MeetingUserDto>
    implements IMeetingUserService {

    @Resource
    private MeetingUserMapper meetingUserMapper;

    @Override
    public List<MeetingUserVo> getMeetingUserListByCondition(MeetingUserConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return meetingUserMapper.getMeetingUserListByCondition(condition);
    }

    @Override
    public AjaxResult addMeetingUser(MeetingUserBo meetingUserBo) {
        MeetingUserDto meetingUser = new MeetingUserDto();
        BeanUtils.copyProperties(meetingUserBo, meetingUser);
        meetingUser.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(meetingUser)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateMeetingUser(MeetingUserBo meetingUserBo) {
        MeetingUserDto meetingUser = new MeetingUserDto();
        BeanUtils.copyProperties(meetingUserBo, meetingUser);
        if (updateById(meetingUser)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public MeetingUserVo getDetail(Long id) {
        MeetingUserConditionBo condition = new MeetingUserConditionBo();
        condition.setMeetingUserId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<MeetingUserVo> list = meetingUserMapper.getMeetingUserListByCondition(condition);
        MeetingUserVo vo = new MeetingUserVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}