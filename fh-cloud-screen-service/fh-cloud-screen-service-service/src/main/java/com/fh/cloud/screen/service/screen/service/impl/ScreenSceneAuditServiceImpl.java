package com.fh.cloud.screen.service.screen.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.*;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.screen.entity.bo.*;
import com.fh.cloud.screen.service.screen.entity.vo.*;
import com.fh.cloud.screen.service.screen.service.IScreenContentService;
import com.fh.cloud.screen.service.screen.service.IScreenSceneService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.user.campus.entity.vo.CampusVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneAuditDto;
import com.fh.cloud.screen.service.screen.service.IScreenSceneAuditService;
import com.fh.cloud.screen.service.screen.mapper.ScreenSceneAuditMapper;
import com.light.core.entity.AjaxResult;
/**
 * 云屏场景审核表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
@Service
public class ScreenSceneAuditServiceImpl extends ServiceImpl<ScreenSceneAuditMapper, ScreenSceneAuditDto> implements IScreenSceneAuditService {

	@Resource
	private ScreenSceneAuditMapper screenSceneAuditMapper;
	@Lazy
	@Resource
	private IScreenSceneService screenSceneService;
	@Resource
	private ApplicationContext applicationContext;
	@Resource
	private IClassesInfoService classesInfoService;
	@Lazy
	@Autowired
	private IScreenContentService screenContentService;
	/**
	 * 云屏link转截图开关开启标识：默认关闭，需要nacos配置才是开启.1开，0关
	 */
	@Value("${screen.content.link.image.enable:0}")
	private String screenContentLinkImageEnable;

	@Resource
	private BaseDataService baseDataService;

	@Resource
	private ISpaceGroupService spaceGroupService;

	@Resource
	private ISpaceInfoService spaceInfoService;
	
    @Override
	public List<ScreenSceneAuditVo> getScreenSceneAuditListByCondition(ScreenSceneAuditConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
		List<ScreenSceneAuditVo> list = new ArrayList<>();
		if (condition.isSearchDeviceScreenScene()) {
			// 点位发布
			condition.setPublishType(ScreenScenePublishType.POINT.getValue());
			// 查询点位发布场景审核列表
			list = screenSceneAuditMapper.getScreenSceneAuditListByPoint(condition);
			// 获取行政教室地点
			if (CollectionUtils.isNotEmpty(list)) {
				List<ScreenSceneAuditVo> listXZ = list.stream().filter(s -> SpaceGroupUseType.XZ.getValue() == s.getSpaceGroupUseType()).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(listXZ)) {
					List<Long> classesIds =
							listXZ.stream().map(ScreenSceneAuditVo::getSpaceInfoId).distinct().collect(Collectors.toList());
					List<ClazzInfoVo> clazzInfoVoList = classesInfoService.listClazzInfoVosByClassIds(classesIds);
					Map<Long, String> clazzInfoMap =
							CollectionUtils.isEmpty(clazzInfoVoList) ? Maps.newHashMap() : clazzInfoVoList.stream()
									.collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getClassesNameShow));
					for (ScreenSceneAuditVo screenSceneAuditVo : list) {
						if (screenSceneAuditVo.getSpaceGroupUseType() == SpaceGroupUseType.XZ.getValue()) {
							screenSceneAuditVo.setSpaceInfoName(clazzInfoMap.get(screenSceneAuditVo.getSpaceInfoId()));
						}
					}
				}
			}
		} else {
			// 统一发布
			condition.setPublishType(ScreenScenePublishType.GLOBAL.getValue());
			// 查询统一发布场景审核列表
			list = screenSceneAuditMapper.getScreenSceneAuditListByCondition(condition);
			// 查询学校、校区信息
			List<Long> organizationIds = list.stream()
					.filter(x -> x.getOrganizationId() != null && x.getOrganizationId() != ConstantsLong.NUM_0)
					.map(ScreenSceneAuditVo::getOrganizationId).collect(Collectors.toList());
			List<ScreenSceneAuditVo> campusScreenSceneAuditList = list.stream()
					.filter(x -> x.getCampusId() != null && x.getCampusId() != ConstantsLong.NUM_0)
					.collect(Collectors.toList());
			Map<Long, OrganizationVo> organizationVoMap = new HashMap<>();
			// 获取全部组织信息
			if (CollectionUtils.isNotEmpty(organizationIds)) {
				List<OrganizationVo> organizationVos = baseDataService.getOrganizationVoList(organizationIds);
				organizationVoMap = organizationVos.stream()
						.collect(Collectors.toMap(OrganizationVo::getId, x -> x, (v1, v2) -> v1));
			}
			// 获取分组列表
			List<SpaceGroupVo> allGroup = spaceGroupService.findAll();
			Map<Long, List<SpaceGroupVo>> allGroupMap =
					allGroup.stream().collect(Collectors.groupingBy(SpaceGroupVo::getSpaceGroupId));
			// 获取校区信息
			Map<Long, CampusVo> campusVoMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(campusScreenSceneAuditList)) {
				List<Long> orgIds = campusScreenSceneAuditList.stream().map(ScreenSceneAuditVo::getOrganizationId)
						.distinct().collect(Collectors.toList());
				List<CampusVo> campusVos = new ArrayList<>();
				for (Long orgId : orgIds) {
					CampusListConditionBo conditionBo = new CampusListConditionBo();
					conditionBo.setOrganizationId(orgId);
					List<CampusVo> campusVoList = baseDataService.getCampusVoByCondition(conditionBo);
					if (CollectionUtils.isNotEmpty(campusVoList)) {
						campusVos.addAll(campusVoList);
					}
				}
				campusVoMap = campusVos.stream().collect(Collectors.toMap(CampusVo::getId, c -> c, (v1, v2) -> v1));
			}
			for (ScreenSceneAuditVo screenSceneAuditVo : list) {
				if (screenSceneAuditVo.getOrganizationId() != null
						&& screenSceneAuditVo.getOrganizationId() != ConstantsLong.NUM_0
						&& organizationVoMap.containsKey(screenSceneAuditVo.getOrganizationId())) {
					screenSceneAuditVo.setOrganizationName(organizationVoMap.get(screenSceneAuditVo.getOrganizationId()).getName());
				}
				if (screenSceneAuditVo.getCampusId() != null && screenSceneAuditVo.getCampusId() != ConstantsLong.NUM_0
						&& campusVoMap.containsKey(screenSceneAuditVo.getCampusId())) {
					screenSceneAuditVo.setCampusName(campusVoMap.get(screenSceneAuditVo.getCampusId()).getName());
				}
				if (screenSceneAuditVo.getSpaceGroupId() != null
						&& screenSceneAuditVo.getSpaceGroupId() != ConstantsLong.NUM_0
						&& allGroupMap.containsKey(screenSceneAuditVo.getSpaceGroupId())) {
					screenSceneAuditVo.setSpaceGroupName(allGroupMap.get(screenSceneAuditVo.getSpaceGroupId())
							.get(ConstantsInteger.NUM_0).getSpaceGroupName());
				}
			}
		}
		return list;
	}

	@Override
	public AjaxResult addScreenSceneAudit(ScreenSceneAuditBo screenSceneAuditBo) {
		ScreenSceneAuditDto screenSceneAudit = new ScreenSceneAuditDto();
		BeanUtils.copyProperties(screenSceneAuditBo, screenSceneAudit);
		screenSceneAudit.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(screenSceneAudit)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScreenSceneAudit(ScreenSceneAuditBo screenSceneAuditBo) {
		ScreenSceneAuditDto screenSceneAudit = new ScreenSceneAuditDto();
		BeanUtils.copyProperties(screenSceneAuditBo, screenSceneAudit);
		if(updateById(screenSceneAudit)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ScreenSceneAuditVo getScreenSceneAuditByCondition(ScreenSceneAuditConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return screenSceneAuditMapper.getScreenSceneAuditByCondition(condition);
	}

	@Override
	public boolean addAndDeleteScreenSceneAudit(ScreenSceneBo screenSceneBo) {
		ScreenSceneAuditDto screenSceneAuditDto = new ScreenSceneAuditDto();
		BeanUtils.copyProperties(screenSceneBo, screenSceneAuditDto);
		// 待审核
		screenSceneAuditDto.setAuditType(AuditType.TO_AUDIT.getValue());
		// 模块数据
		if (CollectionUtils.isNotEmpty(screenSceneBo.getScreenModuleDatas())) {
			screenSceneAuditDto.setScreenModuleData(JSONArray.toJSONString(screenSceneBo.getScreenModuleDatas()));
		}
		// 入参
		screenSceneAuditDto.setParam(JSON.toJSONString(screenSceneBo));
		screenSceneAuditDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
		// 保存待审核记录
		if (save(screenSceneAuditDto)) {
			return true;
		}
		return false;
	}

	@Override
	public AjaxResult audit(ScreenSceneAuditBo screenSceneAuditBo) {
		ScreenSceneAuditDto screenSceneAuditDto = getById(screenSceneAuditBo.getScreenSceneAuditId());
		if (screenSceneAuditDto == null) {
			return AjaxResult.fail("审核数据不存在");
		}
		if (screenSceneAuditDto.getAuditType() != AuditType.TO_AUDIT.getValue()) {
			return AjaxResult.fail("当前数据已审核");
		}
		ScreenSceneAuditDto entity = new ScreenSceneAuditDto();
		entity.setScreenSceneAuditId(screenSceneAuditBo.getScreenSceneAuditId());
		entity.setAuditType(screenSceneAuditBo.getAuditType());
		if (AuditType.AUDIT_REJECT.getValue() == screenSceneAuditBo.getAuditType()) {
			entity.setReason(screenSceneAuditBo.getReason());
		}
		entity.setAuditUser(screenSceneAuditBo.getAuditUser());
		entity.setAuditTime(new Date());
		entity.setUpdateBy(screenSceneAuditBo.getUpdateBy());
		entity.setUpdateTime(new Date());
		if (updateById(entity)) {
			// 审核通过，走之前的逻辑
			if (AuditType.AUDIT_PASS.getValue() == entity.getAuditType()) {
				ScreenSceneBo screenSceneBo = JSON.parseObject(screenSceneAuditDto.getParam(), ScreenSceneBo.class);
				Long sceneId = screenSceneService.saveOrUpdateScreenSceneWithModule(screenSceneBo);
				if (sceneId != null) {
					List<Long> organizationIds = new ArrayList<>();
					if (screenSceneBo.getOrganizationId() != null && screenSceneBo.getOrganizationId() != ConstantsLong.NUM_0) {
						organizationIds.add(screenSceneBo.getOrganizationId());
					}
					if (screenSceneBo.getParentOrganizationId() != null && screenSceneBo.getParentOrganizationId() != ConstantsLong.NUM_0) {
						List<Long> superviseOrgIds = baseDataService.getSuperviseOrganizationIds(screenSceneBo.getParentOrganizationId());
						if (CollectionUtil.isNotEmpty(superviseOrgIds)) {
							organizationIds.addAll(superviseOrgIds);
						}
					}
					// publish event
					for (Long organizationId : organizationIds) {
						applicationContext.publishEvent(PublishEvent.produceScenePublishEvent(MessageWsType.MODIFY_SCENE.getValue(),
								organizationId, sceneId, null));
					}
					return AjaxResult.success(sceneId);
				}
			}
			return AjaxResult.success("审核成功");
		}
		return AjaxResult.fail("审核失败");
	}

	/**
	 * 待发布云屏数据
	 *
	 * @param showDeviceId
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/11/30 16:22
	 **/
	@Override
	public AjaxResult screenToAudit(Long screenSceneAuditId) {
		// 常规场景
		ScreenSceneVo vo = normalScreenSceneAudit(screenSceneAuditId);
		setScreenSceneVo(vo);
		return AjaxResult.success(vo);
	}

	// 封装返回参数
	private void setScreenSceneVo(ScreenSceneVo vo) {
		// 获取校区
		if (vo.getCampusId() != null && vo.getCampusId() != ConstantsLong.NUM_0) {
			CampusListConditionBo conditionBo = new CampusListConditionBo();
			conditionBo.setOrganizationId(vo.getOrganizationId());
			conditionBo.setPageNo(SystemConstants.NO_PAGE);
			List<CampusVo> campusVos = baseDataService.getCampusVoByCondition(conditionBo);
			for (CampusVo campusVo : campusVos) {
				if (vo.getCampusId().equals(campusVo.getId())) {
					vo.setCampusName(campusVo.getName());
				}
			}
		}
		// 获取地点
		if (vo.getSpaceInfoId() != null && vo.getSpaceInfoId() != ConstantsLong.NUM_0) {
			if (SpaceGroupUseType.XZ.getValue() == vo.getSpaceGroupUseType()) {
				// 行政地点
				ClazzVo clazzVo = baseDataService.getByClazzId(vo.getSpaceInfoId());
				if (clazzVo != null) {
					vo.setSpaceInfoName(SchoolYearUtil.gradeMap.get(clazzVo.getGrade()).concat(clazzVo.getClassesName()).concat("班"));
				}
			} else {
				// 非行政地点
				SpaceInfoVo spaceInfoVo = spaceInfoService.getDetail(vo.getSpaceInfoId());
				vo.setSpaceInfoName(spaceInfoVo.getSpaceInfoName());
			}
		}
	}


	/**
	 * 获取审核数量
	 *
	 * @param condition
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/12/1 10:12
	 **/
	@Override
	public AjaxResult getScreenSceneAuditCount(ScreenSceneAuditConditionBo condition) {
		ScreenSceneAuditCountVo countVo = baseMapper.getScreenSceneAuditCount(condition);
		return AjaxResult.success(countVo);
	}

	/**
	 * 常规场景
	 *
	 * @param screenIndexVos
	 * @param showDeviceId
	 * @return void
	 * <AUTHOR>
	 * @date 2023/12/1 10:12
	 **/
	private ScreenSceneVo normalScreenSceneAudit(Long screenSceneAuditId) {
		// 设备待审核的场景数据
		ScreenSceneAuditDto dto = baseMapper.selectById(screenSceneAuditId);
		ScreenSceneVo screenSceneVo = new ScreenSceneVo();
		BeanUtils.copyProperties(dto, screenSceneVo);
		List<Long> screenModuleDataIds = new ArrayList<>();
		List<ScreenModuleDataVo> screenModuleDataVos = new ArrayList<>();
		if (StringUtils.isNotEmpty(dto.getScreenModuleData())) {
			screenModuleDataVos = JSONArray.parseArray(dto.getScreenModuleData(), ScreenModuleDataVo.class);
			screenSceneVo.setScreenModuleDataVos(screenModuleDataVos);
			screenModuleDataIds = screenModuleDataVos.stream()
					.map(ScreenModuleDataVo::getScreenModuleDataId).collect(Collectors.toList());
		}
		if (CollectionUtils.isNotEmpty(screenModuleDataIds)) {
			// 根据模块查询内容数据
			ScreenContentListConditionBo screenContentListConditionBo = new ScreenContentListConditionBo();
			screenContentListConditionBo.setScreenIndexShow(true);
			screenContentListConditionBo.setOrganizationId(dto.getOrganizationId());
			screenContentListConditionBo.setCampusId(dto.getCampusId());
			screenContentListConditionBo.setClassesId(dto.getSpaceInfoId());
			screenContentListConditionBo.setPageNo(SystemConstants.NO_PAGE);
			screenContentListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
			screenContentListConditionBo.setScreenModuleDataIds(screenModuleDataIds);
			screenContentListConditionBo.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
			screenContentListConditionBo.setNowDate(new Date());
			screenContentListConditionBo.setOrderBy("content_nice asc,update_time desc");
			List<ScreenContentVo> screenContentVos =
					screenContentService.getScreenContentListByConditionWithDetail(screenContentListConditionBo);
			// 是否隐藏link转换的图片
			if (SystemConstants.NO.equals(screenContentLinkImageEnable)) {
				screenContentService.hideLinkImage(screenContentVos);
			}
			Map<Long, List<ScreenContentVo>> screenModuleIdMap =
					screenContentVos.stream().collect(Collectors.groupingBy(ScreenContentVo::getScreenModuleDataId));
			screenModuleDataVos.forEach(screenModuleDataVo -> screenModuleDataVo
					.setScreenContentVos(screenModuleIdMap.get(screenModuleDataVo.getScreenModuleDataId())));
		}
		screenSceneVo.setScreenPriority(ScreenPriorityType.NORMAL.getValue());
		// 校区#地点组id#场景类型#场景名称#轮播名称
		String playKey = Joiner.on(ConstString.jh).join(screenSceneVo.getCampusId(),
				screenSceneVo.getSpaceGroupId(), screenSceneVo.getScreenSceneType(),
				screenSceneVo.getScreenSceneName() == null ? "" : screenSceneVo.getScreenSceneName(),
				screenSceneVo.getScreenPlayName() == null ? "" : screenSceneVo.getScreenPlayName());
		screenSceneVo.setScreenPlayKey(playKey);
		screenSceneVo.setStartTime(DateKit.transferYMD2CurrentDay(screenSceneVo.getStartTime()));
		screenSceneVo.setEndTime(DateKit.transferYMD2CurrentDay(screenSceneVo.getEndTime()));
		List<ScreenSceneVo> screenSceneVos = new ArrayList<>();
		screenSceneVos.add(screenSceneVo);
		// 按照场景轮播key组装场景层级结构：组装完成后，后续的screenSceneVos将有层级，如需遍历处理则需要在此之前处理。
		Map<String, List<ScreenSceneVo>> screenSceneGroupMap = screenSceneVos.stream()
				.collect(Collectors.groupingBy(ScreenSceneVo::getScreenPlayKey, LinkedHashMap::new, Collectors.toList()));
		// screenSceneVos替换为新的多层的screenSceneVos
		List<ScreenSceneVo> screenSceneVosWithGroup = Lists.newArrayList();
		for (String screenPlayKey : screenSceneGroupMap.keySet()) {
			List<ScreenSceneVo> screenSceneVosTmp = screenSceneGroupMap.get(screenPlayKey);
			ScreenSceneVo screenSceneVoTemp = new ScreenSceneVo();
			BeanUtils.copyProperties(screenSceneVosTmp.get(0), screenSceneVoTemp);
			screenSceneVoTemp.setScreenSceneLayout(null);
			screenSceneVoTemp.setScreenModuleDataVos(null);
			screenSceneVoTemp.setScreenPlayIndex(null);
			screenSceneVoTemp.setScreenPlayName(null);
			screenSceneVoTemp.setChildren(screenSceneVosTmp);
			screenSceneVosWithGroup.add(screenSceneVoTemp);
		}
		return screenSceneVosWithGroup.get(0);
	}
//	private void normalScreenSceneAudit(List<ScreenIndexVo> screenIndexVos, Long showDeviceId) {
//		// 设备待审核的场景数据
//		List<ScreenSceneAuditDto> list = baseMapper.selectList(new LambdaQueryWrapper<ScreenSceneAuditDto>()
//				.eq(ScreenSceneAuditDto::getShowDeviceId, showDeviceId)
//				.eq(ScreenSceneAuditDto::getAuditType, ScreenSceneAuditType.TO_AUDIT.getValue())
//				.eq(ScreenSceneAuditDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
//		List<ScreenSceneVo> screenSceneVos = new ArrayList<>();
//		for (ScreenSceneAuditDto dto : list) {
//			ScreenSceneVo screenSceneVo = new ScreenSceneVo();
//			BeanUtils.copyProperties(dto, screenSceneVo);
//			List<Long> screenModuleDataIds = new ArrayList<>();
//			List<ScreenModuleDataVo> screenModuleDataVos = new ArrayList<>();
//			if (StringUtils.isNotEmpty(dto.getScreenModuleData())) {
//				screenModuleDataVos = JSONArray.parseArray(dto.getScreenModuleData(), ScreenModuleDataVo.class);
//				screenSceneVo.setScreenModuleDataVos(screenModuleDataVos);
//				screenModuleDataIds = screenModuleDataVos.stream()
//						.map(ScreenModuleDataVo::getScreenModuleDataId).collect(Collectors.toList());
//			}
//			if (CollectionUtils.isNotEmpty(screenModuleDataIds)) {
//				// 根据模块查询内容数据
//				ScreenContentListConditionBo screenContentListConditionBo = new ScreenContentListConditionBo();
//				screenContentListConditionBo.setScreenIndexShow(true);
//				screenContentListConditionBo.setOrganizationId(dto.getOrganizationId());
//				screenContentListConditionBo.setCampusId(dto.getCampusId());
//				screenContentListConditionBo.setClassesId(dto.getSpaceInfoId());
//				screenContentListConditionBo.setPageNo(SystemConstants.NO_PAGE);
//				screenContentListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
//				screenContentListConditionBo.setScreenModuleDataIds(screenModuleDataIds);
//				screenContentListConditionBo.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
//				screenContentListConditionBo.setNowDate(new Date());
//				screenContentListConditionBo.setOrderBy("content_nice asc,update_time desc");
//				List<ScreenContentVo> screenContentVos =
//						screenContentService.getScreenContentListByConditionWithDetail(screenContentListConditionBo);
//				// 是否隐藏link转换的图片
//				if (SystemConstants.NO.equals(screenContentLinkImageEnable)) {
//					screenContentService.hideLinkImage(screenContentVos);
//				}
//				Map<Long, List<ScreenContentVo>> screenModuleIdMap =
//						screenContentVos.stream().collect(Collectors.groupingBy(ScreenContentVo::getScreenModuleDataId));
//				screenModuleDataVos.forEach(screenModuleDataVo -> screenModuleDataVo
//						.setScreenContentVos(screenModuleIdMap.get(screenModuleDataVo.getScreenModuleDataId())));
//			}
//			screenSceneVos.add(screenSceneVo);
//		}
//		screenSceneVos.stream().forEach(screenSceneVo -> {
//			screenSceneVo.setScreenPriority(ScreenPriorityType.NORMAL.getValue());
//			// 校区#地点组id#场景类型#场景名称#轮播名称
//			String playKey = Joiner.on(ConstString.jh).join(screenSceneVo.getCampusId(),
//					screenSceneVo.getSpaceGroupId(), screenSceneVo.getScreenSceneType(),
//					screenSceneVo.getScreenSceneName() == null ? "" : screenSceneVo.getScreenSceneName(),
//					screenSceneVo.getScreenPlayName() == null ? "" : screenSceneVo.getScreenPlayName());
//			screenSceneVo.setScreenPlayKey(playKey);
//		});
//		screenSceneVos = screenSceneVos.stream()
//				.sorted(Comparator.comparing(ScreenSceneVo::getScreenPriority).thenComparing(ScreenSceneVo::getUpdateTime)
//						.reversed().thenComparing(ScreenSceneVo::getScreenIndex)
//						.thenComparing(ScreenSceneVo::getScreenPlayIndex))
//				.collect(Collectors.toList());
//		screenSceneVos.forEach(screenSceneVo -> {
//			screenSceneVo.setStartTime(DateKit.transferYMD2CurrentDay(screenSceneVo.getStartTime()));
//			screenSceneVo.setEndTime(DateKit.transferYMD2CurrentDay(screenSceneVo.getEndTime()));
//		});
//		// 按照场景轮播key组装场景层级结构：组装完成后，后续的screenSceneVos将有层级，如需遍历处理则需要在此之前处理。
//		Map<String, List<ScreenSceneVo>> screenSceneGroupMap = screenSceneVos.stream()
//				.collect(Collectors.groupingBy(ScreenSceneVo::getScreenPlayKey, LinkedHashMap::new, Collectors.toList()));
//		// screenSceneVos替换为新的多层的screenSceneVos
//		List<ScreenSceneVo> screenSceneVosWithGroup = Lists.newArrayList();
//		for (String screenPlayKey : screenSceneGroupMap.keySet()) {
//			List<ScreenSceneVo> screenSceneVosTmp = screenSceneGroupMap.get(screenPlayKey);
//			ScreenSceneVo screenSceneVoTemp = new ScreenSceneVo();
//			BeanUtils.copyProperties(screenSceneVosTmp.get(0), screenSceneVoTemp);
//			screenSceneVoTemp.setScreenSceneLayout(null);
//			screenSceneVoTemp.setScreenModuleDataVos(null);
//			screenSceneVoTemp.setScreenPlayIndex(null);
//			screenSceneVoTemp.setScreenPlayName(null);
//			screenSceneVoTemp.setChildren(screenSceneVosTmp);
//			screenSceneVosWithGroup.add(screenSceneVoTemp);
//		}
//		screenSceneVos = screenSceneVosWithGroup;
//		// 封装返回信息
//		LinkedHashMap<Integer, List<ScreenSceneVo>> screenSceneMap = screenSceneVos.stream()
//				.collect(Collectors.groupingBy(ScreenSceneVo::getScreenPriority, LinkedHashMap::new, Collectors.toList()));
//		for (Integer screenPriority : screenSceneMap.keySet()) {
//			ScreenIndexVo screenIndexVo = new ScreenIndexVo();
//			screenIndexVo.setScreenPriority(screenPriority);
//			screenIndexVo.setScreenSceneVos(screenSceneMap.get(screenPriority));
//			screenIndexVos.add(screenIndexVo);
//		}
//	}

}