package com.fh.cloud.screen.service.face.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fh.cloud.screen.service.face.entity.vo.FaceRecordUploadResultVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.FaceBrandType;
import com.fh.cloud.screen.service.enums.FaceStatusType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.face.api.FaceRecordTeacherApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceCountQueryBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordTeacherDto;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;
import com.fh.cloud.screen.service.face.service.IFaceRecordTeacherService;
import com.fh.cloud.screen.service.facebody.FaceBodyService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PhoneUtil;

/**
 * 教师人脸库
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
@RestController
@Validated
public class FaceRecordTeacherController implements FaceRecordTeacherApi {

    @Autowired
    private IFaceRecordTeacherService faceRecordTeacherService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IFaceConfigService faceConfigService;
    @Autowired
    private FaceBodyService faceBodyService;

    /**
     * 查询教师人脸库分页列表
     *
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult<PageInfo<FaceRecordTeacherVo>>
        getFaceRecordTeacherPageListByCondition(@RequestBody FaceRecordTeacherConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<FaceRecordTeacherVo> faceRecordTeacherVos =
            faceRecordTeacherService.getFaceRecordTeacherListByCondition(condition);
        convertResult(faceRecordTeacherVos);
        PageInfo<FaceRecordTeacherVo> pageInfo = new PageInfo<>(faceRecordTeacherVos);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 返回值转换
     *
     * @param faceRecordTeacherVos
     */
    private void convertResult(List<FaceRecordTeacherVo> faceRecordTeacherVos) {
        if (CollectionUtils.isEmpty(faceRecordTeacherVos)) {
            return;
        }

        List<String> teacherOIds =
            faceRecordTeacherVos.stream().map(FaceRecordTeacherVo::getUserOid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(teacherOIds)) {
            return;
        }

        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        teacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
        teacherConditionBo.setUserOids(teacherOIds);
        List<TeacherVo> teacherVoList = baseDataService.getTeacherVoList(teacherConditionBo);
        if(teacherVoList == null){
            faceRecordTeacherVos = Lists.newArrayList();
            return;
        }
        if (CollectionUtils.isEmpty(teacherVoList)) {
            return;
        }
        List<String> existUserOids =
            teacherVoList.stream().map(teacherVo -> teacherVo.getUserOid()).collect(Collectors.toList());
        for (int i = 0; i < faceRecordTeacherVos.size(); i++) {
            if (!existUserOids.contains(faceRecordTeacherVos.get(i).getUserOid())) {
                faceRecordTeacherVos.remove(i);
                i--;
            }
        }
    }

    /**
     * 查询教师人脸库列表
     *
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult<List<FaceRecordTeacherVo>>
        getFaceRecordTeacherListByCondition(@RequestBody FaceRecordTeacherConditionBo condition) {
        List<FaceRecordTeacherVo> faceRecordTeacherVos =
            faceRecordTeacherService.getFaceRecordTeacherListByCondition(condition);
        convertResult(faceRecordTeacherVos);
        return AjaxResult.success(faceRecordTeacherVos);
    }

    /**
     * 新增教师人脸库
     *
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult addFaceRecordTeacher(@Validated @RequestBody FaceRecordTeacherBo faceRecordTeacherBo) {
        return faceRecordTeacherService.addFaceRecordTeacher(faceRecordTeacherBo);
    }

    /**
     * 修改教师人脸库
     *
     * @param faceRecordTeacherBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult updateFaceRecordTeacher(@Validated @RequestBody FaceRecordTeacherBo faceRecordTeacherBo) {
        if (null == faceRecordTeacherBo.getFaceRecordTeacherId()) {
            return AjaxResult.fail("教师人脸库id不能为空");
        }
        return faceRecordTeacherService.updateFaceRecordTeacher(faceRecordTeacherBo);
    }

    @Override
    public AjaxResult updateFaceRecordTeacherBatch(@RequestBody List<FaceRecordTeacherBo> faceRecordTeacherBoList) {
        if (CollectionUtil.isEmpty(faceRecordTeacherBoList)) {
            return AjaxResult.fail("教师人脸库信息不能为空");
        }
        return faceRecordTeacherService.updateFaceRecordTeacherBatch(faceRecordTeacherBoList);
    }

    /**
     * 查询教师人脸库详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult<FaceRecordTeacherVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("教师人脸库id不能为空");
        }
        FaceRecordTeacherVo vo = faceRecordTeacherService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除教师人脸库
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        FaceRecordTeacherDto faceRecordTeacherDto = new FaceRecordTeacherDto();
        faceRecordTeacherDto.setFaceRecordTeacherId(id);
        faceRecordTeacherDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean result = faceRecordTeacherService.updateById(faceRecordTeacherDto);
        if (result) {
            // 如果使用的是阿里云则删除阿里云人脸库
            FaceRecordTeacherVo detail = faceRecordTeacherService.getDetail(id);
            FaceConfigVo faceConfigVo = faceConfigService.getDetailByOrganizationId(detail.getOrganizationId());
            if (faceConfigVo != null && faceConfigVo.getFaceBrandType().equals(FaceBrandType.ALI.getValue())) {
                faceBodyService.delFaceTransaction(detail.getUserOid());
            }
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult<PageInfo<FaceRecordTeacherVo>>
        getFaceRecordTeacherPageListByConditionWithUser(FaceRecordTeacherConditionBo condition) {
        List<FaceRecordTeacherVo> faceRecordTeacherListByCondition = null;
        List<String> userOids = null;
        if (condition.getFaceStatus() != null) {
            // 查询并设置人脸数据封装返回
            FaceRecordTeacherConditionBo faceRecordTeacherConditionBo = new FaceRecordTeacherConditionBo();
            faceRecordTeacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
            faceRecordTeacherConditionBo.setOrganizationId(condition.getOrganizationId());
            if (!condition.getFaceStatus().equals(FaceStatusType.NOT_UPLOAD.getValue())) {
                faceRecordTeacherConditionBo.setFaceStatus(condition.getFaceStatus());
            }
            faceRecordTeacherListByCondition =
                faceRecordTeacherService.getFaceRecordTeacherListByCondition(faceRecordTeacherConditionBo);
            userOids = faceRecordTeacherListByCondition.stream()
                .filter(faceRecordTeacherVo -> StringUtils.isNotBlank(faceRecordTeacherVo.getUserOid()))
                .map(FaceRecordTeacherVo::getUserOid).collect(Collectors.toList());

            if (!condition.getFaceStatus().equals(FaceStatusType.NOT_UPLOAD.getValue())
                && CollectionUtils.isEmpty(userOids)) {
                PageInfo<FaceRecordTeacherVo> pageInfo = new PageInfo<>();
                pageInfo.setList(Lists.newArrayList());
                pageInfo.setTotal(ConstantsLong.NUM_0);
                return AjaxResult.success(pageInfo);
            }
        }

        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(condition.getPageNo());
        teacherConditionBo.setPageSize(condition.getPageSize());
        teacherConditionBo.setRealName(condition.getRealName());
        teacherConditionBo.setOrganizationId(condition.getOrganizationId());
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (CollectionUtils.isNotEmpty(userOids) && condition.getFaceStatus() != null) {
            if (condition.getFaceStatus().equals(FaceStatusType.NOT_UPLOAD.getValue())) {
                teacherConditionBo.setNotUserOids(userOids);
            } else {
                teacherConditionBo.setUserOids(userOids);
            }
        }
        Map<String, Object> teacherListByCondition = baseDataService.getTeacherListByCondition(teacherConditionBo);
        if (teacherListByCondition == null || teacherListByCondition.get("list") == null) {
            return AjaxResult.success();
        }
        List<TeacherVo> teacherVoList = (List<TeacherVo>)teacherListByCondition.get("list");
        Integer total = (Integer)teacherListByCondition.get("total");
        if (CollectionUtils.isEmpty(teacherVoList)) {
            return AjaxResult.success();
        }

        // 设置重名手机号
        List<String> realNames = teacherVoList.stream().filter(teacherVo -> teacherVo.getUserVo() != null)
            .collect(Collectors.toMap(teacherVo -> teacherVo.getUserVo().getRealName(), e -> 1, Integer::sum))
            .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        teacherVoList.forEach(teacherVo -> {
            if (teacherVo.getUserVo() != null && realNames.contains(teacherVo.getUserVo().getRealName())) {
                teacherVo.setAccountPhone(String.valueOf(PhoneUtil.hideBetween(teacherVo.getAccountPhone())));
            } else {
                teacherVo.setAccountPhone(null);
            }
        });

        // 查询并设置人脸数据封装返回
        if (CollectionUtils.isEmpty(faceRecordTeacherListByCondition)) {
            FaceRecordTeacherConditionBo faceRecordTeacherConditionBo = new FaceRecordTeacherConditionBo();
            faceRecordTeacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
            faceRecordTeacherConditionBo.setOrganizationId(condition.getOrganizationId());
            faceRecordTeacherConditionBo.setFaceStatus(condition.getFaceStatus());
            faceRecordTeacherListByCondition =
                faceRecordTeacherService.getFaceRecordTeacherListByCondition(faceRecordTeacherConditionBo);
        }
        // userOid,FaceRecordTeacherVo
        Map<String, FaceRecordTeacherVo> faceRecordTeacherVoMap = faceRecordTeacherListByCondition.stream()
            .collect(Collectors.toMap(FaceRecordTeacherVo::getUserOid, a -> a, (k1, k2) -> k1));
        List<FaceRecordTeacherVo> faceRecordTeacherVos = teacherVoList.stream().map(teacherVo -> {
            FaceRecordTeacherVo faceRecordTeacherVo = new FaceRecordTeacherVo();
            faceRecordTeacherVo.setPhone(teacherVo.getAccountPhone());
            faceRecordTeacherVo.setRealName(teacherVo.getUserVo().getRealName());
            faceRecordTeacherVo.setUserOid(teacherVo.getUserOid());
            if (faceRecordTeacherVoMap.containsKey(teacherVo.getUserOid())) {
                FaceRecordTeacherVo faceRecordTeacherVoTemp = faceRecordTeacherVoMap.get(teacherVo.getUserOid());
                faceRecordTeacherVo.setFaceStatus(faceRecordTeacherVoTemp.getFaceStatus());
                faceRecordTeacherVo.setTipMessage(faceRecordTeacherVoTemp.getTipMessage());
                faceRecordTeacherVo.setFaceMediaName(faceRecordTeacherVoTemp.getFaceMediaName());
                faceRecordTeacherVo.setFaceMediaUrl(faceRecordTeacherVoTemp.getFaceMediaUrl());
                faceRecordTeacherVo.setFaceMediaNameOri(faceRecordTeacherVoTemp.getFaceMediaNameOri());
                faceRecordTeacherVo.setFaceMediaUrlCompress(faceRecordTeacherVoTemp.getFaceMediaUrlCompress());
                faceRecordTeacherVo.setFaceRecordTeacherId(faceRecordTeacherVoTemp.getFaceRecordTeacherId());
            }
            return faceRecordTeacherVo;
        }).collect(Collectors.toList());
        PageInfo<FaceRecordTeacherVo> pageInfo = new PageInfo<>();
        pageInfo.setList(faceRecordTeacherVos);
        pageInfo.setTotal(total);
        return AjaxResult.success(pageInfo);
    }

    @Override
    public AjaxResult upload(List<FaceRecordTeacherConditionBo> faceRecordTeacherConditionBos) {
        if (CollectionUtils.isEmpty(faceRecordTeacherConditionBos)) {
            return AjaxResult.success();
        }

        // 导入
        AjaxResult ajaxResult = faceRecordTeacherService.upload(faceRecordTeacherConditionBos);
        if (ajaxResult.isSuccess()) {
            Long organizationId = faceRecordTeacherConditionBos.get(0).getOrganizationId();
            // 推送给云屏
            applicationContext.publishEvent(PublishEvent
                .produceDevicePublishEvent(MessageWsType.MODIFY_FACE_TEACHER.getValue(), organizationId, null, null));
            return ajaxResult;
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult<Map<Integer, Integer>> count(FaceCountQueryBo faceCountQueryBo) {
        FaceRecordTeacherConditionBo faceRecordTeacherConditionBo = new FaceRecordTeacherConditionBo();
        faceRecordTeacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
        faceRecordTeacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        faceRecordTeacherConditionBo.setOrganizationId(faceCountQueryBo.getOrganizationId());
        faceRecordTeacherConditionBo.setGroupByUserOid("groupByUserOid");
        List<FaceRecordTeacherVo> faceRecordTeacherVos =
            faceRecordTeacherService.getFaceRecordTeacherListByCondition(faceRecordTeacherConditionBo);
        // faceStatus,数量。未上传人数需要前端减法
        Map<Integer, Integer> faceTeacherCountMap = faceRecordTeacherVos.stream().collect(
            Collectors.toMap(faceRecordTeacherVo -> faceRecordTeacherVo.getFaceStatus(), e -> 1, Integer::sum));
        // 放置总人数
        Long totalUserCount =
            baseDataService.getTeacherVoByOrgId(faceCountQueryBo.getOrganizationId()).stream().count();
        if (totalUserCount == null) {
            totalUserCount = ConstantsLong.NUM_0;
        }
        Integer uploadCount = faceTeacherCountMap.values().stream().reduce(0, Integer::sum);

        faceTeacherCountMap.put(FaceStatusType.NOT_UPLOAD.getValue(),
            totalUserCount.intValue() - uploadCount.intValue());
        faceTeacherCountMap.put(FaceStatusType.TOTAL.getValue(), totalUserCount.intValue());
        return AjaxResult.success(faceTeacherCountMap);
    }

    @Override
    public AjaxResult<List<String>> searchAliFace(FaceRecordTeacherConditionBo faceRecordTeacherConditionBo) {
        if (StringUtils.isBlank(faceRecordTeacherConditionBo.getImageUrl())) {
            return AjaxResult.fail("参数错误");
        }
        FaceConfigVo configVo =
            faceConfigService.getDetailByOrganizationId(faceRecordTeacherConditionBo.getOrganizationId());
        String threshold = ConstantsConfig.faceThreshold;
        if (configVo != null && StringUtils.isNotBlank(configVo.getThreshold())) {
            threshold = configVo.getThreshold();
        }
        List<String> userOids =
            faceBodyService.listFaceTransaction(faceRecordTeacherConditionBo.getImageUrl(), threshold);
        return AjaxResult.success(userOids);
    }
}
