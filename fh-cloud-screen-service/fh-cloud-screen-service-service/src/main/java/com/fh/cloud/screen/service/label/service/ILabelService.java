package com.fh.cloud.screen.service.label.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 标签表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
public interface ILabelService extends IService<LabelDto> {

    List<LabelVo> getLabelListByCondition(LabelConditionBo condition);

    AjaxResult addLabel(LabelBo labelBo);

    AjaxResult updateLabel(LabelBo labelBo);

    LabelVo getDetail(Long id);

    AjaxResult delete(Long labelId);

    /**
     * 初始化海报分组为标签
     *
     * @return void
     * <AUTHOR>
     * @date 2023/5/6 14:03
     */
    void initPosterGroupToLabel();

    /**
     * 获取节日节点组别及组别及下属顺序第一位的标签
     *
     * @return com.fh.cloud.screen.service.label.entity.vo.LabelVo
     * <AUTHOR>
     * @date 2023/5/12 16:53
     */
    LabelVo getPosterTypeLabel();

    /**
     * 获取主题关联的标签的组织
     *
     * @param libraryId 主题id
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023/5/17 18:45
     */
    Long getLabelOrganizationIdByLibraryId(Long libraryId);

}
