package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryLikesDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo;

/**
 * 共话诗词点赞记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
public interface ScreenPoetryLikesMapper extends BaseMapper<ScreenPoetryLikesDto> {

	List<ScreenPoetryLikesVo> getScreenPoetryLikesListByCondition(ScreenPoetryLikesConditionBo condition);

	ScreenPoetryLikesVo getScreenPoetryLikesByCondition(ScreenPoetryLikesConditionBo condition);

	Boolean addScreenPoetryLikesNum(ScreenPoetryLikesBo screenPoetryLikesBo);

}
