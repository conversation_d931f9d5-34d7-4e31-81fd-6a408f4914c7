package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneModuleRel;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo;

import java.util.List;

/**
 * 云屏场景模块关系表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
public interface IScreenSceneModuleRelService extends IService<ScreenSceneModuleRel> {

    List<ScreenSceneModuleRelVo> getScreenSceneModuleRelListByCondition(ScreenSceneModuleRelListConditionBo condition);

    boolean addScreenSceneModuleRel(ScreenSceneModuleRelBo screenSceneModuleRelBo);

    boolean updateScreenSceneModuleRel(ScreenSceneModuleRelBo screenSceneModuleRelBo);

    ScreenSceneModuleRelVo getDetail(Long screenSceneModuleRelId);

    /**
     * 根据screenSceneId先删除后批量插入场景和模块数据的关系
     *
     * @param screenSceneId 场景id
     * @param screenModuleDataIds 模块数据id集合
     * <AUTHOR>
     * @date 2022 -06-14 10:58:55
     */
    void deleteAndAddScreenSceneModuleRelBatch(Long screenSceneId, List<ScreenModuleDataBo> screenModuleDatas);

    /**
     * 根据screenSceneId删除信息
     *
     * @param screenSceneId 场景id
     * <AUTHOR>
     * @date 2022 -05-09 16:12:48
     */
    void deleteByScreenSceneId(Long screenSceneId);

    /**
     * 根据场景id集合查询场景模块信息
     *
     * @param screenSceneIds the screen scene ids
     * @return list
     * <AUTHOR>
     * @date 2022 -06-14 11:23:07
     */
    List<ScreenSceneModuleRelVo> listByScreenSceneIds(List<Long> screenSceneIds);
}
