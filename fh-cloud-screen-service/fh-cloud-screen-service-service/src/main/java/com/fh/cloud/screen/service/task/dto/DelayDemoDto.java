package com.fh.cloud.screen.service.task.dto;

import java.util.Date;

import lombok.Data;

/**
 * 课后延迟演示数据dto
 * 
 * <AUTHOR>
 * @date 2022/8/9 14:41
 */
@Data
public class DelayDemoDto {
    /**
     * 该延迟服务名称
     */
    private String name;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 学校id
     */
    private Long organizationId;

    /**
     * 生成一个对象
     *
     * @param name the name
     * @param startTime the start time
     * @param endTime the end time
     * @param organizationId the organization id
     * @return delay demo dto
     * <AUTHOR>
     * @date 2022 -08-09 14:46:14
     */
    public static DelayDemoDto generateDelayDemoDto(String name, Date startTime, Date endTime, Long organizationId) {
        DelayDemoDto delayDemoDto = new DelayDemoDto();
        delayDemoDto.setName(name);
        delayDemoDto.setStartTime(startTime);
        delayDemoDto.setEndTime(endTime);
        delayDemoDto.setOrganizationId(organizationId);
        return delayDemoDto;
    }
}
