package com.fh.cloud.screen.service.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceFullCustomDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo;

/**
 * 云屏全屏非全屏设置自定义Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
public interface ShowDeviceFullCustomMapper extends BaseMapper<ShowDeviceFullCustomDto> {

	List<ShowDeviceFullCustomVo> getShowDeviceFullCustomListByCondition(ShowDeviceFullCustomConditionBo condition);

	ShowDeviceFullCustomVo getShowDeviceFullCustomByCondition(ShowDeviceFullCustomConditionBo condition);

	/**
	 * 获取所有已有数据的组织
	 *
	 * @return exist organization ids
	 */
	List<Long> getExistOrganizationIds();

}
