package com.fh.cloud.screen.service.screen.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.enums.ScreenContentType;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailListConditionBo;
import com.fh.cp.codec.entity.dto.CodecProcessDto;
import com.fh.cp.codec.enums.CodecBizAppType;
import com.fh.cp.codec.enums.CodecBizType;
import com.fh.cp.codec.enums.CodecCmdType;
import com.fh.cp.codec.rabbitmq.constant.CodecRabbitConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.enums.ScreenContentStatusType;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContent;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenContentMapper;
import com.fh.cloud.screen.service.screen.service.IScreenContentDetailService;
import com.fh.cloud.screen.service.screen.service.IScreenContentService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.google.common.collect.Lists;
import com.light.core.enums.StatusEnum;

/**
 * 云屏内容表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Service
public class ScreenContentServiceImpl extends ServiceImpl<ScreenContentMapper, ScreenContent>
    implements IScreenContentService {

    @Resource
    private ScreenContentMapper screenContentMapper;
    @Lazy
    @Autowired
    private IScreenContentDetailService screenContentDetailService;
    @Lazy
    @Autowired
    private IScreenContentService screenContentService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Override
    public List<ScreenContentVo> getScreenContentListByCondition(ScreenContentListConditionBo condition) {
        return screenContentMapper.getScreenContentListByCondition(condition);
    }

    @Override
    public boolean addScreenContent(ScreenContentBo screenContentBo) {
        ScreenContent screenContent = new ScreenContent();
        BeanUtils.copyProperties(screenContentBo, screenContent);
        screenContent.setIsDelete(StatusEnum.NOTDELETE.getCode());
        boolean result = save(screenContent);
        screenContentBo.setScreenContentId(screenContent.getScreenContentId());
        return result;
    }

    @Override
    public boolean updateScreenContent(ScreenContentBo screenContentBo) {
        ScreenContent screenContent = new ScreenContent();
        BeanUtils.copyProperties(screenContentBo, screenContent);
        return updateById(screenContent);
    }

    @Override
    public ScreenContentVo getDetail(Long screenContentId) {
        if (screenContentId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenContent> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenContent::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenContent::getScreenContentId, screenContentId);
        ScreenContent screenContent = getOne(lqw);
        ScreenContentVo screenContentVo = new ScreenContentVo();
        BeanUtils.copyProperties(screenContent, screenContentVo);
        return screenContentVo;
    }

    @Override
    public List<ScreenContentVo> getScreenContentListByConditionWithDetail(ScreenContentListConditionBo condition) {
        List<ScreenContentVo> screenContentVos = screenContentMapper.getScreenContentListByCondition(condition);
        if (CollectionUtils.isEmpty(screenContentVos)) {
            return new ArrayList<>();
        }

        List<Long> screenContentIds = screenContentVos.stream()
            .map(screenContentVo -> screenContentVo.getScreenContentId()).collect(Collectors.toList());
        List<String> userOids = Lists.newArrayList();
        screenContentVos.forEach(screenContentVo -> {
            userOids.add(screenContentVo.getCreateBy());
            userOids.add(screenContentVo.getUpdateBy());
        });
        List<ScreenContentDetailVo> screenContentDetailVos =
            screenContentDetailService.listScreenContentDetailByScreenContentIds(screenContentIds);
        if (CollectionUtils.isEmpty(screenContentDetailVos)) {
            return screenContentVos;
        }

        Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOids);
        final Map<String, String> finalUserNameMap = userNameMap;
        Map<Long, List<ScreenContentDetailVo>> screenContentDetailVoMap = screenContentDetailVos.stream().collect(
            Collectors.groupingBy(ScreenContentDetailVo::getScreenContentId, LinkedHashMap::new, Collectors.toList()));
        screenContentVos.forEach(screenContentVo -> {
            screenContentVo
                .setScreenContentDetailVos(screenContentDetailVoMap.get(screenContentVo.getScreenContentId()));
            // 设置姓名
            String realName = finalUserNameMap.get(screenContentVo.getUpdateBy());
            if (StringUtils.isBlank(realName)) {
                realName = finalUserNameMap.get(screenContentVo.getCreateBy());
            }
            screenContentVo.setRealName(realName);
            // 重设screenContentStatus
            resetScreenContentStatus(screenContentVo);
        });
        return screenContentVos;
    }

    @Override
    public ScreenContentVo getDetailWithDetail(Long screenContentId) {
        ScreenContentVo screenContentVo = getDetail(screenContentId);
        if (screenContentVo == null) {
            return null;
        }

        List<ScreenContentDetailVo> screenContentDetailVos =
            screenContentDetailService.listScreenContentDetailByScreenContentIds(Lists.newArrayList(screenContentId));
        if (CollectionUtils.isEmpty(screenContentDetailVos)) {
            return screenContentVo;
        }
        screenContentVo.setScreenContentDetailVos(screenContentDetailVos);
        // 重设screenContentStatus
        resetScreenContentStatus(screenContentVo);
        return screenContentVo;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long saveOrUpdateWithDetail(ScreenContentBo screenContentBo) {
        Long screenContentId = screenContentBo.getScreenContentId();
        List<ScreenContentDetailBo> screenContentDetailBos = screenContentBo.getScreenContentDetailBos();

        // save和update逻辑分离，方便扩展
        if (screenContentId == null) {
            screenContentService.addScreenContent(screenContentBo);
            screenContentId = screenContentBo.getScreenContentId();
            final Long finalScreenContentId = screenContentId;
            screenContentDetailBos.stream()
                .forEach(screenContentDetailBo -> screenContentDetailBo.setScreenContentId(finalScreenContentId));
            screenContentDetailService.saveOrUpdateScreenContentDetailBatch(screenContentDetailBos);
        } else {
            screenContentService.updateScreenContent(screenContentBo);
            final Long screenContentIdFinal = screenContentId;
            screenContentDetailBos
                .forEach(screenContentDetailBo -> screenContentDetailBo.setScreenContentId(screenContentIdFinal));
            if (screenContentBo.isDeleteAndAddDetail()) {
                screenContentDetailService.deleteAndAddScreenContentDetailBatch(screenContentId,
                    screenContentDetailBos);
            } else {
                screenContentDetailService.saveOrUpdateScreenContentDetailBatch(screenContentDetailBos);
            }
        }
        return screenContentId;
    }

    @Override
    public Integer countFirstRecord(Integer scopeType, Long screenModuleDataId, Long campusId, Long classesId,
        Date updateTime) {
        if (scopeType == null || screenModuleDataId == null || updateTime == null) {
            return null;
        }
        return screenContentMapper.countFirstRecord(scopeType, screenModuleDataId, campusId, classesId, updateTime);
    }

    @Override
    public void hideLinkImage(List<ScreenContentVo> screenContentVos) {
        if (CollectionUtils.isEmpty(screenContentVos)) {
            return;
        }
        screenContentVos.stream()
            .filter(screenContentVo -> screenContentVo.getScreenContentType() != null
                && screenContentVo.getScreenContentType().equals(ScreenContentType.CONTENT_URL.getValue()))
            .forEach(screenContentVo -> screenContentVo.getScreenContentDetailVos()
                .forEach(screenContentDetailVo -> screenContentDetailVo.setScreenContentMediaUrl(null)));
    }

    @Override
    public boolean produceGenCoverMqMessage(Long screenContentId) {
        if (screenContentId == null) {
            return false;
        }
        ScreenContentDetailListConditionBo screenContentDetailListConditionBo =
            new ScreenContentDetailListConditionBo();
        screenContentDetailListConditionBo.setScreenContentId(screenContentId);
        screenContentDetailListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenContentDetailVo> screenContentDetailVos =
            screenContentDetailService.getScreenContentDetailListByCondition(screenContentDetailListConditionBo);
        if (CollectionUtils.isNotEmpty(screenContentDetailVos)) {
            for (ScreenContentDetailVo screenContentDetailVo : screenContentDetailVos) {
                if (StringUtils.isNotBlank(screenContentDetailVo.getScreenContentMediaUrlCover())) {
                    continue;
                }
                // 生产消息
                CodecProcessDto codecProcessDto = new CodecProcessDto();
                codecProcessDto.setId(screenContentDetailVo.getScreenContentDetailId());
                codecProcessDto.setCmdType(CodecCmdType.GEN_COVER.getValue());
                codecProcessDto.setBizAppType(CodecBizAppType.SCREEN.getValue());
                codecProcessDto.setBizType(CodecBizType.SCREEN_CONTENT_DETAIL.getValue());
                codecProcessDto.setFilePath(screenContentDetailVo.getScreenContentMediaUrl());
                codecProcessDto.setUuid(UUID.randomUUID().toString());
                rabbitTemplate.convertAndSend(CodecRabbitConstant.CODEC_PRODUCER_ADD_QUEUE, codecProcessDto);
            }
        }
        return true;
    }

    /**
     * 重设screenContentVo的重设screenContentStatus
     * 
     * @param screenContentVo
     */
    private void resetScreenContentStatus(ScreenContentVo screenContentVo) {
        if (screenContentVo.getEndTime() != null) {
            if (!DateKit.ifMoreThenDateFormat(screenContentVo.getEndTime(), "yyyy-MM-dd")
                && screenContentVo.getScreenContentStatus().equals(ScreenContentStatusType.PUBLISH.getValue())) {
                screenContentVo.setScreenContentStatus(ScreenContentStatusType.ON_SHOW.getValue());
            }
            // YP-405不展示已过期的状态，因为会和是否发布冲突。
            // if (DateKit.ifMoreThenDateFormat(screenContentVo.getEndTime(), "yyyy-MM-dd")) {
            // screenContentVo.setScreenContentStatus(ScreenContentStatusType.OUT_DATE.getValue());
            // }
        }
    }

}