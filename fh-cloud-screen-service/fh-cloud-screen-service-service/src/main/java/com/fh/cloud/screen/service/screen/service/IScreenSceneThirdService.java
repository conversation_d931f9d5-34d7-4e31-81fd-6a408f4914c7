package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneThirdDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.light.core.entity.AjaxResult;

import java.util.Date;
import java.util.List;

/**
 * 第三方对接云屏场景信息表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
public interface IScreenSceneThirdService extends IService<ScreenSceneThirdDto> {

    List<ScreenSceneThirdVo> getScreenSceneThirdListByCondition(ScreenSceneThirdConditionBo condition);

    AjaxResult addScreenSceneThird(ScreenSceneThirdBo screenSceneThirdBo);

    AjaxResult updateScreenSceneThird(ScreenSceneThirdBo screenSceneThirdBo);

    ScreenSceneThirdVo getDetail(Long id);

    /**
     * 根据主键和appCode软删除
     *
     * @param screenSceneThirdId the screen scene third id
     * @param appCode the app code
     * <AUTHOR>
     * @date 2023 -04-08 17:39:16
     */
    boolean deleteByIdAndAppCode(Long screenSceneThirdId, String appCode);

    /**
     * 根据地点、设备、时间查询第三方对接的场景数据
     *
     * @param spaceGroupUseType 地点组使用类型{@link com.fh.cloud.screen.service.enums.SpaceGroupUseType}，必填
     * @param spaceInfoId 地点id，必填
     * @param showDeviceId 设备id，必填
     * @param date 日期（通常是当天），必填
     * @return 场景数据
     * <AUTHOR>
     * @date 2023 -04-11 16:42:57
     */
    List<ScreenSceneThirdVo> getScreenSceneThirdVoListBySpaceAndDeviceAndDate(Integer spaceGroupUseType,
        Long spaceInfoId, Long showDeviceId, Date date);

}
