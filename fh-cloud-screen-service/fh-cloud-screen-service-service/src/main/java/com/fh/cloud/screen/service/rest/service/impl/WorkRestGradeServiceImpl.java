package com.fh.cloud.screen.service.rest.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGrade;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeVo;
import com.fh.cloud.screen.service.rest.mapper.WorkRestGradeMapper;
import com.fh.cloud.screen.service.rest.service.IWorkRestGradeService;
import com.light.core.enums.StatusEnum;

/**
 * 作息时间年级表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class WorkRestGradeServiceImpl extends ServiceImpl<WorkRestGradeMapper, WorkRestGrade>
    implements IWorkRestGradeService {

    @Resource
    private WorkRestGradeMapper workRestGradeMapper;

    @Lazy
    @Autowired
    private IWorkRestGradeService workRestGradeService;

    @Override
    public List<WorkRestGradeVo> getWorkRestGradeListByCondition(WorkRestGradeListConditionBo condition) {
        return workRestGradeMapper.getWorkRestGradeListByCondition(condition);
    }

    @Override
    public boolean addWorkRestGrade(WorkRestGradeBo workRestGradeBo) {
        WorkRestGrade workRestGrade = new WorkRestGrade();
        BeanUtils.copyProperties(workRestGradeBo, workRestGrade);
        workRestGrade.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(workRestGrade);
    }

    @Override
    public boolean updateWorkRestGrade(WorkRestGradeBo workRestGradeBo) {
        WorkRestGrade workRestGrade = new WorkRestGrade();
        BeanUtils.copyProperties(workRestGradeBo, workRestGrade);
        return updateById(workRestGrade);
    }

    @Override
    public WorkRestGradeVo getDetail(Long workRestGradeId) {
        if (workRestGradeId == null) {
            return null;
        }

        LambdaQueryWrapper<WorkRestGrade> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WorkRestGrade::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(WorkRestGrade::getWorkRestGradeId, workRestGradeId);
        WorkRestGrade workRestGrade = getOne(lqw);
        WorkRestGradeVo workRestGradeVo = new WorkRestGradeVo();
        BeanUtils.copyProperties(workRestGrade, workRestGradeVo);
        return workRestGradeVo;
    }

    @Override
    public List<WorkRestGradeBo> deleteAndSaveBatch(Long workRestId, List<WorkRestGradeBo> workRestGradeBoList) {
        if (CollectionUtils.isEmpty(workRestGradeBoList)) {
            return workRestGradeBoList;
        }
        if (workRestId != null) {
            workRestGradeService.deleteByWorkRestId(workRestId);
        }
        List<WorkRestGrade> workRestGrades = workRestGradeBoList.stream().map(workRestGradeBo -> {
            WorkRestGrade workRestGrade = new WorkRestGrade();
            BeanUtils.copyProperties(workRestGradeBo, workRestGrade);
            return workRestGrade;
        }).collect(Collectors.toList());
        saveBatch(workRestGrades);
        for (int i = 0; i < workRestGradeBoList.size(); i++) {
            workRestGradeBoList.get(i).setWorkRestGradeId(workRestGrades.get(i).getWorkRestGradeId());
        }
        return workRestGradeBoList;
    }

    @Override
    public void deleteByWorkRestId(Long workRestId) {
        if (workRestId == null) {
            return;
        }
        UpdateWrapper<WorkRestGrade> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("work_rest_id", workRestId);
        WorkRestGrade workRestGrade = new WorkRestGrade();
        workRestGrade.setIsDelete(StatusEnum.ISDELETE.getCode());
        workRestGradeMapper.update(workRestGrade, updateWrapper);
    }
}