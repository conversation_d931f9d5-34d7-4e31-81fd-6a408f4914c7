package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenPoetryPictureApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryPictureDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo;
import com.fh.cloud.screen.service.screen.service.IScreenPoetryPictureService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 共话诗词图片资源表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
@RestController
@Validated
public class ScreenPoetryPictureController implements ScreenPoetryPictureApi {
	
    @Autowired
    private IScreenPoetryPictureService screenPoetryPictureService;

    /**
     * 查询共话诗词图片资源表分页列表
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ScreenPoetryPictureVo>> getScreenPoetryPicturePageListByCondition(@RequestBody ScreenPoetryPictureConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ScreenPoetryPictureVo> pageInfo = new PageInfo<>(screenPoetryPictureService.getScreenPoetryPictureListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询共话诗词图片资源表列表
	 * <AUTHOR>
	 * @date 2023-06-26 16:32:25
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ScreenPoetryPictureVo>> getScreenPoetryPictureListByCondition(@RequestBody ScreenPoetryPictureConditionBo condition){
		List<ScreenPoetryPictureVo> list = screenPoetryPictureService.getScreenPoetryPictureListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增共话诗词图片资源表
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addScreenPoetryPicture(@Validated @RequestBody ScreenPoetryPictureBo screenPoetryPictureBo){
		return screenPoetryPictureService.addScreenPoetryPicture(screenPoetryPictureBo);
    }

    /**
	 * 修改共话诗词图片资源表
	 * @param screenPoetryPictureBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateScreenPoetryPicture(@Validated @RequestBody ScreenPoetryPictureBo screenPoetryPictureBo) {
		if(null == screenPoetryPictureBo.getScreenPoetryPictureId()) {
			return AjaxResult.fail("共话诗词图片资源表id不能为空");
		}
		return screenPoetryPictureService.updateScreenPoetryPicture(screenPoetryPictureBo);
	}

	/**
	 * 查询共话诗词图片资源表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ScreenPoetryPictureVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("共话诗词图片资源表id不能为空");
		}
		ScreenPoetryPictureConditionBo condition = new ScreenPoetryPictureConditionBo();
		condition.setScreenPoetryPictureId(id);
		ScreenPoetryPictureVo vo = screenPoetryPictureService.getScreenPoetryPictureByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除共话诗词图片资源表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-26 16:32:25
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ScreenPoetryPictureDto screenPoetryPictureDto = new ScreenPoetryPictureDto();
		screenPoetryPictureDto.setScreenPoetryPictureId(id);
		screenPoetryPictureDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(screenPoetryPictureService.updateById(screenPoetryPictureDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}



}
