package com.fh.cloud.screen.service.attendance.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("attendance_user")
public class AttendanceUser implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "attendance_user_id", type = IdType.AUTO)
    private Long attendanceUserId;

    /**
     * 该用户当天考勤记录状态：1正常，2异常（保留状态），3迟到，4早退，5迟到早退，6缺卡
     */
    @TableField("attendance_record_type")
    private Integer attendanceRecordType;

    /**
     * 考勤规则id，当天考勤使用的考勤规则
     */
    @TableField("attendance_rule_id")
    private Long attendanceRuleId;

    /**
     * 考勤类型：1教师考勤，2学生考勤
     */
    @TableField("attendance_type")
    private Integer attendanceType;

    /**
     * 考勤日期:yyyy-MM-dd
     */
    @TableField("attendance_date")
    private Date attendanceDate;

    /**
     * 考勤日 yyyy-MM-dd
     */
    private String attendanceDay;

    /**
     * 考勤月份 yyyy-MM
     */
    private String attendanceMonth;

    /**
     * 用户oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    @TableField(exist = false)
    private List<AttendanceUserDetail> attendanceUserDetailList;

}
