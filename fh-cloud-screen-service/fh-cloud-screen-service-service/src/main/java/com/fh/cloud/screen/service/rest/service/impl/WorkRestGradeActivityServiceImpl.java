package com.fh.cloud.screen.service.rest.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGradeActivity;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeActivityVo;
import com.fh.cloud.screen.service.rest.mapper.WorkRestGradeActivityMapper;
import com.fh.cloud.screen.service.rest.service.IWorkRestGradeActivityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 作息时间年级活动课设置表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class WorkRestGradeActivityServiceImpl extends ServiceImpl<WorkRestGradeActivityMapper, WorkRestGradeActivity>
    implements IWorkRestGradeActivityService {

    @Resource
    private WorkRestGradeActivityMapper workRestGradeActivityMapper;

    @Override
    public List<WorkRestGradeActivityVo>
        getWorkRestGradeActivityListByCondition(WorkRestGradeActivityListConditionBo condition) {
        return null;
    }

    @Override
    public boolean addWorkRestGradeActivity(WorkRestGradeActivityBo workRestGradeActivityBo) {
        return false;
    }

    @Override
    public boolean updateWorkRestGradeActivity(WorkRestGradeActivityBo workRestGradeActivityBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long workRestGradeActivityId) {
        return null;
    }

}