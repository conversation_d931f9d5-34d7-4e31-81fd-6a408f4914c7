package com.fh.cloud.screen.service.label.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;
import com.fh.cloud.screen.service.common.service.IScreenDictionaryDataService;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.AuditType;
import com.fh.cloud.screen.service.enums.LabelEnums;
import com.fh.cloud.screen.service.enums.PosterEnums;
import com.fh.cloud.screen.service.festival.entity.dto.FestivalDto;
import com.fh.cloud.screen.service.festival.service.IFestivalService;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.service.ILabelFestivalRelService;
import com.fh.cloud.screen.service.label.service.ILabelLibraryRelService;
import com.fh.cloud.screen.service.label.service.ILabelService;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.LabelLibraryAuditRelDto;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;
import com.fh.cloud.screen.service.screen.mapper.LabelLibraryAuditRelMapper;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Iterables;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import org.apache.poi.hssf.record.DVALRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.fh.cloud.screen.service.label.mapper.LabelMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 标签表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
@Service
public class LabelServiceImpl extends ServiceImpl<LabelMapper, LabelDto> implements ILabelService {

    @Resource
    private LabelMapper labelMapper;
    @Resource
    private IScreenDictionaryDataService dictionaryDataService;
    @Resource
    private IScreenModuleLibraryService screenModuleLibraryService;
    @Resource
    private ILabelLibraryRelService labelLibraryRelService;
    @Resource
    private ILabelFestivalRelService labelFestivalRelService;
    @Resource
    private IFestivalService festivalService;
    @Resource
    private LabelLibraryAuditRelMapper labelLibraryAuditRelMapper;

    @Override
    public List<LabelVo> getLabelListByCondition(LabelConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (null == condition.getOrganizationId()) {
            condition.setOrganizationId(ConstantsLong.NUM_0);
        }
        if (null == condition.getClassesId()) {
            condition.setClassesId(ConstantsLong.NUM_0);
        }
        FuzzyQueryUtil.transferMeanBean(condition);
        List<LabelVo> labelVos = labelMapper.getLabelListByCondition(condition);
        if (CollectionUtils.isEmpty(labelVos)) {
            return labelVos;
        }
        if (!StatusEnum.YES.getCode().equals(condition.getQueryLibraryId())) {
            return labelVos;
        }

        // 获取标签下题列表。并封装返回
        List<Long> labelIds = labelVos.stream().map(LabelVo::getLabelId).collect(Collectors.toList());
        LambdaQueryWrapper<LabelLibraryRelDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.in(LabelLibraryRelDto::getLabelId, labelIds);
        List<LabelLibraryRelDto> relDtoList = labelLibraryRelService.list(lqw);
        if (CollectionUtils.isEmpty(relDtoList)) {
            return labelVos;
        }
        Map<Long, List<LabelLibraryRelDto>> relMap =
            relDtoList.stream().collect(Collectors.groupingBy(LabelLibraryRelDto::getLabelId));
        labelVos.forEach(x -> {
            List<LabelLibraryRelDto> labelLibraryRelDtos = relMap.get(x.getLabelId());
            if (CollectionUtils.isNotEmpty(labelLibraryRelDtos)) {
                x.setLibraryIds(labelLibraryRelDtos.stream().map(y -> y.getScreenModuleLibraryId().toString())
                    .distinct().collect(Collectors.joining(",")));
            }
        });
        return labelVos;

    }

    /**
     * 1、查询db所有数据。2、判断名称是否重复。3、判断校本标签有没有标签组，没有则初始化一个，并且设置正确的父标签id。4、设置排序。5、设置级别分类。6、保存
     * 
     * @param labelBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addLabel(LabelBo labelBo) {
        if (StringUtil.isBlank(labelBo.getLabelName())
            || (null == labelBo.getOrganizationId() && null == labelBo.getClassesId())) {
            return AjaxResult.fail("参数错误");
        }

        // 判断名称是否重复
        LambdaQueryWrapper<LabelDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        if (labelBo.getOrganizationId() != null) {
            lqw.eq(LabelDto::getOrganizationId, labelBo.getOrganizationId());
        }
        if (labelBo.getClassesId() != null) {
            lqw.eq(LabelDto::getClassesId, labelBo.getClassesId());
        }
        List<LabelDto> allList = this.list(lqw);
        // 校本海报/班级海报新增标签，没有则新增一级标签
        if ((labelBo.getOrganizationId() != null && ConstantsLong.NUM_0 != labelBo.getOrganizationId())
            || (labelBo.getClassesId() != null && ConstantsLong.NUM_0 != labelBo.getClassesId())) {
            LabelDto labelGroupDto;
            if (CollectionUtils.isEmpty(allList)) {
                labelGroupDto = new LabelDto();
                labelGroupDto.setLabelName(ConstantsConfig.DEFAULT_LABEL_GROUP_NAME);
                labelGroupDto.setLabelSort(ConstantsInteger.NUM_0);
                if (labelBo.getOrganizationId() != null) {
                    labelGroupDto.setOrganizationId(labelBo.getOrganizationId());
                }
                if (labelBo.getClassesId() != null) {
                    labelGroupDto.setClassesId(labelBo.getClassesId());
                }
                this.save(labelGroupDto);
            } else {
                List<LabelDto> labelOneLevelList = allList.stream()
                    .filter(x -> x.getLevel().equals(LabelEnums.LEVEL_ONW.getCode())).collect(Collectors.toList());
                labelGroupDto = labelOneLevelList.get(0);
            }
            labelBo.setParentLabelId(labelGroupDto.getLabelId());
        }
        if (CollectionUtils.isNotEmpty(allList)) {
            List<String> dbLabelNames =
                allList.stream().filter(labelDto -> StringUtils.isNotBlank(labelDto.getLabelName()))
                    .map(LabelDto::getLabelName).distinct().collect(Collectors.toList());
            if (dbLabelNames.contains(labelBo.getLabelName())) {
                return AjaxResult.fail("名称重复：" + labelBo.getLabelName());
            }
        }

        // 排序，为了后面获取的sort准确
        if (CollectionUtils.isNotEmpty(allList)) {
            // 过滤同组别下，设置排序
            allList = allList.stream().filter(x -> x.getParentLabelId().equals(labelBo.getParentLabelId()))
                .sorted(Comparator.comparing(LabelDto::getLabelSort)).collect(Collectors.toList());
        }
        // 设置排序，取同级别数量+1 接口文档
        Integer labelSort = ConstantsInteger.NUM_1;
        if (CollectionUtils.isNotEmpty(allList)) {
            labelSort = Iterables.getLast(allList, null).getLabelSort() + 1;
        }
        labelBo.setLabelSort(labelSort);

        // 设置级别，类型
        labelBo.setLevel(ConstantsInteger.NUM_1);
        if (null != labelBo.getParentLabelId() && !labelBo.getParentLabelId().equals(ConstantsLong.NUM_0)) {
            LabelDto parentLabel = this.getById(labelBo.getParentLabelId());
            if (labelBo.getType() == null) {
                labelBo.setType(parentLabel.getType());
            }
            labelBo.setLevel(parentLabel.getLevel() + 1);
        }

        // 保存标签
        LabelDto label = new LabelDto();
        BeanUtils.copyProperties(labelBo, label);
        label.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(label)) {
            return AjaxResult.success(label);
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateLabel(LabelBo labelBo) {
        LabelDto byId = this.getById(labelBo.getLabelId());
        if (StatusEnum.ISDELETE.getCode().equals(labelBo.getIsDelete())
            || byId.getLabelName().equals(labelBo.getLabelName())) {
            labelBo.setLabelName(null);
        } else {
            // 判断名称是否重复
            LambdaQueryWrapper<LabelDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(LabelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            if (labelBo.getOrganizationId() != null) {
                lqw.eq(LabelDto::getOrganizationId, labelBo.getOrganizationId());
            }
            if (labelBo.getClassesId() != null) {
                lqw.eq(LabelDto::getClassesId, labelBo.getClassesId());
            }
            List<LabelDto> allList = this.list(lqw);
            if (CollectionUtils.isNotEmpty(allList)) {
                for (LabelDto labelDto : allList) {
                    if (labelDto.getLabelName().equals(labelBo.getLabelName())) {
                        return AjaxResult.fail("名称重复");
                    }
                }
            }
        }
        LabelDto label = new LabelDto();
        BeanUtils.copyProperties(labelBo, label);
        if (updateById(label)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public LabelVo getDetail(Long labelId) {
        LabelConditionBo condition = new LabelConditionBo();
        condition.setLabelId(labelId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<LabelVo> list = labelMapper.getLabelListByCondition(condition);
        LabelVo vo = new LabelVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult delete(Long labelId) {
        LabelDto labelVo = getById(labelId);
        if (null == labelVo) {
            return AjaxResult.fail("标签不存在");
        }
        // 校本海报库/班级海报库，删除标签，校验标签下是否有主题及图片，若有，无法删除【同步删除标签下所有主题及图片】
        if (ConstantsLong.NUM_0 != labelVo.getOrganizationId() || ConstantsLong.NUM_0 != labelVo.getClassesId()) {
            // 已发布的数据
            LambdaQueryWrapper<LabelLibraryRelDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            lqw.eq(LabelLibraryRelDto::getLabelId, labelVo.getLabelId());
            List<LabelLibraryRelDto> labelLibraryRelDtos = labelLibraryRelService.list(lqw);
            // 审核通过的数据
            LabelLibraryAuditRelConditionBo conditionBo = new LabelLibraryAuditRelConditionBo();
            conditionBo.setLabelId(labelVo.getLabelId());
            conditionBo.setAuditType(AuditType.AUDIT_PASS.getValue());
            List<LabelLibraryAuditRelVo> labelLibraryAuditRelVos =
                labelLibraryAuditRelMapper.getLabelLibraryAuditRelListWithLibraryAudit(conditionBo);

            if (CollectionUtils.isNotEmpty(labelLibraryRelDtos)
                || CollectionUtils.isNotEmpty(labelLibraryAuditRelVos)) {
                // List<Long> libraryIds =
                // labelLibraryRelDtos.stream().map(LabelLibraryRelDto::getScreenModuleLibraryId)
                // .collect(Collectors.toList());
                // screenModuleLibraryService.deleteScreenModuleLibraryAndLibraryMediaByLibraryIds(libraryIds);
                return AjaxResult.fail("已有主题海报，无法删除");
            }
        }
        // 删除判断是否最后一个标签组，否则不允许删除 (删除包括子级标签（两层海报标签）)
        if (ConstantsLong.NUM_0 == labelVo.getParentLabelId()) {
            LambdaQueryWrapper<LabelDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(LabelDto::getParentLabelId, ConstantsLong.NUM_0);
            lqw.eq(LabelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            lqw.eq(LabelDto::getOrganizationId, ConstantsLong.NUM_0);
            lqw.eq(LabelDto::getType, LabelEnums.POSTER.getCode());
            lqw.eq(LabelDto::getPosterType, LabelEnums.POSTER_DEFAULT.getCode());
            int count = this.count(lqw);
            if (count == 1) {
                return AjaxResult.fail("最后一个标签组不允许删除");
            }
            LambdaUpdateWrapper<LabelDto> luw = new LambdaUpdateWrapper<>();
            luw.eq(LabelDto::getLabelId, labelId).or().eq(LabelDto::getParentLabelId, labelId);
            luw.set(LabelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
            this.update(luw);
        } else {
            LabelBo labelBo = new LabelBo();
            labelBo.setLabelId(labelId);
            labelBo.setIsDelete(StatusEnum.ISDELETE.getCode());
            this.updateLabel(labelBo);
        }
        return AjaxResult.success("删除成功");
    }

    /**
     * 初始化海报分组为标签及其绑定关系。节日标签一对一关系
     *
     * @return void
     * <AUTHOR>
     * @date 2023/5/6 14:03
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initPosterGroupToLabel() {
        // 1、海报分组
        DictionaryDataListConditionBo condition = new DictionaryDataListConditionBo();
        condition.setType(PosterEnums.TYPE_SHOW.getCode());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // condition.setOrganizationId(ConstantsLong.NUM_0);
        List<DictionaryDataVo> dictionaryDataList = dictionaryDataService.getDictionaryDataListByCondition(condition);
        if (CollectionUtils.isEmpty(dictionaryDataList)) {
            return;
        }
        // (organizationId,海报分类列表)
        Map<Long, List<DictionaryDataVo>> dictionaryDataMap =
            dictionaryDataList.stream().collect(Collectors.groupingBy(DictionaryDataVo::getOrganizationId));

        // 2、海报主题
        List<Long> groupTypeIdList =
            dictionaryDataList.stream().map(x -> Long.valueOf(x.getDictValue())).collect(Collectors.toList());
        LambdaQueryWrapper<ScreenModuleLibrary> lqw = new LambdaQueryWrapper<ScreenModuleLibrary>()
            .eq(ScreenModuleLibrary::getIsDelete, StatusEnum.NOTDELETE.getCode())
            .in(ScreenModuleLibrary::getModuleGroupType, groupTypeIdList);
        // 查询有类型的所有海报主题
        List<ScreenModuleLibrary> screenModuleLibraryList = screenModuleLibraryService.list(lqw);
        // (海报分类,海报主题列表)
        Map<Long, List<ScreenModuleLibrary>> libraryMap =
            screenModuleLibraryList.stream().collect(Collectors.groupingBy(ScreenModuleLibrary::getModuleGroupType));

        // 3、已存在海报标签
        LambdaQueryWrapper<LabelDto> labelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        labelLambdaQueryWrapper.eq(LabelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        labelLambdaQueryWrapper.eq(LabelDto::getType, LabelEnums.POSTER.getCode());
        labelLambdaQueryWrapper.eq(LabelDto::getPosterType, LabelEnums.POSTER_DEFAULT.getCode());
        List<LabelDto> labelDtoList = labelMapper.selectList(labelLambdaQueryWrapper);
        // (organizationId,标签组列表)
        Map<Long, List<LabelDto>> oneLevelLabelMap = new HashMap<>();
        // (labelId,海报主题列表)
        Map<Long, List<LabelLibraryRelDto>> labelLibraryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(labelDtoList)) {
            oneLevelLabelMap = labelDtoList.stream().filter(x -> x.getParentLabelId().equals(ConstantsLong.NUM_0))
                .collect(Collectors.groupingBy(LabelDto::getOrganizationId));
            List<Long> labelIds = labelDtoList.stream().map(LabelDto::getLabelId).collect(Collectors.toList());

            // 标签海报关联关系
            LambdaQueryWrapper<LabelLibraryRelDto> labelLibraryRelDtoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            labelLibraryRelDtoLambdaQueryWrapper.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            labelLibraryRelDtoLambdaQueryWrapper.in(LabelLibraryRelDto::getLabelId, labelIds);
            List<LabelLibraryRelDto> labelLibraryRelDtos =
                labelLibraryRelService.list(labelLibraryRelDtoLambdaQueryWrapper);
            labelLibraryMap =
                labelLibraryRelDtos.stream().collect(Collectors.groupingBy(LabelLibraryRelDto::getLabelId));
        }

        // 4、新增标签
        List<LabelLibraryRelDto> labelLibraryRelDtos = new ArrayList<>();
        // 所有有分类的学校（也包含0的）
        Set<Long> keySet = dictionaryDataMap.keySet();
        for (Long organizationIdKey : keySet) {
            // 如果有分类这个标签组则继续使用已有的分类标签组，否则创建一个分类标签组（顺序可能有问题）
            // 找出“分类”标签组
            LabelDto labelGroupDto = null;
            List<LabelDto> labelList = oneLevelLabelMap.get(organizationIdKey);
            if (CollectionUtils.isNotEmpty(labelList)) {
                for (LabelDto labelDto : labelList) {
                    if (ConstantsConfig.DEFAULT_LABEL_GROUP_NAME.equals(labelDto.getLabelName())) {
                        labelGroupDto = labelDto;
                        break;
                    }
                }
            }
            if (null == labelGroupDto) {
                labelGroupDto = new LabelDto();
                labelGroupDto.setLabelName(ConstantsConfig.DEFAULT_LABEL_GROUP_NAME);
                labelGroupDto.setLabelSort(ConstantsInteger.NUM_1);
                labelGroupDto.setOrganizationId(organizationIdKey);
                this.save(labelGroupDto);
            }

            // 新增二级标签
            AtomicInteger labelSort = new AtomicInteger(-1);
            dictionaryDataList = dictionaryDataMap.get(organizationIdKey);

            // 标签组下已存在的二级标签
            LabelDto finalLabelGroupDto = labelGroupDto;
            List<LabelDto> twoLevelLabels =
                labelDtoList.stream().filter(x -> finalLabelGroupDto.getLabelId().equals(x.getParentLabelId()))
                    .sorted(Comparator.comparing(LabelDto::getLabelSort)).collect(Collectors.toList());
            Map<String, List<LabelDto>> twoLevelLabelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(twoLevelLabels)) {
                twoLevelLabelMap = twoLevelLabels.stream().collect(Collectors.groupingBy(LabelDto::getLabelName));
                labelSort.addAndGet(twoLevelLabels.get(twoLevelLabels.size() - 1).getLabelSort() + 1);
            }
            // 循环新增逻辑
            Map<String, List<LabelDto>> finalTwoLevelLabelMap = twoLevelLabelMap;
            Map<Long, List<LabelLibraryRelDto>> finalLabelLibraryMap = labelLibraryMap;
            dictionaryDataList.forEach(x -> {
                // 分类标签
                if (null == finalTwoLevelLabelMap.get(x.getDictLabel())) {
                    LabelDto labelDto = new LabelDto();
                    labelDto.setParentLabelId(finalLabelGroupDto.getLabelId());
                    labelDto.setLevel(LabelEnums.LEVEL_TWO.getCode());
                    labelDto.setLabelSort(labelSort.addAndGet(1));
                    labelDto.setLabelName(x.getDictLabel());
                    labelDto.setOrganizationId(organizationIdKey);
                    this.save(labelDto);
                    // 标签海报关联
                    List<ScreenModuleLibrary> screenModuleLibrarys = libraryMap.get(Long.valueOf(x.getDictValue()));
                    if (CollectionUtils.isNotEmpty(screenModuleLibrarys)) {
                        screenModuleLibrarys.forEach(y -> {
                            LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
                            labelLibraryRelDto.setLabelId(labelDto.getLabelId());
                            labelLibraryRelDto.setScreenModuleLibraryId(y.getScreenModuleLibraryId());
                            labelLibraryRelDtos.add(labelLibraryRelDto);
                        });
                    }
                } else {
                    // 分类存在，继续判断分类下属主题，是否与分类对应的标签想绑定
                    LabelDto labelDto = finalTwoLevelLabelMap.get(x.getDictLabel()).get(0);
                    // 分类下所有主题
                    List<ScreenModuleLibrary> screenModuleLibraryDtoList =
                        libraryMap.get(Long.valueOf(x.getDictValue()));
                    // 标签已绑定所有主题
                    List<LabelLibraryRelDto> labelLibraryRelDtoList = finalLabelLibraryMap.get(labelDto.getLabelId());
                    // 已有主题不为空
                    if (CollectionUtils.isNotEmpty(screenModuleLibraryDtoList)) {
                        // 已有标签下的关系为空，全部新增关系
                        if (CollectionUtils.isEmpty(labelLibraryRelDtoList)) {
                            screenModuleLibraryDtoList.forEach(y -> {
                                LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
                                labelLibraryRelDto.setLabelId(labelDto.getLabelId());
                                labelLibraryRelDto.setScreenModuleLibraryId(y.getScreenModuleLibraryId());
                                labelLibraryRelDtos.add(labelLibraryRelDto);
                            });
                        } else {
                            // 标签主题关系不为空，过滤出未有关系的主题，新增关系
                            Map<Long, List<LabelLibraryRelDto>> map = labelLibraryRelDtoList.stream()
                                .collect(Collectors.groupingBy(LabelLibraryRelDto::getScreenModuleLibraryId));
                            screenModuleLibraryDtoList.forEach(y -> {
                                if (map.get(y.getScreenModuleLibraryId()) == null) {
                                    LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
                                    labelLibraryRelDto.setLabelId(labelDto.getLabelId());
                                    labelLibraryRelDto.setScreenModuleLibraryId(y.getScreenModuleLibraryId());
                                    labelLibraryRelDtos.add(labelLibraryRelDto);
                                }
                            });
                        }
                    }

                }

            });
        }
        if (CollectionUtils.isNotEmpty(labelLibraryRelDtos)) {
            labelLibraryRelService.saveBatch(labelLibraryRelDtos);
        }

        /// 5、已存在节日与标签
        LambdaQueryWrapper<LabelFestivalRelDto> festivalRelDtoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        festivalRelDtoLambdaQueryWrapper.eq(LabelFestivalRelDto::getIsDelete, StatusEnum.NOTDELETE);
        List<LabelFestivalRelDto> labelFestivalRelDtos = labelFestivalRelService.list(festivalRelDtoLambdaQueryWrapper);
        Map<String, List<LabelFestivalRelDto>> labelFestivalRelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(labelFestivalRelDtos)) {
            labelFestivalRelMap =
                labelFestivalRelDtos.stream().collect(Collectors.groupingBy(LabelFestivalRelDto::getFestivalCode));
        }

        // 6、已存在节日
        LambdaQueryWrapper<FestivalDto> festivalDtoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        festivalDtoLambdaQueryWrapper.eq(FestivalDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        festivalDtoLambdaQueryWrapper.orderByAsc(FestivalDto::getStartDay);
        List<FestivalDto> list = festivalService.list(festivalDtoLambdaQueryWrapper);

        /// 7、获取现有节日标签一级分组
        LabelVo festivalTypeLabel = getPosterTypeLabel();
        if (null == festivalTypeLabel) {
            LabelDto labelGroupDto = new LabelDto();
            labelGroupDto.setLabelName("节日节气");
            labelGroupDto.setLabelSort(ConstantsInteger.NUM_0);
            labelGroupDto.setPosterType(LabelEnums.POSTER_FESTIVAL.getCode());
            this.save(labelGroupDto);
            festivalTypeLabel = new LabelVo();
            BeanUtils.copyProperties(labelGroupDto, festivalTypeLabel);
        }

        // 8、循环现有节日，新增标签
        AtomicInteger labelSort = new AtomicInteger();
        List<LabelFestivalRelDto> addLabelFestivalRelDtos = new ArrayList<>();
        for (FestivalDto festivalDto : list) {
            // 不存在该节日标签关系，新增。
            if (null == labelFestivalRelMap.get(festivalDto.getFestivalCode())) {
                LabelDto labelDto = new LabelDto();
                labelDto.setParentLabelId(festivalTypeLabel.getLabelId());
                labelDto.setLevel(LabelEnums.LEVEL_TWO.getCode());
                labelDto.setLabelSort(labelSort.addAndGet(-1));
                labelDto.setLabelName(festivalDto.getName());
                labelDto.setPosterType(LabelEnums.POSTER_FESTIVAL.getCode());
                this.save(labelDto);
                LabelFestivalRelDto labelFestivalRelDto = new LabelFestivalRelDto();
                labelFestivalRelDto.setFestivalCode(festivalDto.getFestivalCode());
                labelFestivalRelDto.setLabelId(labelDto.getLabelId());
                addLabelFestivalRelDtos.add(labelFestivalRelDto);
            }
        }
        if (CollectionUtils.isNotEmpty(addLabelFestivalRelDtos)) {
            labelFestivalRelService.saveBatch(addLabelFestivalRelDtos);
        }
    }

    /**
     * 获取节日节点组别
     *
     * @return com.fh.cloud.screen.service.label.entity.vo.LabelVo
     * <AUTHOR>
     * @date 2023/5/12 16:53
     */
    @Override
    public LabelVo getPosterTypeLabel() {
        LabelVo labelVo;
        LabelConditionBo conditionBo = new LabelConditionBo();
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setPosterType(LabelEnums.POSTER_FESTIVAL.getCode());
        conditionBo.setLevel(LabelEnums.LEVEL_ONW.getCode());
        List<LabelVo> labelVoList = labelMapper.getLabelListByCondition(conditionBo);
        if (CollectionUtils.isEmpty(labelVoList)) {
            return null;
        }
        labelVo = labelVoList.get(ConstantsInteger.NUM_0);

        conditionBo.setParentLabelId(labelVo.getLabelId());
        PageHelper.startPage(ConstantsInteger.NUM_1, ConstantsInteger.NUM_1, "label_sort");
        conditionBo.setLevel(LabelEnums.LEVEL_TWO.getCode());
        PageInfo<LabelVo> pageInfo = new PageInfo<>(labelMapper.getLabelListByCondition(conditionBo));
        if (CollectionUtils.isNotEmpty(pageInfo.getList())) {
            List<LabelVo> childrenLabels = new ArrayList<>();
            childrenLabels.add(pageInfo.getList().get(ConstantsInteger.NUM_0));
            labelVo.setChildrenLabels(childrenLabels);
        }
        return labelVo;
    }

    /**
     * 获取主题关联的标签的组织
     *
     * @param libraryId 主题id
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023/5/17 18:45
     */
    @Override
    public Long getLabelOrganizationIdByLibraryId(Long libraryId) {
        LambdaQueryWrapper<LabelLibraryRelDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(LabelLibraryRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.in(LabelLibraryRelDto::getScreenModuleLibraryId, libraryId);
        List<LabelLibraryRelDto> list = labelLibraryRelService.list(lqw);
        if (CollectionUtils.isEmpty(list)) {
            return ConstantsLong.NUM_0;
        }
        LabelDto byId = this.getById(list.get(0).getLabelId());
        if (null == byId) {
            return ConstantsLong.NUM_0;
        }
        return byId.getOrganizationId();
    }

}