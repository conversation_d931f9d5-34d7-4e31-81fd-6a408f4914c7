package com.fh.cloud.screen.service.device.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.device.api.ShowDeviceFullCustomApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceFullCustomDto;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceFullCustomService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 云屏全屏非全屏设置自定义
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
@RestController
@Validated
public class ShowDeviceFullCustomController implements ShowDeviceFullCustomApi {

    @Autowired
    private IShowDeviceFullCustomService showDeviceFullCustomService;

    /**
     * 查询云屏全屏非全屏设置自定义分页列表
     * 
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @Override
    public AjaxResult<PageInfo<ShowDeviceFullCustomVo>>
        getShowDeviceFullCustomPageListByCondition(@RequestBody ShowDeviceFullCustomConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ShowDeviceFullCustomVo> pageInfo =
            new PageInfo<>(showDeviceFullCustomService.getShowDeviceFullCustomListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询云屏全屏非全屏设置自定义列表
     * 
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @Override
    public AjaxResult<List<ShowDeviceFullCustomVo>>
        getShowDeviceFullCustomListByCondition(@RequestBody ShowDeviceFullCustomConditionBo condition) {
        List<ShowDeviceFullCustomVo> list =
            showDeviceFullCustomService.getShowDeviceFullCustomListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增云屏全屏非全屏设置自定义
     * 
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @Override
    public AjaxResult addShowDeviceFullCustom(@Validated @RequestBody ShowDeviceFullCustomBo showDeviceFullCustomBo) {
        return showDeviceFullCustomService.addShowDeviceFullCustom(showDeviceFullCustomBo);
    }

    /**
     * 修改云屏全屏非全屏设置自定义
     * 
     * @param showDeviceFullCustomBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @Override
    public AjaxResult
        updateShowDeviceFullCustom(@Validated @RequestBody ShowDeviceFullCustomBo showDeviceFullCustomBo) {
        if (null == showDeviceFullCustomBo.getFullCustomId()) {
            return AjaxResult.fail("云屏全屏非全屏设置自定义id不能为空");
        }
        return showDeviceFullCustomService.updateShowDeviceFullCustom(showDeviceFullCustomBo);
    }

    /**
     * 查询云屏全屏非全屏设置自定义详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @Override
    public AjaxResult<ShowDeviceFullCustomVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("云屏全屏非全屏设置自定义id不能为空");
        }
        ShowDeviceFullCustomConditionBo condition = new ShowDeviceFullCustomConditionBo();
        condition.setFullCustomId(id);
        ShowDeviceFullCustomVo vo = showDeviceFullCustomService.getShowDeviceFullCustomByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除云屏全屏非全屏设置自定义
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-06-12 16:12:06
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ShowDeviceFullCustomDto showDeviceFullCustomDto = new ShowDeviceFullCustomDto();
        showDeviceFullCustomDto.setFullCustomId(id);
        showDeviceFullCustomDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (showDeviceFullCustomService.updateById(showDeviceFullCustomDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
