package com.fh.cloud.screen.service.screen.controller;

import com.fh.app.role.service.role.enums.RoleIdentifierType;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.config.WxMpProperties;
import com.fh.cloud.screen.service.enums.AuditType;
import com.fh.cloud.screen.service.role.servise.UserRoleService;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryAuditApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryAuditDto;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.wx.service.WxMpMsgService;
import com.fh.sso.service.user.api.UserAuthorizationApi;
import com.fh.sso.service.user.entity.bo.UserAuthorizationConditionBo;
import com.fh.sso.service.user.entity.vo.UserAuthorizationVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.user.entity.vo.LoginUserVo;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryAuditService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模块库审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
@Slf4j
@RestController
@Validated
public class ScreenModuleLibraryAuditController implements ScreenModuleLibraryAuditApi {

    @Autowired
    private IScreenModuleLibraryAuditService screenModuleLibraryAuditService;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private BaseDataService baseDataService;
    /**
     * 用于推送消息的公众号appId和appSecret
     */
    @Resource
    private WxMpProperties wxMpProperties;
    @Resource
    private WxMpMsgService wxMpMsgService;
    @Resource
    private UserAuthorizationApi userAuthorizationApi;

    @Value("${wxMpSubMsgAuditPage:}")
    private String wxMpSubMsgAuditPage;
    @Value("${wxMpSubMsgAuditTemplateId:}")
    private String wxMpSubMsgAuditTemplateId;

    @Value("${wxMpSubMsgAuditResultPage:}")
    private String wxMpSubMsgAuditResultPage;
    @Value("${wxMpSubMsgAuditResultTemplateId:}")
    private String wxMpSubMsgAuditResultTemplateId;

    /**
     * 查询模块库审核表分页列表
     * 
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<PageInfo<ScreenModuleLibraryAuditVo>>
        getScreenModuleLibraryAuditPageListByCondition(@RequestBody ScreenModuleLibraryAuditConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenModuleLibraryAuditVo> pageInfo =
            new PageInfo<>(screenModuleLibraryAuditService.getScreenModuleLibraryAuditListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询模块库审核表列表
     * 
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<List<ScreenModuleLibraryAuditVo>>
        getScreenModuleLibraryAuditListByCondition(@RequestBody ScreenModuleLibraryAuditConditionBo condition) {
        List<ScreenModuleLibraryAuditVo> list =
            screenModuleLibraryAuditService.getScreenModuleLibraryAuditListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增模块库审核表
     * 
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult
        addScreenModuleLibraryAudit(@Validated @RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        return screenModuleLibraryAuditService.addScreenModuleLibraryAudit(screenModuleLibraryAuditBo);
    }

    /**
     * 修改模块库审核表
     * 
     * @param screenModuleLibraryAuditBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult
        updateScreenModuleLibraryAudit(@Validated @RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        if (null == screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId()) {
            return AjaxResult.fail("模块库审核表id不能为空");
        }
        return screenModuleLibraryAuditService.updateScreenModuleLibraryAudit(screenModuleLibraryAuditBo);
    }

    /**
     * 查询模块库审核表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<ScreenModuleLibraryAuditVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("模块库审核表id不能为空");
        }
        ScreenModuleLibraryAuditConditionBo condition = new ScreenModuleLibraryAuditConditionBo();
        condition.setScreenModuleLibraryAuditId(id);
        ScreenModuleLibraryAuditVo vo =
            screenModuleLibraryAuditService.getScreenModuleLibraryAuditByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除模块库审核表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:39
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenModuleLibraryAuditDto screenModuleLibraryAuditDto = new ScreenModuleLibraryAuditDto();
        screenModuleLibraryAuditDto.setScreenModuleLibraryAuditId(id);
        screenModuleLibraryAuditDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenModuleLibraryAuditService.updateById(screenModuleLibraryAuditDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 获取审核海报资源列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/6 14:37
     **/
    @Override
    public AjaxResult getPosterAuditPage(@RequestBody ScreenModuleLibraryAuditConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenModuleLibraryAuditVo> pageInfo =
            new PageInfo<>(screenModuleLibraryAuditService.getPosterAuditList(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 新增海报审核数据
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:19
     **/
    @Override
    public AjaxResult addPosterAudit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        AjaxResult ajaxResult = screenModuleLibraryAuditService.addPosterAudit(screenModuleLibraryAuditBo);

        try {
            if (screenModuleLibraryAuditBo.getAuditType() != null
                && screenModuleLibraryAuditBo.getAuditType().equals(AuditType.TO_AUDIT.getValue())) {
                sendWxMsgOfAuditTo(screenModuleLibraryAuditBo);
            }
        } catch (Exception e) {
            log.error("addPosterAudit sendWxMsgOfAudit error:", e);
        }
        return ajaxResult;
    }

    /**
     * 编辑海报审核数据（编辑后重新提交审核或仅保存）
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:21
     **/
    @Override
    public AjaxResult updatePosterAudit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        AjaxResult ajaxResult = screenModuleLibraryAuditService.updatePosterAudit(screenModuleLibraryAuditBo);

        try {
            if (screenModuleLibraryAuditBo.getAuditType() != null
                && screenModuleLibraryAuditBo.getAuditType().equals(AuditType.TO_AUDIT.getValue())) {
                sendWxMsgOfAuditTo(screenModuleLibraryAuditBo);
            }
        } catch (Exception e) {
            log.error("addPosterAudit sendWxMsgOfAudit error:", e);
        }
        return ajaxResult;
    }

    /**
     * 海报资源审核
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 17:43
     **/
    @Override
    public AjaxResult audit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        if (screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId() == null) {
            return AjaxResult.fail("请选择数据");
        }
        AjaxResult auditAjaxResult = screenModuleLibraryAuditService.audit(screenModuleLibraryAuditBo);
        if (auditAjaxResult.isSuccess()) {
            try {
                sendWxMsgOfAuditPassOrReject(
                    Lists.newArrayList(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId()));
            } catch (Exception e) {
                log.error("audit sendWxMsgOfAuditPassOrReject error:", e);
            }
        }
        return auditAjaxResult;
    }

    /**
     * 海报资源批量审核
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 17:44
     **/
    @Override
    public AjaxResult auditBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        if (CollectionUtils.isEmpty(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds())) {
            return AjaxResult.fail("请选择数据");
        }
        List<Long> screenModuleLibraryAuditIds = Lists.newArrayList();
        for (Long screenModuleLibraryAuditId : screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds()) {
            ScreenModuleLibraryAuditBo bo = new ScreenModuleLibraryAuditBo();
            bo.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
            bo.setAuditType(screenModuleLibraryAuditBo.getAuditType());
            bo.setAuditUser(screenModuleLibraryAuditBo.getAuditUser());
            bo.setReason(screenModuleLibraryAuditBo.getReason());
            AjaxResult auditAjaxResult = screenModuleLibraryAuditService.audit(bo);
            screenModuleLibraryAuditIds.add(screenModuleLibraryAuditId);
        }
        try {
            sendWxMsgOfAuditPassOrReject(screenModuleLibraryAuditIds);
        } catch (Exception e) {
            log.error("auditBatch sendWxMsgOfAuditPassOrReject error:", e);
        }
        return AjaxResult.success("审核成功");
    }

    /**
     * 海报资源审核详情
     *
     * @param screenModuleLibraryAuditId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/11 16:44
     **/
    @Override
    public AjaxResult
        getPosterAuditDetail(@RequestParam("screenModuleLibraryAuditId") Long screenModuleLibraryAuditId) {
        if (screenModuleLibraryAuditId == null) {
            return AjaxResult.fail("请选择数据");
        }
        return screenModuleLibraryAuditService.getPosterAuditDetail(screenModuleLibraryAuditId);
    }

    /**
     * 海报资源发布、取消发布
     *
     * @param screenModuleLibraryAuditBo，需要传参：screenModuleLibraryAuditId、releaseType
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/12 9:46
     **/
    @Override
    public AjaxResult releasePoster(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        if (screenModuleLibraryAuditBo.getScreenModuleLibraryAuditId() == null) {
            return AjaxResult.fail("请选择数据");
        }
        return screenModuleLibraryAuditService.releasePoster(screenModuleLibraryAuditBo);
    }

    /**
     * 批量更新海报（审核）主题标签
     *
     * @param screenModuleLibraryAuditBo the screen module library audit bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -05-21 16:12:06
     */
    @Override
    public AjaxResult updatePosterAuditLabel(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        AjaxResult ajaxResult = screenModuleLibraryAuditService.updatePosterAuditLabel(screenModuleLibraryAuditBo);

        try {
            if (screenModuleLibraryAuditBo.getAuditType() != null
                && screenModuleLibraryAuditBo.getAuditType().equals(AuditType.TO_AUDIT.getValue())) {
                sendWxMsgOfAuditTo(screenModuleLibraryAuditBo);
            }
        } catch (Exception e) {
            log.error("updatePosterAuditLabel sendWxMsgOfAudit error:", e);
        }
        return ajaxResult;
    }

    @Override
    public AjaxResult deleteBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        try {
            screenModuleLibraryAuditService.deleteBatch(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds());
            return AjaxResult.success("批量删除成功");
        } catch (Exception e) {
            log.error("ScreenModuleLibraryAuditController deleteBatch error:", e);
        }
        return AjaxResult.fail("批量删除失败");
    }

    @Override
    public AjaxResult releasePosterBatch(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        if (CollectionUtils.isEmpty(screenModuleLibraryAuditBo.getScreenModuleLibraryAuditIds())) {
            return AjaxResult.fail("请选择数据");
        }

        try {
            return screenModuleLibraryAuditService.releasePosterBatch(screenModuleLibraryAuditBo);
        } catch (Exception e) {
            log.error("ScreenModuleLibraryAuditController releasePosterBatch error:", e);
        }
        return AjaxResult.fail("批量发布/取消发布失败");
    }

    /**
     * 发送审核的微信提醒
     *
     * @param screenModuleLibraryAuditBo the screen module library audit bo
     * @return boolean boolean
     */
    private boolean sendWxMsgOfAuditTo(ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        Long organizationId = screenModuleLibraryAuditBo.getOrganizationId();
        List<String> userOidsByRoleId =
            userRoleService.getUserOidsByRoleId(RoleIdentifierType.SCHOOL_ADMIN.getRoleId(), organizationId);
        if (CollectionUtils.isEmpty(userOidsByRoleId)) {
            return true;
        }
        // 查询userOid和openId的映射关系
        String appId = wxMpProperties.getConfigs().get(0).getAppId();
        UserAuthorizationConditionBo userAuthorizationConditionBo = new UserAuthorizationConditionBo();
        userAuthorizationConditionBo.setAppId(appId);
        userAuthorizationConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        userAuthorizationConditionBo.setUserOids(userOidsByRoleId);
        AjaxResult<List<UserAuthorizationVo>> userAuthorizationVosAjaxResult =
            userAuthorizationApi.getUserAuthorizationListByCondition(userAuthorizationConditionBo);
        Map<String, String> userOidOpenIdMap = Maps.newHashMap();
        if (userAuthorizationVosAjaxResult.isSuccess()
            && CollectionUtils.isNotEmpty(userAuthorizationVosAjaxResult.getData())) {
            userOidOpenIdMap = userAuthorizationVosAjaxResult.getData().stream().collect(
                Collectors.toMap(UserAuthorizationVo::getUserOid, UserAuthorizationVo::getOpenId, (k1, k2) -> k1));
        }

        String realName = "";
        LoginAccountVo currentUser = baseDataService.getCurrentUser();
        if (currentUser != null) {
            LoginUserVo currentUserVo = currentUser.getCurrentUser();
            if (currentUserVo != null) {
                realName = currentUserVo.getRealName();
            }
        }
        for (String userOid : userOidsByRoleId) {
            String toUser = userOidOpenIdMap.get(userOid);
            // 组装消息推送
            WxMpTemplateData wxMpTemplateData = new WxMpTemplateData("thing1", realName, "#FF0000");
            WxMpTemplateData wxMpTemplateData2 =
                new WxMpTemplateData("time2", DateKit.date2String(new Date(), "yyyy年MM月dd日"), "#FF0000");
            List<WxMpTemplateData> data = Lists.newArrayList(wxMpTemplateData, wxMpTemplateData2);
            wxMpMsgService.sendTemplateMsg(appId, wxMpSubMsgAuditPage, toUser, data, null, wxMpSubMsgAuditTemplateId);
        }
        return true;
    }

    /**
     * 审核通过或拒绝发送微信通知
     *
     * @param screenModuleLibraryAuditBo the screen module library audit bo
     * <AUTHOR>
     * @date 2024 -04-09 14:27:47
     */
    private void sendWxMsgOfAuditPassOrReject(List<Long> screenModuleLibraryAuditIds) {
        // 审核通过或者驳回，发送微信消息给创建人
        ScreenModuleLibraryAuditConditionBo condition = new ScreenModuleLibraryAuditConditionBo();
        condition.setScreenModuleLibraryAuditIds(screenModuleLibraryAuditIds);
        List<ScreenModuleLibraryAuditVo> screenModuleLibraryAuditList =
            screenModuleLibraryAuditService.getScreenModuleLibraryAuditListByCondition(condition);
        if (CollectionUtils.isNotEmpty(screenModuleLibraryAuditList)) {
            List<String> auditUserOids = screenModuleLibraryAuditList.stream()
                .map(ScreenModuleLibraryAuditVo::getAuditUser).collect(Collectors.toList());
            Map<String, String> userOidRealNameMap = baseDataService.getRealNameByUserOids(auditUserOids);
            // 查询userOid和openId的映射关系
            String appId = wxMpProperties.getConfigs().get(0).getAppId();
            UserAuthorizationConditionBo userAuthorizationConditionBo = new UserAuthorizationConditionBo();
            userAuthorizationConditionBo.setAppId(appId);
            userAuthorizationConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            userAuthorizationConditionBo.setUserOids(auditUserOids);
            AjaxResult<List<UserAuthorizationVo>> userAuthorizationVosAjaxResult =
                userAuthorizationApi.getUserAuthorizationListByCondition(userAuthorizationConditionBo);
            Map<String, String> userOidOpenIdMap = Maps.newHashMap();
            if (userAuthorizationVosAjaxResult.isSuccess()
                && CollectionUtils.isNotEmpty(userAuthorizationVosAjaxResult.getData())) {
                userOidOpenIdMap = userAuthorizationVosAjaxResult.getData().stream().collect(
                    Collectors.toMap(UserAuthorizationVo::getUserOid, UserAuthorizationVo::getOpenId, (k1, k2) -> k1));
            }

            for (ScreenModuleLibraryAuditVo screenModuleLibraryAuditVo : screenModuleLibraryAuditList) {
                if (screenModuleLibraryAuditVo.getAuditType() == null
                    || screenModuleLibraryAuditVo.getAuditType().equals(AuditType.TO_AUDIT.getValue())
                    || screenModuleLibraryAuditVo.getAuditType().equals(AuditType.TO_SUBMIT.getValue())) {
                    continue;
                }
                if (StringUtils.isBlank(screenModuleLibraryAuditVo.getAuditUser())) {
                    continue;
                }
                String auditRealName = userOidRealNameMap.get(screenModuleLibraryAuditVo.getAuditUser());
                String auditResult =
                    screenModuleLibraryAuditVo.getAuditType().equals(AuditType.AUDIT_PASS.getValue()) ? "通过" : "驳回";
                String auditTime = DateKit.date2String(screenModuleLibraryAuditVo.getAuditTime(), "yyyy年MM月dd日 HH:mm");
                String toUser = userOidOpenIdMap.get(screenModuleLibraryAuditVo.getCreateBy());
                // 组装消息推送
                WxMpTemplateData wxMpTemplateData = new WxMpTemplateData("time3", auditTime, "#FF0000");
                WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("const4", auditResult, "#FF0000");
                WxMpTemplateData wxMpTemplateData3 = new WxMpTemplateData("thing5", auditRealName, "#FF0000");
                List<WxMpTemplateData> data =
                    Lists.newArrayList(wxMpTemplateData, wxMpTemplateData2, wxMpTemplateData3);
                wxMpMsgService.sendTemplateMsg(appId, wxMpSubMsgAuditResultPage, toUser, data, null,
                    wxMpSubMsgAuditResultTemplateId);
            }
        }
    }
}
