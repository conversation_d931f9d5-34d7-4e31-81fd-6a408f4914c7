package com.fh.cloud.screen.service.rabbitmq.listener;

import com.fh.cloud.screen.service.rabbitmq.constant.SpaceInfoRabbitConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *
 * 地点相关监听
 */
@Component
@Slf4j
public class SpaceInfoListener {

    /**
     *  地点删除消息接收
     *
     * @param message the message
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(SpaceInfoRabbitConstant.SPACE_INFO_DELETE_QUEUE),
            exchange = @Exchange(name = SpaceInfoRabbitConstant.SPACE_INFO_EXCHANGE, declare = "false")))
    public void spaceInfoDeleteListener(String message) {

        try {
            log.info("======= spaceInfo.delete receive :{} =========",message);
        } catch (Exception e) {
            log.error("============= spaceInfo.delete error:{} ==========",e.getMessage());
        }

    }

}
