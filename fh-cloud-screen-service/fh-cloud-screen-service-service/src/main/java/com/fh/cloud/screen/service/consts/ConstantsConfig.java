package com.fh.cloud.screen.service.consts;

/**
 * 全部常量配置类
 */
public class ConstantsConfig {

    /**
     * 学年-月份
     */
    public static int SCHOOL_YEAR_MONTH;
    /**
     * 学年-日
     */
    public static int SCHOOL_YEAR_DAY;

    /**
     * aes加密的key
     */
    public static String AES_KEY = "pJjPQgcwTnnFbaxz";

    /**
     * 截图磁盘路径前缀，不允许修改
     */
    public static String SCREEN_SHOT_DISK_PATH_PREFIX = "/gaocq/nodejs/projects/nodejs-url-screenshot";

    /**
     * 默认的如果没有海报但配置了海报模块的时候展示的主题分类-竖版
     */
    public static final Long DEFAULT_MODULE_GROUP_TYPE_VERTICAL = 17L;
    /**
     * 默认的如果没有海报但配置了海报模块的时候展示的主题分类-横板
     */
    public static final Long DEFAULT_MODULE_GROUP_TYPE_HORIZONTAL = 17L;

    /**
     * 默认场景布局-竖版
     */
    public static final String DEFAULT_SCENE_LAYOUT_VERTICAL =
        "['{'\"x\":0,\"y\":0,\"i\":0,\"t\":\"2x4\",\"s\":24,\"d\":[1],\"size\":\"24\",\"w\":2,\"h\":4,\"data\":'{'\"screenModuleDataId\":{0},\"organizationId\":{1},\"screenModuleLibraryId\":1001,\"moduleSource\":1,\"customModuleName\":null,\"customModuleGroupType\":1,\"createTime\":\"2022-07-12 11:56:11\",\"createBy\":\"g5zhtwih7wjie0xhmznsi3bcaerijwi0o2z1s36q4slx8swh8oxet1bvcrl2kgra\",\"updateTime\":\"2022-07-12 11:56:11\",\"updateBy\":null,\"isDelete\":0,\"screenContentVos\":null,\"moduleName\":\"主题海报\",\"moduleGroupType\":1,\"screenSceneId\":null,\"isPoster\":1,\"screenModuleLibrarySelIds\":null,\"sizeList\":['{'\"t\":\"1x2\",\"s\":12,\"d\":[1],\"size\":\"12\",\"w\":1,\"h\":2'}','{'\"t\":\"2x4\",\"s\":24,\"d\":[1],\"size\":\"24\",\"w\":2,\"h\":4'}','{'\"t\":\"4x2\",\"s\":42,\"d\":[1],\"size\":\"42\",\"w\":4,\"h\":2'}'],\"moduleGroupName\":\"信息发布\"'}',\"_action\":\"add\",\"xys\":[\"0:0\",\"0:1\",\"0:2\",\"0:3\",\"1:0\",\"1:1\",\"1:2\",\"1:3\"],\"idx\":\"{2}\",\"hasData\":true'}']";

    /**
     * 默认场景布局-横板,screenModuleDataId,organizationId,idx
     */
    public static final String DEFAULT_SCENE_LAYOUT_HORIZONTAL =
        "['{'\"x\":0,\"y\":0,\"i\":0,\"t\":\"4x2\",\"s\":42,\"d\":[1],\"size\":\"42\",\"w\":4,\"h\":2,\"data\":'{'\"screenModuleDataId\":{0},\"organizationId\":{1},\"screenModuleLibraryId\":1001,\"moduleSource\":1,\"customModuleName\":null,\"customModuleGroupType\":1,\"createTime\":\"2022-07-12 11:56:11\",\"createBy\":\"g5zhtwih7wjie0xhmznsi3bcaerijwi0o2z1s36q4slx8swh8oxet1bvcrl2kgra\",\"updateTime\":\"2022-07-12 11:56:11\",\"updateBy\":null,\"isDelete\":0,\"screenContentVos\":null,\"moduleName\":\"主题海报\",\"moduleGroupType\":1,\"screenSceneId\":null,\"isPoster\":1,\"screenModuleLibrarySelIds\":null,\"sizeList\":['{'\"t\":\"1x2\",\"s\":12,\"d\":[1],\"size\":\"12\",\"w\":1,\"h\":2'}','{'\"t\":\"2x4\",\"s\":24,\"d\":[1],\"size\":\"24\",\"w\":2,\"h\":4'}','{'\"t\":\"4x2\",\"s\":42,\"d\":[1],\"size\":\"42\",\"w\":4,\"h\":2'}'],\"moduleGroupName\":\"信息发布\"'}',\"_action\":\"add\",\"xys\":[\"0:0\",\"0:1\",\"1:0\",\"1:1\",\"2:0\",\"2:1\",\"3:0\",\"3:1\"],\"idx\":\"{2}\",\"hasData\":true'}']";

    /**
     * 普通教师的模块分组id，对应space_group表
     */
    public static final Long SPACE_GROUP_ID_PTJS = 1L;

    /**
     * 阿里云人脸识别，单个人脸列表中搜索人脸结果数量限制
     */
    public static final Integer aliFaceLimit = 5;
    /**
     * 阿里云人脸识别，对输入图像中的多少个人脸进行比对
     */
    public static final Long aliFaceMaxNum = 5L;
    /**
     * 默认的云屏人脸库
     */
    public static final String faceDbName = "screen_face";
    /**
     * 默认的人脸识别阈值
     */
    public static final String faceThreshold = "0.6";

    /**
     * 默认海报分组的名称
     */
    public static final String DEFAULT_LABEL_GROUP_NAME = "分类";
}
