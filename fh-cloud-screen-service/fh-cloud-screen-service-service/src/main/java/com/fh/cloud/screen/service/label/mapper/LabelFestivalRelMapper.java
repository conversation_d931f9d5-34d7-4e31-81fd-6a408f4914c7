package com.fh.cloud.screen.service.label.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo;

/**
 * 标签节日关联表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
public interface LabelFestivalRelMapper extends BaseMapper<LabelFestivalRelDto> {

	List<LabelFestivalRelVo> getLabelFestivalRelListByCondition(LabelFestivalRelConditionBo condition);

}
