package com.fh.cloud.screen.service.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo;
import com.light.core.entity.AjaxResult;

import java.util.Date;
import java.util.List;

/**
 * 长期预约表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
public interface IMeetingLongService extends IService<MeetingLongDto> {

    List<MeetingLongVo> getMeetingLongListByCondition(MeetingLongConditionBo condition);

    AjaxResult addMeetingLong(MeetingLongBo meetingLongBo);

    AjaxResult updateMeetingLong(MeetingLongBo meetingLongBo);

    MeetingLongVo getMeetingLongByCondition(MeetingLongConditionBo condition);

    /**
     * 从长期预约表生成会议表
     *
     * @param nowDay ：指定生成某天（格式：yyyy-MM-dd）的会议，必填
     * @return the boolean，成功返回true，失败返回false
     * <AUTHOR>
     * @date 2023 -12-12 15:48:15
     */
    boolean generateMeetingFromMeetingLong(Date nowDay);

}
