package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolBroadcastInfoDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo;

/**
 * 放学播报信息表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
public interface LeaveSchoolBroadcastInfoMapper extends BaseMapper<LeaveSchoolBroadcastInfoDto> {

	List<LeaveSchoolBroadcastInfoVo> getLeaveSchoolBroadcastInfoListByCondition(LeaveSchoolBroadcastInfoConditionBo condition);

	LeaveSchoolBroadcastInfoVo getLeaveSchoolBroadcastInfoByCondition(LeaveSchoolBroadcastInfoConditionBo condition);

}
