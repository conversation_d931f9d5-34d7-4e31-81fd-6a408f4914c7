package com.fh.cloud.screen.service.rest.controller;

import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeListConditionBo;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeVo;
import com.fh.cloud.screen.service.rest.service.IWorkRestGradeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 作息时间年级表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@RequestMapping("/rest/grade")
@Validated
public class WorkRestGradeController {

    @Autowired
    private IWorkRestGradeService workRestGradeService;

    /**
     * 查询作息时间年级表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询作息时间年级表列表", httpMethod = "POST")
    public AjaxResult getWorkRestGradeListByCondition(@RequestBody WorkRestGradeListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<WorkRestGradeVo> pageInfo =
            new PageInfo<>(workRestGradeService.getWorkRestGradeListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("workRestGradeList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增作息时间年级表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增作息时间年级表", httpMethod = "POST")
    public AjaxResult addWorkRestGrade(@RequestBody WorkRestGradeBo workRestGradeBo) {
        boolean save = workRestGradeService.addWorkRestGrade(workRestGradeBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改作息时间年级表
     * 
     * @param workRestGradeBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改作息时间年级表", httpMethod = "POST")
    public AjaxResult updateWorkRestGrade(@RequestBody WorkRestGradeBo workRestGradeBo) {
        if (null == workRestGradeBo.getWorkRestGradeId()) {
            return AjaxResult.fail("作息时间年级表id不能为空");
        }
        boolean update = workRestGradeService.updateWorkRestGrade(workRestGradeBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询作息时间年级表详情
     * 
     * @param workRestGradeId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询作息时间年级表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("workRestGradeId") Long workRestGradeId) {
        WorkRestGradeVo workRestGradeVo = workRestGradeService.getDetail(workRestGradeId);
        return AjaxResult.success(workRestGradeVo);
    }

    /**
     * 删除作息时间年级表
     * 
     * @param workRestGradeId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除作息时间年级表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("workRestGradeId") Long workRestGradeId) {
        WorkRestGradeBo workRestGradeBo = new WorkRestGradeBo();
        workRestGradeBo.setWorkRestGradeId(workRestGradeId);
        workRestGradeBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = workRestGradeService.updateWorkRestGrade(workRestGradeBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
