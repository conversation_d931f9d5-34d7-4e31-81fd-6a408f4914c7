package com.fh.cloud.screen.service.space.controller;

import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 区域组表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@RequestMapping("/space/group")
@Validated
public class SpaceGroupController {

    @Autowired
    private ISpaceGroupService spaceGroupService;

    /**
     * 查询区域组表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询区域组表列表", httpMethod = "POST")
    public AjaxResult getSpaceGroupListByCondition(@RequestBody SpaceGroupListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SpaceGroupVo> pageInfo = new PageInfo<>(spaceGroupService.getSpaceGroupListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("spaceGroupList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 查询所有区域组信息
     *
     * @return
     */
    @GetMapping("findAll")
    @ApiOperation(value = "查询所有区域组信息", httpMethod = "POST")
    public AjaxResult<List<SpaceGroupVo>> findAll() {
        return AjaxResult.success(this.spaceGroupService.findAll());
    }

    /**
     * 新增区域组表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增区域组表", httpMethod = "POST")
    public AjaxResult addSpaceGroup(@RequestBody SpaceGroupBo spaceGroupBo) {
        boolean save = spaceGroupService.addSpaceGroup(spaceGroupBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改区域组表
     *
     * @param spaceGroupBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改区域组表", httpMethod = "POST")
    public AjaxResult updateSpaceGroup(@RequestBody SpaceGroupBo spaceGroupBo) {
        if (null == spaceGroupBo.getSpaceGroupId()) {
            return AjaxResult.fail("区域组表id不能为空");
        }
        boolean update = spaceGroupService.updateSpaceGroup(spaceGroupBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询区域组表详情
     *
     * @param spaceGroupId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询区域组表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("spaceGroupId") Long spaceGroupId) {
        Map<String, Object> map = spaceGroupService.getDetail(spaceGroupId);
        return AjaxResult.success(map);
    }

    /**
     * 删除区域组表
     *
     * @param spaceGroupId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除区域组表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("spaceGroupId") Long spaceGroupId) {
        SpaceGroupBo spaceGroupBo = new SpaceGroupBo();
        spaceGroupBo.setSpaceGroupId(spaceGroupId);
        spaceGroupBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = spaceGroupService.updateSpaceGroup(spaceGroupBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
