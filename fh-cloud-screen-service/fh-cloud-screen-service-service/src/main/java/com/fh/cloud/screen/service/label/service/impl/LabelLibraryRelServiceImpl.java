package com.fh.cloud.screen.service.label.service.impl;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.service.ILabelFestivalRelService;
import com.fh.cloud.screen.service.label.service.ILabelLibraryRelService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo;
import com.fh.cloud.screen.service.label.mapper.LabelLibraryRelMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 标签海报关联表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@Service
public class LabelLibraryRelServiceImpl extends ServiceImpl<LabelLibraryRelMapper, LabelLibraryRelDto>
    implements ILabelLibraryRelService {

    @Resource
    private LabelLibraryRelMapper labelLibraryRelMapper;
    @Resource
    private ILabelFestivalRelService labelFestivalRelService;

    @Override
    public List<LabelLibraryRelVo> getLabelLibraryRelListByCondition(LabelLibraryRelConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return labelLibraryRelMapper.getLabelLibraryRelListByCondition(condition);
    }

    @Override
    public AjaxResult addLabelLibraryRel(LabelLibraryRelBo labelLibraryRelBo) {
        List<LabelBo> labelBos = labelLibraryRelBo.getLabelBos();
        // 删除已绑定
        LambdaUpdateWrapper<LabelLibraryRelDto> luw = new LambdaUpdateWrapper<>();
        luw.eq(LabelLibraryRelDto::getScreenModuleLibraryId, labelLibraryRelBo.getScreenModuleLibraryId());
        luw.set(LabelLibraryRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        this.update(luw);
        if (CollectionUtils.isEmpty(labelBos)) {
            return AjaxResult.success("保存成功");
        }
        // 添加新绑定
        List<LabelLibraryRelDto> addList = new ArrayList<>();
        for (LabelBo labelBo : labelBos) {
            LabelLibraryRelDto labelLibraryRel = new LabelLibraryRelDto();
            labelLibraryRel.setScreenModuleLibraryId(labelLibraryRelBo.getScreenModuleLibraryId());
            labelLibraryRel.setLabelId(labelBo.getLabelId());
            labelLibraryRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
            addList.add(labelLibraryRel);
        }
        if (saveBatch(addList)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 新增或编辑 标签和节日 与海报关联关系
     *
     * @param labelLibraryRelBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/18 15:41
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateLabelOrFestivalLibraryRel(LabelLibraryRelBo labelLibraryRelBo) {
        // 标签绑定主题
        if (null != labelLibraryRelBo.getLabelId()) {
            if (updateLabelLibraryRel(labelLibraryRelBo.getLabelId(), labelLibraryRelBo.getScreenModuleLibraryIds())) {
                return AjaxResult.success();
            }
        }
        // 节日绑定主题
        if (null != labelLibraryRelBo.getFestivalCode()) {
            // 获取节日关联的标签
            LambdaQueryWrapper<LabelFestivalRelDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(LabelFestivalRelDto::getFestivalCode, labelLibraryRelBo.getFestivalCode());
            lqw.eq(LabelFestivalRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            LabelFestivalRelDto one = labelFestivalRelService.getOne(lqw);
            if (updateLabelLibraryRel(one.getLabelId(), labelLibraryRelBo.getScreenModuleLibraryIds())) {
                return AjaxResult.success();
            }
        }
        return AjaxResult.fail("参数错误");
    }

    /**
     * 根据主题id集合更新标签和主题关联关系
     *
     * @param labelId, ScreenModuleLibraryIds
     * @return boolean
     * <AUTHOR>
     * @date 2023/5/18 15:58
     */
    private boolean updateLabelLibraryRel(Long labelId, String ScreenModuleLibraryIds) {
        // 删除已绑定
        LambdaUpdateWrapper<LabelLibraryRelDto> luw = new LambdaUpdateWrapper<>();
        luw.eq(LabelLibraryRelDto::getLabelId, labelId);
        luw.set(LabelLibraryRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        this.update(luw);
        // 添加新绑定
        if (StringUtil.isBlank(ScreenModuleLibraryIds)) {
            return true;
        }
        List<LabelLibraryRelDto> addList = new ArrayList<>();
        String[] libraryIds = ScreenModuleLibraryIds.split(",");
        for (String libraryId : libraryIds) {
            LabelLibraryRelDto labelLibraryRel = new LabelLibraryRelDto();
            labelLibraryRel.setScreenModuleLibraryId(Long.parseLong(libraryId));
            labelLibraryRel.setLabelId(labelId);
            addList.add(labelLibraryRel);
        }
        return this.saveBatch(addList);
    }

    @Override
    public AjaxResult updateLabelLibraryRel(LabelLibraryRelBo labelLibraryRelBo) {
        LabelLibraryRelDto labelLibraryRel = new LabelLibraryRelDto();
        BeanUtils.copyProperties(labelLibraryRelBo, labelLibraryRel);
        if (updateById(labelLibraryRel)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public LabelLibraryRelVo getDetail(Long id) {
        LabelLibraryRelConditionBo condition = new LabelLibraryRelConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<LabelLibraryRelVo> list = labelLibraryRelMapper.getLabelLibraryRelListByCondition(condition);
        LabelLibraryRelVo vo = new LabelLibraryRelVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public List<LabelLibraryRelVo> getLabelLibraryRelListByConditionOfLabel(LabelLibraryRelConditionBo condition) {
        return labelLibraryRelMapper.getLabelLibraryRelListByConditionOfLabel(condition);
    }
}