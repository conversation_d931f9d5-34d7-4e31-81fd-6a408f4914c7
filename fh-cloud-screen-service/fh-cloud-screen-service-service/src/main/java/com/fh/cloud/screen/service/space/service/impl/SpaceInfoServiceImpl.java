package com.fh.cloud.screen.service.space.service.impl;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.mapper.SpaceInfoMapper;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 区域信息表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class SpaceInfoServiceImpl extends ServiceImpl<SpaceInfoMapper, SpaceInfo> implements ISpaceInfoService {

    @Resource
    private SpaceInfoMapper spaceInfoMapper;
    @Autowired
    private ISpaceInfoService spaceInfoService;
    @Autowired
    private BaseDataService baseDataService;

    @Override
    public List<SpaceInfoVo> getSpaceInfoListByCondition(SpaceInfoListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return spaceInfoMapper.getSpaceInfoListByCondition(condition);
    }

    @Override
    public AjaxResult addSpaceInfo(SpaceInfoBo spaceInfoBo) {
        spaceInfoBo.setSpaceInfoName(spaceInfoBo.getSpaceInfoName().trim());
        List<SpaceInfo> spaceInfos = baseMapper.selectList(
            new LambdaQueryWrapper<SpaceInfo>().eq(SpaceInfo::getSpaceGroupId, spaceInfoBo.getSpaceGroupId())
                .eq(SpaceInfo::getCampusId, spaceInfoBo.getCampusId())
                .eq(SpaceInfo::getIsDelete, StatusEnum.NOTDELETE.getCode())
                .eq(SpaceInfo::getSpaceInfoName, spaceInfoBo.getSpaceInfoName()));
        if (CollectionUtil.isNotEmpty(spaceInfos)) {
            return AjaxResult.fail("名称重复");
        }
        SpaceInfo spaceInfo = new SpaceInfo();
        BeanUtils.copyProperties(spaceInfoBo, spaceInfo);
        spaceInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        save(spaceInfo);
        return AjaxResult.success("新增成功");
    }

    @Override
    public boolean updateSpaceInfo(SpaceInfoBo spaceInfoBo) {
        SpaceInfo spaceInfo = new SpaceInfo();
        BeanUtils.copyProperties(spaceInfoBo, spaceInfo);
        // spaceInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return updateById(spaceInfo);
    }

    @Override
    public SpaceInfoVo getDetail(Long spaceInfoId) {
        LambdaQueryWrapper<SpaceInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SpaceInfo::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(SpaceInfo::getSpaceInfoId, spaceInfoId);
        SpaceInfo spaceInfo = getOne(lqw);
        SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
        BeanUtils.copyProperties(spaceInfo, spaceInfoVo);
        return spaceInfoVo;
    }

    @Override
    public List<ClazzInfoVo> getSpaceInfoVosByIdsXz(List<Long> classesIds) {
        if (CollectionUtils.isEmpty(classesIds)) {
            return Lists.newArrayList();
        }
        ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
        clazzConditionBoExt.setIds(classesIds);
        clazzConditionBoExt.setIsDelete(StatusEnum.NOTDELETE.getCode());
        clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
        clazzConditionBoExt.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC,\n"
                + "    CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        clazzConditionBoExt.setQueryType(ConstantsInteger.APP_QUERY);
        Map<String, Object> clazzInfoVoListMap = baseDataService.getClazzInfoVoList(clazzConditionBoExt);
        if (clazzInfoVoListMap != null && clazzInfoVoListMap.get("list") != null) {
            return (List<ClazzInfoVo>)clazzInfoVoListMap.get("list");
        }
        return Lists.newArrayList();
    }

    @Override
    public List<SpaceInfoVo> getSpaceInfoVosByIdsNotXz(List<Long> spaceInfoIds) {
        if (CollectionUtils.isEmpty(spaceInfoIds)) {
            return Lists.newArrayList();
        }
        SpaceInfoListConditionBo condition = new SpaceInfoListConditionBo();
        condition.setSpaceInfoIds(spaceInfoIds);
        List<SpaceInfoVo> spaceInfoVoList = spaceInfoService.getSpaceInfoListByCondition(condition);
        return spaceInfoVoList;
    }

    @Override
    public SpaceInfoVo getDetailVoByUseType(Long spaceInfoId, Integer spaceGroupUseType) {
        if (spaceInfoId == null || spaceGroupUseType == null) {
            return null;
        }
        if (spaceGroupUseType.equals(SpaceGroupUseType.XZ.getValue())) {
            List<ClazzInfoVo> clazzInfoVoList = this.getSpaceInfoVosByIdsXz(Lists.newArrayList(spaceInfoId));
            if (CollectionUtils.isEmpty(clazzInfoVoList)) {
                return null;
            }
            ClazzInfoVo clazzInfoVo = clazzInfoVoList.get(0);
            SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
            spaceInfoVo.setSpaceInfoId(clazzInfoVo.getId());
            spaceInfoVo.setSpaceInfoName(clazzInfoVo.getClassesNameShow());
            spaceInfoVo.setOrganizationId(clazzInfoVo.getOrganizationId());
            spaceInfoVo.setCampusId(clazzInfoVo.getCampusId());
            spaceInfoVo.setCampusName(clazzInfoVo.getCampusName());
            spaceInfoVo.setGrade(clazzInfoVo.getGrade());
            spaceInfoVo.setGradeName(clazzInfoVo.getGradeName());
            spaceInfoVo.setSpaceGroupUseType(spaceGroupUseType);
            return spaceInfoVo;
        }
        if (spaceGroupUseType.equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            List<SpaceInfoVo> spaceInfoVos = this.getSpaceInfoVosByIdsNotXz(Lists.newArrayList(spaceInfoId));
            if (CollectionUtils.isEmpty(spaceInfoVos)) {
                return null;
            }
            SpaceInfoVo spaceInfoVo = spaceInfoVos.get(0);
            spaceInfoVo.setSpaceGroupUseType(spaceGroupUseType);
            return spaceInfoVo;
        }
        return null;
    }

    @Override
    public List<SpaceInfoVo> getSpaceInfoListByConditionSlow(SpaceInfoListConditionBo condition) {
        return spaceInfoMapper.getSpaceInfoListByCondition(condition);
    }
}