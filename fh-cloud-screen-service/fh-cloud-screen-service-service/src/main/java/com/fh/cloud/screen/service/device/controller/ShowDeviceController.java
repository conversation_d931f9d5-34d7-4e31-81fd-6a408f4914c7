package com.fh.cloud.screen.service.device.controller;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.net.URLEncoder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.async.DeviceAsync;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.device.entity.bo.*;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.entity.vo.DeviceCountVo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceListVo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceOverviewVo;
import com.fh.cloud.screen.service.device.enums.DeviceActivationEnums;
import com.fh.cloud.screen.service.enums.DeviceFullType;
import com.fh.cloud.screen.service.enums.DevicePatternType;
import com.fh.cloud.screen.service.enums.FaceModType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.enums.SuperviseStateType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDeviceService;
import com.fh.cloud.screen.service.leaveschool.service.LeaveSchoolConfigDeviceApiService;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.role.servise.UserRoleService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenQrcodeContentVo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.EncryptUtil;
import com.fh.cloud.screen.service.utils.MessageUtil;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceConditionBo;
import com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubDeviceVo;
import com.fh.cloud.screen.service.wx.service.IWxMsgSubDeviceService;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.device.api.ShowDeviceApi;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.DeviceStatusType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 展示设备表，例如云屏
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Api(value = "", tags = "设备管理")
@Slf4j
public class ShowDeviceController implements ShowDeviceApi {
    @Autowired
    private IShowDeviceService showDeviceService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private ApplicationContext applicationContext;
    @Resource
    private DeviceAsync deviceAsync;
    @Lazy
    @Autowired
    private MessageService messageService;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private IWxMsgSubDeviceService wxMsgSubDeviceService;

    @Value("${env.mobile.h5.url:}")
    private String mobileH5Url;

    @Resource
    private ISpaceInfoService spaceInfoService;

    @Resource
    private ILeaveSchoolConfigDeviceService leaveSchoolConfigDeviceService;

    /**
     * 查询展示设备表，例如云屏列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询展示设备表，例如云屏列表", httpMethod = "POST")
    @Override
    public AjaxResult listByCondition(@RequestBody ShowDeviceListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("update_time desc");
        // 检索时，开关机状态，需要连线判断异常
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            List<ShowDeviceVo> showDeviceVos = showDeviceService.getShowDeviceListByCondition(condition);
            showDeviceVos.forEach(
                showDeviceVo -> showDeviceVo.setQrcodeContent(EncryptUtil.encodeAes(showDeviceVo.getDeviceNumber())));
            map.put("list", showDeviceVos);
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            List<ShowDeviceVo> showDeviceVos = showDeviceService.getShowDeviceListByCondition(condition);
            showDeviceVos.forEach(
                showDeviceVo -> showDeviceVo.setQrcodeContent(EncryptUtil.encodeAes(showDeviceVo.getDeviceNumber())));
            PageInfo<ShowDeviceVo> pageInfo = new PageInfo<>(showDeviceVos);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 新增展示设备表，例如云屏
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @Override
    @ApiOperation(value = "新增展示设备表，例如云屏", httpMethod = "POST")
    public AjaxResult addShowDevice(@RequestBody ShowDeviceBo showDeviceBo) {
        boolean save = showDeviceService.addShowDevice(showDeviceBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改展示设备表，例如云屏
     *
     * @param showDeviceBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @Override
    @ApiOperation(value = "修改展示设备表，例如云屏", httpMethod = "POST")
    public AjaxResult updateShowDevice(@RequestBody ShowDeviceBo showDeviceBo) {
        if (null == showDeviceBo.getShowDeviceId()) {
            return AjaxResult.fail("展示设备表，例如云屏id不能为空");
        }
        boolean update = showDeviceService.updateShowDevice(showDeviceBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询展示设备表，例如云屏详情
     *
     * @param showDeviceId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询展示设备表，例如云屏详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("showDeviceId") Long showDeviceId) {
        ShowDeviceVo showDeviceVo = showDeviceService.getDetail(showDeviceId);
        return AjaxResult.success(showDeviceVo);
    }

    /**
     * 删除展示设备表，例如云屏
     *
     * @param showDeviceId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "删除展示设备表，例如云屏", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("showDeviceId") Long showDeviceId) {
        ShowDeviceBo showDeviceBo = new ShowDeviceBo();
        showDeviceBo.setShowDeviceId(showDeviceId);
        showDeviceBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = showDeviceService.updateShowDevice(showDeviceBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 导入设备
     *
     * @param file
     * @return
     */
    @Override
    public AjaxResult upload(@RequestPart(value = "file") MultipartFile file, Long organizationId) {
        ImportParams params = new ImportParams();
        // 表头设置为2行
        params.setHeadRows(2);
        params.setTitleRows(1);
        // params.setNeedVerify(true);
        ExcelImportResult<ShowDeviceExcelModel> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), ShowDeviceExcelModel.class, params);
        } catch (Exception e) {
            return AjaxResult.fail();
        }
        List<ShowDeviceExcelModel> list = result.getList();
        List<ShowDeviceBo> deviceBos = new ArrayList<>();
        for (ShowDeviceExcelModel model : list) {
            ShowDeviceBo showDeviceBo = new ShowDeviceBo();
            BeanUtils.copyProperties(model, showDeviceBo);
            showDeviceBo.setOrganizationId(organizationId);
            try {
                showDeviceBo.setDeviceType(Integer.valueOf(model.getDeviceType()));
                showDeviceBo.setDeviceBrand(Integer.valueOf(model.getDeviceBrand()));
            } catch (Exception e) {
                e.printStackTrace();
                return AjaxResult.fail("设备类型或品牌输入错误，请修改后重新上传");
            }
            deviceBos.add(showDeviceBo);
        }
        return AjaxResult.success(showDeviceService.addShowDeviceBatch(deviceBos));
    }

    /**
     * 激活设备
     *
     * @param bo
     * @return
     */
    @Override
    @ApiOperation(value = "激活设备", httpMethod = "POST")
    public AjaxResult<ShowDeviceVo> activate(@RequestBody ShowDeviceBo bo) {
        AjaxResult<ShowDeviceVo> activateResult = this.showDeviceService.activate(bo);
        // 逻辑有问题暂时不处理，因为设备和地点的关系这时候还没有
        // if (activateResult != null && activateResult.isSuccess()) {
        // // 激活设备的事件
        // List<String> deviceNumbers = Lists.newArrayList(bo.getDeviceNumber());
        // applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
        // MessageWsType.SCREEN_ACTIVE.getValue(), bo.getOrganizationId(), deviceNumbers, null));
        // }
        return activateResult;
    }

    /**
     * 获取设备信息
     *
     * @param deviceNubmer
     * @return
     */
    @Override
    @ApiOperation(value = "获取设备信息", httpMethod = "GET")
    public AjaxResult<ShowDeviceVo> getByDeviceNumber(@PathVariable("deviceNubmer") String deviceNubmer) {
        return AjaxResult.success(this.showDeviceService.getByDeviceNum(deviceNubmer));
    }

    /**
     * 查询绑定了地点的设备列表-根据地点组查询
     *
     * @param condition the condition
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     */
    @ApiOperation(value = "查询绑定了地点的设备列表", httpMethod = "POST")
    public AjaxResult listShowDeviceDataByCondition(@RequestBody ShowDeviceListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("update_time desc");

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceDataByCondition(condition);

            WxMsgSubDeviceConditionBo wxMsgSubDeviceConditionBo = new WxMsgSubDeviceConditionBo();
            wxMsgSubDeviceConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            wxMsgSubDeviceConditionBo.setUserOid(baseDataService.getCurrentUserOid());
            List<WxMsgSubDeviceVo> wxMsgSubDeviceVos =
                wxMsgSubDeviceService.getWxMsgSubDeviceListByCondition(wxMsgSubDeviceConditionBo);
            List<String> subDeviceNumbers = CollectionUtils.isEmpty(wxMsgSubDeviceVos) ? Lists.newArrayList()
                : wxMsgSubDeviceVos.stream().map(WxMsgSubDeviceVo::getDeviceNumber).collect(Collectors.toList());

            // 查询设备是否为放学设备
            List<LeaveSchoolConfigDeviceVo> leaveSchoolDevices = Lists.newArrayList();
            if (condition.getQueryLeaveSchoolDevice() != null && condition.getQueryLeaveSchoolDevice()) {
                LeaveSchoolConfigDeviceConditionBo leaveSchoolDeviceCondition = new LeaveSchoolConfigDeviceConditionBo();
                leaveSchoolDeviceCondition.setOrganizationId(condition.getOrganizationId());
                leaveSchoolDeviceCondition.setCampusId(condition.getCampusId());
                leaveSchoolDeviceCondition.setSpaceGroupUseType(condition.getSpaceGroupUseType());
                leaveSchoolDevices = leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceList(leaveSchoolDeviceCondition);
            }

            List<String> leaveSchoolDeviceNumbers = CollectionUtils.isEmpty(leaveSchoolDevices)
                    ? Lists.newArrayList()
                    : leaveSchoolDevices.stream().map(LeaveSchoolConfigDeviceVo::getDeviceNumber).collect(Collectors.toList());
            showDeviceVos.stream().forEach(showDeviceVo -> {
                showDeviceVo.setQrcodeContent(EncryptUtil.encodeAes(showDeviceVo.getDeviceNumber()));
                showDeviceVo.setWxMsgSub(subDeviceNumbers.contains(showDeviceVo.getDeviceNumber()));
                showDeviceVo.setLeaveSchoolDevice(leaveSchoolDeviceNumbers.contains(showDeviceVo.getDeviceNumber()));
            });
            map.put("list", showDeviceVos);
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceDataByCondition(condition);

            // 查询设备是否微信消息订阅
            WxMsgSubDeviceConditionBo wxMsgSubDeviceConditionBo = new WxMsgSubDeviceConditionBo();
            wxMsgSubDeviceConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            wxMsgSubDeviceConditionBo.setUserOid(baseDataService.getCurrentUserOid());
            List<WxMsgSubDeviceVo> wxMsgSubDeviceVos =
                wxMsgSubDeviceService.getWxMsgSubDeviceListByCondition(wxMsgSubDeviceConditionBo);
            List<String> subDeviceNumbers = CollectionUtils.isEmpty(wxMsgSubDeviceVos) ? Lists.newArrayList()
                : wxMsgSubDeviceVos.stream().map(WxMsgSubDeviceVo::getDeviceNumber).collect(Collectors.toList());

            // 查询设备是否为放学设备
            List<LeaveSchoolConfigDeviceVo> leaveSchoolDevices = Lists.newArrayList();
            if (condition.getQueryLeaveSchoolDevice() != null && condition.getQueryLeaveSchoolDevice()) {
                LeaveSchoolConfigDeviceConditionBo leaveSchoolDeviceCondition = new LeaveSchoolConfigDeviceConditionBo();
                leaveSchoolDeviceCondition.setOrganizationId(condition.getOrganizationId());
                leaveSchoolDeviceCondition.setCampusId(condition.getCampusId());
                leaveSchoolDeviceCondition.setSpaceGroupUseType(condition.getSpaceGroupUseType());
                leaveSchoolDevices = leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceList(leaveSchoolDeviceCondition);
            }
            List<String> leaveSchoolDeviceNumbers = CollectionUtils.isEmpty(leaveSchoolDevices)
                    ? Lists.newArrayList()
                    : leaveSchoolDevices.stream().map(LeaveSchoolConfigDeviceVo::getDeviceNumber).collect(Collectors.toList());

            showDeviceVos.stream().forEach(showDeviceVo -> {
                showDeviceVo.setQrcodeContent(EncryptUtil.encodeAes(showDeviceVo.getDeviceNumber()));
                showDeviceVo.setWxMsgSub(subDeviceNumbers.contains(showDeviceVo.getDeviceNumber()));
                if (CollectionUtils.isNotEmpty(leaveSchoolDeviceNumbers)) {
                    showDeviceVo.setLeaveSchoolDevice(leaveSchoolDeviceNumbers.contains(showDeviceVo.getDeviceNumber()));
                }
            });
            PageInfo<ShowDeviceVo> pageInfo = new PageInfo<>(showDeviceVos);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    @Override
    public AjaxResult updateStatusByDeviceNum(@PathVariable("deviceNumber") String deviceNumber,
        @PathVariable("status") Integer status) {
        return this.showDeviceService.updateStatusByDeviceNum(deviceNumber, status);
    }

    /**
     * 根据设备号开关机
     *
     * @param deviceStatusType 设备开关机状态枚举
     * @param organizationId 学校id（必填）
     * @param deviceNumbers 设备号列表（不允许为空）
     * @return
     */
    @Override
    @ApiOperation(value = "根据设备号开关机", httpMethod = "POST")
    public AjaxResult switchOperate(ShowDeviceOperateBo showDeviceOperateBo) {
        Long organizationId = showDeviceOperateBo.getOrganizationId();
        Integer deviceStatusType = showDeviceOperateBo.getDeviceStatusType();
        List<String> deviceNumbers = showDeviceOperateBo.getDeviceNumbers();
        if (organizationId == null || CollectionUtils.isEmpty(deviceNumbers) || deviceStatusType == null) {
            return AjaxResult.fail();
        }

        try {
            if (deviceStatusType.equals(DeviceStatusType.ON.getValue())) {
                showDeviceService.updateShowDeviceByDeviceNumbers(DeviceStatusType.ON_ING.getValue(), organizationId,
                    deviceNumbers);
                // publish event
                applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
                    MessageWsType.DEVICE_OPEN.getValue(), organizationId, deviceNumbers, null));
            }
            if (deviceStatusType.equals(DeviceStatusType.OFF.getValue())) {
                showDeviceService.updateShowDeviceByDeviceNumbers(DeviceStatusType.OFF_ING.getValue(), organizationId,
                    deviceNumbers);
                // publish event
                applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
                    MessageWsType.DEVICE_CLOSE.getValue(), organizationId, deviceNumbers, null));
            }
            return AjaxResult.success();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("switchOperate error:", e);
            }
            return AjaxResult.fail();
        }
    }

    @Override
    public AjaxResult changePattern(@PathVariable("deviceNumber") String deviceNumber,
        @PathVariable("pattern") Integer pattern) {
        return AjaxResult.success(this.showDeviceService.updatePatternByDeviceNumber(deviceNumber, pattern));
    }

    @Override
    public AjaxResult<ShowDeviceVo> full(ShowDeviceBo bo) {
        return AjaxResult.success(this.showDeviceService.full(bo));
    }

    @Override
    public AjaxResult arcCode(ShowDeviceBo bo) {
        boolean result = this.showDeviceService.arcCode(bo);
        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult aboutByDeviceNum(String deviceNumber) {
        return this.showDeviceService.aboutByDeviceNum(deviceNumber);
    }

    @Override
    public AjaxResult qrcodeContent(ScreenBusinessBo bo) {
        // 拼一个地址给前端
        ScreenQrcodeContentVo screenQrcodeContentVo = ScreenQrcodeContentVo.trans2ScreenQrcodeContentVo(bo);
        ShowDevice byDeviceNum = showDeviceService.getByDeviceNum(bo.getDeviceNumber());
        if (byDeviceNum == null) {
            return AjaxResult.fail("can not query device by number");
        }
        screenQrcodeContentVo.setShowDeviceId(byDeviceNum.getShowDeviceId());
        screenQrcodeContentVo.setDevicePattern(byDeviceNum.getDevicePattern());

        // 设置缓存信息,content存放加密的设备号
        showDeviceService.cacheQrcodeContent(screenQrcodeContentVo);
        String content = EncryptUtil.encodeAes(bo.getDeviceNumber());
        String f = bo.getF();

        String qrcodeContent = mobileH5Url + "/#/scan?q=" + URLEncodeUtil.encode(content) + "&f=" + f;
        return AjaxResult.success(qrcodeContent);
    }

    @Override
    public AjaxResult qrDecode(String qrcodeContent) {
        String deviceNumber = EncryptUtil.decodeAes(qrcodeContent);
        ScreenQrcodeContentVo qrcodeContentVo = showDeviceService.getQrcodeContent(deviceNumber);
        if (qrcodeContentVo == null) {
            return AjaxResult.fail("未查询到二维码内容");
        }
        qrcodeContentVo.setScanResultType(ConstantsInteger.SCAN_RESULT_TYPE_SUCCESS);

        // 校验当前登录人有没有权限管理该设备
        if (qrcodeContentVo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            Integer userDataAuthority = userRoleService.getUserMaxDataAuthority(null);
            if (userDataAuthority == null || userDataAuthority <= ConstantsInteger.USER_DATA_AUTHORITY_GRADE) {
                qrcodeContentVo.setScanResultType(ConstantsInteger.SCAN_RESULT_TYPE_FAIL);
            }
        } else if (qrcodeContentVo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue())) {
            ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
            clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
            clazzConditionBoExt.setOrganizationId(qrcodeContentVo.getOrganizationId());
            clazzConditionBoExt.setIsDelete(StatusEnum.NOTDELETE.getCode());
            AjaxResult<List<Long>> classesIdsResult =
                baseDataService.getClassesIdsListByDataAuthority(clazzConditionBoExt);
            if (classesIdsResult.isFail() || CollectionUtils.isEmpty(classesIdsResult.getData())
                || !classesIdsResult.getData().contains(qrcodeContentVo.getSpaceInfoId())) {
                qrcodeContentVo.setScanResultType(ConstantsInteger.SCAN_RESULT_TYPE_FAIL);
            }
        }
        return AjaxResult.success(qrcodeContentVo);
    }

    /**
     * 查询设备统计
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/15 14:47
     */
    @Override
    public AjaxResult countDeviceByCondition(@RequestBody ShowDeviceListConditionBo conditionBo) {
        DeviceCountVo countVo = new DeviceCountVo();
        // 获取所有设备
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = this.listByCondition(conditionBo);
        if (ajaxResult.isFail()) {
            return ajaxResult;
        }
        Map<String, Object> map = (Map<String, Object>)ajaxResult.getData();
        List<ShowDeviceVo> allDeviceVos =
            JSONObject.parseArray(JSONObject.toJSONString(map.get("list")), ShowDeviceVo.class);
        // 过滤激活绑定地点的设备
        List<ShowDeviceVo> showDeviceVos =
            allDeviceVos.stream().filter(x -> DeviceActivationEnums.ENABLED.getVal().equals(x.getDeviceActivation()))
                .collect(Collectors.toList());
        // 算出总数、未激活总数（总数-激活总数）
        countVo.setCount(allDeviceVos.size());
        countVo.setNotActiveCount(allDeviceVos.size() - showDeviceVos.size());
        // 计算开关机及异常总数
        if (CollectionUtils.isNotEmpty(showDeviceVos)) {
            // 绑定地点的设备，通过webSocket 设备异常状态检测
            List<String> showDeviceNumbers = messageService.listOnLineDeviceNumber(conditionBo.getOrganizationId());
            showDeviceVos.forEach(showDeviceVo -> {
                if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                    showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
                } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                    showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
                }
            });
            countVo
                .setOpenCount(showDeviceVos.stream().filter(x -> DeviceStatusType.ON.getValue() == x.getDeviceStatus()
                    || DeviceStatusType.ON_ING.getValue() == x.getDeviceStatus()).count());
            countVo
                .setCloseCount(showDeviceVos.stream().filter(x -> DeviceStatusType.OFF.getValue() == x.getDeviceStatus()
                    || DeviceStatusType.OFF_ING.getValue() == x.getDeviceStatus()).count());
            countVo.setErrorCount(
                showDeviceVos.stream().filter(x -> DeviceStatusType.ERROR.getValue() == x.getDeviceStatus()).count());
        }

        return AjaxResult.success(countVo);
    }

    /**
     * 根据设备号，更新设备版本
     *
     * @param deviceNumBer
     * @param version
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/20 11:38
     */
    @Override
    public AjaxResult updateVersionByDeviceNumber(@RequestParam("deviceNumber") String deviceNumBer,
        @RequestParam("version") String version) {
        // 异步更新客户端版本
        ShowDeviceBo deviceBo = new ShowDeviceBo();
        deviceBo.setDeviceNumber(deviceNumBer);
        deviceBo.setClientVersion(version);
        deviceAsync.updateVersionByDeviceNumber(deviceBo);
        return AjaxResult.success();
    }

    /**
     * 根据设备id 设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @Override
    public AjaxResult setPosterRule(@RequestBody ShowDeviceBo showDeviceBo) {
        return showDeviceService.setPosterRule(showDeviceBo);
    }

    /**
     * 根据空间列表 批量设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @Override
    public AjaxResult setPosterRuleBatch(ShowDeviceBo showDeviceBo) {
        if (showDeviceBo.getOrganizationId() == null || CollectionUtils.isEmpty(showDeviceBo.getSpaceDeviceRelList())) {
            return AjaxResult.fail("请选择所属学校设备空间");
        }
        return showDeviceService.setPosterRuleBatch(showDeviceBo);
    }

    /**
     * 根据设备id 获取设备主动推送及设备标签列表
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @Override
    public AjaxResult getPosterRule(@RequestParam("showDeviceId") Long showDeviceId) {
        return showDeviceService.getPosterRule(showDeviceId);
    }

    // 运营 -- 修改横竖屏，全屏、消息初始化
    /**
     * 手动推送初始化云屏的接口
     *
     * @param showDeviceOperateBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/18 14:53
     */
    @Override
    public AjaxResult initDeviceByOrganizationIdAndDeviceNumbers(@RequestBody ShowDeviceOperateBo showDeviceOperateBo) {
        Long organizationId = showDeviceOperateBo.getOrganizationId();
        List<String> deviceNumbers = showDeviceOperateBo.getDeviceNumbers();

        MessageVo messageVo = new MessageVo();
        messageVo.setMessageBody(null);
        messageVo.setMessageType(MessageWsType.INIT.getValue());
        return messageService.sendMessageWsBySchool(organizationId, deviceNumbers, messageVo);
    }

    /**
     * 更新设备横竖版、是否全屏、海报播放间隔时长(通知app设备变更)
     *
     * @param showDeviceBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/18 15:12
     */
    @Override
    public AjaxResult updateDeviceByDeviceNumberWithSendApp(@RequestBody ShowDeviceBo showDeviceBo) {
        Long organizationId = showDeviceBo.getOrganizationId();
        String deviceNumber = showDeviceBo.getDeviceNumber();
        Integer devicePattern = showDeviceBo.getDevicePattern();
        Integer deviceFullType = showDeviceBo.getDeviceFullType();
        Integer devicePosterDuration = showDeviceBo.getDevicePosterDuration();
        String arcsoftFaceCode = showDeviceBo.getArcsoftFaceCode();
        String deviceName = showDeviceBo.getDeviceName();
        if ((null == deviceFullType && null == devicePattern && null == devicePosterDuration && null == arcsoftFaceCode
            && null == deviceName) || null == organizationId || StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("参数错误");
        }
        int messageWsType = 0;
        Object messageBody = null;
        // 横竖屏修改
        if (null != devicePattern) {
            boolean update = showDeviceService.updatePatternByDeviceNumber(deviceNumber, devicePattern);
            if (!update) {
                return AjaxResult.fail();
            }
            messageWsType = MessageWsType.DEVICE_PATTERN.getValue();
            messageBody = devicePattern;
        }
        // 是否全屏修改
        if (null != deviceFullType) {
            ShowDeviceVo full = showDeviceService.full(showDeviceBo);
            if (null == full) {
                return AjaxResult.fail();
            }
            messageWsType = MessageWsType.DEVICE_FULL.getValue();
            messageBody = deviceFullType;
        }
        // 海报时长设置
        if (null != devicePosterDuration) {
            boolean update = showDeviceService.updatePosterDurationByDeviceNumber(deviceNumber, devicePosterDuration);
            if (!update) {
                return AjaxResult.fail();
            }
            messageWsType = MessageWsType.DEVICE_POSTER_DURATION.getValue();
            messageBody = devicePosterDuration;
        }
        // 虹软人脸激活码
        if (arcsoftFaceCode != null) {
            boolean update = showDeviceService.updateArcsoftFaceCodeByDeviceNumber(deviceNumber, arcsoftFaceCode);
            if (!update) {
                return AjaxResult.fail();
            }
            messageWsType = MessageWsType.DEVICE_FACE_ARCSOFT_CODE.getValue();
            messageBody = arcsoftFaceCode;
        }
        // 设备名称修改
        if (deviceName != null) {
            boolean update = showDeviceService.updateDeviceNameByDeviceNumber(deviceNumber, deviceName);
            if (!update) {
                return AjaxResult.fail();
            }
            // 为了安卓端不修改，暂时使用设备横竖屏消息类型
            messageWsType = MessageWsType.MODIFY_SCENE.getValue();
            messageBody = deviceName;
        }
        // 发送通知
        List<String> deviceNumbers = new ArrayList<>();
        deviceNumbers.add(deviceNumber);
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        return messageService.sendMessageWs(organizationId, deviceNumbers, messageVo);
    }

    @Override
    public AjaxResult deviceOverview() {
        // 设备查询
        ShowDeviceOverviewVo showDeviceOverviewVo = new ShowDeviceOverviewVo();
        ShowDeviceListConditionBo condition = new ShowDeviceListConditionBo();
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("update_time desc");
        condition.setPageNo(SystemConstants.NO_PAGE);
        List<ShowDeviceVo> showDeviceVos = showDeviceService.getShowDeviceListByConditionSingle(condition);
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(showDeviceOverviewVo);
        }

        // 保留学校未删除的设备
        List<Long> organizationIds =
            showDeviceVos.stream().map(ShowDeviceVo::getOrganizationId).collect(Collectors.toList());
        List<OrganizationVo> enableOrganizationVoList = baseDataService.getOrganizationVoList(organizationIds);
        List<Long> enableOrganizationIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(enableOrganizationVoList)) {
            enableOrganizationIds =
                enableOrganizationVoList.stream().map(OrganizationVo::getId).collect(Collectors.toList());
        }
        final List<Long> finalEnableOrganizationIds = enableOrganizationIds;
        showDeviceVos = showDeviceVos.stream()
            .filter(showDeviceVo -> finalEnableOrganizationIds.contains(showDeviceVo.getOrganizationId()))
            .collect(Collectors.toList());

        // 计算总览数量
        Long totalNum = (long)showDeviceVos.size();
        Long activeNum = showDeviceVos.stream()
            .filter(showDeviceVo -> showDeviceVo.getDeviceActivation().equals(DeviceActivationEnums.ENABLED.getVal()))
            .count();
        Long notActiveNum = showDeviceVos.stream()
            .filter(showDeviceVo -> showDeviceVo.getDeviceActivation().equals(DeviceActivationEnums.DISABLED.getVal()))
            .count();
        Long useOrganizationNum =
            showDeviceVos.stream().map(showDeviceVo -> showDeviceVo.getOrganizationId()).distinct().count();
        showDeviceOverviewVo.setShowDeviceTotalNum(totalNum);
        showDeviceOverviewVo.setShowDeviceActiveNum(activeNum);
        showDeviceOverviewVo.setShowDeviceNotActiveNum(notActiveNum);
        showDeviceOverviewVo.setUseOrganizationNum(useOrganizationNum);

        List<String> showDeviceNumbers = messageService.listAllOnLineDeviceNumber();
        showDeviceVos.forEach(showDeviceVo -> {
            if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            }
        });
        Long onNum = showDeviceVos.stream()
            .filter(showDeviceVo -> DeviceStatusType.ON.getValue() == showDeviceVo.getDeviceStatus()
                || DeviceStatusType.ON_ING.getValue() == showDeviceVo.getDeviceStatus())
            .count();
        Long offNum = showDeviceVos.stream()
            .filter(showDeviceVo -> DeviceStatusType.OFF.getValue() == showDeviceVo.getDeviceStatus()
                || DeviceStatusType.OFF_ING.getValue() == showDeviceVo.getDeviceStatus())
            .count();
        Long errorNum = showDeviceVos.stream()
            .filter(showDeviceVo -> showDeviceVo.getDeviceActivation().equals(DeviceActivationEnums.ENABLED.getVal()))
            .filter(showDeviceVo -> DeviceStatusType.ERROR.getValue() == showDeviceVo.getDeviceStatus()).count();
        showDeviceOverviewVo.setShowDeviceOnNum(onNum);
        showDeviceOverviewVo.setShowDeviceOffNum(offNum);
        showDeviceOverviewVo.setShowDeviceErrorNum(errorNum);

        // 版本数据封装
        Map<String, Long> clientVersionCountMap =
            showDeviceVos.stream().filter(showDeviceVo -> StringUtils.isNotBlank(showDeviceVo.getClientVersion()))
                .collect(Collectors.groupingBy(ShowDeviceVo::getClientVersion, Collectors.counting()));
        List<ShowDeviceVo> clientVersionList = Lists.newArrayList();
        for (String clientVersion : clientVersionCountMap.keySet()) {
            if (StringUtils.isBlank(clientVersion)) {
                continue;
            }
            ShowDeviceVo showDeviceVo = new ShowDeviceVo();
            showDeviceVo.setClientVersion(clientVersion);
            showDeviceVo.setClientVersionNum(clientVersionCountMap.get(clientVersion));
            clientVersionList.add(showDeviceVo);
        }
        clientVersionList = clientVersionList.stream().sorted(Comparator.comparing(ShowDeviceVo::getClientVersion))
            .collect(Collectors.toList());
        showDeviceOverviewVo.setClientVersionList(clientVersionList);
        return AjaxResult.success(showDeviceOverviewVo);
    }

    @Override
    public AjaxResult restartApp(@RequestBody ShowDeviceBo showDeviceBo) {
        if (showDeviceBo.getOrganizationId() == null || StringUtils.isBlank(showDeviceBo.getDeviceNumber())) {
            return AjaxResult.fail("参数错误");
        }
        List deviceNumbers = Lists.newArrayList();
        deviceNumbers.add(showDeviceBo.getDeviceNumber());
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(MessageWsType.APP_RESTART.getValue());
        AjaxResult ajaxResult =
            messageService.sendMessageWs(showDeviceBo.getOrganizationId(), deviceNumbers, messageVo);
        return ajaxResult;
    }

    /**
     * 根据监管教育局id查询绑定了地点的设备列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/5 14:51
     **/
    @Override
    public AjaxResult listShowDeviceDataByParentOrganizationId(@RequestBody ShowDeviceListConditionBo condition) {
        // 1、获取全部组织信息
        List<OrganizationVo> organizationVos =
            baseDataService.getSuperviseOrganizationList(condition.getParentOrganizationId());
        if (CollectionUtils.isEmpty(organizationVos)) {
            return AjaxResult.success(new ArrayList<>(), ConstantsLong.NUM_0, condition.getPageNo(),
                condition.getPageSize());
        }
        Map<Long, OrganizationVo> organizationVoMap =
            organizationVos.stream().collect(Collectors.toMap(OrganizationVo::getId, x -> x, (v1, v2) -> v1));
        List<Long> organizationIds = organizationVos.stream().map(OrganizationVo::getId).collect(Collectors.toList());

        // 2、查询全部设备
        ShowDeviceListConditionBo conditionBo = new ShowDeviceListConditionBo();
        conditionBo.setOrganizationIds(organizationIds);
        conditionBo.setOrganizationId(condition.getOrganizationId());
        conditionBo.setSpaceInfoId(condition.getSpaceInfoId());
        conditionBo.setSpaceGroupId(condition.getSpaceGroupId());
        conditionBo.setSpaceGroupUseType(condition.getSpaceGroupUseType());
        // 查询监管中的设备
        conditionBo.setSuperviseState(SuperviseStateType.MONITOR_YES.getValue());
        List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceBindByCondition(conditionBo);
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(new ArrayList<>(), ConstantsLong.NUM_0, condition.getPageNo(),
                    condition.getPageSize());
        }

        // 3、反向查询地点（排除没有地点的设备）
        List<SpaceInfoVo> spaceInfoVoList = getAllSpaceInfoListByDeviceList(showDeviceVos);
        Map<String,
            SpaceInfoVo> spaceInfoVoMap = spaceInfoVoList.stream()
                .collect(Collectors.toMap(
                    x -> x.getSpaceInfoId() + "-" + x.getSpaceGroupId() + "-" + x.getSpaceGroupUseType(), x -> x,
                    (v1, v2) -> v1));
        // 判断地点列表是否为空
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(spaceInfoVoList)) {
            List<SpaceInfoBo> spaceInfoBos = spaceInfoVoList.stream().map(x -> {
                SpaceInfoBo bo = new SpaceInfoBo();
                BeanUtils.copyProperties(x, bo);
                return bo;
            }).collect(Collectors.toList());
            // 仅查询地点还未被删除的地点下的设备
            conditionBo.setSpaceInfoBos(spaceInfoBos);
        } else {
            return AjaxResult.success(new ArrayList<>(), ConstantsLong.NUM_0, condition.getPageNo(),
                    condition.getPageSize());
        }

        // 4、查询分页的符合条件的设备列表
        PageInfo<ShowDeviceVo> pageInfo = new PageInfo<>();
        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        }
        pageInfo = new PageInfo<>(showDeviceService.listShowDeviceBindByCondition(conditionBo));
        showDeviceVos = JSONObject.parseArray(JSONObject.toJSONString(pageInfo.getList()), ShowDeviceVo.class);

        // 5、设备异常状态检测
        List<String> showDeviceNumbers = Lists.newArrayList();
        if (condition.getOrganizationId() != null && condition.getOrganizationId() != ConstantsLong.NUM_0) {
            List<String> showDeviceNumberList = messageService.listOnLineDeviceNumber(condition.getOrganizationId());
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(showDeviceNumberList)) {
                showDeviceNumbers.addAll(showDeviceNumberList);
            }
        } else {
            List<String> showDeviceNumberList = messageService.listOnLineDeviceNumberBatch(organizationIds);
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(showDeviceNumberList)) {
                showDeviceNumbers.addAll(showDeviceNumberList);
            }
        }

        // 6、循环设置返回值
        showDeviceVos.stream().forEach(showDeviceVo -> {
            if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(showDeviceNumbers)) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            }
            String key = showDeviceVo.getSpaceInfoId() + "-" + showDeviceVo.getSpaceGroupId() + "-"
                + showDeviceVo.getSpaceGroupUseType();
            if (spaceInfoVoMap.containsKey(key)) {
                showDeviceVo.setSpaceInfoName(spaceInfoVoMap.get(key).getSpaceInfoName());
            }
            if (organizationVoMap.containsKey(showDeviceVo.getOrganizationId())) {
                showDeviceVo.setOrganizationName(organizationVoMap.get(showDeviceVo.getOrganizationId()).getName());
            }
        });

        return AjaxResult.success(showDeviceVos, pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    /**
     * 根据设备查询地点
     *
     * @param showDeviceVos
     * @return java.util.List<com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo>
     * <AUTHOR>
     * @date 2024/8/2 15:44
     **/
    private List<SpaceInfoVo> getAllSpaceInfoListByDeviceList(List<ShowDeviceVo> showDeviceVos) {
        List<SpaceInfoVo> spaceInfoVoList = new ArrayList<>();
        // 行政教室
        List<Long> classesIds = new ArrayList<>();
        // 非行政教室
        List<Long> spaceInfoIds = new ArrayList<>();
        List<ShowDeviceVo> showDeviceVosXz = showDeviceVos.stream()
            .filter(s -> SpaceGroupUseType.XZ.getValue() == s.getSpaceGroupUseType()).collect(Collectors.toList());
        List<ShowDeviceVo> showDeviceVosNotXz = showDeviceVos.stream()
            .filter(s -> SpaceGroupUseType.NOT_XZ.getValue() == s.getSpaceGroupUseType()).collect(Collectors.toList());
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(showDeviceVosXz)) {
            List<Long> xzSpaceInfoIds =
                showDeviceVosXz.stream().map(ShowDeviceVo::getSpaceInfoId).collect(Collectors.toList());
            classesIds.addAll(xzSpaceInfoIds);
        }
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(showDeviceVosNotXz)) {
            List<Long> notXzSpaceInfoIds =
                showDeviceVosNotXz.stream().map(ShowDeviceVo::getSpaceInfoId).collect(Collectors.toList());
            spaceInfoIds.addAll(notXzSpaceInfoIds);
        }
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(classesIds)) {
            ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
            clazzConditionBo.setIds(classesIds);
            AjaxResult classesResult = baseDataService.getClassesListByClassesIds(clazzConditionBo);
            if (classesResult.isSuccess()) {
                Map classesResultMap = JSON.parseObject(JSON.toJSONString(classesResult.getData()), Map.class);
                List<ClazzInfoVo> clazzInfoVos =
                    JSONArray.parseArray(JSONArray.toJSONString(classesResultMap.get("list")), ClazzInfoVo.class);
                for (ClazzInfoVo clazzInfoVo : clazzInfoVos) {
                    SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
                    String grade = SchoolYearUtil.gradeMap.get(clazzInfoVo.getGrade());
                    String spaceName = grade.concat(clazzInfoVo.getClassesName()).concat("班");
                    spaceInfoVo.setSpaceInfoId(clazzInfoVo.getId());
                    spaceInfoVo.setSpaceInfoName(spaceName);
                    spaceInfoVo.setClassesName(clazzInfoVo.getClassesName());
                    spaceInfoVo.setRemark(spaceName.concat("教室"));
                    spaceInfoVo.setClassesId(clazzInfoVo.getId());
                    spaceInfoVo.setCampusId(clazzInfoVo.getCampusId());
                    spaceInfoVo.setCampusName(clazzInfoVo.getCampusName());
                    spaceInfoVo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
                    spaceInfoVo.setGrade(clazzInfoVo.getGrade());
                    spaceInfoVo.setGradeName(clazzInfoVo.getGradeName());
                    spaceInfoVo.setEnrollmentYear(clazzInfoVo.getEnrollmentYear());
                    spaceInfoVo.setSpaceGroupId(ConstantsConfig.SPACE_GROUP_ID_PTJS);
                    spaceInfoVo.setOrganizationId(clazzInfoVo.getOrganizationId());
                    spaceInfoVoList.add(spaceInfoVo);
                }
            }

        }
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(spaceInfoIds)) {
            SpaceInfoListConditionBo spaceInfoListConditionBo = new SpaceInfoListConditionBo();
            spaceInfoListConditionBo.setSpaceInfoIds(spaceInfoIds);
            List<SpaceInfoVo> spaceInfoVos = spaceInfoService.getSpaceInfoListByCondition(spaceInfoListConditionBo);
            spaceInfoVoList.addAll(spaceInfoVos);
        }
        return spaceInfoVoList;
    }

    @Override
    @ApiOperation(value = "根据设备号批量开关机", httpMethod = "POST")
    public AjaxResult switchOperateBatch(ShowDeviceOperateListBo operateListBo) {
        Integer deviceStatusType = operateListBo.getDeviceStatusType();
        List<ShowDeviceOperateBo> operateDeviceList = operateListBo.getShowDevices();
        if (CollectionUtils.isEmpty(operateDeviceList) || deviceStatusType == null) {
            return AjaxResult.fail();
        }

        try {
            if (deviceStatusType.equals(DeviceStatusType.ON.getValue())) {
                for (ShowDeviceOperateBo operateDeviceBo : operateDeviceList) {
                    showDeviceService.updateShowDeviceByDeviceNumbers(DeviceStatusType.ON_ING.getValue(),
                        operateDeviceBo.getOrganizationId(), Lists.newArrayList(operateDeviceBo.getDeviceNumber()));
                    // publish event
                    applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
                        MessageWsType.DEVICE_OPEN.getValue(), operateDeviceBo.getOrganizationId(),
                        Lists.newArrayList(operateDeviceBo.getDeviceNumber()), null));
                }
            }
            if (deviceStatusType.equals(DeviceStatusType.OFF.getValue())) {
                for (ShowDeviceOperateBo operateDeviceBo : operateDeviceList) {
                    showDeviceService.updateShowDeviceByDeviceNumbers(DeviceStatusType.OFF_ING.getValue(),
                        operateDeviceBo.getOrganizationId(), Lists.newArrayList(operateDeviceBo.getDeviceNumber()));
                    // publish event
                    applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
                        MessageWsType.DEVICE_CLOSE.getValue(), operateDeviceBo.getOrganizationId(),
                        Lists.newArrayList(operateDeviceBo.getDeviceNumber()), null));
                }
            }
            return AjaxResult.success();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("switchOperate error:", e);
            }
            return AjaxResult.fail();
        }
    }

    @Override
    public AjaxResult changeFaceMod(ShowDeviceOperateBo showDeviceOperateBo) {
        Long organizationId = showDeviceOperateBo.getOrganizationId();
        Integer faceModType = showDeviceOperateBo.getFaceModType();
        List<String> deviceNumbers = showDeviceOperateBo.getDeviceNumbers();
        if (organizationId == null || CollectionUtils.isEmpty(deviceNumbers) || faceModType == null) {
            return AjaxResult.fail("参数错误");
        }

        try {
            if (faceModType.equals(FaceModType.SCHOOL.getValue())) {
                showDeviceService.updateShowDeviceFaceModByDeviceNumbers(FaceModType.SCHOOL.getValue(), organizationId,
                    deviceNumbers);
                // publish event
                applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(MessageWsType.INIT.getValue(),
                    organizationId, deviceNumbers, null));
            }
            if (faceModType.equals(FaceModType.CLASSES.getValue())) {
                showDeviceService.updateShowDeviceFaceModByDeviceNumbers(FaceModType.CLASSES.getValue(), organizationId,
                    deviceNumbers);
                // publish event
                applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(MessageWsType.INIT.getValue(),
                    organizationId, deviceNumbers, null));
            }
            return AjaxResult.success();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("changeFaceMod error:", e);
            }
            return AjaxResult.fail();
        }
    }

    @Override
    public AjaxResult changeSuperviseState(ShowDeviceOperateBo showDeviceOperateBo) {
        Long organizationId = showDeviceOperateBo.getOrganizationId();
        Integer superviseState = showDeviceOperateBo.getSuperviseState();
        List<String> deviceNumbers = showDeviceOperateBo.getDeviceNumbers();
        if (organizationId == null || CollectionUtils.isEmpty(deviceNumbers) || superviseState == null) {
            return AjaxResult.fail("参数错误");
        }

        try {
            if (superviseState.equals(SuperviseStateType.MONITOR_YES.getValue())) {
                showDeviceService.updateShowDeviceSuperviseStateByDeviceNumbers(SuperviseStateType.MONITOR_YES.getValue(),
                    organizationId, deviceNumbers);
                // 监管屏不推送立即生效
            }
            if (superviseState.equals(SuperviseStateType.MONITOR_NO.getValue())) {
                showDeviceService.updateShowDeviceSuperviseStateByDeviceNumbers(SuperviseStateType.MONITOR_NO.getValue(),
                    organizationId, deviceNumbers);
                // 监管屏不推送立即生效
            }
            return AjaxResult.success();
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("changeSuperviseState error:", e);
            }
            return AjaxResult.fail();
        }
    }
}
