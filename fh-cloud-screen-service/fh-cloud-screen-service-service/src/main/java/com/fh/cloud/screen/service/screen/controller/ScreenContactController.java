package com.fh.cloud.screen.service.screen.controller;

import java.util.List;

import com.light.user.valid.api.ValidApi;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenContactApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContactDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContactVo;
import com.fh.cloud.screen.service.screen.service.IScreenContactService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;

/**
 * 云屏产品咨询收集联系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
@RestController
@Validated
public class ScreenContactController implements ScreenContactApi {

    @Autowired
    private IScreenContactService screenContactService;
    @Resource
    private ValidApi validApi;

    /**
     * 查询云屏产品咨询收集联系表分页列表
     * 
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @Override
    public AjaxResult<PageInfo<ScreenContactVo>>
        getScreenContactPageListByCondition(@RequestBody ScreenContactConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenContactVo> pageInfo =
            new PageInfo<>(screenContactService.getScreenContactListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询云屏产品咨询收集联系表列表
     * 
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @Override
    public AjaxResult<List<ScreenContactVo>>
        getScreenContactListByCondition(@RequestBody ScreenContactConditionBo condition) {
        List<ScreenContactVo> list = screenContactService.getScreenContactListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增云屏产品咨询收集联系表
     * 
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @Override
    public AjaxResult addScreenContact(@Validated @RequestBody ScreenContactBo screenContactBo) {
        // 校验图形验证码
        if (StringUtils.isBlank(screenContactBo.getImageCode())) {
            return AjaxResult.fail("请输入图形验证码");
        }
        AjaxResult result = validApi.validCaptchaCode(screenContactBo.getUuid(), screenContactBo.getImageCode());
        if (result.isFail()) {
            return AjaxResult.fail("验证码错误");
        }
        return screenContactService.addScreenContact(screenContactBo);
    }

    /**
     * 修改云屏产品咨询收集联系表
     * 
     * @param screenContactBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @Override
    public AjaxResult updateScreenContact(@Validated @RequestBody ScreenContactBo screenContactBo) {
        if (null == screenContactBo.getId()) {
            return AjaxResult.fail("云屏产品咨询收集联系表id不能为空");
        }
        return screenContactService.updateScreenContact(screenContactBo);
    }

    /**
     * 查询云屏产品咨询收集联系表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @Override
    public AjaxResult<ScreenContactVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("云屏产品咨询收集联系表id不能为空");
        }
        ScreenContactConditionBo condition = new ScreenContactConditionBo();
        condition.setId(id);
        ScreenContactVo vo = screenContactService.getScreenContactByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除云屏产品咨询收集联系表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-07-22 10:52:57
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenContactDto screenContactDto = new ScreenContactDto();
        screenContactDto.setId(id);
        screenContactDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenContactService.updateById(screenContactDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
