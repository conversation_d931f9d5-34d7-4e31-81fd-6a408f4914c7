package com.fh.cloud.screen.service.er.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考场_考试计划里面一次考试的学生
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("er_exam_info_student")
public class ExamInfoStudentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "exam_info_student_id", type = IdType.AUTO)
	private Long examInfoStudentId;

	/**
	 * 考试科目表id
	 */
	@TableField("exam_info_subject_id")
	private Long examInfoSubjectId;

	/**
	 * 考试id
	 */
	@TableField("exam_info_id")
	private Long examInfoId;

	/**
	 * 考试计划id
	 */
	@TableField("exam_plan_id")
	private Long examPlanId;

	/**
	 * 学生的user_oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 学生的姓名
	 */
	@TableField("real_name")
	private String realName;

	/**
	 * 用户来源类型：1校内，2校外
	 */
	@TableField("user_from_type")
	private Integer userFromType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 打卡时间
	 */
	@TableField("attendance_time")
	private Date attendanceTime;

	/**
	 * 打卡状态，1：未签到，2：已签到[正常]，3已签到[迟到]
	 */
	@TableField("attendance_status")
	private Integer attendanceStatus;

	/**
	 * 班级id
	 */
	@TableField("classes_id")
	private Long classesId;

	/**
	 * 年级
	 */
	@TableField("grade")
	private String grade;

	/**
	 * 准考证号
	 */
	@TableField("registration_number")
	private String registrationNumber;

	/**
	 * 座位号
	 */
	@TableField("seat_number")
	private String seatNumber;
}
