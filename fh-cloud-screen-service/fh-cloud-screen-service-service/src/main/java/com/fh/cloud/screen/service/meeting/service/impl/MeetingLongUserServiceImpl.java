package com.fh.cloud.screen.service.meeting.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongUserDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongUserVo;
import com.fh.cloud.screen.service.meeting.mapper.MeetingLongUserMapper;
import com.fh.cloud.screen.service.meeting.service.IMeetingLongUserService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * 长期预约表人员表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@Service
public class MeetingLongUserServiceImpl extends ServiceImpl<MeetingLongUserMapper, MeetingLongUserDto> implements IMeetingLongUserService {

	@Resource
	private MeetingLongUserMapper meetingLongUserMapper;
	
    @Override
	public List<MeetingLongUserVo> getMeetingLongUserListByCondition(MeetingLongUserConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return meetingLongUserMapper.getMeetingLongUserListByCondition(condition);
	}

	@Override
	public AjaxResult addMeetingLongUser(MeetingLongUserBo meetingLongUserBo) {
		MeetingLongUserDto meetingLongUser = new MeetingLongUserDto();
		BeanUtils.copyProperties(meetingLongUserBo, meetingLongUser);
		meetingLongUser.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(meetingLongUser)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateMeetingLongUser(MeetingLongUserBo meetingLongUserBo) {
		MeetingLongUserDto meetingLongUser = new MeetingLongUserDto();
		BeanUtils.copyProperties(meetingLongUserBo, meetingLongUser);
		if(updateById(meetingLongUser)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public MeetingLongUserVo getMeetingLongUserByCondition(MeetingLongUserConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		MeetingLongUserVo vo = meetingLongUserMapper.getMeetingLongUserByCondition(condition);
		return vo;
	}

}