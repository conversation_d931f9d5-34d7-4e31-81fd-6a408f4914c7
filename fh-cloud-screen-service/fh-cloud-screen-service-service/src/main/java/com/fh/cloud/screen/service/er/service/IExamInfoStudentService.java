package com.fh.cloud.screen.service.er.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoStudentDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 考场_考试计划里面一次考试的学生接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface IExamInfoStudentService extends IService<ExamInfoStudentDto> {

    List<ExamInfoStudentVo> getExamInfoStudentListByCondition(ExamInfoStudentConditionBo condition);

    AjaxResult addExamInfoStudent(ExamInfoStudentBo examInfoStudentBo);

    AjaxResult updateExamInfoStudent(ExamInfoStudentBo examInfoStudentBo);

    ExamInfoStudentVo getDetail(Long id);

    /**
     * 批量添加
     * 
     * @param examInfoStudentBos
     * @return
     */
    AjaxResult addExamInfoStudentBatch(List<ExamInfoStudentBo> examInfoStudentBos);

    /**
     * 批量添加通过xml
     *
     * @param examInfoStudentBos
     * @return
     */
    AjaxResult addExamInfoStudentBatchByXML(List<ExamInfoStudentBo> examInfoStudentBos);

    /**
     * 批量更新
     * 
     * @param examInfoStudentBos
     * @return
     */
    @Deprecated
    AjaxResult updateExamInfoStudentBatch(List<ExamInfoStudentBo> examInfoStudentBos);

    /**
     * 批量删除
     *
     * @param examInfoId
     * @return
     */
    AjaxResult deleteExamInfoStudentBatch(Long examInfoId);

}
