package com.fh.cloud.screen.service.label.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo;

/**
 * 标签海报关联表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
public interface LabelLibraryRelMapper extends BaseMapper<LabelLibraryRelDto> {

	List<LabelLibraryRelVo> getLabelLibraryRelListByCondition(LabelLibraryRelConditionBo condition);

	/**
	 * 自定义sql查询【给定海报id集合】里面符合【给定节日id】的海报
	 *
	 * @param condition the condition
	 * @return label library rel list by condition of label
	 * <AUTHOR>
	 * @date 2023 -03-06 11:26:19
	 */
	List<LabelLibraryRelVo> getLabelLibraryRelListByConditionOfLabel(LabelLibraryRelConditionBo condition);
}
