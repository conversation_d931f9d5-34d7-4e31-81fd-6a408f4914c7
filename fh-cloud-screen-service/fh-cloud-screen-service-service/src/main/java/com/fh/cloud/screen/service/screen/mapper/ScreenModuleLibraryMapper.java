package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryStatisticsVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模块库表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface ScreenModuleLibraryMapper extends BaseMapper<ScreenModuleLibrary> {

    List<ScreenModuleLibraryVo> getScreenModuleLibraryListByCondition(ScreenModuleLibraryListConditionBo condition);

    /**
     * 海报条件查询主题列表，只查询关联了标签的海报主题
     *
     * @param conditionBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo>
     * <AUTHOR>
     * @date 2023/4/4 16:57
     */
    List<ScreenModuleLibraryVo> getPosterList(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 查询海报统计信息
     *
     * @param conditionBo the condition bo
     * @return the config label screen module library statistics
     * <AUTHOR>
     * @date 2023 /4/4 16:57
     */
    ScreenModuleLibraryStatisticsVo
        getConfigLabelScreenModuleLibraryStatistics(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 海报条件查询主题列表，只根据已选择的海报查询，忽略是否关联标签
     *
     * @param conditionBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo>
     * <AUTHOR>
     * @date 2023/4/4 16:57
     */
    List<ScreenModuleLibraryVo> getPosterListSel(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 获取未配置标签的海报库主题列表（校本海报都有标签）
     *
     * @param conditionBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo>
     * <AUTHOR>
     * @date 2023/5/24 14:03
     */
    List<ScreenModuleLibraryVo> getNotConfigLabelScreenModuleLibrary(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 获取未配置标签的海报库统计信息
     *
     * @param conditionBo the condition bo
     * @return the not config label screen module library statistics
     * <AUTHOR>
     * @date 2023 /5/24 14:03
     */
    ScreenModuleLibraryStatisticsVo
        getNotConfigLabelScreenModuleLibraryStatistics(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 获取海报库主题数量
     *
     * @param conditionBo the condition bo
     * @return poster school num
     * <AUTHOR>
     * @date 2024 -07-01 15:18:21
     */
    List<ScreenModuleLibraryNumVo> getPosterSchoolNum(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 获取海报库海报数
     *
     * @param conditionBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo>
     * <AUTHOR>
     * @date 2024/8/2 14:22
     **/
    List<ScreenModuleLibraryNumVo> getPosterMediaNum(ScreenModuleLibraryListConditionBo conditionBo);
}
