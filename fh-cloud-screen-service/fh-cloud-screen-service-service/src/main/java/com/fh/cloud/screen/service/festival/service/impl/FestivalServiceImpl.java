package com.fh.cloud.screen.service.festival.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.FestivalEnum;
import com.fh.cloud.screen.service.enums.LabelEnums;
import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.fh.cloud.screen.service.label.service.ILabelFestivalRelService;
import com.fh.cloud.screen.service.label.service.ILabelService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.google.common.collect.Lists;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.festival.entity.dto.FestivalDto;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalBo;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;
import com.fh.cloud.screen.service.festival.service.IFestivalService;
import com.fh.cloud.screen.service.festival.mapper.FestivalMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 节日表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
@Service
public class FestivalServiceImpl extends ServiceImpl<FestivalMapper, FestivalDto> implements IFestivalService {

    @Resource
    private FestivalMapper festivalMapper;
    @Resource
    private ILabelFestivalRelService labelFestivalRelService;
    @Resource
    private ILabelService labelService;

    @Override
    public List<FestivalVo> getFestivalListByCondition(FestivalConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<FestivalVo> festivalVos = festivalMapper.getFestivalListByCondition(condition);
        if (!StatusEnum.YES.getCode().equals(condition.getQueryLibraryId())) {
            return festivalVos;
        }

        // 获取节日节点 关联的标签关联的海报主题列表
        List<String> festivalCodes = festivalVos.stream().map(FestivalVo::getFestivalCode).collect(Collectors.toList());
        List<FestivalVo> festivalLibraryRelList = festivalMapper.getFestivalLibraryRelList(festivalCodes);
        if (CollectionUtils.isEmpty(festivalLibraryRelList)) {
            return festivalVos;
        }
        Map<String, List<FestivalVo>> map =
            festivalLibraryRelList.stream().collect(Collectors.groupingBy(FestivalVo::getFestivalCode));
        festivalVos.forEach(x -> {
            List<FestivalVo> vos = map.get(x.getFestivalCode());
            if (CollectionUtils.isNotEmpty(vos)) {
                x.setScreenModuleLibraryIds(
                    vos.stream().map(y -> y.getScreenModuleLibraryId().toString()).collect(Collectors.joining(",")));
            }
        });
        return festivalVos;
    }

    @Override
    public AjaxResult addFestival(FestivalBo festivalBo) {
        // 重名校验--海报订阅版本改为全部节日、节点校验
        List<FestivalVo> allCustomFestival = getFestivalListByCondition(new FestivalConditionBo());
        if (CollectionUtils.isNotEmpty(allCustomFestival)) {
            for (FestivalVo festivalVo : allCustomFestival) {
                if (festivalVo.getName().equals(festivalBo.getName())) {
                    return AjaxResult.fail("名称重复");
                }
            }
        }

        FestivalDto festival = new FestivalDto();
        BeanUtils.copyProperties(festivalBo, festival);
        festival.setFestivalCode(UuidUtils.generateUuid().replace("-", ""));
        festival.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(festival)) {
            // 同步新增节日对应标签
            LabelVo posterTypeLabel = labelService.getPosterTypeLabel();
            LabelDto labelDto = new LabelDto();
            labelDto.setParentLabelId(posterTypeLabel.getLabelId());
            labelDto.setLevel(LabelEnums.LEVEL_TWO.getCode());
            labelDto.setLabelName(festival.getName());
            labelDto.setPosterType(LabelEnums.POSTER_FESTIVAL.getCode());
            labelDto.setLabelSort(ConstantsInteger.NUM_1);
            if (CollectionUtils.isNotEmpty(posterTypeLabel.getChildrenLabels())) {
                labelDto.setLabelSort(posterTypeLabel.getLabelSort() - 1);
            }
            labelService.save(labelDto);
            LabelFestivalRelDto labelFestivalRelDto = new LabelFestivalRelDto();
            labelFestivalRelDto.setFestivalCode(festival.getFestivalCode());
            labelFestivalRelDto.setLabelId(labelDto.getLabelId());
            labelFestivalRelService.save(labelFestivalRelDto);
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateFestival(FestivalBo festivalBo) {
        FestivalDto byId = this.getById(festivalBo.getFestivalId());
        if (byId.getName().equals(festivalBo.getName())) {
            festivalBo.setName(null);
        } else {
            List<FestivalVo> allCustomFestival = getFestivalListByCondition(new FestivalConditionBo());
            if (CollectionUtils.isNotEmpty(allCustomFestival)) {
                for (FestivalVo festivalVo : getAllCustomFestival()) {
                    if (festivalVo.getName().equals(festivalBo.getName())) {
                        return AjaxResult.fail("名称重复");
                    }
                }
            }

        }
        FestivalDto festival = new FestivalDto();
        BeanUtils.copyProperties(festivalBo, festival);
        if (updateById(festival)) {
            // 同步修改标签名称
            LambdaQueryWrapper<LabelFestivalRelDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(LabelFestivalRelDto::getFestivalCode, byId.getFestivalCode());
            lqw.eq(LabelFestivalRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            LabelFestivalRelDto one = labelFestivalRelService.getOne(lqw);
            if(one != null){
                LabelDto labelDto = new LabelDto();
                labelDto.setLabelId(one.getLabelId());
                labelDto.setLabelName(festival.getName());
                labelService.updateById(labelDto);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public FestivalVo getDetail(Long festivalId) {
        FestivalConditionBo condition = new FestivalConditionBo();
        condition.setFestivalId(festivalId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<FestivalVo> list = festivalMapper.getFestivalListByCondition(condition);
        FestivalVo vo = new FestivalVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult delete(Long festivalId) {
        FestivalDto byId = this.getById(festivalId);
        if (null == byId) {
            return AjaxResult.fail("节日节点不存在");
        }
        FestivalDto festivalDto = new FestivalDto();
        festivalDto.setFestivalId(festivalId);
        festivalDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (this.updateById(festivalDto)) {
            // 同步删除 节点标签绑定关系
            LambdaQueryWrapper<LabelFestivalRelDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(LabelFestivalRelDto::getFestivalCode, byId.getFestivalCode());
            lqw.eq(LabelFestivalRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            LabelFestivalRelDto one = labelFestivalRelService.getOne(lqw);
            if(one != null){
                one.setIsDelete(StatusEnum.ISDELETE.getCode());
                labelFestivalRelService.updateById(one);
                LabelDto labelDto = new LabelDto();
                labelDto.setLabelId(one.getLabelId());
                labelDto.setIsDelete(StatusEnum.ISDELETE.getCode());
                labelService.updateById(labelDto);
            }
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 获取 {date}那一天的节日，包括节日类型属性
     *
     * @param dateString the date string
     * @return festival list by date
     * <AUTHOR>
     * @date 2023 -03-03 15:20:53
     */
    @Override
    public List<FestivalVo> getFestivalDurationListByDate(String dateString) {
        List<FestivalVo> festivalVos = Lists.newArrayList();
        Date date = null;
        if (StringUtils.isBlank(dateString)) {
            dateString = DateKit.getCurrentDay();
        }
        date = DateKit.string2Date(dateString, "yyyy-MM-dd");

        LambdaQueryWrapper<FestivalDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(FestivalDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.le(FestivalDto::getStartDay, date);
        lqw.ge(FestivalDto::getEndDay, date);

        List<FestivalDto> festivalDtos = this.list(lqw);
        if (CollectionUtils.isEmpty(festivalDtos)) {
            return festivalVos;
        }
        festivalVos = festivalDtos.stream().map(festivalDto -> {
            FestivalVo festivalVo = new FestivalVo();
            BeanUtils.copyProperties(festivalDto, festivalVo);
            return festivalVo;
        }).distinct().collect(Collectors.toList());
        return festivalVos;
    }

    /**
     * 获取所有未删除自定义节点
     *
     * @return java.util.List<com.fh.cloud.screen.service.festival.entity.vo.FestivalVo>
     * <AUTHOR>
     * @date 2023/3/31 9:37
     */
    private List<FestivalVo> getAllCustomFestival() {
        FestivalConditionBo condition = new FestivalConditionBo();
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setType(FestivalEnum.CUSTOMIZE.getCode());
        return festivalMapper.getFestivalListByCondition(condition);
    }
}