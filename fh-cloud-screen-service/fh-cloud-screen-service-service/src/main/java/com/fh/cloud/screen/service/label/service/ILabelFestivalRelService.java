package com.fh.cloud.screen.service.label.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 标签节日关联表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
public interface ILabelFestivalRelService extends IService<LabelFestivalRelDto> {

    List<LabelFestivalRelVo> getLabelFestivalRelListByCondition(LabelFestivalRelConditionBo condition);

	AjaxResult addLabelFestivalRel(LabelFestivalRelBo labelFestivalRelBo);

	AjaxResult updateLabelFestivalRel(LabelFestivalRelBo labelFestivalRelBo);

	LabelFestivalRelVo getDetail(Long id);

}

