package com.fh.cloud.screen.service.attendance.controller;

import com.fh.cloud.screen.service.attendance.api.AttendanceUserApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserResultVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserService;
import com.fh.cloud.screen.service.enums.AttendanceUserRecordTypeEnums;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@RestController
@Api(value = "", tags = "用户考勤接口")
public class AttendanceUserController implements AttendanceUserApi {

    @Autowired
    private IAttendanceUserService attendanceUserService;

    /**
     * 查询考勤用户表（一个人一天的考勤记录），需要日终计算列表
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "分页查询用户考勤信息", httpMethod = "POST")
    public AjaxResult pageList(@RequestBody AttendanceUserListConditionBo condition) {

        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<AttendanceUserVo> pageInfo =
            new PageInfo<>(attendanceUserService.getAttendanceUserListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("attendanceUserList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 根据条件查询考勤列表
     *
     * @param condition
     * @return
     */
    @Override
    @ApiOperation(value = "条件查询用户考勤信息", httpMethod = "POST")
    public AjaxResult<List<AttendanceUserVo>> list(AttendanceUserListConditionBo condition) {
        return AjaxResult.success(attendanceUserService.getAttendanceUserListByCondition(condition));
    }

    /**
     * 条件查询考勤信息（日期必传参）
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "日期条件查询用户考勤信息", httpMethod = "POST")
    public AjaxResult<AttendanceUserCensusVo>
        getListByDateCondition(@RequestBody AttendanceUserListConditionBo condition) {
        return AjaxResult.success(attendanceUserService.getByDateCondition(condition));
    }

    /**
     * 条件查询考勤信息（日期必传参）-导出信息查询
     * 
     * @param condition
     * @return
     */
    @ApiOperation(value = "日期条件查询导出用户考勤信息", httpMethod = "POST")
    @Override
    public AjaxResult<AttendanceUserResultVo> getListExportByDateCondition(AttendanceUserListConditionBo condition) {
        return AjaxResult.success(attendanceUserService.getListExportByDateCondition(condition));
    }

    /**
     * 新增考勤用户表（一个人一天的考勤记录），需要日终计算
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "新增考勤用户表（一个人一天的考勤记录），需要日终计算", httpMethod = "POST")
    public AjaxResult addAttendanceUser(@Validated @RequestBody AttendanceUserBo attendanceUserBo) {
        boolean save = attendanceUserService.addAttendanceUser(attendanceUserBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改考勤用户表（一个人一天的考勤记录），需要日终计算
     *
     * @param attendanceUserBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "修改考勤用户表（一个人一天的考勤记录），需要日终计算", httpMethod = "POST")
    public AjaxResult updateAttendanceUser(@Validated @RequestBody AttendanceUserBo attendanceUserBo) {
        if (null == attendanceUserBo.getAttendanceUserId()) {
            return AjaxResult.fail("考勤用户表（一个人一天的考勤记录），需要日终计算id不能为空");
        }
        boolean update = attendanceUserService.updateAttendanceUser(attendanceUserBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询考勤用户表（一个人一天的考勤记录），需要日终计算详情
     *
     * @param attendanceUserId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "查询考勤用户表（一个人一天的考勤记录），需要日终计算详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long attendanceUserId) {
        Map<String, Object> map = attendanceUserService.getDetail(attendanceUserId);
        return AjaxResult.success(map);
    }

    /**
     * 删除考勤用户表（一个人一天的考勤记录），需要日终计算
     *
     * @param attendanceUserId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @ApiOperation(value = "删除考勤用户表（一个人一天的考勤记录），需要日终计算", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long attendanceUserId) {
        AttendanceUserBo attendanceUserBo = new AttendanceUserBo();
        attendanceUserBo.setAttendanceUserId(attendanceUserId);
        attendanceUserBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = attendanceUserService.updateAttendanceUser(attendanceUserBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 更改考勤状态(实际操作 及 创建一条新记录)
     *
     * @param bo
     * @return
     */
    @Override
    public AjaxResult changeState(@RequestBody AttendanceUserBo bo) {
        bo.setAttendanceRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
        return this.attendanceUserService.changeState(bo);
    }
}
