package com.fh.cloud.screen.service.meeting.controller;

import com.fh.cloud.screen.service.meeting.api.MeetingUserApi;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.service.IMeetingUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 会议人员表
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@RestController
@Validated
public class MeetingUserController implements Meeting<PERSON>ser<PERSON><PERSON> {

    @Autowired
    private IMeetingUserService meetingUserService;

    /**
     * 查询会议人员表分页列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult<PageInfo<MeetingUserVo>>
        getMeetingUserPageListByCondition(@RequestBody MeetingUserConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<MeetingUserVo> pageInfo = new PageInfo<>(meetingUserService.getMeetingUserListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询会议人员表列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult<List<MeetingUserVo>>
        getMeetingUserListByCondition(@RequestBody MeetingUserConditionBo condition) {
        List<MeetingUserVo> list = meetingUserService.getMeetingUserListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增会议人员表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult addMeetingUser(@Validated @RequestBody MeetingUserBo meetingUserBo) {
        return meetingUserService.addMeetingUser(meetingUserBo);
    }

    /**
     * 修改会议人员表
     * 
     * @param meetingUserBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult updateMeetingUser(@Validated @RequestBody MeetingUserBo meetingUserBo) {
        if (null == meetingUserBo.getMeetingUserId()) {
            return AjaxResult.fail("会议人员表id不能为空");
        }
        return meetingUserService.updateMeetingUser(meetingUserBo);
    }

    /**
     * 查询会议人员表详情
     * 
     * @param meetingUserId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult<MeetingUserVo> getDetail(@RequestParam("meetingUserId") Long meetingUserId) {
        if (null == meetingUserId) {
            return AjaxResult.fail("会议人员表id不能为空");
        }
        MeetingUserVo vo = meetingUserService.getDetail(meetingUserId);
        return AjaxResult.success(vo);
    }

    /**
     * 删除会议人员表
     * 
     * @param meetingUserId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult delete(@RequestParam("meetingUserId") Long meetingUserId) {
        if (null == meetingUserId) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        MeetingUserDto meetingUserDto = new MeetingUserDto();
        meetingUserDto.setMeetingUserId(meetingUserId);
        meetingUserDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (meetingUserService.updateById(meetingUserDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
