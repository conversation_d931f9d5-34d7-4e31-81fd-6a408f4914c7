package com.fh.cloud.screen.service.consts;

/**
 * 云屏首页缓存的key:必须以screen_index_cache_{organizationId}开头
 * 
 * <AUTHOR>
 * @date 2023/01/12 10:54
 */
public interface ConstantsRedisScreenIndex {

    /**
     * 云屏首页缓存前缀
     */
    String SCREEN_INDEX_PREFIX = "screen_index_cache_";

    /**
     * 班级信息。最终拼出的key是：screen_index_cache_{organizationId}_clazz_info_{classesId}
     */
    String CLAZZ_INFO = SCREEN_INDEX_PREFIX + "{0}_clazz_info_{1}";

    /**
     * 班级信息缓存时间：4小时
     */
    long CLAZZ_INFO_EXPIRE_IN = 60L * 60 * 4;

    /**
     * 学校信息。最终拼出的key是：screen_index_cache_{organizationId}_org_info_
     */
    String ORG_INFO = SCREEN_INDEX_PREFIX + "{0}_org_info_";

    /**
     * 学校信息缓存时间：4小时
     */
    long ORG_INFO_EXPIRE_IN = 60L * 60 * 4;

    /**
     * 云屏菜单第一页查询时间缓存，只有查询第一页的时候记录查询时间。最终拼出的key是：screen_index_cache_{organizationId}_index_menu_query_time_{scopeType}_{screenModuleLibraryId}_{campusId}_{classesId}。存放的值是上一次查询时间
     */
    String INDEX_MENU_QUERY_TIME = SCREEN_INDEX_PREFIX + "{0}_index_menu_query_time_{1}_{2}_{3}_{4}";

    /**
     * 云屏菜单【第一页】缓存。最终拼出的key是：screen_index_cache_{organizationId}_index_menu_{scopeType}_{screenModuleLibraryId}_{campusId}_{classesId。存放的值是一页查询的数据
     */
    String INDEX_MENU = SCREEN_INDEX_PREFIX + "{0}_index_menu_{1}_{2}_{3}_{4}";

    /**
     * 云屏菜单缓存时间：12小时
     */
    long INDEX_MENU_EXPIRE_IN = 60L * 60 * 12;
}
