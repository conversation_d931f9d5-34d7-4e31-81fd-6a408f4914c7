package com.fh.cloud.screen.service.face.controller;

import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.face.api.FaceConfigApi;
import com.fh.cloud.screen.service.face.entity.dto.FaceConfigDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.face.entity.bo.FaceConfigConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.List;

/**
 * 人脸对比参数
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:35
 */
@RestController
@Validated
public class FaceConfigController implements FaceConfigApi {

    @Autowired
    private IFaceConfigService faceConfigService;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 查询人脸对比参数分页列表
     * 
     * <AUTHOR>
     * @date 2022-11-18 14:16:35
     */
    @Override
    public AjaxResult<PageInfo<FaceConfigVo>>
        getFaceConfigPageListByCondition(@RequestBody FaceConfigConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<FaceConfigVo> pageInfo = new PageInfo<>(faceConfigService.getFaceConfigListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询人脸对比参数列表
     * 
     * <AUTHOR>
     * @date 2022-11-18 14:16:35
     */
    @Override
    public AjaxResult<List<FaceConfigVo>> getFaceConfigListByCondition(@RequestBody FaceConfigConditionBo condition) {
        List<FaceConfigVo> list = faceConfigService.getFaceConfigListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增人脸对比参数
     * 
     * <AUTHOR>
     * @date 2022-11-18 14:16:35
     */
    @Override
    public AjaxResult addFaceConfig(@Validated @RequestBody FaceConfigBo faceConfigBo) {
        return faceConfigService.addFaceConfig(faceConfigBo);
    }

    /**
     * 修改人脸对比参数
     * 
     * @param faceConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:35
     */
    @Override
    public AjaxResult updateFaceConfig(@Validated @RequestBody FaceConfigBo faceConfigBo) {
        if (null == faceConfigBo.getId()) {
            return AjaxResult.fail("人脸对比参数id不能为空");
        }
        return faceConfigService.updateFaceConfig(faceConfigBo);
    }

    /**
     * 查询人脸对比参数详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:35
     */
    @Override
    public AjaxResult<FaceConfigVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("人脸对比参数id不能为空");
        }
        FaceConfigVo vo = faceConfigService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除人脸对比参数
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:35
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        FaceConfigDto faceConfigDto = new FaceConfigDto();
        faceConfigDto.setId(id);
        faceConfigDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (faceConfigService.updateById(faceConfigDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult<FaceConfigVo> getDetailByOrganizationId(Long organizationId) {
        if (organizationId == null) {
            return AjaxResult.fail("学校参数不可为空");
        }

        FaceConfigVo detailByOrganizationId = faceConfigService.getDetailByOrganizationId(organizationId);
        return AjaxResult.success(detailByOrganizationId);
    }

    @Override
    public AjaxResult saveFaceConfig(FaceConfigBo faceConfigBo) {
        Long id = faceConfigBo.getId();
        if (id == null) {
            faceConfigService.addFaceConfig(faceConfigBo);
        } else {
            faceConfigService.updateFaceConfig(faceConfigBo);
        }
        // 推送配置变更
        applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
            MessageWsType.MODIFY_FACE_CONFIG.getValue(), faceConfigBo.getOrganizationId(), null, null));
        return AjaxResult.success();
    }
}
