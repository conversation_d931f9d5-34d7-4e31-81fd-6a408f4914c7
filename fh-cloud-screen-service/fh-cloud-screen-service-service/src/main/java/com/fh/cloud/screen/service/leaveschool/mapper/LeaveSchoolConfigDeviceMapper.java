package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDeviceDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;

/**
 * 放学配置设备表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
public interface LeaveSchoolConfigDeviceMapper extends BaseMapper<LeaveSchoolConfigDeviceDto> {

	List<LeaveSchoolConfigDeviceVo> getLeaveSchoolConfigDeviceListByCondition(LeaveSchoolConfigDeviceConditionBo condition);

	LeaveSchoolConfigDeviceVo getLeaveSchoolConfigDeviceByCondition(LeaveSchoolConfigDeviceConditionBo condition);

	/**
	 * 获取放学配置设备列表
	 *
	 * @param condition
	 * @return java.util.List<com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo>
	 * <AUTHOR>
	 * @date 2023/8/23 14:11
	 **/
	List<LeaveSchoolConfigDeviceVo> getLeaveSchoolConfigDeviceList(LeaveSchoolConfigDeviceConditionBo condition);

}
