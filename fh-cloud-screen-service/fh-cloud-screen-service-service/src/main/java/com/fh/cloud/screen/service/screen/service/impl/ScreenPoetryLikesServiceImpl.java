package com.fh.cloud.screen.service.screen.service.impl;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureBo;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryLikesDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryLikesBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo;
import com.fh.cloud.screen.service.screen.service.IScreenPoetryLikesService;
import com.fh.cloud.screen.service.screen.mapper.ScreenPoetryLikesMapper;
import com.light.core.entity.AjaxResult;
/**
 * 共话诗词点赞记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:38
 */
@Service
public class ScreenPoetryLikesServiceImpl extends ServiceImpl<ScreenPoetryLikesMapper, ScreenPoetryLikesDto> implements IScreenPoetryLikesService {

	@Resource
	private ScreenPoetryLikesMapper screenPoetryLikesMapper;
	
    @Override
	public List<ScreenPoetryLikesVo> getScreenPoetryLikesListByCondition(ScreenPoetryLikesConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return screenPoetryLikesMapper.getScreenPoetryLikesListByCondition(condition);
	}

	@Override
	public AjaxResult addScreenPoetryLikes(ScreenPoetryLikesBo screenPoetryLikesBo) {
		ScreenPoetryLikesDto screenPoetryLikes = new ScreenPoetryLikesDto();
		BeanUtils.copyProperties(screenPoetryLikesBo, screenPoetryLikes);
		screenPoetryLikes.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(screenPoetryLikes)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScreenPoetryLikes(ScreenPoetryLikesBo screenPoetryLikesBo) {
		ScreenPoetryLikesDto screenPoetryLikes = new ScreenPoetryLikesDto();
		BeanUtils.copyProperties(screenPoetryLikesBo, screenPoetryLikes);
		if(updateById(screenPoetryLikes)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ScreenPoetryLikesVo getScreenPoetryLikesByCondition(ScreenPoetryLikesConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return screenPoetryLikesMapper.getScreenPoetryLikesByCondition(condition);
	}

	@Override
	public AjaxResult addScreenPoetryLikesNum(Long screenPoetryContentId) {
		ScreenPoetryLikesBo screenPoetryLikesBo = new ScreenPoetryLikesBo();
		screenPoetryLikesBo.setScreenPoetryContentId(screenPoetryContentId);
		screenPoetryLikesBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if (baseMapper.addScreenPoetryLikesNum(screenPoetryLikesBo)) {
			return AjaxResult.success("点赞成功");
		}
		return AjaxResult.fail("点赞失败");
	}

}