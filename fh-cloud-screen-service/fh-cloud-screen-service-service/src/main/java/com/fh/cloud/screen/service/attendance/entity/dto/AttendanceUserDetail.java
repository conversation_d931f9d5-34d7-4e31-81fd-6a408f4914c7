package com.fh.cloud.screen.service.attendance.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤用户详情表，需要日终计算
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("attendance_user_detail")
public class AttendanceUserDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "attendance_user_detail_id", type = IdType.AUTO)
    private Long attendanceUserDetailId;

    /**
     * FK考勤用户表id
     */
    @TableField("attendance_user_id")
    private Long attendanceUserId;

    /**
     * 考勤一组顺序：1，2，3...
     */
    @TableField("attendance_rule_day_index")
    private Integer attendanceRuleDayIndex;

    /**
     * 考签到考勤设备号，例如云屏设备的设备号
     */
    @TableField("sign_in_device_number")
    private String signInDeviceNumber;

    /**
     * 签退考勤设备号，例如云屏设备的设备号
     */
    @TableField("sign_out_device_number")
    private String signOutDeviceNumber;

    /**
     * 签到地点
     */
    @TableField("sign_in_address")
    private String signInAddress;

    /**
     * 签退地点
     */
    @TableField("sign_out_address")
    private String signOutAddress;

    /**
     * 签到时间
     */
    @TableField("sign_in_time")
    private Date signInTime;

    /**
     * 签退时间
     */
    @TableField("sign_out_time")
    private Date signOutTime;

    /**
     * 一组考勤记录状态：1正常，2异常（保留状态），3迟到，4早退,6缺卡
     */
    @TableField("sign_in_record_type")
    private Integer signInRecordType;

    /**
     * 一组考勤记录状态：1正常，2异常（保留状态），3迟到，4早退,6缺卡
     */
    @TableField("sign_out_record_type")
    private Integer signOutRecordType;

    /**
     * 匹配到的规则：1只签到，2只签退
     */
    @TableField("only_sign")
    private Integer onlySign;

    /**
     * 签到考勤方式：1实体卡，2人脸识别
     */
    @TableField("sign_in_attendance_method")
    private Integer signInAttendanceMethod;

    /**
     * 签退考勤方式：1实体卡，2人脸识别
     */
    @TableField("sign_out_attendance_method")
    private Integer signOutAttendanceMethod;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 签到人脸图片fileOid
     */
    @TableField("sign_in_face_media_id")
    private String signInFaceMediaId;

    /**
     * 签退人脸图片fileOid
     */
    @TableField("sign_out_face_media_id")
    private String signOutFaceMediaId;

}
