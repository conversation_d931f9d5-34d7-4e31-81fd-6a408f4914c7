package com.fh.cloud.screen.service.rest.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestDay;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;
import com.fh.cloud.screen.service.rest.mapper.WorkRestDayMapper;
import com.fh.cloud.screen.service.rest.service.IWorkRestDayService;
import com.light.core.enums.StatusEnum;

/**
 * 作息时间天设置表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class WorkRestDayServiceImpl extends ServiceImpl<WorkRestDayMapper, WorkRestDay> implements IWorkRestDayService {

    @Resource
    private WorkRestDayMapper workRestDayMapper;

    @Lazy
    @Autowired
    private IWorkRestDayService workRestDayService;

    @Override
    public List<WorkRestDayVo> getWorkRestDayListByCondition(WorkRestDayListConditionBo condition) {
        return workRestDayMapper.getWorkRestDayListByCondition(condition);
    }

    @Override
    public boolean addWorkRestDay(WorkRestDayBo workRestDayBo) {
        WorkRestDay workRestDay = new WorkRestDay();
        BeanUtils.copyProperties(workRestDayBo, workRestDay);
        workRestDay.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(workRestDay);
    }

    @Override
    public boolean updateWorkRestDay(WorkRestDayBo workRestDayBo) {
        WorkRestDay workRestDay = new WorkRestDay();
        BeanUtils.copyProperties(workRestDayBo, workRestDay);
        return updateById(workRestDay);
    }

    @Override
    public WorkRestDayVo getDetail(Long workRestDayId) {
        if (workRestDayId == null) {
            return null;
        }

        LambdaQueryWrapper<WorkRestDay> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WorkRestDay::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(WorkRestDay::getWorkRestDayId, workRestDayId);
        WorkRestDay workRestDay = getOne(lqw);
        WorkRestDayVo workRestDayVo = new WorkRestDayVo();
        BeanUtils.copyProperties(workRestDay, workRestDayVo);
        return workRestDayVo;
    }

    @Override
    public void deleteAndSaveBatch(Long workRestId, List<WorkRestDayBo> workRestDayBoList) {
        if (CollectionUtils.isEmpty(workRestDayBoList)) {
            return;
        }
        if (workRestId != null) {
            workRestDayService.deleteByWorkRestId(workRestId);
        }
        List<WorkRestDay> workRestDays = workRestDayBoList.stream().map(workRestDayBo -> {
            WorkRestDay workRestDay = new WorkRestDay();
            BeanUtils.copyProperties(workRestDayBo, workRestDay);
            return workRestDay;
        }).collect(Collectors.toList());
        saveBatch(workRestDays);
    }

    @Override
    public void deleteByWorkRestId(Long workRestId) {
        if (workRestId == null) {
            return;
        }
        UpdateWrapper<WorkRestDay> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("work_rest_id", workRestId);
        WorkRestDay workRestDay = new WorkRestDay();
        workRestDay.setIsDelete(StatusEnum.ISDELETE.getCode());
        workRestDayMapper.update(workRestDay, updateWrapper);
    }

    @Override
    public List<WorkRestDayVo> getWorkRestDayListByGrade(Long organizationId, String grade) {
        if(organizationId == null){
            return Lists.newArrayList();
        }
        return workRestDayMapper.getWorkRestDayListByGrade(organizationId, grade);
    }
}