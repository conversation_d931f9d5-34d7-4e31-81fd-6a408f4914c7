package com.fh.cloud.screen.service.er.controller;

import com.fh.cloud.screen.service.er.api.ExamInfoTeacherApi;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoTeacherDto;
import com.fh.cloud.screen.service.er.service.IExamInfoTeacherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 考场_考试计划里面一次考试的老师
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@RestController
@Validated
public class ExamInfoTeacherController implements ExamInfoTeacherApi{
	
    @Autowired
    private IExamInfoTeacherService examInfoTeacherService;

    /**
     * 查询考场_考试计划里面一次考试的老师分页列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<PageInfo<ExamInfoTeacherVo>> getExamInfoTeacherPageListByCondition(@RequestBody ExamInfoTeacherConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ExamInfoTeacherVo> pageInfo = new PageInfo<>(examInfoTeacherService.getExamInfoTeacherListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询考场_考试计划里面一次考试的老师列表
	 * <AUTHOR>
	 * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult<List<ExamInfoTeacherVo>> getExamInfoTeacherListByCondition(@RequestBody ExamInfoTeacherConditionBo condition){
		List<ExamInfoTeacherVo> list = examInfoTeacherService.getExamInfoTeacherListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增考场_考试计划里面一次考试的老师
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
	@Override
    public AjaxResult addExamInfoTeacher(@Validated @RequestBody ExamInfoTeacherBo examInfoTeacherBo){
		return examInfoTeacherService.addExamInfoTeacher(examInfoTeacherBo);
    }

    /**
	 * 修改考场_考试计划里面一次考试的老师
	 * @param examInfoTeacherBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult updateExamInfoTeacher(@Validated @RequestBody ExamInfoTeacherBo examInfoTeacherBo) {
		if(null == examInfoTeacherBo.getExamInfoTeacherId()) {
			return AjaxResult.fail("考场_考试计划里面一次考试的老师id不能为空");
		}
		return examInfoTeacherService.updateExamInfoTeacher(examInfoTeacherBo);
	}

	/**
	 * 查询考场_考试计划里面一次考试的老师详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult<ExamInfoTeacherVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("考场_考试计划里面一次考试的老师id不能为空");
		}
		ExamInfoTeacherVo vo = examInfoTeacherService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除考场_考试计划里面一次考试的老师
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ExamInfoTeacherDto examInfoTeacherDto = new ExamInfoTeacherDto();
		examInfoTeacherDto.setExamInfoTeacherId(id);
		examInfoTeacherDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(examInfoTeacherService.updateById(examInfoTeacherDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
