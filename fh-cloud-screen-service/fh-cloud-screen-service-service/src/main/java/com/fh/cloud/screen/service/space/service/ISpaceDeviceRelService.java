package com.fh.cloud.screen.service.space.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;

import java.util.List;
import java.util.Map;

/**
 * 地点和设备关系表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ISpaceDeviceRelService extends IService<SpaceDeviceRel> {

    List<SpaceDeviceRelVo> getSpaceDeviceRelListByCondition(SpaceDeviceRelListConditionBo condition);

    boolean saveSpaceDeviceRel(SpaceDeviceRelBo spaceDeviceRelBo);

    boolean updateSpaceDeviceRel(SpaceDeviceRelBo spaceDeviceRelBo);

    Map<String, Object> getDetail(Long spaceDeviceRelId);

    SpaceDeviceRel getByDeviceId(Long deviceId);
}
