package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.light.core.utils.FuzzyQueryUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.LabelLibraryAuditRelDto;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;
import com.fh.cloud.screen.service.screen.service.ILabelLibraryAuditRelService;
import com.fh.cloud.screen.service.screen.mapper.LabelLibraryAuditRelMapper;
import com.light.core.entity.AjaxResult;

/**
 * 标签海报关联表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
@Service
public class LabelLibraryAuditRelServiceImpl extends ServiceImpl<LabelLibraryAuditRelMapper, LabelLibraryAuditRelDto>
    implements ILabelLibraryAuditRelService {

    @Resource
    private LabelLibraryAuditRelMapper labelLibraryAuditRelMapper;

    @Override
    public List<LabelLibraryAuditRelVo>
        getLabelLibraryAuditRelListByCondition(LabelLibraryAuditRelConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return labelLibraryAuditRelMapper.getLabelLibraryAuditRelListByCondition(condition);
    }

    @Override
    public AjaxResult addLabelLibraryAuditRel(LabelLibraryAuditRelBo labelLibraryAuditRelBo) {
        LabelLibraryAuditRelDto labelLibraryAuditRel = new LabelLibraryAuditRelDto();
        BeanUtils.copyProperties(labelLibraryAuditRelBo, labelLibraryAuditRel);
        labelLibraryAuditRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(labelLibraryAuditRel)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateLabelLibraryAuditRel(LabelLibraryAuditRelBo labelLibraryAuditRelBo) {
        LabelLibraryAuditRelDto labelLibraryAuditRel = new LabelLibraryAuditRelDto();
        BeanUtils.copyProperties(labelLibraryAuditRelBo, labelLibraryAuditRel);
        if (updateById(labelLibraryAuditRel)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public LabelLibraryAuditRelVo getLabelLibraryAuditRelByCondition(LabelLibraryAuditRelConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return labelLibraryAuditRelMapper.getLabelLibraryAuditRelByCondition(condition);
    }

    @Override
    public List<LabelVo> getLabelListByLibraryAuditId(Long screenModuleLibraryAuditId) {
        return baseMapper.getLabelListByLibraryAuditId(screenModuleLibraryAuditId);
    }

    @Override
    public boolean updateLabelList(Long screenModuleLibraryAuditId, List<LabelBo> labelBos) {
        if (screenModuleLibraryAuditId == null) {
            return false;
        }
        // 先查询所有的标签
        List<LabelVo> dbLabelList = getLabelListByLibraryAuditId(screenModuleLibraryAuditId);
        // 如果db里面的和本次的都不为空且一模一样，则不做任何操作
        if (CollectionUtils.isNotEmpty(dbLabelList) && CollectionUtils.isNotEmpty(labelBos)) {
            List<Long> dbLabelIds = dbLabelList.stream().map(LabelVo::getLabelId).collect(Collectors.toList());
            List<Long> labelIds = labelBos.stream().map(LabelBo::getLabelId).collect(Collectors.toList());
            if (CollectionUtils.isEqualCollection(dbLabelIds, labelIds)) {
                return true;
            }
        }

        // 其他任何情况先删除db里面的，再新增本次的
        LambdaUpdateWrapper<LabelLibraryAuditRelDto> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(LabelLibraryAuditRelDto::getScreenModuleLibraryAuditId, screenModuleLibraryAuditId);
        queryWrapper.eq(LabelLibraryAuditRelDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        queryWrapper.set(LabelLibraryAuditRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        update(queryWrapper);
        if (CollectionUtils.isNotEmpty(labelBos)) {
            List<LabelLibraryAuditRelDto> addLabelList = labelBos.stream().map(x -> {
                LabelLibraryAuditRelDto dto = new LabelLibraryAuditRelDto();
                dto.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
                dto.setLabelId(x.getLabelId());
                dto.setIsDelete(StatusEnum.NOTDELETE.getCode());
                return dto;
            }).collect(Collectors.toList());
            saveBatch(addLabelList);
        }
        return true;
    }

    @Override
    public boolean updateLabelList(List<Long> screenModuleLibraryAuditIds, List<LabelBo> labelBos) {
        if (CollectionUtils.isEmpty(screenModuleLibraryAuditIds)) {
            return false;
        }
        screenModuleLibraryAuditIds.forEach(x -> updateLabelList(x, labelBos));
        return true;
    }
}