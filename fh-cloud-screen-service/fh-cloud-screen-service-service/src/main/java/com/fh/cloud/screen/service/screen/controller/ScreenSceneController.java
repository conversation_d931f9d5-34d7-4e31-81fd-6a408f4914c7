package com.fh.cloud.screen.service.screen.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.app.role.service.role.entity.bo.OrganizationPackageRelConditionBo;
import com.fh.app.role.service.role.entity.vo.OrganizationPackageRelVo;
import com.fh.app.role.service.role.enums.AppPackageType;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.ScreenScenePublishType;
import com.fh.cloud.screen.service.enums.ScreenSceneType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.screen.api.ScreenSceneApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditCheckVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneAuditService;
import com.fh.cloud.screen.service.screen.service.IScreenSceneService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云屏场景表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Api(value = "", tags = "云屏场景管理")
@Slf4j
public class ScreenSceneController implements ScreenSceneApi {

    @Autowired
    private IScreenSceneService screenSceneService;
    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private IScreenSceneAuditService screenSceneAuditService;

    /**
     * 查询云屏场景表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询云屏场景表列表", httpMethod = "POST")
    public AjaxResult getScreenSceneListByCondition(@RequestBody ScreenSceneListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // web场景列表查询的时候，空认为是全校分类
        if (condition.getCampusId() == null) {
            condition.setCampusId(ConstantsLong.NUM_0);
        }
        // 按点位发布的时候，如果是按照地点发布。这里也可用publishType综合判断
        if (condition.getSpaceInfoId() != null && condition.getShowDeviceId() == null) {
            condition.setShowDeviceId(ConstantsLong.NUM_0);
        }
        condition.setOrderBy("screen_index asc,screen_play_index asc");

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", screenSceneService.getScreenSceneListByConditionWithModule(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ScreenSceneVo> pageInfo =
                    new PageInfo<>(screenSceneService.getScreenSceneListByConditionWithModule(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                    condition.getPageSize());
        }
    }

    /**
     * 新增云屏场景表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @Deprecated
    @ApiOperation(value = "新增云屏场景表", httpMethod = "POST")
    public AjaxResult addScreenScene(@RequestBody ScreenSceneBo screenSceneBo) {
        boolean save = screenSceneService.addScreenScene(screenSceneBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏场景表
     *
     * @param screenSceneBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @Deprecated
    @ApiOperation(value = "修改云屏场景表", httpMethod = "POST")
    public AjaxResult updateScreenScene(@RequestBody ScreenSceneBo screenSceneBo) {
        if (null == screenSceneBo.getScreenSceneId()) {
            return AjaxResult.fail("云屏场景表id不能为空");
        }
        boolean update = screenSceneService.updateScreenScene(screenSceneBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏场景表详情
     *
     * @param screenSceneId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询云屏场景表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenSceneId") Long screenSceneId) {
        ScreenSceneVo screenSceneVo = screenSceneService.getDetail(screenSceneId);
        return AjaxResult.success(screenSceneVo);
    }

    /**
     * 删除云屏场景表
     *
     * @param screenSceneId  the screen scene id
     * @param organizationId the organization id
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     * @returnType AjaxResult
     */
    @ApiOperation(value = "删除云屏场景表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenSceneId") Long screenSceneId, Long organizationId, Long parentOrganizationId) {
        ScreenSceneBo screenSceneBo = new ScreenSceneBo();
        screenSceneBo.setScreenSceneId(screenSceneId);
        screenSceneBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenSceneService.updateScreenScene(screenSceneBo);
        if (delete) {
            List<Long> organizationIds = new ArrayList<>();
            if (organizationId != null && organizationId != ConstantsLong.NUM_0) {
                organizationIds.add(organizationId);
            }
            if (parentOrganizationId != null && parentOrganizationId != ConstantsLong.NUM_0) {
                List<Long> superviseOrgIds = baseDataService.getSuperviseOrganizationIds(parentOrganizationId);
                if (CollectionUtil.isNotEmpty(superviseOrgIds)) {
                    organizationIds.addAll(superviseOrgIds);
                }
            }
            for (Long orgId : organizationIds) {
                // publish event
                applicationContext.publishEvent(PublishEvent.produceScenePublishEvent(MessageWsType.MODIFY_SCENE.getValue(),
                        orgId, screenSceneId, null));
            }
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 新增或者修改云屏场景表(多组轮播时候需要提醒前端做离开事件判断)
     *
     * @param screenSceneBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "新增或者修改云屏场景表", httpMethod = "POST")
    public AjaxResult saveOrUpdateScreenScene(@RequestBody ScreenSceneBo screenSceneBo) {
        if (null == screenSceneBo.getOrganizationId() && null == screenSceneBo.getParentOrganizationId()) {
            return AjaxResult.fail("组织机构id不能为空");
        }
        // 是否跳过审核流程
        if (!screenSceneBo.isSkipAudit()) {
            if (screenSceneAudit(screenSceneBo)) {
                boolean result = screenSceneAuditService.addAndDeleteScreenSceneAudit(screenSceneBo);
                if (result) {
                    ScreenSceneAuditCheckVo checkVo = new ScreenSceneAuditCheckVo();
                    checkVo.setNeedAudit(true);
                    return AjaxResult.success(checkVo);
                }
                return AjaxResult.fail();
            }
        }
        Long sceneId = screenSceneService.saveOrUpdateScreenSceneWithModule(screenSceneBo);
        updateSceneFieldNull(screenSceneBo, sceneId);
        if (sceneId != null) {
            List<Long> organizationIds = new ArrayList<>();
            if (screenSceneBo.getOrganizationId() != null && screenSceneBo.getOrganizationId() != ConstantsLong.NUM_0) {
                organizationIds.add(screenSceneBo.getOrganizationId());
            }
            if (screenSceneBo.getParentOrganizationId() != null && screenSceneBo.getParentOrganizationId() != ConstantsLong.NUM_0) {
                List<Long> superviseOrgIds = baseDataService.getSuperviseOrganizationIds(screenSceneBo.getParentOrganizationId());
                if (CollectionUtil.isNotEmpty(superviseOrgIds)) {
                    organizationIds.addAll(superviseOrgIds);
                }
            }
            // publish event
            for (Long organizationId : organizationIds) {
                applicationContext.publishEvent(PublishEvent.produceScenePublishEvent(MessageWsType.MODIFY_SCENE.getValue(),
                        organizationId, sceneId, null));
            }
            return AjaxResult.success(sceneId);
        }
        return AjaxResult.fail();
    }

    /**
     * 新增或者修改云屏场景表(多组轮播时候需要提醒前端做离开事件判断)-批量
     *
     * @param screenSceneBos the screen scene bos
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     * @returnType AjaxResult
     */
    @ApiOperation(value = "新增或者修改云屏场景批量", httpMethod = "POST")
    public AjaxResult saveOrUpdateScreenSceneBath(List<ScreenSceneBo> screenSceneBos) {
        if (CollectionUtils.isEmpty(screenSceneBos)) {
            return AjaxResult.fail("场景数据不能为空");
        }
        List<String> screenSceneNames =
                screenSceneBos.stream().filter(screenSceneBo -> StringUtils.isNotBlank(screenSceneBo.getScreenSceneName()))
                        .map(ScreenSceneBo::getScreenSceneName).collect(Collectors.toList());
        long count = screenSceneNames.stream().distinct().count();
        if (screenSceneNames.size() != count) {
            return AjaxResult.fail("场景名称不允许重复");
        }

        List<Long> resultIds = Lists.newArrayList();
        boolean needAudit = false;
        for (ScreenSceneBo screenSceneBo : screenSceneBos) {
            // 是否跳过审核流程
            if (!screenSceneBo.isSkipAudit()) {
                if (screenSceneAudit(screenSceneBo)) {
                    needAudit = true;
                    screenSceneAuditService.addAndDeleteScreenSceneAudit(screenSceneBo);
                    continue;
                }
            }
            Long sceneId = screenSceneService.saveOrUpdateScreenSceneWithModule(screenSceneBo);
            updateSceneFieldNull(screenSceneBo, sceneId);
            if (sceneId != null) {
                List<Long> organizationIds = new ArrayList<>();
                if (screenSceneBo.getOrganizationId() != null && screenSceneBo.getOrganizationId() != ConstantsLong.NUM_0) {
                    organizationIds.add(screenSceneBo.getOrganizationId());
                }
                if (screenSceneBo.getParentOrganizationId() != null && screenSceneBo.getParentOrganizationId() != ConstantsLong.NUM_0) {
                    List<Long> superviseOrgIds = baseDataService.getSuperviseOrganizationIds(screenSceneBo.getParentOrganizationId());
                    if (CollectionUtil.isNotEmpty(superviseOrgIds)) {
                        organizationIds.addAll(superviseOrgIds);
                    }
                }
                // publish event
                for (Long organizationId : organizationIds) {
                    applicationContext.publishEvent(PublishEvent.produceScenePublishEvent(
                            MessageWsType.MODIFY_SCENE.getValue(), organizationId, sceneId, null));
                }
                resultIds.add(sceneId);
            }
        }
        if (needAudit && CollectionUtils.isEmpty(resultIds)) {
            ScreenSceneAuditCheckVo checkVo = new ScreenSceneAuditCheckVo();
            checkVo.setNeedAudit(needAudit);
            return AjaxResult.success(checkVo);
        }
        return AjaxResult.success(resultIds);
    }

    /**
     * 将场景里面的部分信息更新为null，解决更新不了为空的问题
     *
     * @param screenSceneBo the screen scene bo
     * @param sceneId the scene id
     * <AUTHOR>
     * @date 2024 -06-28 17:38:32
     */
    private void updateSceneFieldNull(ScreenSceneBo screenSceneBo, Long sceneId) {
        // 更新场景全屏非全屏
        if(screenSceneBo.getDeviceFullType() == null){
            screenSceneService.updateScreenSceneDeviceFullTypeNull(sceneId);
        }
        // 更新场景日期为空
        if(screenSceneBo.getStartDate() == null){
            screenSceneService.updateScreenSceneStartDateNull(sceneId);
        }
        if(screenSceneBo.getEndDate() == null){
            screenSceneService.updateScreenSceneEndDateNull(sceneId);
        }
        // 更新周次为空
        if(StringUtils.isBlank(screenSceneBo.getWeeks())){
            screenSceneService.updateScreenSceneWeeksNull(sceneId);
        }
    }

    /**
     * 是否走场景审核
     * true:场景审核 false:不走场景审核
     *
     * @param screenSceneBo
     * @return
     */
    private boolean screenSceneAudit(ScreenSceneBo screenSceneBo) {
        // 常规场景|自定义场景 开启审核
        OrganizationPackageRelConditionBo organizationPackageRelConditionBo = new OrganizationPackageRelConditionBo();
        if (screenSceneBo.getParentOrganizationId() != null && screenSceneBo.getParentOrganizationId() != ConstantsLong.NUM_0) {
            // 教育局场景
            organizationPackageRelConditionBo.setOrganizationId(screenSceneBo.getParentOrganizationId());
        } else {
            // 学校场景
            organizationPackageRelConditionBo.setOrganizationId(screenSceneBo.getOrganizationId());
        }
        organizationPackageRelConditionBo.setAppPackageId(AppPackageType.SCREEN_SCENE_AUDIT.getAppPackageType().longValue());
        organizationPackageRelConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<OrganizationPackageRelVo> organizationPackageRelListByCondition =
                baseDataService.getOrganizationPackageRelListByCondition(organizationPackageRelConditionBo);

        // 常规场景|自定义场景   开启审核
        if(screenSceneBo.getScreenSceneType() == null){
            return false;
        }
        return (ScreenSceneType.NORMAL.getValue() == screenSceneBo.getScreenSceneType()
                || ScreenSceneType.CUSTOM.getValue() == screenSceneBo.getScreenSceneType())
                && CollectionUtils.isNotEmpty(organizationPackageRelListByCondition);
    }
}
