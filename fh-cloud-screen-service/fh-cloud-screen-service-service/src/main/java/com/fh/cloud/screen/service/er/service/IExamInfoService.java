package com.fh.cloud.screen.service.er.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 考场_考试计划里面一次考试信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface IExamInfoService extends IService<ExamInfoDto> {

    List<ExamInfoVo> getExamInfoListByCondition(ExamInfoConditionBo condition);

    AjaxResult addExamInfo(ExamInfoBo examInfoBo);

    AjaxResult updateExamInfo(ExamInfoBo examInfoBo);

    ExamInfoVo getDetail(Long id);

    /**
     * 添加，包含科目学生等信息
     *
     * @param examInfoBo the exam info bo
     * @return ajax result
     */
    AjaxResult addExamInfoWithDetail(ExamInfoBo examInfoBo);

    /**
     * 编辑，包含科目学生等信息
     * 
     * @param examInfoBo
     * @return
     */
    AjaxResult updateExamInfoWithDetail(ExamInfoBo examInfoBo);

    /**
     * 导入批量添加考试信息
     *
     * @param examInfoBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/12 15:53
     */
    AjaxResult addExamInfoBatchWithDetail(List<ExamInfoBo> examInfoBos);

}
