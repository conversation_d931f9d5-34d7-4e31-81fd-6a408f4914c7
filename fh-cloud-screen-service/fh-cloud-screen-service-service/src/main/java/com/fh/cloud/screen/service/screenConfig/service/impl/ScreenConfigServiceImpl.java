package com.fh.cloud.screen.service.screenConfig.service.impl;

import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screenConfig.entity.dto.ScreenConfigDto;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;
import com.fh.cloud.screen.service.screenConfig.service.IScreenConfigService;
import com.fh.cloud.screen.service.screenConfig.mapper.ScreenConfigMapper;
import com.light.core.entity.AjaxResult;
/**
 * 云屏配置表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
@Service
public class ScreenConfigServiceImpl extends ServiceImpl<ScreenConfigMapper, ScreenConfigDto> implements IScreenConfigService {

	@Resource
	private ScreenConfigMapper screenConfigMapper;
	
    @Override
	public List<ScreenConfigVo> getScreenConfigListByCondition(ScreenConfigConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return screenConfigMapper.getScreenConfigListByCondition(condition);
	}

	@Override
	public AjaxResult addScreenConfig(ScreenConfigBo screenConfigBo) {
		ScreenConfigDto screenConfig = new ScreenConfigDto();
		BeanUtils.copyProperties(screenConfigBo, screenConfig);
		screenConfig.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(screenConfig)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScreenConfig(ScreenConfigBo screenConfigBo) {
		ScreenConfigDto screenConfig = new ScreenConfigDto();
		BeanUtils.copyProperties(screenConfigBo, screenConfig);
		if(updateById(screenConfig)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ScreenConfigVo getScreenConfigByCondition(ScreenConfigConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return screenConfigMapper.getScreenConfigByCondition(condition);
	}

	@Override
	public AjaxResult saveScreenConfig(ScreenConfigBo configBo) {
		ScreenConfigConditionBo condition = new ScreenConfigConditionBo();
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		condition.setOrganizationId(configBo.getOrganizationId());
		condition.setType(configBo.getType());
		ScreenConfigVo configVo = screenConfigMapper.getScreenConfigByCondition(condition);
		ScreenConfigDto configDto = new ScreenConfigDto();
		BeanUtils.copyProperties(configBo, configDto);
		if (configVo == null) {
			save(configDto);
		} else {
			configDto.setScreenConfigId(configVo.getScreenConfigId());
			updateById(configDto);
		}
		return AjaxResult.success();
	}

}