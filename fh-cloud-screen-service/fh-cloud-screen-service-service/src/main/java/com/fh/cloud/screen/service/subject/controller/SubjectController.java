package com.fh.cloud.screen.service.subject.controller;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.subject.api.SubjectApi;
import com.fh.cloud.screen.service.subject.entity.bo.SubjectBo;
import com.light.core.entity.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * 科目
 * <AUTHOR>
 * @date 2022/10/14 10:41
 */
@RestController
public class SubjectController implements SubjectApi {
    @Autowired
    private BaseDataService baseDataService;

    @Override
    public AjaxResult listSubject(SubjectBo subjectBo) {
        return baseDataService.listSubject(subjectBo);
    }
}
