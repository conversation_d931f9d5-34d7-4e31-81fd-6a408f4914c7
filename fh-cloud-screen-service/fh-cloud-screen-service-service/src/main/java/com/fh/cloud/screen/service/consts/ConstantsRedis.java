package com.fh.cloud.screen.service.consts;

/**
 * redis的key前后缀
 * 
 * <AUTHOR>
 * @date 2022/7/25 10:54
 */
public interface ConstantsRedis {

    /**
     * 课后延迟服务demo数据的缓存key前缀demo_delay_cache_{organizationId}
     */
    String DEMO_DELAY_CACHE_PREFIX = "demo_delay_cache_";
    /**
     * 课后延迟服务demo数据的缓存key前缀demo_meeting_cache_{organizationId}
     */
    String DEMO_MEETING_CACHE_PREFIX = "demo_meeting_cache_";
    /**
     * 课后延迟服务demo数据的缓存key有效期
     */
    // 取到截至时间为止的时间
    //
    // app查看会议vo缓存 meeting:{meetingId}
    String MEETING_REDIS_KEY = "meetingId:";

    /**
     * 云屏设备二维码cache前缀，后面拼deviceNumber
     */
    String SCREEN_DEVICE_QRCODE_CONTENT_PREFIX = "device_qrcode_content_cache_";
    /**
     * 设备二维码缓存一小时
     */
    long SCREEN_DEVICE_QRCODE_CONTENT_EXPIRE_IN = 60L * 60;

    /**
     * url转图片截图任务队列缓存（list里面存放任务）
     */
    String CHROMEDP_TASK_LIST = "chromedp_task_list_";
    /**
     * url转图片截图任务成功队列缓存（list里面存放任务）-由截图服务rpush成功的数据
     */
    String CHROMEDP_TASK_LIST_SUCCESS = "chromedp_task_list_success_";

    /**
     * 学校自定义配置的全屏非全屏设置，存放的key为：device_full_custom_{organizationId}，value为字符串
     */
    String DEVICE_FULL_CUSTOM_BY_ORG_PREFIX = "device_full_custom_";

    /**
     * 学校自定义配置的全屏非全屏设置，缓存1h
     */
    long DEVICE_FULL_CUSTOM_BY_ORG_EXPIRE_IN = 12L * 60 * 60;

    /**
     * 校历锁key
     */
    String CALENDAR_LOCK_KEY_PREFIX = "calendar_lock_";

    /**
     * 一次考试的考试科目的打卡记录缓存key(key里面存储的是list)，最终key为：exam:subject:{exam_info_subject_id}
     */
    String EXAM_SUBJECT_ATTENDANCE_LOG_KEY_PREFIX = "exam:subject:";
}
