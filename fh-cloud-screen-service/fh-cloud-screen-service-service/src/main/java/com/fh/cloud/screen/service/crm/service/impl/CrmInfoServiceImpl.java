package com.fh.cloud.screen.service.crm.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.crm.service.ICrmContactService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoConditionBo;
import com.fh.cloud.screen.service.crm.entity.dto.CrmInfoDto;
import com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo;
import com.fh.cloud.screen.service.crm.mapper.CrmInfoMapper;
import com.fh.cloud.screen.service.crm.service.ICrmInfoService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * CRM商讯管理表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@Service
public class CrmInfoServiceImpl extends ServiceImpl<CrmInfoMapper, CrmInfoDto> implements ICrmInfoService {

    @Resource
    private CrmInfoMapper crmInfoMapper;
    @Resource
    private ICrmInfoService crmInfoService;
    @Resource
    private ICrmContactService crmContactService;

    @Override
    public List<CrmInfoVo> getCrmInfoListByCondition(CrmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return crmInfoMapper.getCrmInfoListByCondition(condition);
    }

    @Override
    public Long addCrmInfo(CrmInfoBo crmInfoBo) {
        CrmInfoDto crmInfo = new CrmInfoDto();
        BeanUtils.copyProperties(crmInfoBo, crmInfo);
        crmInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(crmInfo)) {
            return crmInfo.getCrmInfoId();
        } else {
            return null;
        }
    }

    @Override
    public AjaxResult updateCrmInfo(CrmInfoBo crmInfoBo) {
        CrmInfoDto crmInfo = new CrmInfoDto();
        BeanUtils.copyProperties(crmInfoBo, crmInfo);
        if (updateById(crmInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CrmInfoVo getCrmInfoByCondition(CrmInfoConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CrmInfoVo vo = crmInfoMapper.getCrmInfoByCondition(condition);
        return vo;
    }

    @Override
    public AjaxResult addCrmInfoWithContact(CrmInfoBo crmInfoBo) {
        // 新增主记录
        Long crmInfoRecordId = crmInfoService.addCrmInfo(crmInfoBo);
        if (crmInfoRecordId == null) {
            return AjaxResult.fail("新增失败");
        }

        // 新增联系人信息
        AjaxResult ajaxResult = crmContactService.addCrmContactBatch(crmInfoRecordId, crmInfoBo.getCrmContactBos());
        return ajaxResult;
    }
}