package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordOperateDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordOperateConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordOperateBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordOperateVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 放学记录操作表id接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-09-13 10:26:18
 */
public interface ILeaveSchoolRecordOperateService extends IService<LeaveSchoolRecordOperateDto> {

    List<LeaveSchoolRecordOperateVo> getLeaveSchoolRecordOperateListByCondition(LeaveSchoolRecordOperateConditionBo condition);

	AjaxResult addLeaveSchoolRecordOperate(LeaveSchoolRecordOperateBo leaveSchoolRecordOperateBo);

	AjaxResult updateLeaveSchoolRecordOperate(LeaveSchoolRecordOperateBo leaveSchoolRecordOperateBo);

	LeaveSchoolRecordOperateVo getLeaveSchoolRecordOperateByCondition(LeaveSchoolRecordOperateConditionBo condition);

}

