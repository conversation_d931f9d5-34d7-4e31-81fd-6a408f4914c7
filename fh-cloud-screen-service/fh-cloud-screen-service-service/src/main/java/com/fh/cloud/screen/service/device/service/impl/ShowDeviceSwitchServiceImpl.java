package com.fh.cloud.screen.service.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceSwitch;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceSwitchVo;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceSwitchMapper;
import com.fh.cloud.screen.service.device.service.IShowDeviceSwitchService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 开关机设置接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class ShowDeviceSwitchServiceImpl extends ServiceImpl<ShowDeviceSwitchMapper, ShowDeviceSwitch>
    implements IShowDeviceSwitchService {

    @Resource
    private ShowDeviceSwitchMapper showDeviceSwitchMapper;

    @Override
    public List<ShowDeviceSwitchVo> getShowDeviceSwitchListByCondition(ShowDeviceSwitchListConditionBo condition) {
        List<ShowDeviceSwitchVo> showDeviceSwitchVos =
            showDeviceSwitchMapper.getShowDeviceSwitchListByCondition(condition);
        if (CollectionUtils.isEmpty(showDeviceSwitchVos)) {
            return Lists.newArrayList();
        }
        showDeviceSwitchVos.forEach(showDeviceSwitchVo -> {
            showDeviceSwitchVo.setOffTime(DateKit.transferYMD2CurrentDay(showDeviceSwitchVo.getOffTime()));
            showDeviceSwitchVo.setOnTime(DateKit.transferYMD2CurrentDay(showDeviceSwitchVo.getOnTime()));
        });

        return showDeviceSwitchVos;
    }

    @Override
    public boolean addShowDeviceSwitch(ShowDeviceSwitchBo showDeviceSwitchBo) {
        ShowDeviceSwitch showDeviceSwitch = new ShowDeviceSwitch();
        BeanUtils.copyProperties(showDeviceSwitchBo, showDeviceSwitch);
        showDeviceSwitch.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(showDeviceSwitch);
    }

    @Override
    public boolean updateShowDeviceSwitch(ShowDeviceSwitchBo showDeviceSwitchBo) {
        ShowDeviceSwitch showDeviceSwitch = new ShowDeviceSwitch();
        BeanUtils.copyProperties(showDeviceSwitchBo, showDeviceSwitch);
        return updateById(showDeviceSwitch);
    }

    @Override
    public ShowDeviceSwitchVo getDetail(Long showDeviceSwitchId) {
        if (showDeviceSwitchId == null) {
            return null;
        }

        LambdaQueryWrapper<ShowDeviceSwitch> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ShowDeviceSwitch::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ShowDeviceSwitch::getShowDeviceSwitchId, showDeviceSwitchId);
        ShowDeviceSwitch showDeviceSwitch = getOne(lqw);
        ShowDeviceSwitchVo showDeviceSwitchVo = new ShowDeviceSwitchVo();
        BeanUtils.copyProperties(showDeviceSwitch, showDeviceSwitchVo);
        return showDeviceSwitchVo;
    }

    @Override
    public void deleteAndAddScreenSceneModuleRelBatch(Long organizationId, Long campusId,
        List<ShowDeviceSwitchBo> showDeviceSwitchBos) {
        // 删除
        LambdaUpdateWrapper<ShowDeviceSwitch> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShowDeviceSwitch::getOrganizationId, organizationId);
        if (campusId != null) {
            updateWrapper.eq(ShowDeviceSwitch::getCampusId, campusId);
        }
        ShowDeviceSwitch showDeviceSwitch = new ShowDeviceSwitch();
        showDeviceSwitch.setIsDelete(StatusEnum.ISDELETE.getCode());
        showDeviceSwitchMapper.update(showDeviceSwitch, updateWrapper);

        if (CollectionUtils.isEmpty(showDeviceSwitchBos)) {
            return;
        }
        // 新增
        List<ShowDeviceSwitch> showDeviceSwitches = showDeviceSwitchBos.stream().map(showDeviceSwitchBo -> {
            ShowDeviceSwitch showDeviceSwitchTemp = new ShowDeviceSwitch();
            BeanUtils.copyProperties(showDeviceSwitchBo, showDeviceSwitchTemp);
            return showDeviceSwitchTemp;
        }).collect(Collectors.toList());
        saveBatch(showDeviceSwitches);
    }
}