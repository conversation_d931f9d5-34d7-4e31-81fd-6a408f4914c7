package com.fh.cloud.screen.service.role.servise;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.app.role.service.role.api.RoleAppRelApi;
import com.fh.app.role.service.role.entity.bo.RoleManagerConditionBo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.enums.StatusEnum;
import com.light.user.clazz.api.ClazzHeadmasterApi;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.teacher.api.TeacherClassesSubjectApi;
import com.light.user.teacher.entity.bo.TeacherClassesSubjectConditionBo;
import com.light.user.teacher.entity.vo.TeacherClassesSubjectVo;
import com.light.user.user.entity.vo.UserRoleVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.fh.app.role.service.role.api.RoleDataAuthorityApi;
import com.fh.app.role.service.role.entity.bo.RoleAppRelConditionBo;
import com.fh.app.role.service.role.entity.bo.RoleDataAuthorityConditionBo;
import com.fh.app.role.service.role.service.RoleAppRelApiService;
import com.light.core.entity.AjaxResult;

/**
 * 用户角色权限service
 *
 * <AUTHOR>
 * @date 2022/7/19
 */
@Service
public class UserRoleService {
    @Resource
    private RoleDataAuthorityApi roleDataAuthorityApi;

    @Resource
    private RoleAppRelApiService roleAppRelApiService;

    @Resource
    private TeacherClassesSubjectApi teacherClassesSubjectApi;

    @Resource
    private ClazzHeadmasterApi clazzHeadmasterApi;

    /**
     * 获取当前用户最大数据权限，
     *
     * @param appId 应用id,过滤出属于该应用的角色的最大数据权限
     * @return java.lang.Integer 2,4
     * <AUTHOR>
     * @date 2022/7/20 15:07
     */
    public Integer getUserMaxDataAuthority(Long appId) {
        RoleDataAuthorityConditionBo roleDataAuthorityConditionBo = new RoleDataAuthorityConditionBo();
        roleDataAuthorityConditionBo.setAppId(appId);
        AjaxResult ajaxResult = roleDataAuthorityApi.getRoleDataAuthorityListByCondition(roleDataAuthorityConditionBo);
        List<Integer> dataTypes = JSONObject.parseArray(JSONObject.toJSONString(ajaxResult.getData()), Integer.class);
        if (CollectionUtils.isEmpty(dataTypes)) {
            return null;
        }
        return Collections.max(dataTypes);
    }

    /**
     * 获取用户功能权限
     *
     * @param condition appIds
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/7/22 14:52
     */
    public AjaxResult getRoleAppRelPageListByCondition(RoleAppRelConditionBo condition) {
        return roleAppRelApiService.getUserRolePermissionByCondition(condition);
    }

    /**
     * 获取用户任教科目的班级ids
     *
     * @param userOid 用户oid
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/7/21 15:01
     */
    public List<Long> getTeacherSubjectClassIds(String userOid) {
        List<Long> classIds = new ArrayList<>();
        TeacherClassesSubjectConditionBo subjectConditionBo = new TeacherClassesSubjectConditionBo();
        subjectConditionBo.setTeacherUserOid(userOid);
        subjectConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        subjectConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = teacherClassesSubjectApi.getTeacherClassesSubjectListByCondition(subjectConditionBo);
        if (ajaxResult.isFail()) {
            return classIds;
        }
        HashMap map = (HashMap)ajaxResult.getData();

        List<TeacherClassesSubjectVo> teacherClassesSubjectVos =
            JSONObject.parseArray(JSONObject.toJSONString(map.get("list")), TeacherClassesSubjectVo.class);
        if (CollectionUtil.isNotEmpty(teacherClassesSubjectVos)) {
            List<Long> collect = teacherClassesSubjectVos.stream().map(TeacherClassesSubjectVo::getClassesId)
                .collect(Collectors.toList());
            classIds.addAll(collect);
        }
        return classIds;
    }

    /**
     * 获取用户任教班主任班级ids
     *
     * @param userOid 用户Oid
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2022/7/21 15:25
     */
    public List<Long> getTeacherClassIds(String userOid) {
        List<Long> classIds = new ArrayList<>();
        AjaxResult ajaxResult = clazzHeadmasterApi.getByUserOid(userOid);
        if (ajaxResult.isFail()) {
            return classIds;
        }
        List<ClazzHeadmasterVo> clazzHeadmasterVos = new ArrayList<>();
        if (null != ajaxResult.getData()) {
            clazzHeadmasterVos =
                JSONObject.parseArray(JSONObject.toJSONString(ajaxResult.getData()), ClazzHeadmasterVo.class);
            List<Long> collect =
                clazzHeadmasterVos.stream().map(ClazzHeadmasterVo::getClassesId).collect(Collectors.toList());
            classIds.addAll(collect);
        }
        return classIds;
    }

    /**
     * 获取当前用户的roleFlag，目前仅仅用到是否是班主任，后续如果有特别业务判断是否是xxx角色的也使用这个接口
     *
     * @return role flag
     * <AUTHOR>
     * @date 2023 -01-17 10:01:56
     */
    public List<String> getRoleFlag() {
        AjaxResult<List<String>> schoolHeadmasterAjaxResult = roleAppRelApiService.getCurrentUserRoleCodes();
        if (schoolHeadmasterAjaxResult.isFail() || schoolHeadmasterAjaxResult.getData() == null) {
            return Lists.newArrayList();
        }
        return schoolHeadmasterAjaxResult.getData();
    }

    /**
     * 根据roleId和organizationId查询用户oid
     *
     * @param roleId 角色id,必填
     * @param organizationId 学校id,必填
     * @return user oids by role id
     * <AUTHOR>
     * @date 2024 -04-08 18:01:04
     */
    public List<String> getUserOidsByRoleId(Long roleId, Long organizationId) {
        if (roleId == null || organizationId == null) {
            return Lists.newArrayList();
        }
        RoleManagerConditionBo roleManagerConditionBo = new RoleManagerConditionBo();
        roleManagerConditionBo.setRoleId(roleId);
        roleManagerConditionBo.setOrganizationId(organizationId);
        AjaxResult<List<UserRoleVo>> userRoleVosByRoleIds =
            roleAppRelApiService.getUserRoleVosByRoleIds(roleManagerConditionBo);
        if (userRoleVosByRoleIds.isFail() || userRoleVosByRoleIds.getData() == null) {
            return Lists.newArrayList();
        }
        return userRoleVosByRoleIds.getData().stream().map(UserRoleVo::getUserOid).collect(Collectors.toList());
    }

}
