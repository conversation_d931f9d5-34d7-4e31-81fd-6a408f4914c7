package com.fh.cloud.screen.service.space.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 行政区域内容扩展信息表（班级扩展信息）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 16:11:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("classes_info")
public class ClassesInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "classes_info_id", type = IdType.AUTO)
    private Long classesInfoId;

    /**
     * FK区域组表主键
     */
    @TableField("space_group_id")
    private Long spaceGroupId;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @TableField("campus_id")
    private Long campusId;

    /**
     * 班级ID
     */
    @TableField("classes_id")
    private Long classesId;

    /**
     * 是否有电脑，0：否，1：是
     */
    @TableField("computer_use")
    private Integer computerUse;

    /**
     * 是否有网络，0：否，1：是
     */
    @TableField("network_use")
    private Integer networkUse;

    /**
     * 是否有投影，0：否，1：是
     */
    @TableField("shadow_use")
    private Integer shadowUse;

    /**
     * 容纳人数
     */
    @TableField("user_capacity")
    private Integer userCapacity;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 是否有会议室，0：否，1：是
     */
    @TableField("meeting_use")
    private Integer meetingUse;
}
