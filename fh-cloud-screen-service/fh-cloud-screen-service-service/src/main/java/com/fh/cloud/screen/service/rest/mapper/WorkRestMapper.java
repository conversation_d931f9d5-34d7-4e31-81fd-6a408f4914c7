package com.fh.cloud.screen.service.rest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRest;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestVo;

import java.util.List;

/**
 * 作息时间主表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface WorkRestMapper extends BaseMapper<WorkRest> {

    List<WorkRestVo> getWorkRestListByCondition(WorkRestListConditionBo condition);

}
