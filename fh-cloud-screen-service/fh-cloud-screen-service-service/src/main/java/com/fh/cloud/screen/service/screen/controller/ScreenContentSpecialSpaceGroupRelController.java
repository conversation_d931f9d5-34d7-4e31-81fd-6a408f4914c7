package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialSpaceGroupRelVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialSpaceGroupRelService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 云屏紧急发布内容-地点组关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@RestController
@RequestMapping("/screen/content-special/rel")
@Validated
public class ScreenContentSpecialSpaceGroupRelController {

    @Autowired
    private IScreenContentSpecialSpaceGroupRelService screenContentSpecialSpaceGroupRelService;

    /**
     * 查询云屏紧急发布内容-地点组关系表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询云屏紧急发布内容-地点组关系表列表", httpMethod = "POST")
    public AjaxResult getScreenContentSpecialSpaceGroupRelListByCondition(
        @RequestBody ScreenContentSpecialSpaceGroupRelListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenContentSpecialSpaceGroupRelVo> pageInfo = new PageInfo<>(
            screenContentSpecialSpaceGroupRelService.getScreenContentSpecialSpaceGroupRelListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("screenContentSpecialSpaceGroupRelList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增云屏紧急发布内容-地点组关系表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增云屏紧急发布内容-地点组关系表", httpMethod = "POST")
    public AjaxResult addScreenContentSpecialSpaceGroupRel(
        @RequestBody ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo) {
        boolean save = screenContentSpecialSpaceGroupRelService
            .addScreenContentSpecialSpaceGroupRel(screenContentSpecialSpaceGroupRelBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏紧急发布内容-地点组关系表
     * 
     * @param screenContentSpecialSpaceGroupRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改云屏紧急发布内容-地点组关系表", httpMethod = "POST")
    public AjaxResult updateScreenContentSpecialSpaceGroupRel(
        @RequestBody ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo) {
        if (null == screenContentSpecialSpaceGroupRelBo.getId()) {
            return AjaxResult.fail("云屏紧急发布内容-地点组关系表id不能为空");
        }
        boolean update = screenContentSpecialSpaceGroupRelService
            .updateScreenContentSpecialSpaceGroupRel(screenContentSpecialSpaceGroupRelBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏紧急发布内容-地点组关系表详情
     * 
     * @param screenContentSpecialSpaceGroupRelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询云屏紧急发布内容-地点组关系表详情", httpMethod = "GET")
    public AjaxResult
        getDetail(@RequestParam("screenContentSpecialSpaceGroupRelId") Long screenContentSpecialSpaceGroupRelId) {
        ScreenContentSpecialSpaceGroupRelVo screenContentSpecialSpaceGroupRelVo =
            screenContentSpecialSpaceGroupRelService.getDetail(screenContentSpecialSpaceGroupRelId);
        return AjaxResult.success(screenContentSpecialSpaceGroupRelVo);
    }

    /**
     * 删除云屏紧急发布内容-地点组关系表
     * 
     * @param screenContentSpecialSpaceGroupRelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除云屏紧急发布内容-地点组关系表", httpMethod = "GET")
    public AjaxResult
        delete(@RequestParam("screenContentSpecialSpaceGroupRelId") Long screenContentSpecialSpaceGroupRelId) {
        ScreenContentSpecialSpaceGroupRelBo screenContentSpecialSpaceGroupRelBo =
            new ScreenContentSpecialSpaceGroupRelBo();
        screenContentSpecialSpaceGroupRelBo.setId(screenContentSpecialSpaceGroupRelId);
        screenContentSpecialSpaceGroupRelBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenContentSpecialSpaceGroupRelService
            .updateScreenContentSpecialSpaceGroupRel(screenContentSpecialSpaceGroupRelBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
