package com.fh.cloud.screen.service.face.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.face.entity.dto.FaceConfigDto;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigConditionBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;

/**
 * 人脸对比参数Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:35
 */
public interface FaceConfigMapper extends BaseMapper<FaceConfigDto> {

	List<FaceConfigVo> getFaceConfigListByCondition(FaceConfigConditionBo condition);

}
