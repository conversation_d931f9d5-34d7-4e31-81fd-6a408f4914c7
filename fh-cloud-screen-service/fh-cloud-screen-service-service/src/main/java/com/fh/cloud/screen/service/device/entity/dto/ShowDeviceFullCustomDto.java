package com.fh.cloud.screen.service.device.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云屏全屏非全屏设置自定义
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("show_device_full_custom")
public class ShowDeviceFullCustomDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "full_custom_id", type = IdType.AUTO)
	private Long fullCustomId;

	/**
	 * 设备id
	 */
	@TableField("show_device_id")
	private Long showDeviceId;

	/**
	 * 设备序列号【冗余】
	 */
	@TableField("device_number")
	private String deviceNumber;

	/**
	 * 是否全屏类型：1全屏，2不是全屏
	 */
	@TableField("device_full_type")
	private Integer deviceFullType;

	/**
	 * 设置的改变状态的时间
	 */
	@TableField("custom_time")
	private Date customTime;

	/**
	 * 所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 星期几：默认0不生效表示每一天；1-7，分别为星期一到星期日。
	 */
	@TableField("week")
	private Integer week;

}
