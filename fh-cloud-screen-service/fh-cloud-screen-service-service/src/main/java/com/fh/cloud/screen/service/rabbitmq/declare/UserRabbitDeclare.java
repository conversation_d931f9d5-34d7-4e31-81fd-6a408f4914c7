package com.fh.cloud.screen.service.rabbitmq.declare;

import com.fh.cloud.screen.service.rabbitmq.constant.UserRabbitConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 *  用户 相关rabbitmq 声明 （不声明在启动时，如mq重没有该信息 会报错 但不影响服务启动）
 *  注：可放到单独模块供全局使用
 */
@Configuration
public class UserRabbitDeclare {


    @Bean
    public DirectExchange userExchange(){
        return new DirectExchange(UserRabbitConstant.USER_EXCHANGE);
    }


    /**
     * ######################## 学生 服务监听 ############################
     */

    @Bean
    public Queue studentUpdateQueue(){
        return new Queue(UserRabbitConstant.STUDENT_UPDATE_QUEUE);
    }

    @Bean
    public Queue studentAddQueue(){
        return new Queue(UserRabbitConstant.STUDENT_ADD_QUEUE);
    }

    @Bean
    public Binding studentUpdateBinding(){
        return BindingBuilder.bind(studentUpdateQueue()).to(userExchange()).withQueueName();
    }

    @Bean
    public Binding studentAddBinding(){
        return BindingBuilder.bind(studentAddQueue()).to(userExchange()).withQueueName();
    }


    /**
     * ######################## 老师服务监听 ############################
     */
    @Bean
    public Queue teacherUpdateQueue(){
        return new Queue(UserRabbitConstant.TEACHER_UPDATE_QUEUE);
    }

    @Bean
    public Queue teacherAddQueue(){
        return new Queue(UserRabbitConstant.TEACHER_ADD_QUEUE);
    }

    @Bean
    public Binding teacherUpdateBinding(){
        return BindingBuilder.bind(teacherUpdateQueue()).to(userExchange()).withQueueName();
    }

    @Bean
    public Binding teacherAddBinding(){
        return BindingBuilder.bind(teacherAddQueue()).to(userExchange()).withQueueName();
    }
}
