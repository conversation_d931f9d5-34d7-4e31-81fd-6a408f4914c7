package com.fh.cloud.screen.service.screenConfig.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screenConfig.entity.dto.ScreenConfigDto;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 云屏配置表接口
 *
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
public interface IScreenConfigService extends IService<ScreenConfigDto> {

    List<ScreenConfigVo> getScreenConfigListByCondition(ScreenConfigConditionBo condition);

	AjaxResult addScreenConfig(ScreenConfigBo screenConfigBo);

	AjaxResult updateScreenConfig(ScreenConfigBo screenConfigBo);

	ScreenConfigVo getScreenConfigByCondition(ScreenConfigConditionBo condition);

	/**
	 * 保存云屏配置
	 *
	 * @param configBo
	 * @return
	 */
	AjaxResult saveScreenConfig(ScreenConfigBo configBo);

}

