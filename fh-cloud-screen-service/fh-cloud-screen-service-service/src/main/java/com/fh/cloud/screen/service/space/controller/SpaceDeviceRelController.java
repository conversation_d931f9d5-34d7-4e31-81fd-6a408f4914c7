package com.fh.cloud.screen.service.space.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.light.core.utils.StringUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.space.api.SpaceDeviceApi;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;
import com.fh.cloud.screen.service.space.service.ISpaceDeviceRelService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

/**
 * 地点和设备关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@RequestMapping("/space/device-rel")
@Validated
public class SpaceDeviceRelController implements SpaceDeviceApi {

    @Autowired
    private ISpaceDeviceRelService spaceDeviceRelService;
    @Resource
    private ApplicationContext applicationContext;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Autowired
    private ISpaceInfoService spaceInfoService;

    /**
     * 查询地点和设备关系表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询地点和设备关系表列表", httpMethod = "POST")
    public AjaxResult getSpaceDeviceRelListByCondition(@RequestBody SpaceDeviceRelListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SpaceDeviceRelVo> pageInfo =
            new PageInfo<>(spaceDeviceRelService.getSpaceDeviceRelListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("spaceDeviceRelList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增地点和设备关系表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存地点和设备关系表", httpMethod = "POST")
    public AjaxResult saveSpaceDeviceRel(@RequestBody SpaceDeviceRelBo spaceDeviceRelBo) {
        boolean save = spaceDeviceRelService.saveSpaceDeviceRel(spaceDeviceRelBo);
        if (save) {
            String deviceNumber = "";
            if (spaceDeviceRelBo.getShowDeviceId() != null) {
                ShowDeviceVo showDeviceVo = showDeviceService.getDetail(spaceDeviceRelBo.getShowDeviceId());
                if (showDeviceVo != null) {
                    deviceNumber = showDeviceVo.getDeviceNumber();
                }
            }
            List<String> deviceNumbers = Lists.newArrayList();
            if (StringUtils.isNotBlank(deviceNumber)) {
                deviceNumbers.add(deviceNumber);
            }
            applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(
                MessageWsType.SCREEN_ACTIVE.getValue(), spaceDeviceRelBo.getOrganizationId(), deviceNumbers, null));
        }
        return AjaxResult.success();
    }

    /**
     * 修改地点和设备关系表
     * 
     * @param spaceDeviceRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改地点和设备关系表", httpMethod = "POST")
    public AjaxResult updateSpaceDeviceRel(@RequestBody SpaceDeviceRelBo spaceDeviceRelBo) {
        boolean update = spaceDeviceRelService.updateSpaceDeviceRel(spaceDeviceRelBo);
        if (update) {
            List<String> deviceNumbers = Lists.newArrayList();
            ShowDeviceVo showDeviceVo = showDeviceService.getDetail(spaceDeviceRelBo.getShowDeviceId());
            if (showDeviceVo != null) {
                deviceNumbers.add(showDeviceVo.getDeviceNumber());
            }
            SpaceInfoVo spaceInfoVo = spaceInfoService.getDetailVoByUseType(spaceDeviceRelBo.getSpaceInfoId(),
                spaceDeviceRelBo.getSpaceGroupUseType());
            // publish event，设备更换地点重新执行初始化
            applicationContext
                .publishEvent(PublishEvent.produceDevicePublishEvent(MessageWsType.SCREEN_CHANGE_SPACE.getValue(),
                    spaceDeviceRelBo.getOrganizationId(), deviceNumbers, spaceInfoVo));
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    /**
     * 查询地点和设备关系表详情
     * 
     * @param spaceDeviceRelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询地点和设备关系表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("spaceDeviceRelId") Long spaceDeviceRelId) {
        Map<String, Object> map = spaceDeviceRelService.getDetail(spaceDeviceRelId);
        return AjaxResult.success(map);
    }

    /**
     * 删除地点和设备关系表
     * 
     * @param spaceDeviceRelId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除地点和设备关系表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("spaceDeviceRelId") Long spaceDeviceRelId) {
        SpaceDeviceRelBo spaceDeviceRelBo = new SpaceDeviceRelBo();
        spaceDeviceRelBo.setId(spaceDeviceRelId);
        spaceDeviceRelBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = spaceDeviceRelService.updateSpaceDeviceRel(spaceDeviceRelBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 根据deviceId查询设备关系信息
     * 
     * @param deviceId
     * @return
     */
    @GetMapping("/getByDeviceId/{deviceId}")
    public AjaxResult<SpaceDeviceRelVo> getByDeviceId(@PathVariable("deviceId") Long deviceId) {
        SpaceDeviceRel spaceDeviceRel = this.spaceDeviceRelService.getByDeviceId(deviceId);
        if (spaceDeviceRel == null) {
            return AjaxResult.success();
        }
        return AjaxResult.success(BeanUtil.toBean(spaceDeviceRel, SpaceDeviceRelVo.class));
    }

}
