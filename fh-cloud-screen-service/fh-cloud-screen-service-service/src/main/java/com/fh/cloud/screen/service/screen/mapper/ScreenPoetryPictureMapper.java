package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryPictureDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo;

/**
 * 共话诗词图片资源表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
public interface ScreenPoetryPictureMapper extends BaseMapper<ScreenPoetryPictureDto> {

	List<ScreenPoetryPictureVo> getScreenPoetryPictureListByCondition(ScreenPoetryPictureConditionBo condition);

	ScreenPoetryPictureVo getScreenPoetryPictureByCondition(ScreenPoetryPictureConditionBo condition);

}
