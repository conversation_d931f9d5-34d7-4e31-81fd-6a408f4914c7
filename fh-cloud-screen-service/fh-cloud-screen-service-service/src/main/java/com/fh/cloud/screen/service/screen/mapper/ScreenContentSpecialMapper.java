package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecial;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 云屏紧急发布内容表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface ScreenContentSpecialMapper extends BaseMapper<ScreenContentSpecial> {

    List<ScreenContentSpecialVo> getScreenContentSpecialListByCondition(ScreenContentSpecialListConditionBo condition);

    /**
     * 根据地点组获取紧急发布内容
     *
     * @param organizationId 组织结构id，必须
     * @param campusId 校区id
     * @param spaceGroupId 地点组id，必须
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-17 10:02:15
     */
    List<ScreenContentSpecialVo> listScreenContentSpecialVosBySpaceGroupId(@Param("organizationId") Long organizationId,
        @Param("campusId") Long campusId, @Param("spaceGroupId") Long spaceGroupId, @Param("nowDate") Date nowDate);
}
