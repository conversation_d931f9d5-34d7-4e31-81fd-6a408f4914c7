package com.fh.cloud.screen.service.rest.controller;

import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayListConditionBo;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;
import com.fh.cloud.screen.service.rest.service.IWorkRestDayService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 作息时间天设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@RequestMapping("/rest/day")
@Validated
public class WorkRestDayController {

    @Autowired
    private IWorkRestDayService workRestDayService;

    /**
     * 查询作息时间天设置表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询作息时间天设置表列表", httpMethod = "POST")
    public AjaxResult getWorkRestDayListByCondition(@RequestBody WorkRestDayListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<WorkRestDayVo> pageInfo = new PageInfo<>(workRestDayService.getWorkRestDayListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("workRestDayList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增作息时间天设置表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增作息时间天设置表", httpMethod = "POST")
    public AjaxResult addWorkRestDay(@RequestBody WorkRestDayBo workRestDayBo) {
        boolean save = workRestDayService.addWorkRestDay(workRestDayBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改作息时间天设置表
     * 
     * @param workRestDayBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改作息时间天设置表", httpMethod = "POST")
    public AjaxResult updateWorkRestDay(@RequestBody WorkRestDayBo workRestDayBo) {
        if (null == workRestDayBo.getWorkRestDayId()) {
            return AjaxResult.fail("作息时间天设置表id不能为空");
        }
        boolean update = workRestDayService.updateWorkRestDay(workRestDayBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询作息时间天设置表详情
     * 
     * @param workRestDayId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询作息时间天设置表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("workRestDayId") Long workRestDayId) {
        WorkRestDayVo workRestDayVo = workRestDayService.getDetail(workRestDayId);
        return AjaxResult.success(workRestDayVo);
    }

    /**
     * 删除作息时间天设置表
     * 
     * @param workRestDayId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除作息时间天设置表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("workRestDayId") Long workRestDayId) {
        WorkRestDayBo workRestDayBo = new WorkRestDayBo();
        workRestDayBo.setWorkRestDayId(workRestDayId);
        workRestDayBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = workRestDayService.updateWorkRestDay(workRestDayBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
