package com.fh.cloud.screen.service.er.controller;

import com.fh.cloud.screen.service.er.api.ExamInfoSubjectApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoSubjectDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.service.IExamInfoSubjectService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 考场_考试计划里面一次考试科目信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@RestController
@Validated
public class ExamInfoSubjectController implements ExamInfoSubjectApi {

    @Autowired
    private IExamInfoSubjectService examInfoSubjectService;

    /**
     * 查询考场_考试计划里面一次考试科目信息分页列表
     * 
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @Override
    public AjaxResult<PageInfo<ExamInfoSubjectVo>>
        getExamInfoSubjectPageListByCondition(@RequestBody ExamInfoSubjectConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ExamInfoSubjectVo> pageInfo =
            new PageInfo<>(examInfoSubjectService.getExamInfoSubjectListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询考场_考试计划里面一次考试科目信息列表
     * 
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @Override
    public AjaxResult<List<ExamInfoSubjectVo>>
        getExamInfoSubjectListByCondition(@RequestBody ExamInfoSubjectConditionBo condition) {
        List<ExamInfoSubjectVo> list = examInfoSubjectService.getExamInfoSubjectListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增考场_考试计划里面一次考试科目信息
     * 
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @Override
    public AjaxResult addExamInfoSubject(@Validated @RequestBody ExamInfoSubjectBo examInfoSubjectBo) {
        return examInfoSubjectService.addExamInfoSubject(examInfoSubjectBo);
    }

    /**
     * 修改考场_考试计划里面一次考试科目信息
     * 
     * @param examInfoSubjectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @Override
    public AjaxResult updateExamInfoSubject(@Validated @RequestBody ExamInfoSubjectBo examInfoSubjectBo) {
        if (null == examInfoSubjectBo.getExamInfoSubjectId()) {
            return AjaxResult.fail("考场_考试计划里面一次考试科目信息id不能为空");
        }
        return examInfoSubjectService.updateExamInfoSubject(examInfoSubjectBo);
    }

    /**
     * 查询考场_考试计划里面一次考试科目信息详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @Override
    public AjaxResult<ExamInfoSubjectVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("考场_考试计划里面一次考试科目信息id不能为空");
        }
        ExamInfoSubjectVo vo = examInfoSubjectService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除考场_考试计划里面一次考试科目信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-10-09 15:27:03
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ExamInfoSubjectDto examInfoSubjectDto = new ExamInfoSubjectDto();
        examInfoSubjectDto.setExamInfoSubjectId(id);
        examInfoSubjectDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (examInfoSubjectService.updateById(examInfoSubjectDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 获取当前考试详情
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/1/16 10:00
     */
    @Override
    public AjaxResult getNowExamInfoByCondition(ExamInfoSubjectConditionBo conditionBo) {
        if (null == conditionBo.getSpaceGroupUseType() || null == conditionBo.getSpaceInfoId()
            || null == conditionBo.getExamStartTime()) {
            return AjaxResult.fail("参数错误");
        }
        return examInfoSubjectService.getNowExamInfoByCondition(conditionBo);
    }

}
