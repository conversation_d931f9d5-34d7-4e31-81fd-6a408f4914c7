package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContactDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContactVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 云屏产品咨询收集联系表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
public interface IScreenContactService extends IService<ScreenContactDto> {

    List<ScreenContactVo> getScreenContactListByCondition(ScreenContactConditionBo condition);

	AjaxResult addScreenContact(ScreenContactBo screenContactBo);

	AjaxResult updateScreenContact(ScreenContactBo screenContactBo);

	ScreenContactVo getScreenContactByCondition(ScreenContactConditionBo condition);

}

