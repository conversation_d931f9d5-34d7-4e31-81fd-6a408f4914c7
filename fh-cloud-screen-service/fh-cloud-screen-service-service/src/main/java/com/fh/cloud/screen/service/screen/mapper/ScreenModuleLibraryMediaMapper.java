package com.fh.cloud.screen.service.screen.mapper;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryGroupVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo;

import java.util.List;

/**
 * <p>
 * 云屏模块库媒体资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
public interface ScreenModuleLibraryMediaMapper extends BaseMapper<ScreenModuleLibraryMedia> {
    List<ScreenModuleLibraryMediaVo> getScreenModuleLibraryMediaListByCondition(ScreenModuleLibraryMediaBo condition);

    /**
     * 获取海报图片列表冗余 module_name、module_group_type、dict_label、organization_id
     *
     * @param condition dictionaryDataOrganizationId screenModuleLibraryIds
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo>
     * <AUTHOR>
     * @date 2023/3/23 15:14
     */
    List<ScreenModuleLibraryMediaVo> getPosterList(ScreenModuleLibraryMediaBo condition);

    /**
     * 查询【节日id集合】关联的海报图片
     *
     * @param condition the condition
     * @return festival module library media list
     * <AUTHOR>
     * @date 2023 -03-23 15:05:53
     */
    List<ScreenModuleLibraryMediaVo> getLabelModuleLibraryMediaList(ScreenModuleLibraryMediaBo condition);

    /**
     * 海报分组列表
     *
     * @param screenModuleLibraryMediaBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia>
     * <AUTHOR>
     * @date 2023/4/11 15:43
     */
    List<ScreenModuleLibraryGroupVo>
        getModuleGroupListByCondition(ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo);

    /**
     * 获取海报图片与标签关联记录1：N
     *
     * @param condition
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo>
     * <AUTHOR>
     * @date 2023/5/26 15:26
     */
    List<ScreenModuleLibraryMediaVo> getPosterLabelRel(ScreenModuleLibraryMediaBo condition);
}
