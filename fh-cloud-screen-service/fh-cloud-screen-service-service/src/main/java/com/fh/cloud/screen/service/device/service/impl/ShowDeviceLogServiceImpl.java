package com.fh.cloud.screen.service.device.service.impl;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceCaptureDto;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLogDto;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceLogMapper;
import com.fh.cloud.screen.service.device.service.IShowDeviceLogService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 设备日志表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
@Service
public class ShowDeviceLogServiceImpl extends ServiceImpl<ShowDeviceLogMapper, ShowDeviceLogDto>
    implements IShowDeviceLogService {

    @Resource
    private ShowDeviceLogMapper showDeviceLogMapper;

    @Override
    public List<ShowDeviceLogVo> getShowDeviceLogListByCondition(ShowDeviceLogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return showDeviceLogMapper.getShowDeviceLogListByCondition(condition);
    }

    @Override
    public Long addShowDeviceLog(ShowDeviceLogBo showDeviceLogBo) {
        ShowDeviceLogDto showDeviceLog = new ShowDeviceLogDto();
        BeanUtils.copyProperties(showDeviceLogBo, showDeviceLog);
        showDeviceLog.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(showDeviceLog)) {
            return showDeviceLog.getId();
        } else {
            return null;
        }
    }

    @Override
    public AjaxResult updateShowDeviceLog(ShowDeviceLogBo showDeviceLogBo) {
        ShowDeviceLogDto showDeviceLog = new ShowDeviceLogDto();
        BeanUtils.copyProperties(showDeviceLogBo, showDeviceLog);
        if (updateById(showDeviceLog)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ShowDeviceLogVo getShowDeviceLogByCondition(ShowDeviceLogConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ShowDeviceLogVo vo = showDeviceLogMapper.getShowDeviceLogByCondition(condition);
        return vo;
    }

    @Override
    public ShowDeviceLogVo getDetail(Long id) {
        ShowDeviceLogConditionBo condition = new ShowDeviceLogConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ShowDeviceLogVo> list = showDeviceLogMapper.getShowDeviceLogListByCondition(condition);
        ShowDeviceLogVo vo = new ShowDeviceLogVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public ShowDeviceLogVo getDetailByNumber(String deviceNumber) {
        LambdaQueryWrapper<ShowDeviceLogDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ShowDeviceLogDto::getDeviceNumber, deviceNumber);
        lqw.eq(ShowDeviceLogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.orderByDesc(ShowDeviceLogDto::getId);
        lqw.last("limit 1");
        ShowDeviceLogDto one = showDeviceLogMapper.selectOne(lqw);
        if (one == null) {
            return null;
        }
        ShowDeviceLogVo vo = new ShowDeviceLogVo();
        BeanUtils.copyProperties(one, vo);
        return vo;
    }
}