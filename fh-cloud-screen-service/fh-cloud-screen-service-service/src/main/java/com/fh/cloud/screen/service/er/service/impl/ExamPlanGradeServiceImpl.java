package com.fh.cloud.screen.service.er.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.ScreenContentStatusType;
import com.fh.cloud.screen.service.er.service.IExamPlanGradeService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecial;
import com.google.common.collect.Lists;
import com.light.core.utils.FuzzyQueryUtil;
import groovy.transform.AutoImplement;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.er.entity.dto.ExamPlanGradeDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo;
import com.fh.cloud.screen.service.er.mapper.ExamPlanGradeMapper;
import com.light.core.entity.AjaxResult;

/**
 * 考场_考试计划涉及的年级接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Service
public class ExamPlanGradeServiceImpl extends ServiceImpl<ExamPlanGradeMapper, ExamPlanGradeDto>
    implements IExamPlanGradeService {

    @Resource
    private ExamPlanGradeMapper examPlanGradeMapper;
    @Autowired
    private IExamPlanGradeService examPlanGradeService;

    @Override
    public List<ExamPlanGradeVo> getExamPlanGradeListByCondition(ExamPlanGradeConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return examPlanGradeMapper.getExamPlanGradeListByCondition(condition);
    }

    @Override
    public AjaxResult addExamPlanGrade(ExamPlanGradeBo examPlanGradeBo) {
        ExamPlanGradeDto examPlanGrade = new ExamPlanGradeDto();
        BeanUtils.copyProperties(examPlanGradeBo, examPlanGrade);
        examPlanGrade.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(examPlanGrade)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamPlanGrade(ExamPlanGradeBo examPlanGradeBo) {
        ExamPlanGradeDto examPlanGrade = new ExamPlanGradeDto();
        BeanUtils.copyProperties(examPlanGradeBo, examPlanGrade);
        if (updateById(examPlanGrade)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ExamPlanGradeVo getDetail(Long id) {
        ExamPlanGradeConditionBo condition = new ExamPlanGradeConditionBo();
        condition.setExamPlanGradeId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamPlanGradeVo> list = examPlanGradeMapper.getExamPlanGradeListByCondition(condition);
        ExamPlanGradeVo vo = new ExamPlanGradeVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public void batchDelAddExamPlanGrade(Long examPlanId, List<String> grades) {
        if (examPlanId == null || CollectionUtils.isEmpty(grades)) {
            return;
        }
        // 删除
        LambdaUpdateWrapper<ExamPlanGradeDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExamPlanGradeDto::getExamPlanId, examPlanId);
        updateWrapper.eq(ExamPlanGradeDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ExamPlanGradeDto examPlanGradeDto = new ExamPlanGradeDto();
        examPlanGradeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        update(examPlanGradeDto, updateWrapper);

        // 批量保存
        List<ExamPlanGradeDto> gradeDtos = grades.stream().map(grade -> {
            ExamPlanGradeDto examPlanGrade = new ExamPlanGradeDto();
            examPlanGrade.setExamPlanId(examPlanId);
            examPlanGrade.setGrade(grade);
            examPlanGrade.setIsDelete(StatusEnum.NOTDELETE.getCode());
            return examPlanGrade;
        }).collect(Collectors.toList());
        saveBatch(gradeDtos);
    }
}