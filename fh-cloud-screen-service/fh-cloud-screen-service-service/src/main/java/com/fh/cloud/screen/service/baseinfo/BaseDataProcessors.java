package com.fh.cloud.screen.service.baseinfo;

import java.util.ServiceLoader;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 处理器
 *
 * <AUTHOR>
 * @date 2022/6/1 10:40
 */
public class BaseDataProcessors {

    private static final ConcurrentHashMap<String, BaseDataService> PROCESSORS = new ConcurrentHashMap<>();

    static {
        ServiceLoader<BaseDataService> baseDataServices = ServiceLoader.load(BaseDataService.class);

        for (BaseDataService baseDataService : baseDataServices) {
            PROCESSORS.put(baseDataService.name(), baseDataService);
        }
    }

    /**
     * 获取指定的服务
     * 
     * @param extend
     * @return
     */
    public static BaseDataService get(String extend) {
        return PROCESSORS.get(extend);
    }

    /**
     * 获取所有支持的服务
     * 
     * @return
     */
    public static Set<String> getSupportedExtends() {
        return PROCESSORS.keySet();
    }
}
