package com.fh.cloud.screen.service.utils;

import java.nio.charset.StandardCharsets;

import com.fh.cloud.screen.service.consts.ConstantsConfig;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.crypto.symmetric.AES;

/**
 * 1、AES可逆的加解密方式，使用base64的方式，key为16位长度 2、同时提供md5摘要加密的封装
 *
 * <AUTHOR>
 * @date 2022/7/19 14:23
 */
public class EncryptUtil {

    /**
     * aes-bae64加解密方式-加密
     * 
     * @param content
     * @return
     */
    public static String encodeAes(String content) {
        return encodeAesBase64(content, ConstantsConfig.AES_KEY);
    }

    /**
     * aes-base64加解密方式-解密
     *
     * @param content
     * @param key
     * @return
     */
    public static String decodeAes(String content) {
        // 解决http的get请求+编码为空格的问题
        content = content.replace(" ", "+");
        return decodeAesBase64(content, ConstantsConfig.AES_KEY);
    }

    /**
     * aes的加解密方式-加密
     *
     * @param content
     * @param key
     * @return
     */
    private static String encodeAesBase64(String content, String key) {
        AES aes = SecureUtil.aes(key.getBytes(StandardCharsets.UTF_8));
        return aes.encryptBase64(content);
    }

    /**
     * aes的加解密方式-解密
     *
     * @param content
     * @param key
     * @return
     */
    private static String decodeAesBase64(String content, String key) {
        AES aes = SecureUtil.aes(key.getBytes(StandardCharsets.UTF_8));
        return aes.decryptStr(content);
    }

    /**
     * md5盐值加密(盐加在末尾)，用于摘要算法的加密
     *
     * @param content 内容拼接，通常排除appCode
     * @param salt 通常使用appSecret
     * @return
     */
    public static String md5(String content, String salt) {
        MD5 md5 = SecureUtil.md5();
        md5.setSalt(salt.getBytes(StandardCharsets.UTF_8));
        md5.setSaltPosition(content.length());
        // 32位小写的十六进制
        return md5.digestHex(content);
    }

    public static void main(String[] args) {
        // String json =
        // "{\"userOid\":\"owjt4ngqi39wk9hpf3rzzccvz3es0v7ult8igobp5q1dei98a0889xkte7d568wv\",\"organizationId\":\"12\"}";
        // String encryptKey = "pJjPQgcwTnnFbaxz";
        // System.out.println(encode(json, encryptKey));
        // System.out.println(decode("MYdy1Yfq5OdclEvFJejwCuyJ70KmDiqc1DYX17dvpKz+3WxVtJ4oDmUWuRWdg+LETG2ouKVN/I87ii/A8eqVUKv0b6J8hRH0eQajyN6T81LC5osXZXsBA6hq8tYYNnoGrzZPrOqQj/E64+0IV+WPBQ==",
        // encryptKey));
        // System.out.println(md5(json, encryptKey));
        // System.out.println(encodeAes("张三"));
        // System.out.println(decodeAes("rESw0tVbMLJhPsTcPd4+xw=="));

        // System.out.println(encodeAes("KKKKKK"));
    }
}
