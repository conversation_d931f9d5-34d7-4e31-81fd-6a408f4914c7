package com.fh.cloud.screen.service.label.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.label.entity.dto.LabelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelConditionBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;

/**
 * 标签表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
public interface LabelMapper extends BaseMapper<LabelDto> {

	List<LabelVo> getLabelListByCondition(LabelConditionBo condition);

}
