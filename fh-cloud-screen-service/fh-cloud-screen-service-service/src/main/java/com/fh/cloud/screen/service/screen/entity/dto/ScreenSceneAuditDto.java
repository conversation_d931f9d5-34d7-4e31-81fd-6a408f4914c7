package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云屏场景审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_scene_audit")
public class ScreenSceneAuditDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_scene_audit_id", type = IdType.AUTO)
	private Long screenSceneAuditId;

	/**
	 * FK所属组织ID
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * FK所属校区ID
	 */
	@TableField("campus_id")
	private Long campusId;

	/**
	 * FK空间ID或者班级id
	 */
	@TableField("space_info_id")
	private Long spaceInfoId;

	/**
	 * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
	 */
	@TableField("space_group_use_type")
	private Integer spaceGroupUseType;

	/**
	 * FK设备id
	 */
	@TableField("show_device_id")
	private Long showDeviceId;

	/**
	 * 场景名称
	 */
	@TableField("screen_scene_name")
	private String screenSceneName;

	/**
	 * 云屏场景布局，透传前端数据
	 */
	@TableField("screen_scene_layout")
	private String screenSceneLayout;

	/**
	 * 地点组id
	 */
	@TableField("space_group_id")
	private Long spaceGroupId;

	/**
	 * 场景类型:1考勤，2课堂，3课间，4其他，5常规，6自建场景
	 */
	@TableField("screen_scene_type")
	private Long screenSceneType;

	/**
	 * 设备模式：1横屏，2竖屏。
	 */
	@TableField("screen_device_pattern")
	private Integer screenDevicePattern;

	/**
	 * 场景顺序：1，2，3...
	 */
	@TableField("screen_index")
	private Long screenIndex;

	/**
	 * 同一个场景内轮播的场景名称
	 */
	@TableField("screen_play_name")
	private String screenPlayName;

	/**
	 * 同一个场景内的轮播的场景顺序：1，2，3...
	 */
	@TableField("screen_play_index")
	private Long screenPlayIndex;

	/**
	 * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
	 */
	@TableField("start_time")
	private Date startTime;

	/**
	 * 场景时间-开始时间，多个轮播场景的时候，场景时间相同
	 */
	@TableField("end_time")
	private Date endTime;

	/**
	 * 发布方式：1全局发布，2点位发布
	 */
	@TableField("publish_type")
	private Integer publishType;

	/**
	 * 自定义场景是否全屏类型：1全屏，2不是全屏
	 */
	@TableField("device_full_type")
	private Integer deviceFullType;

	/**
	 * 审核状态 1-待审核 2-审核通过 3-审核驳回
	 */
	@TableField("audit_type")
	private Integer auditType;

	/**
	 * 驳回原因
	 */
	@TableField("reason")
	private String reason;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 云屏模块数据
	 */
	@TableField("screen_module_data")
	private String screenModuleData;

	/**
	 * 请求参数
	 */
	@TableField("param")
	private String param;

	/**
	 * 审核人
	 */
	@TableField("audit_user")
	private String auditUser;

	/**
	 * 审核时间
	 */
	@TableField("audit_time")
	private Date auditTime;

	/**
	 * 开始日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
	 */
	@TableField("start_date")
	private Date startDate;

	/**
	 * 结束日期yyyy-MM-dd，用于控制周期性的自定义场景时间生效
	 */
	@TableField("end_date")
	private Date endDate;

	/**
	 * 星期几：默认0，1-7，分别为星期一到星期日,多个使用英文逗号分割，用于控制周期性的自定义场景时间生效
	 */
	@TableField("weeks")
	private String weeks;

	/**
	 * 监管教育局id
	 */
	@TableField("parent_organization_id")
	private Long parentOrganizationId;

}
