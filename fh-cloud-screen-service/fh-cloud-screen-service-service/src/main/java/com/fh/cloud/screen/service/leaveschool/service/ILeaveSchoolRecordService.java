package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 放学记录表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
public interface ILeaveSchoolRecordService extends IService<LeaveSchoolRecordDto> {

    List<LeaveSchoolRecordVo> getLeaveSchoolRecordListByCondition(LeaveSchoolRecordConditionBo condition);

	AjaxResult addLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo);

	AjaxResult updateLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo);

	LeaveSchoolRecordVo getLeaveSchoolRecordByCondition(LeaveSchoolRecordConditionBo condition);

	/**
	 * 获取放学记录
	 *
	 * @param leaveSchoolRecordBo
	 * @return com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordVo
	 * <AUTHOR>
	 * @date 2023/8/23 19:46
	 **/
	LeaveSchoolRecordVo getLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo);

	/**
	 * 更新并推送放学消息
	 *
	 * @param leaveSchoolRecordBo
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2024/10/8 11:54
	 **/
	AjaxResult updateLeaveSchoolRecordAndSendMsg(LeaveSchoolRecordBo leaveSchoolRecordBo);

}

