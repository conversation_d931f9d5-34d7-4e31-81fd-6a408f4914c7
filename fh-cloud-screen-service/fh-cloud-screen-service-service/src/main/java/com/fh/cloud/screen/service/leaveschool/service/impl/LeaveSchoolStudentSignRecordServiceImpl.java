package com.fh.cloud.screen.service.leaveschool.service.impl;

import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordSaveBatchBo;
import org.apache.commons.lang3.StringUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolStudentSignRecordDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolStudentSignRecordService;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolStudentSignRecordMapper;
import com.light.core.entity.AjaxResult;
/**
 * 学生进出记录表接口实现类
 *
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:58:52
 */
@Service
public class LeaveSchoolStudentSignRecordServiceImpl extends ServiceImpl<LeaveSchoolStudentSignRecordMapper, LeaveSchoolStudentSignRecordDto> implements ILeaveSchoolStudentSignRecordService {

	@Resource
	private LeaveSchoolStudentSignRecordMapper leaveSchoolStudentSignRecordMapper;
	
    @Override
	public List<LeaveSchoolStudentSignRecordVo> getLeaveSchoolStudentSignRecordListByCondition(LeaveSchoolStudentSignRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolStudentSignRecordMapper.getLeaveSchoolStudentSignRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolStudentSignRecord(LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo) {
		LeaveSchoolStudentSignRecordDto leaveSchoolStudentSignRecord = new LeaveSchoolStudentSignRecordDto();
		BeanUtils.copyProperties(leaveSchoolStudentSignRecordBo, leaveSchoolStudentSignRecord);
		leaveSchoolStudentSignRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(leaveSchoolStudentSignRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateLeaveSchoolStudentSignRecord(LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo) {
		LeaveSchoolStudentSignRecordDto leaveSchoolStudentSignRecord = new LeaveSchoolStudentSignRecordDto();
		BeanUtils.copyProperties(leaveSchoolStudentSignRecordBo, leaveSchoolStudentSignRecord);
		if(updateById(leaveSchoolStudentSignRecord)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public LeaveSchoolStudentSignRecordVo getLeaveSchoolStudentSignRecordByCondition(LeaveSchoolStudentSignRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return leaveSchoolStudentSignRecordMapper.getLeaveSchoolStudentSignRecordByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolStudentSignRecordBatch(LeaveSchoolStudentSignRecordSaveBatchBo leaveSchoolStudentSignRecordSaveBatchBo) {
		return null;
	}

}