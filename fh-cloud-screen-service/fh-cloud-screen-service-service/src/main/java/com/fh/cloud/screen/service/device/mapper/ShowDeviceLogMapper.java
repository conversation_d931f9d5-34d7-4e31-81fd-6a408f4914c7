package com.fh.cloud.screen.service.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLogDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;

/**
 * 设备日志表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
public interface ShowDeviceLogMapper extends BaseMapper<ShowDeviceLogDto> {

	List<ShowDeviceLogVo> getShowDeviceLogListByCondition(ShowDeviceLogConditionBo condition);

	ShowDeviceLogVo getShowDeviceLogByCondition(ShowDeviceLogConditionBo condition);

}
