package com.fh.cloud.screen.service.calendar.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;
import com.fh.cloud.screen.service.calendar.mapper.SchoolCalendarDayMapper;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarDayService;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarService;
import com.fh.cloud.screen.service.enums.SchoolCalendarRedisKeyEnum;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 校历上课日日期表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Service
public class SchoolCalendarDayServiceImpl extends ServiceImpl<SchoolCalendarDayMapper, SchoolCalendarDay>
    implements ISchoolCalendarDayService {

    @Resource
    private SchoolCalendarDayMapper schoolCalendarDayMapper;

    @Autowired
    private ISchoolCalendarService schoolCalendarService;

    @Resource
    private RedisComponent redisComponent;

    @Override
    public List<SchoolCalendarDayVo> getSchoolCalendarDayListByCondition(SchoolCalendarDayListConditionBo condition) {
        SchoolCalendarVo detail = schoolCalendarService.getDetail(condition.getOrganizationId());
        condition.setSchoolCalendarId(detail.getSchoolCalendarId());
        List<SchoolCalendarDayVo> schoolCalendarDayVos =
            schoolCalendarDayMapper.getSchoolCalendarDayListByCondition(condition);
        // 日期格式化为00:00:00点
        if (CollectionUtils.isNotEmpty(schoolCalendarDayVos)) {
            for (SchoolCalendarDayVo schoolCalendarDayVo : schoolCalendarDayVos) {
                schoolCalendarDayVo.setDay(DateKit.getDayZore(schoolCalendarDayVo.getDay()));
            }
        }
        return schoolCalendarDayVos;
    }

    @Override
    public boolean saveOrUpdateSchoolCalendarDay(SchoolCalendarDayBo schoolCalendarDayBo) {
        SchoolCalendarVo detail = schoolCalendarService.getDetail(schoolCalendarDayBo.getOrganizationId());
        schoolCalendarDayBo.setSchoolCalendarId(detail.getSchoolCalendarId());

        SchoolCalendarDay schoolCalendarDay = new SchoolCalendarDay();
        BeanUtils.copyProperties(schoolCalendarDayBo, schoolCalendarDay);
        if (null == schoolCalendarDay.getSchoolCalendarDayId()) {
            schoolCalendarDay.setIsDelete(StatusEnum.NOTDELETE.getCode());
        }
        // 设置缓冲失效
        redisComponent.del(SchoolCalendarRedisKeyEnum.SCHOOL_WEEK_REDIS_KEY.getValue()
            .concat(schoolCalendarDayBo.getOrganizationId().toString()));
        redisComponent.del(SchoolCalendarRedisKeyEnum.SCHOOL_DAY_REDIS_KEY.getValue()
            .concat(schoolCalendarDayBo.getOrganizationId().toString()));
        return saveOrUpdate(schoolCalendarDay);
    }

    @Override
    public boolean updateSchoolCalendarDay(SchoolCalendarDayBo schoolCalendarDayBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long schoolCalendarDayId) {
        return null;
    }

    @Override
    public List<SchoolCalendarDay> getBySchoolCalendarId(Long schoolCalendarId) {
        QueryWrapper<SchoolCalendarDay> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SchoolCalendarDay::getSchoolCalendarId, schoolCalendarId)
            .eq(SchoolCalendarDay::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.schoolCalendarDayMapper.selectList(queryWrapper);
    }

    @Override
    public List<SchoolCalendarDay> getBySchoolCalendarIdAndMonth(Long schoolCalendarId, String month) {
        return this.schoolCalendarDayMapper.getBySchoolCalendarIdAndMonth(schoolCalendarId, month);
    }

}