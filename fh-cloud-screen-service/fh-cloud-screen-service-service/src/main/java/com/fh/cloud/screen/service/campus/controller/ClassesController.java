package com.fh.cloud.screen.service.campus.controller;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.campus.api.ClassesScreenApi;
import com.light.core.entity.AjaxResult;
import com.light.user.clazz.entity.vo.ClazzVo;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/13 2:26 下午 @description：
 */
@RestController
@Api(value = "", tags = "班级")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class ClassesController implements ClassesScreenApi {

    private final BaseDataService baseDataService;

    /**
     * 根据ID获取班级信息
     *
     * @param classesId
     * @return
     */
    @Override
    public AjaxResult<ClazzVo> getById(@PathVariable("classesId") Long classesId) {
        final ClazzVo classVo = this.baseDataService.getByClazzId(classesId);
        return AjaxResult.success(classVo);
    }
}
