package com.fh.cloud.screen.service.er.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoTeacherDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 考场_考试计划里面一次考试的老师接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface IExamInfoTeacherService extends IService<ExamInfoTeacherDto> {

    List<ExamInfoTeacherVo> getExamInfoTeacherListByCondition(ExamInfoTeacherConditionBo condition);

    AjaxResult addExamInfoTeacher(ExamInfoTeacherBo examInfoTeacherBo);

    AjaxResult updateExamInfoTeacher(ExamInfoTeacherBo examInfoTeacherBo);

    ExamInfoTeacherVo getDetail(Long id);

    /**
     * 批量添加
     *
     * @param examInfoTeacherBos
     * @return
     */
    AjaxResult addExamInfoTeacherBatch(List<ExamInfoTeacherBo> examInfoTeacherBos);

    /**
     * 批量添加
     *
     * @param examInfoTeacherBos
     * @return
     */
    AjaxResult addExamInfoTeacherBatchByXML(List<ExamInfoTeacherBo> examInfoTeacherBos);

    /**
     * 批量更新
     *
     * @param examInfoTeacherBos
     * @return
     */
    @Deprecated
    AjaxResult updateExamInfoTeacherBatch(List<ExamInfoTeacherBo> examInfoTeacherBos);

    /**
     * 批量删除
     *
     * @param examInfoId
     * @return
     */
    AjaxResult deleteExamInfoTeacherBatch(Long examInfoId);
}
