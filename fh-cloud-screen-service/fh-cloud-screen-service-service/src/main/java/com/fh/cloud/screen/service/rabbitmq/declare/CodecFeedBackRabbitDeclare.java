package com.fh.cloud.screen.service.rabbitmq.declare;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fh.cp.codec.rabbitmq.constant.CodecRabbitConstant;


/**
 * 生产消息使用
 * <AUTHOR> codec相关rabbitmq 声明，生产消息使用 （不声明在启动时，如mq中没有该信息 会报错 但不影响服务启动） 注：可放到单独模块供全局使用
 */
@Configuration
public class CodecFeedBackRabbitDeclare {

    /**
     * excahnge一个类型一个即可，可以公用
     * @return
     */
    @Bean
    public DirectExchange codecProducerExchange() {
        return new DirectExchange(CodecRabbitConstant.CODEC_PRODUCER_EXCHANGE);
    }

    /**
     * ######################## 转码服务监听 ############################
     */
    /**
     * Queue和Binding成对出现即可，按照业务
     * @return
     */
    @Bean
    public Queue codecProducerAddQueue() {
        return new Queue(CodecRabbitConstant.CODEC_PRODUCER_ADD_QUEUE);
    }
    @Bean
    public Binding codecProducerAddBinding() {
        return BindingBuilder.bind(codecProducerAddQueue()).to(codecProducerExchange()).withQueueName();
    }
}
