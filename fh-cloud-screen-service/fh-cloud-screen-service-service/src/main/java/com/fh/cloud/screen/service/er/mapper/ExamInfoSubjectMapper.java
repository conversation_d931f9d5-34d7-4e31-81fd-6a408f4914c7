package com.fh.cloud.screen.service.er.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoSubjectDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import org.apache.ibatis.annotations.Param;

/**
 * 考场_考试计划里面一次考试科目信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
public interface ExamInfoSubjectMapper extends BaseMapper<ExamInfoSubjectDto> {

    List<ExamInfoSubjectVo> getExamInfoSubjectListByCondition(ExamInfoSubjectConditionBo condition);

    List<ExamInfoSubjectVo> getExamInfoSubjectListBySpaceAndDate(@Param("spaceGroupUseType") Integer spaceGroupUseType,
        @Param("spaceInfoId") Long spaceInfoId, @Param("date") Date date);

    List<ExamInfoSubjectVo> getNowExamInfoByCondition(ExamInfoSubjectConditionBo conditionBo);

    List<ExamInfoSubjectVo> getExamInfoSubjectListByOrganizationId(@Param("organizationId")Long organizationId);
}
