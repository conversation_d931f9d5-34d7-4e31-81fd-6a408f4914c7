package com.fh.cloud.screen.service.label.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 标签表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:33
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("label")
public class LabelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "label_id", type = IdType.AUTO)
    private Long labelId;

    /**
     * 父级id
     */
    @TableField("parent_label_id")
    private Long parentLabelId;

    /**
     * 层级：1：标签组别，2：标签
     */
    @TableField("level")
    private Integer level;

    /**
     * 标签类别，1：海报标签
     */
    @TableField("type")
    private Integer type;

    /**
     * 标签名称
     */
    @TableField("label_name")
    private String labelName;

    /**
     * 标签名称
     */
    @TableField("label_sort")
    private Integer labelSort;

    /**
     * 自定义节点对应学校，默认0：通用所有学校
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 海报标签类别：1默认海报标签，2节假日海报标签
     */
    @TableField("poster_type")
    private Integer posterType;

    /**
     * 班级id
     */
    @TableField("classes_id")
    private Long classesId;

}
