package com.fh.cloud.screen.service.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.common.entity.dto.DictionaryData;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/30 16:58
 */
public interface ScreenDictionaryDataMapper extends BaseMapper<DictionaryData> {

    List<DictionaryDataVo> getDictionaryDataListByCondition(DictionaryDataListConditionBo condition);
}
