package com.fh.cloud.screen.service.rest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestDay;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 作息时间天设置表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface WorkRestDayMapper extends BaseMapper<WorkRestDay> {

    List<WorkRestDayVo> getWorkRestDayListByCondition(WorkRestDayListConditionBo condition);

    /**
     * 查询该年级的作息时间列表
     *
     * @param organizationId the organization id
     * @param grade the grade
     * @return work rest day list by grade
     * <AUTHOR>
     * @date 2023 -09-19 15:20:06
     */
    List<WorkRestDayVo> getWorkRestDayListByGrade(@Param("organizationId") Long organizationId,
        @Param("grade") String grade);
}
