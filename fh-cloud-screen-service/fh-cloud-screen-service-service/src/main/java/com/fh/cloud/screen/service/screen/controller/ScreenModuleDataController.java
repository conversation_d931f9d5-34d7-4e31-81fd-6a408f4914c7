package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.common.service.IScreenDictionaryDataService;
import com.fh.cloud.screen.service.enums.DictionaryType;
import com.fh.cloud.screen.service.screen.api.ScreenModuleApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleDataService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云屏模块表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@RestController
@Api(value = "", tags = "模块管理")
@Slf4j
public class ScreenModuleDataController implements ScreenModuleApi {

    @Autowired
    private IScreenModuleDataService screenModuleDataService;
    @Autowired
    private IScreenDictionaryDataService dictionaryDataService;

    /**
     * 查询云屏模块表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询云屏模块表列表", httpMethod = "POST")
    public AjaxResult getScreenModuleDataListByCondition(@RequestBody ScreenModuleDataListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", screenModuleDataService.getScreenModuleDataListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ScreenModuleDataVo> pageInfo =
                new PageInfo<>(screenModuleDataService.getScreenModuleDataListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 新增云屏模块表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "新增云屏模块表", httpMethod = "POST")
    public AjaxResult addScreenModuleData(@RequestBody ScreenModuleDataBo screenModuleDataBo) {
        if (screenModuleDataService.checkCustomModuleNameExist(screenModuleDataBo)) {
            return AjaxResult.fail("模块名称重复");
        }

        boolean save = screenModuleDataService.addScreenModuleData(screenModuleDataBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏模块表
     *
     * @param screenModuleDataBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "修改云屏模块表", httpMethod = "POST")
    public AjaxResult updateScreenModuleData(@RequestBody ScreenModuleDataBo screenModuleDataBo) {
        if (null == screenModuleDataBo.getScreenModuleDataId()) {
            return AjaxResult.fail("云屏模块表id不能为空");
        }
        if (screenModuleDataService.checkCustomModuleNameExist(screenModuleDataBo)) {
            return AjaxResult.fail("模块名称重复");
        }
        boolean update = screenModuleDataService.updateScreenModuleData(screenModuleDataBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏模块表详情
     *
     * @param screenModuleDataId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询云屏模块表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenModuleDataId") Long screenModuleDataId) {
        ScreenModuleDataVo screenModuleDataVo = screenModuleDataService.getDetail(screenModuleDataId);
        return AjaxResult.success(screenModuleDataVo);
    }

    /**
     * 删除云屏模块表
     *
     * @param screenModuleDataId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "删除云屏模块表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenModuleDataId") Long screenModuleDataId) {
        ScreenModuleDataBo screenModuleDataBo = new ScreenModuleDataBo();
        screenModuleDataBo.setScreenModuleDataId(screenModuleDataId);
        screenModuleDataBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenModuleDataService.updateScreenModuleData(screenModuleDataBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏模块表列表-学校（如果学校没有模块会初始化预置的模块）
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "查询云屏模块表分组显示-学校", httpMethod = "POST")
    public AjaxResult getScreenModuleDataGroupMapByConditionOfSchool(ScreenModuleDataListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleDataVo> screenModuleDataVos =
            screenModuleDataService.getScreenModuleDataListByCondition(condition);
        if (CollectionUtils.isEmpty(screenModuleDataVos)) {
            // 如果数据为空则需要不带条件查询一次全数据，全数据为空则初始化
            ScreenModuleDataListConditionBo conditionAll = new ScreenModuleDataListConditionBo();
            conditionAll.setOrganizationId(condition.getOrganizationId());
            conditionAll.setPageNo(SystemConstants.NO_PAGE);
            List<ScreenModuleDataVo> screenModuleDataVosAll =
                screenModuleDataService.getScreenModuleDataListByCondition(conditionAll);
            if (CollectionUtils.isEmpty(screenModuleDataVosAll)) {
                screenModuleDataService.initScreenModuleData(condition.getOrganizationId());
            }
            screenModuleDataVos = screenModuleDataService.getScreenModuleDataListByCondition(condition);
        }
        LinkedHashMap<Long, List<ScreenModuleDataVo>> moduleGroupMap = screenModuleDataVos.stream().collect(
            Collectors.groupingBy(ScreenModuleDataVo::getModuleGroupType, LinkedHashMap::new, Collectors.toList()));
        LinkedHashMap<String, List<ScreenModuleDataVo>> resultMap = Maps.newLinkedHashMap();
        if (!moduleGroupMap.isEmpty()) {
            Map<String, String> dictionaryDataMap =
                dictionaryDataService.getDictionaryMapByType(DictionaryType.MODULE_GROUP_TYPE.getValue());
            moduleGroupMap.forEach((moduleGroupType, screenModuleDataVosTemp) -> {
                String labelName = dictionaryDataMap.get(String.valueOf(moduleGroupType));
                if (StringUtils.isNotBlank(labelName)) {
                    resultMap.put(labelName, screenModuleDataVosTemp);
                }
            });
        }
        return AjaxResult.success(resultMap);
    }

    /**
     * 学校模块新增或移除预置模块
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @ApiOperation(value = "学校模块新增或移除预置模块", httpMethod = "POST")
    public AjaxResult updatePresetModule(ScreenModuleDataBo screenModuleDataBo) {
        if (screenModuleDataBo.getOrganizationId() == null) {
            return AjaxResult.fail("组织不允许为空");
        }
        if (CollectionUtils.isEmpty(screenModuleDataBo.getSelectModuleLibraryIds())) {
            return AjaxResult.fail("预置模块不允许为空");
        }
        try {
            screenModuleDataService.updatePresetModule(screenModuleDataBo);
        } catch (Exception e) {
            log.error("updatePresetModule error:", e);
            return AjaxResult.fail("学校模块新增或移除预置模块错误");
        }
        return AjaxResult.success();
    }
}
