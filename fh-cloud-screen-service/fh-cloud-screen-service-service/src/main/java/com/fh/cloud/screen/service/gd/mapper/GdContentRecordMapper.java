package com.fh.cloud.screen.service.gd.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.gd.entity.dto.GdContentRecordDto;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordConditionBo;
import com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo;

/**
 * 稿定内容记录Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-12 13:54:37
 */
public interface GdContentRecordMapper extends BaseMapper<GdContentRecordDto> {

	List<GdContentRecordVo> getGdContentRecordListByCondition(GdContentRecordConditionBo condition);

	GdContentRecordVo getGdContentRecordByCondition(GdContentRecordConditionBo condition);

}
