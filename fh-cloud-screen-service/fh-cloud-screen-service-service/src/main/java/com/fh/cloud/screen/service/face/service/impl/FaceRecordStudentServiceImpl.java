package com.fh.cloud.screen.service.face.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fh.cloud.screen.service.enums.FaceBrandType;
import com.fh.cloud.screen.service.enums.FaceStatusType;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordUploadResultVo;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;
import com.fh.cloud.screen.service.facebody.FaceBodyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.enums.FaceSourceType;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordStudentDto;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.fh.cloud.screen.service.face.mapper.FaceRecordStudentMapper;
import com.fh.cloud.screen.service.face.service.IFaceRecordStudentService;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import com.light.user.student.entity.vo.StudentVo;

/**
 * 学生人脸库接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
@Service
public class FaceRecordStudentServiceImpl extends ServiceImpl<FaceRecordStudentMapper, FaceRecordStudentDto>
    implements IFaceRecordStudentService {

    @Resource
    private FaceRecordStudentMapper faceRecordStudentMapper;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private IFaceConfigService faceConfigService;
    @Autowired
    private FaceBodyService faceBodyService;

    @Override
    public List<FaceRecordStudentVo> getFaceRecordStudentListByCondition(FaceRecordStudentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return faceRecordStudentMapper.getFaceRecordStudentListByCondition(condition);
    }

    @Override
    public AjaxResult addFaceRecordStudent(FaceRecordStudentBo faceRecordStudentBo) {
        FaceRecordStudentDto faceRecordStudent = new FaceRecordStudentDto();
        BeanUtils.copyProperties(faceRecordStudentBo, faceRecordStudent);
        faceRecordStudent.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(faceRecordStudent)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateFaceRecordStudent(FaceRecordStudentBo faceRecordStudentBo) {
        FaceRecordStudentDto faceRecordStudent = new FaceRecordStudentDto();
        BeanUtils.copyProperties(faceRecordStudentBo, faceRecordStudent);

        LambdaUpdateWrapper<FaceRecordStudentDto> lambdaUpdateWrapper =
            new UpdateWrapper<FaceRecordStudentDto>().lambda();
        lambdaUpdateWrapper.eq(FaceRecordStudentDto::getFaceRecordStudentId,
            faceRecordStudentBo.getFaceRecordStudentId());
        lambdaUpdateWrapper.ne(FaceRecordStudentDto::getFaceStatus, FaceStatusType.UPLOAD_SUCCESS.getValue());
        if (update(faceRecordStudent, lambdaUpdateWrapper)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateFaceRecordStudentBatch(List<FaceRecordStudentBo> faceRecordStudentBoList) {
        for (FaceRecordStudentBo faceRecordStudentBo : faceRecordStudentBoList) {
            updateFaceRecordStudent(faceRecordStudentBo);
        }
        return AjaxResult.success("修改成功");
    }

    @Override
    public FaceRecordStudentVo getDetail(Long id) {
        FaceRecordStudentConditionBo condition = new FaceRecordStudentConditionBo();
        condition.setFaceRecordStudentId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<FaceRecordStudentVo> list = faceRecordStudentMapper.getFaceRecordStudentListByCondition(condition);
        FaceRecordStudentVo vo = new FaceRecordStudentVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public AjaxResult<FaceRecordUploadResultVo>
        upload(List<FaceRecordStudentConditionBo> faceRecordStudentConditionBos) {
        // 参数处理：校验->默认字段设置
        if (CollectionUtils.isEmpty(faceRecordStudentConditionBos)) {
            return AjaxResult.success();
        }
        faceRecordStudentConditionBos.forEach(faceRecordStudentConditionBo -> {
            if (StringUtils.isNotBlank(faceRecordStudentConditionBo.getFaceMediaNameOri())) {
                faceRecordStudentConditionBo
                    .setFaceMediaName(StringUtils.substring(faceRecordStudentConditionBo.getFaceMediaNameOri(), 0,
                        faceRecordStudentConditionBo.getFaceMediaNameOri().indexOf(ConstString.dot)));
                if (StringUtils.isBlank(faceRecordStudentConditionBo.getRealName())) {
                    faceRecordStudentConditionBo.setRealName(faceRecordStudentConditionBo.getFaceMediaName());
                }
            }
        });
        Long organizationId = faceRecordStudentConditionBos.get(0).getOrganizationId();
        Integer sourceType = faceRecordStudentConditionBos.get(0).getSourceType();
        Long classesId = faceRecordStudentConditionBos.get(0).getClassesId();

        // 封装导入db的数据
        List<FaceRecordStudentDto> faceRecordStudentDtos = Lists.newArrayList();
        List<FaceRecordStudentVo> faceRecordStudentVosSuccess = Lists.newArrayList();
        List<FaceRecordStudentVo> faceRecordStudentVosFail = Lists.newArrayList();
        if (sourceType.equals(FaceSourceType.IMPORT.getValue())) {
            // realName,FaceRecordStudentConditionBo
            Map<String, FaceRecordStudentConditionBo> faceRecordStudentConditionBoMapRealName =
                faceRecordStudentConditionBos.stream().filter(
                    faceRecordStudentConditionBo -> StringUtils.isNotBlank(faceRecordStudentConditionBo.getRealName()))
                    .collect(Collectors.toMap(FaceRecordStudentConditionBo::getRealName, a -> a, (k1, k2) -> k1));
            List<String> realNameList = Lists.newArrayList(faceRecordStudentConditionBoMapRealName.keySet());
            // realName，StudentVo
            List<StudentVo> StudentVosFromDB = baseDataService.getStudentVoListByClassesId(classesId);
            Map<String,
                List<StudentVo>> realNameStudentVoMap = StudentVosFromDB.stream()
                    .filter(StudentVo -> StudentVo.getUserVo() != null
                        && StringUtils.isNotBlank(StudentVo.getUserVo().getRealName()))
                    .collect(Collectors.groupingBy(StudentVo -> StudentVo.getUserVo().getRealName()));
            // userOid，FaceRecordStudentVo
            FaceRecordStudentConditionBo faceRecordStudentConditionBo = new FaceRecordStudentConditionBo();
            faceRecordStudentConditionBo.setPageNo(SystemConstants.NO_PAGE);
            faceRecordStudentConditionBo.setOrganizationId(organizationId);
            faceRecordStudentConditionBo.setRealNames(realNameList);
            List<FaceRecordStudentVo> faceRecordStudentVos =
                getFaceRecordStudentListByCondition(faceRecordStudentConditionBo);
            Map<String,
                FaceRecordStudentVo> userOidFaceRecordStudentVoMap = faceRecordStudentVos.stream()
                    .filter(faceRecordStudentVo -> StringUtils.isNotBlank(faceRecordStudentVo.getUserOid()))
                    .collect(Collectors.toMap(FaceRecordStudentVo::getUserOid, a -> a, (k1, k2) -> k1));
            // 封装更新数据或者新增数据入待更新list
            for (String realName : realNameList) {
                FaceRecordStudentVo tempResultVo = new FaceRecordStudentVo();
                BeanUtils.copyProperties(faceRecordStudentConditionBoMapRealName.get(realName), tempResultVo);
                if (!realNameStudentVoMap.containsKey(realName)) {
                    faceRecordStudentVosFail.add(tempResultVo);
                    continue;
                }
                faceRecordStudentVosSuccess.add(tempResultVo);
                List<StudentVo> studentVosTemp = realNameStudentVoMap.get(realName);
                for (StudentVo studentVo : studentVosTemp) {
                    // 该userOid的人脸数据已经有了则更新
                    if (userOidFaceRecordStudentVoMap.containsKey(studentVo.getUserOid())) {
                        FaceRecordStudentDto faceRecordStudent = new FaceRecordStudentDto();
                        FaceRecordStudentVo faceRecordStudentVo =
                            userOidFaceRecordStudentVoMap.get(studentVo.getUserOid());
                        FaceRecordStudentConditionBo recordStudentConditionBo =
                            faceRecordStudentConditionBoMapRealName.get(realName);
                        recordStudentConditionBo.setFaceRecordStudentId(faceRecordStudentVo.getFaceRecordStudentId());
                        recordStudentConditionBo.setFaceStatus(FaceStatusType.UPLOAD_ING.getValue());
                        BeanUtils.copyProperties(recordStudentConditionBo, faceRecordStudent);
                        faceRecordStudent.setUpdateTime(new Date());
                        faceRecordStudentDtos.add(faceRecordStudent);
                    } else {
                        FaceRecordStudentConditionBo faceRecordStudentConditionBoAdd =
                            faceRecordStudentConditionBoMapRealName.get(realName);
                        faceRecordStudentConditionBoAdd.setUserOid(studentVo.getUserOid());
                        FaceRecordStudentDto faceRecordStudent = new FaceRecordStudentDto();
                        BeanUtils.copyProperties(faceRecordStudentConditionBoAdd, faceRecordStudent);
                        faceRecordStudentDtos.add(faceRecordStudent);
                    }
                }
            }
        }
        if (sourceType.equals(FaceSourceType.SINGLE.getValue())) {
            // userOid,FaceRecordStudentConditionBo
            FaceRecordStudentConditionBo faceRecordStudentConditionBo = faceRecordStudentConditionBos.get(0);
            String userOid = faceRecordStudentConditionBo.getUserOid();
            FaceRecordStudentConditionBo faceRecordStudentConditionBoQuery = new FaceRecordStudentConditionBo();
            faceRecordStudentConditionBoQuery.setPageNo(SystemConstants.NO_PAGE);
            faceRecordStudentConditionBoQuery.setOrganizationId(organizationId);
            faceRecordStudentConditionBoQuery.setUserOid(userOid);
            List<FaceRecordStudentVo> faceRecordStudentVos =
                getFaceRecordStudentListByCondition(faceRecordStudentConditionBoQuery);
            if (CollectionUtils.isNotEmpty(faceRecordStudentVos)) {
                FaceRecordStudentVo faceRecordStudentVoTemp = faceRecordStudentVos.get(0);
                FaceRecordStudentDto faceRecordStudent = new FaceRecordStudentDto();
                FaceRecordStudentConditionBo recordStudentConditionBo = faceRecordStudentConditionBo;
                recordStudentConditionBo.setFaceRecordStudentId(faceRecordStudentVoTemp.getFaceRecordStudentId());
                recordStudentConditionBo.setFaceStatus(FaceStatusType.UPLOAD_ING.getValue());
                BeanUtils.copyProperties(recordStudentConditionBo, faceRecordStudent);
                faceRecordStudent.setUpdateTime(new Date());
                faceRecordStudentDtos.add(faceRecordStudent);
            } else {
                FaceRecordStudentConditionBo faceRecordStudentConditionBoAdd = faceRecordStudentConditionBo;
                FaceRecordStudentDto faceRecordStudent = new FaceRecordStudentDto();
                BeanUtils.copyProperties(faceRecordStudentConditionBoAdd, faceRecordStudent);
                faceRecordStudentDtos.add(faceRecordStudent);
            }
        }

        // 返回的结果
        FaceRecordUploadResultVo faceRecordUploadResultVo = new FaceRecordUploadResultVo();
        faceRecordUploadResultVo.setFaceRecordStudentVosSuccess(faceRecordStudentVosSuccess);
        faceRecordUploadResultVo.setFaceRecordStudentVosFail(faceRecordStudentVosFail);
        if (CollectionUtils.isEmpty(faceRecordStudentDtos)) {
            return AjaxResult.success(faceRecordUploadResultVo);
        }
        // 导入数据
        saveOrUpdateBatch(faceRecordStudentDtos);

        // 阿里云人脸库建模
        FaceConfigVo faceConfigVo = faceConfigService.getDetailByOrganizationId(organizationId);
        if (faceConfigVo != null) {
            if (faceConfigVo.getFaceBrandType() != null
                && faceConfigVo.getFaceBrandType().equals(FaceBrandType.ALI.getValue())) {
                faceRecordStudentDtos.forEach(
                    faceRecordStudentDto -> faceBodyService.addFaceTransaction(faceRecordStudentDto.getUserOid(),
                        faceRecordStudentDto.getFaceMediaUrl(), faceRecordStudentDto.getRealName()));
            }
        }

        // 返回
        return AjaxResult.success(faceRecordUploadResultVo);
    }

    @Override
    public AjaxResult updateRealNameByUserOid(String userOid, String realName) {
        if (StringUtils.isBlank(userOid) || StringUtils.isBlank(realName)) {
            return AjaxResult.fail("参数不允许为空");
        }
        faceRecordStudentMapper.updateRealNameByUserOid(userOid, realName);
        return AjaxResult.success();
    }
}
