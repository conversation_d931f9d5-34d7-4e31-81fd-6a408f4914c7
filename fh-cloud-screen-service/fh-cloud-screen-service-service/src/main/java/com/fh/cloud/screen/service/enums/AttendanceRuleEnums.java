package com.fh.cloud.screen.service.enums;

public enum AttendanceRuleEnums {

    /*
     * 考勤类型：1教师考勤，2学生考勤
     */
    RULE_TEACHER(1, "教师考勤"), RULE_STUDENT(2, "学生考勤"),

    /*
     * 年级考勤是否一致：1一致，2不一致
     */
    GRADE_SAME_IS(1, "一致"), GRADE_SAME_NOT(2, "不一致"),

    /**
     * 签到签退都有的考勤规则的状态，尤其注意SIGN_MIDDLE是必须有一次签到记录才算
     */
    SIGN_TYPE_IN(1, "签到"), SIGN_TYPE_OUT(2, "签退"),
    SIGN_MIDDLE(3,"迟到或早退，不做DB处理"),

    /**
     * 单组签到，签退，状态判断
     */
    SIGN_LATER(4,"迟到"),
    SIGN_EARLY(5,"早退"),

    /**
     * 一组只签到或只签退标志
     */
    ONLY_SIGN_IN(1,"只签到"),
    ONLY_SING_OUT(2,"只签退"),

    /*
     * 一天几次考勤
     */
    ATTENDANCE_MODE_NUM_1(1, "一天一次考勤");

    private final Integer code;
    private final String value;

    AttendanceRuleEnums(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getValue() {
        return this.value;
    }
}
