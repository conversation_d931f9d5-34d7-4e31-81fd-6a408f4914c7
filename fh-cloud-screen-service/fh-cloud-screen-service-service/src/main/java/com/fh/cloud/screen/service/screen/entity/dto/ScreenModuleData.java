package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏模块表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_module_data")
public class ScreenModuleData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "screen_module_data_id", type = IdType.AUTO)
    private Long screenModuleDataId;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * FK模块库表id，学校自定义模块的时候，该值为0
     */
    @TableField("screen_module_library_id")
    private Long screenModuleLibraryId;

    /**
     * 模块来源类型：1预置模块，2自定义模块
     */
    @TableField("module_source")
    private Integer moduleSource;

    /**
     * 自定义模块名称
     */
    @TableField("custom_module_name")
    private String customModuleName;

    /**
     * 自定义模块分组类型：1信息发布，2功能发布，3校本内容，4校外内容
     */
    @TableField("custom_module_group_type")
    private Long customModuleGroupType;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
