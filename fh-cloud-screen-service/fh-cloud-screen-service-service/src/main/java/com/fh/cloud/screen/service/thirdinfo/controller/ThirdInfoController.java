package com.fh.cloud.screen.service.thirdinfo.controller;

import java.util.List;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.fh.basic.third.td.api.SyncThirdApi;
import com.fh.cloud.screen.service.thirdinfo.api.ThirdInfoApi;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.entity.AjaxResult;

/**
 * <AUTHOR>
 * @date 2024/3/6 15:26
 */
@RestController
public class ThirdInfoController implements ThirdInfoApi {
    @Resource
    private SyncThirdApi syncThirdApi;
    @Resource
    private MessageService messageService;

    @Override
    public AjaxResult<String> syncThirdSyllabusInfo(@RequestBody SyllabusInfoBo syllabusInfoBo) {
        // 获取当天所在的周一到周五
        List<String> syllabusDays = DateKit.getWeeksDateOfNowDate(null);
        AjaxResult<String> ajaxResult = syncThirdApi.syncThirdDataBiSyllabus(syllabusDays);
        // 发送消息
        if (ajaxResult.isSuccess()) {
            MessageVo messageVo = new MessageVo();
            messageVo.setMessageBody(null);
            messageVo.setMessageType(MessageWsType.MODIFY_CONTENT.getValue());
            messageService.sendMessageWsByClassesId(syllabusInfoBo.getOrganizationId(), syllabusInfoBo.getClassesId(),
                messageVo);
        }
        return ajaxResult;
    }
}
