package com.fh.cloud.screen.service.festival.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.festival.entity.dto.FestivalDto;
import com.fh.cloud.screen.service.festival.entity.bo.FestivalConditionBo;
import com.fh.cloud.screen.service.festival.entity.vo.FestivalVo;
import org.apache.ibatis.annotations.Param;

/**
 * 节日表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
public interface FestivalMapper extends BaseMapper<FestivalDto> {

    List<FestivalVo> getFestivalListByCondition(FestivalConditionBo condition);

    List<FestivalVo> getFestivalListByDate(@Param("date") Date date);

    /**
     * 获取节日节点对应的海报主题ids（节日节点：标签=1:1）
     *
     * @param festivalCodes
     * @return java.util.List<com.fh.cloud.screen.service.festival.entity.vo.FestivalVo>
     * <AUTHOR>
     * @date 2023/5/15 11:34
     */
    List<FestivalVo> getFestivalLibraryRelList(@Param("festivalCodes") List<String> festivalCodes);

}
