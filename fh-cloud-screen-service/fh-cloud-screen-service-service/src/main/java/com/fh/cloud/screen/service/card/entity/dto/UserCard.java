package com.fh.cloud.screen.service.card.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户卡表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_card")
public class UserCard implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "user_card_id", type = IdType.AUTO)
    private Long userCardId;

    /**
     * 卡号
     */
    @TableField("card_number")
    private String cardNumber;

    /**
     * 卡类型：1学生卡，2教师卡
     */
    @TableField("card_type")
    private Integer cardType;

    /**
     * 卡设备型号：1默认卡
     */
    @TableField("card_device_model")
    private Integer cardDeviceModel;

    /**
     * 卡所属人user_oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;
}
