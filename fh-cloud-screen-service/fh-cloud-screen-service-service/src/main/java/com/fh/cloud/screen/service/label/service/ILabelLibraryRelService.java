package com.fh.cloud.screen.service.label.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 标签海报关联表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
public interface ILabelLibraryRelService extends IService<LabelLibraryRelDto> {

    List<LabelLibraryRelVo> getLabelLibraryRelListByCondition(LabelLibraryRelConditionBo condition);

    AjaxResult addLabelLibraryRel(LabelLibraryRelBo labelLibraryRelBo);

    /**
     * 新增或编辑 标签和节日 与海报关联关系
     *
     * @param labelLibraryRelBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/18 15:41
     */
    AjaxResult updateLabelOrFestivalLibraryRel(LabelLibraryRelBo labelLibraryRelBo);

    AjaxResult updateLabelLibraryRel(LabelLibraryRelBo labelLibraryRelBo);

    LabelLibraryRelVo getDetail(Long id);

    /**
     * 自定义sql查询【给定海报id集合】里面符合【给定节日id】的海报
     *
     * @param condition the condition
     * @return label library rel list by condition of label
     * <AUTHOR>
     * @date 2023 -03-06 11:26:19
     */
    List<LabelLibraryRelVo> getLabelLibraryRelListByConditionOfLabel(LabelLibraryRelConditionBo condition);
}
