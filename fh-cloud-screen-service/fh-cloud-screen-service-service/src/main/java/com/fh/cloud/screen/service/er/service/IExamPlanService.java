package com.fh.cloud.screen.service.er.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.er.entity.dto.ExamPlanDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 考场_考试计划接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface IExamPlanService extends IService<ExamPlanDto> {

    List<ExamPlanVo> getExamPlanListByCondition(ExamPlanConditionBo condition);

	AjaxResult addExamPlan(ExamPlanBo examPlanBo);

	AjaxResult updateExamPlan(ExamPlanBo examPlanBo);

	ExamPlanVo getDetail(Long id);

}

