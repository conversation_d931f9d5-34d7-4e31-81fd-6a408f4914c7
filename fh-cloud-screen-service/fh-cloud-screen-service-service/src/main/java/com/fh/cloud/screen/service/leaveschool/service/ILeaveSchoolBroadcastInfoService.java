package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolBroadcastInfoDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 放学播报信息表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
public interface ILeaveSchoolBroadcastInfoService extends IService<LeaveSchoolBroadcastInfoDto> {

    List<LeaveSchoolBroadcastInfoVo> getLeaveSchoolBroadcastInfoListByCondition(LeaveSchoolBroadcastInfoConditionBo condition);

	AjaxResult addLeaveSchoolBroadcastInfo(LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo);

	AjaxResult updateLeaveSchoolBroadcastInfo(LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo);

	LeaveSchoolBroadcastInfoVo getLeaveSchoolBroadcastInfoByCondition(LeaveSchoolBroadcastInfoConditionBo condition);

}

