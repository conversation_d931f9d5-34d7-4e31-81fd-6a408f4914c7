package com.fh.cloud.screen.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ScreenScreenEnums {

    /*
     * 是否同步修改场景模块关联表
     */
    SYNC_IS(1, "是"), SYNC_NOT(2, "否"),

    /**
     * 功能场景的类型functionalType 1课后延迟服务，2会议,3考试
     */
    FUN_TYPE_AFTER_SCHOOL(1, "课后延迟服务"), FUN_TYPE_MEETING(2, "会议"), FUN_TYPE_EXAM(3, "考试"),FUN_TYPE_LEAVE_SCHOOL(4, "放学");

    private final Integer code;
    private final String value;
}
