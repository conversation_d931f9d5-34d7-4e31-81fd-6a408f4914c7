package com.fh.cloud.screen.service.cs.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.cs.entity.dto.CsContactDto;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactConditionBo;
import com.fh.cloud.screen.service.cs.entity.vo.CsContactVo;

/**
 * Cultural-Station文化小站联系表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
public interface CsContactMapper extends BaseMapper<CsContactDto> {

	List<CsContactVo> getCsContactListByCondition(CsContactConditionBo condition);

	CsContactVo getCsContactByCondition(CsContactConditionBo condition);

}
