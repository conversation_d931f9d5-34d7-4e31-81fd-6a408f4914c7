package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云屏紧急发布内容-地点组关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_content_special_space_group_rel")
public class ScreenContentSpecialSpaceGroupRel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键,无实际意义
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FK云屏紧急发布内容表主键
     */
    @TableField("screen_content_special_id")
    private Long screenContentSpecialId;

    /**
     * FK地点分组表主键id
     */
    @TableField("space_group_id")
    private Long spaceGroupId;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
