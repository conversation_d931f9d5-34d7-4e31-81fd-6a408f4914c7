package com.fh.cloud.screen.service.syllabus.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.dto.SyllabusInfoDto;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;

/**
 * 课表信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:22:20
 */
public interface SyllabusInfoMapper extends BaseMapper<SyllabusInfoDto> {

	List<SyllabusInfoVo> getSyllabusInfoListByCondition(SyllabusInfoConditionBo condition);

	SyllabusInfoVo getSyllabusInfoByCondition(SyllabusInfoConditionBo condition);

}
