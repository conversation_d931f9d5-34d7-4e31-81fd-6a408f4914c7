package com.fh.cloud.screen.service.er.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考场_考试计划里面一次考试信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("er_exam_info")
public class ExamInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "exam_info_id", type = IdType.AUTO)
	private Long examInfoId;

	/**
	 * 考试计划id
	 */
	@TableField("exam_plan_id")
	private Long examPlanId;

	/**
	 * 考场号（名称）
	 */
	@TableField("exam_room_name")
	private String examRoomName;

	/**
	 * 区域分组使用类型：1是行政教室，2不是行政教室
	 */
	@TableField("space_group_use_type")
	private Integer spaceGroupUseType;

	/**
	 * 地点id
	 */
	@TableField("space_info_id")
	private Long spaceInfoId;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 地点组id
	 */
	@TableField("space_group_id")
	private Long spaceGroupId;

	/**
	 * 地点组名称
	 */
	@TableField("space_group_name")
	private String spaceGroupName;

}
