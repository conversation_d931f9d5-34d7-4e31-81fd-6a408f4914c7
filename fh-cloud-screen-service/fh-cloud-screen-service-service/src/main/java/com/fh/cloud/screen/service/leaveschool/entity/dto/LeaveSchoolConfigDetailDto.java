package com.fh.cloud.screen.service.leaveschool.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 放学配置详情表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("leave_school_config_detail")
public class LeaveSchoolConfigDetailDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学配置详情表id
	 */
	@TableId(value = "leave_school_config_detail_id", type = IdType.AUTO)
	private Long leaveSchoolConfigDetailId;

	/**
	 * 放学配置表id
	 */
	@TableField("leave_school_config_id")
	private Long leaveSchoolConfigId;

	/**
	 * 年级
	 */
	@TableField("grade")
	private String grade;

	/**
	 * 周几
	 */
	@TableField("week_day")
	private Long weekDay;

	/**
	 * 放学开始时间
	 */
	@TableField("leave_school_start_time")
	private Date leaveSchoolStartTime;

	/**
	 * 放学结束时间
	 */
	@TableField("leave_school_end_time")
	private Date leaveSchoolEndTime;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
