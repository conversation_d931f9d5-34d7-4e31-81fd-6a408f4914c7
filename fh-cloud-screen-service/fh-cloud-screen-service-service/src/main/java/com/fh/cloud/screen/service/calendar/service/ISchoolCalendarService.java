package com.fh.cloud.screen.service.calendar.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendar;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayOfMonthVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;

import java.util.List;

/**
 * 校历主表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
public interface ISchoolCalendarService extends IService<SchoolCalendar> {

    List<SchoolCalendarVo> getSchoolCalendarListByCondition(SchoolCalendarListConditionBo condition);

    boolean addSchoolCalendar(SchoolCalendarBo schoolCalendarBo);

    boolean updateSchoolCalendar(SchoolCalendarBo schoolCalendarBo);

    SchoolCalendarVo getDetail(Long organizationId);

    List<SchoolCalendarDayOfMonthVo> getDayInfoByMonthAndOrgId(String attendanceMonth, Long organizationId);

    /**
     * 校验一个学校当天是否不上课，如果当天不上课则返回true，否则返回false
     *
     * @return boolean boolean
     * <AUTHOR>
     * @date 2023 -06-15 15:10:07
     */
    boolean checkNotWork(Long organizationId);
}
