package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenScene;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo;

import java.util.List;

/**
 * 云屏场景表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IScreenSceneService extends IService<ScreenScene> {

    List<ScreenSceneVo> getScreenSceneListByCondition(ScreenSceneListConditionBo condition);

    boolean addScreenScene(ScreenSceneBo screenSceneBo);

    boolean updateScreenScene(ScreenSceneBo screenSceneBo);

    ScreenSceneVo getDetail(Long screenSceneId);

    /**
     * 根据场景ids获取数据
     *
     * @param sceneIds the scene ids
     * @param isDelete the is delete
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:15:26
     */
    List<ScreenSceneVo> listScreenSceneVoByScreenSceneIds(List<Long> sceneIds, Integer isDelete);

    /**
     * 新增或者更新场景信息(附带更新场景和模块关系)
     *
     * @param screenSceneBo the screen scene bo
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -06-14 10:46:42
     */
    Long saveOrUpdateScreenSceneWithModule(ScreenSceneBo screenSceneBo);

    /**
     * 查询场景列表和该场景关联的模块数据信息
     *
     * @param condition the condition
     * @return screen scene list by condition with module
     */
    List<ScreenSceneVo> getScreenSceneListByConditionWithModule(ScreenSceneListConditionBo condition);

    /**
     * 查询三种模式发布的所有场景（按设备、按地点、按地点组）-根据设备查场景使用。
     *
     * @param condition the condition
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-15 14:16:00
     */
    List<ScreenSceneVo> listScreenSceneOfAllByDevice(ScreenSceneListConditionBo condition);

    /**
     * 初始化场景为该学校默认的海报模块（不选择具体的海报主题，仅仅是一个包含海报模块的场景）
     */
    void initScreenSceneByDeviceNumber(String deviceNumber);

    /**
     * 将场景的设备全屏类型置空
     *
     * @param screenSceneId the screen scene id
     * <AUTHOR>
     * @date 2024 -06-13 17:13:44
     */
    void updateScreenSceneDeviceFullTypeNull(Long screenSceneId);

    /**
     * 将场景的设备全屏类型置空
     *
     * @param screenSceneId the screen scene id
     * <AUTHOR>
     * @date 2024 -06-13 17:13:44
     */
    void updateScreenSceneStartDateNull(Long screenSceneId);

    /**
     * 将场景的设备全屏类型置空
     *
     * @param screenSceneId the screen scene id
     * <AUTHOR>
     * @date 2024 -06-13 17:13:44
     */
    void updateScreenSceneEndDateNull(Long screenSceneId);

    /**
     * 将场景的设备全屏类型置空
     *
     * @param screenSceneId the screen scene id
     * <AUTHOR>
     * @date 2024 -06-13 17:13:44
     */
    void updateScreenSceneWeeksNull(Long screenSceneId);
}
