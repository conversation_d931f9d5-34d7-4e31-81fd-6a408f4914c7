package com.fh.cloud.screen.service.er.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考场_考试计划
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("er_exam_plan")
public class ExamPlanDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "exam_plan_id", type = IdType.AUTO)
	private Long examPlanId;

	/**
	 * 考场计划名称
	 */
	@TableField("exam_plan_name")
	private String examPlanName;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 考试计划开始日期
	 */
	@TableField("exam_plan_start_time")
	private Date examPlanStartTime;

	/**
	 * 考试计划结束日期
	 */
	@TableField("exam_plan_end_time")
	private Date examPlanEndTime;

	/**
	 * 考场计划说明
	 */
	@TableField("exam_plan_remark")
	private String examPlanRemark;

	/**
	 * 考场计划类型：1未发布，2已发布
	 */
	@TableField("exam_plan_type")
	private Integer examPlanType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 是否签到，0：否，1：是
	 */
	@TableField("is_sign_in")
	private Integer isSignIn;
}
