package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.google.common.collect.Lists;
import com.light.core.utils.FuzzyQueryUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMediaAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaAuditService;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMediaAuditMapper;
import com.light.core.entity.AjaxResult;

/**
 * 云屏模块库媒体资源审核表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
@Service
public class ScreenModuleLibraryMediaAuditServiceImpl
    extends ServiceImpl<ScreenModuleLibraryMediaAuditMapper, ScreenModuleLibraryMediaAuditDto>
    implements IScreenModuleLibraryMediaAuditService {

    @Resource
    private ScreenModuleLibraryMediaAuditMapper screenModuleLibraryMediaAuditMapper;

    @Override
    public List<ScreenModuleLibraryMediaAuditVo>
        getScreenModuleLibraryMediaAuditListByCondition(ScreenModuleLibraryMediaAuditConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenModuleLibraryMediaAuditMapper.getScreenModuleLibraryMediaAuditListByCondition(condition);
    }

    @Override
    public AjaxResult
        addScreenModuleLibraryMediaAudit(ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo) {
        ScreenModuleLibraryMediaAuditDto screenModuleLibraryMediaAudit = new ScreenModuleLibraryMediaAuditDto();
        BeanUtils.copyProperties(screenModuleLibraryMediaAuditBo, screenModuleLibraryMediaAudit);
        screenModuleLibraryMediaAudit.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenModuleLibraryMediaAudit)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult
        updateScreenModuleLibraryMediaAudit(ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo) {
        ScreenModuleLibraryMediaAuditDto screenModuleLibraryMediaAudit = new ScreenModuleLibraryMediaAuditDto();
        BeanUtils.copyProperties(screenModuleLibraryMediaAuditBo, screenModuleLibraryMediaAudit);
        if (updateById(screenModuleLibraryMediaAudit)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenModuleLibraryMediaAuditVo
        getScreenModuleLibraryMediaAuditByCondition(ScreenModuleLibraryMediaAuditConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return screenModuleLibraryMediaAuditMapper.getScreenModuleLibraryMediaAuditByCondition(condition);
    }

    @Override
    public boolean updateMediaAuditList(Long screenModuleLibraryAuditId,
        List<ScreenModuleLibraryMediaAuditBo> mediaAuditBos) {
        LambdaQueryWrapper<ScreenModuleLibraryMediaAuditDto> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScreenModuleLibraryMediaAuditDto::getScreenModuleLibraryAuditId, screenModuleLibraryAuditId);
        queryWrapper.eq(ScreenModuleLibraryMediaAuditDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        // list为空，删除所有图片；不为空，修改
        if (CollectionUtils.isEmpty(mediaAuditBos)) {
            ScreenModuleLibraryMediaAuditDto screenModuleLibraryMediaAuditDto = new ScreenModuleLibraryMediaAuditDto();
            screenModuleLibraryMediaAuditDto.setIsDelete(StatusEnum.ISDELETE.getCode());
            update(screenModuleLibraryMediaAuditDto, queryWrapper);
        } else {
            List<ScreenModuleLibraryMediaAuditDto> mediaAuditDtos = list(queryWrapper);
            mediaAuditDtos.forEach(x -> x.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId));
            if (CollectionUtils.isEmpty(mediaAuditDtos)) {
                List<ScreenModuleLibraryMediaAuditDto> addMediaAuditList = mediaAuditBos.stream().map(x -> {
                    ScreenModuleLibraryMediaAuditDto dto = new ScreenModuleLibraryMediaAuditDto();
                    BeanUtils.copyProperties(x, dto);
                    dto.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    dto.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
                    return dto;
                }).collect(Collectors.toList());
                saveBatch(addMediaAuditList);
            } else {
                List<ScreenModuleLibraryMediaAuditBo> addMediaList = new ArrayList<>();
                List<ScreenModuleLibraryMediaAuditDto> delMediaList = new ArrayList<>(mediaAuditDtos);
                List<ScreenModuleLibraryMediaAuditDto> updateMediaList = Lists.newArrayList();
                for (ScreenModuleLibraryMediaAuditBo mediaAuditBo : mediaAuditBos) {
                    for (ScreenModuleLibraryMediaAuditDto mediaAuditDto : mediaAuditDtos) {
                        // 新增的记录
                        if (null == mediaAuditBo.getScreenModuleLibraryMediaAuditId()) {
                            addMediaList.add(mediaAuditBo);
                            break;
                        }
                        // 修改的记录
                        if (mediaAuditBo.getScreenModuleLibraryMediaAuditId()
                            .equals(mediaAuditDto.getScreenModuleLibraryMediaAuditId())) {
                            // 从删除中剔除
                            delMediaList.remove(mediaAuditDto);
                            // 应该更新的字段
                            mediaAuditDto.setMediaSort(mediaAuditBo.getMediaSort());
                            updateMediaList.add(mediaAuditDto);
                            break;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(delMediaList)) {
                    delMediaList.forEach(x -> x.setIsDelete(StatusEnum.ISDELETE.getCode()));
                    updateBatchById(delMediaList);
                }
                if (CollectionUtils.isNotEmpty(updateMediaList)) {
                    updateBatchById(updateMediaList);
                }
                if (CollectionUtils.isNotEmpty(addMediaList)) {
                    List<ScreenModuleLibraryMediaAuditDto> addMediaDtos = addMediaList.stream().map(x -> {
                        ScreenModuleLibraryMediaAuditDto dto = new ScreenModuleLibraryMediaAuditDto();
                        BeanUtils.copyProperties(x, dto);
                        dto.setIsDelete(StatusEnum.NOTDELETE.getCode());
                        dto.setScreenModuleLibraryAuditId(screenModuleLibraryAuditId);
                        return dto;
                    }).collect(Collectors.toList());
                    saveBatch(addMediaDtos);
                }
            }
        }
        return true;
    }

}