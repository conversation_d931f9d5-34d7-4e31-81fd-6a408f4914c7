package com.fh.cloud.screen.service.leaveschool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDeviceDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDeviceService;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolConfigDeviceMapper;
import com.light.core.entity.AjaxResult;
/**
 * 放学配置设备表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
@Service
public class LeaveSchoolConfigDeviceServiceImpl extends ServiceImpl<LeaveSchoolConfigDeviceMapper, LeaveSchoolConfigDeviceDto> implements ILeaveSchoolConfigDeviceService {

	@Resource
	private LeaveSchoolConfigDeviceMapper leaveSchoolConfigDeviceMapper;
	
    @Override
	public List<LeaveSchoolConfigDeviceVo> getLeaveSchoolConfigDeviceListByCondition(LeaveSchoolConfigDeviceConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolConfigDeviceMapper.getLeaveSchoolConfigDeviceListByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolConfigDevice(LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo) {
		LeaveSchoolConfigDeviceDto leaveSchoolConfigDevice = new LeaveSchoolConfigDeviceDto();
		BeanUtils.copyProperties(leaveSchoolConfigDeviceBo, leaveSchoolConfigDevice);
		leaveSchoolConfigDevice.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(leaveSchoolConfigDevice)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateLeaveSchoolConfigDevice(LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo) {
		LeaveSchoolConfigDeviceDto leaveSchoolConfigDevice = new LeaveSchoolConfigDeviceDto();
		BeanUtils.copyProperties(leaveSchoolConfigDeviceBo, leaveSchoolConfigDevice);
		if(updateById(leaveSchoolConfigDevice)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public LeaveSchoolConfigDeviceVo getLeaveSchoolConfigDeviceByCondition(LeaveSchoolConfigDeviceConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return leaveSchoolConfigDeviceMapper.getLeaveSchoolConfigDeviceByCondition(condition);
	}

	@Override
	public boolean deleteAndAddLeaveSchoolConfigDeviceList(Long leaveSchoolConfigId, List<LeaveSchoolConfigDeviceBo> deviceList) {
    	if (leaveSchoolConfigId == null) {
    		return false;
		}
		LambdaUpdateWrapper<LeaveSchoolConfigDeviceDto> updateWrapper = new LambdaUpdateWrapper<>();
    	updateWrapper.eq(LeaveSchoolConfigDeviceDto::getLeaveSchoolConfigId, leaveSchoolConfigId);
    	updateWrapper.set(LeaveSchoolConfigDeviceDto::getIsDelete, StatusEnum.ISDELETE.getCode());
    	update(updateWrapper);

    	if (CollectionUtil.isEmpty(deviceList)) {
    		return true;
		}
    	List<LeaveSchoolConfigDeviceDto> entities = deviceList.stream().map(d -> {
    		LeaveSchoolConfigDeviceDto entity = new LeaveSchoolConfigDeviceDto();
    		BeanUtils.copyProperties(d, entity);
    		entity.setLeaveSchoolConfigId(leaveSchoolConfigId);
    		entity.setIsDelete(StatusEnum.NOTDELETE.getCode());
    		return entity;
		}).collect(Collectors.toList());

		return saveBatch(entities);
	}

	@Override
	public List<LeaveSchoolConfigDeviceVo> getLeaveSchoolConfigDeviceList(LeaveSchoolConfigDeviceConditionBo condition) {
    	condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return baseMapper.getLeaveSchoolConfigDeviceList(condition);
	}

}