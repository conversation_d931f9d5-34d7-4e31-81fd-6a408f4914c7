package com.fh.cloud.screen.service.grade.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.enums.GradeEnums;
import com.fh.cloud.screen.service.grade.api.GradeScreenApi;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.student.entity.vo.StudentVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/13 2:26 下午 @description：
 */
@RestController
@Api(value = "", tags = "年级")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class GradeController implements GradeScreenApi {

    @Autowired
    private BaseDataService baseDataService;

    /**
     * 根据组织机构 查询年级 （组织机构所属教育级别）
     *
     * @param orgId
     * @return
     */
    @Override
    public AjaxResult getBySectionOrgId(@RequestParam("orgId") Long orgId) {
        final OrganizationVo organizationVo = baseDataService.getOrganizationVoByOrgId(orgId);
        if (organizationVo == null) {
            return AjaxResult.success();
        }
        // 教育级别 获取学段
        final String section = organizationVo.getSection();
        final List<String> sections = SchoolYearUtil.eduLevelSectionListMap.get(section);

        // 根据学段获取年级
        List<GradeEnums> gradeEnums = GradeEnums.getBySections(sections);
        List<Map<String, Object>> list = new ArrayList<>();
        gradeEnums.forEach(x -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("code", x.getCode());
            map.put("label", x.getLabel());
            list.add(map);
        });

        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult getBySectionOrgIdGroup(@RequestParam("orgId") Long orgId) {
        final OrganizationVo organizationVo = baseDataService.getOrganizationVoByOrgId(orgId);
        if (organizationVo == null) {
            return AjaxResult.success();
        }
        // 教育级别 获取学段
        final String section = organizationVo.getSection();
        final List<String> sections = SchoolYearUtil.eduLevelSectionListMap.get(section);

        // 根据学段获取年级
        List<GradeEnums> gradeEnums = GradeEnums.getBySections(sections);
        Map<String, List<Map<String, Object>>> resultMap = Maps.newHashMap();
        gradeEnums.forEach(x -> {
            if (!resultMap.containsKey(x.getSection())) {
                resultMap.put(x.getSection(), new ArrayList<>());
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("code", x.getCode());
            map.put("label", x.getLabel());
            map.put("section", x.getSection());
            resultMap.get(x.getSection()).add(map);
        });

        return AjaxResult.success(resultMap);
    }

    /**
     * 当前学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查询当前学年", httpMethod = "GET")
    public AjaxResult currentYear() {
        String currentYear = SchoolYearUtil.getCurrentSchoolYear();
        return AjaxResult.success(currentYear);
    }

    @Override
    public AjaxResult getSectionByOrgId(@RequestParam("orgId") Long orgId) {
        final OrganizationVo organizationVo = baseDataService.getOrganizationVoByOrgId(orgId);
        if (organizationVo == null) {
            return AjaxResult.success();
        }
        // 教育级别 获取学段
        final String section = organizationVo.getSection();
        final List<String> sections = SchoolYearUtil.eduLevelSectionListMap.get(section);
        return AjaxResult.success(sections);
    }

    @Override
    public AjaxResult getClassesListByCondition(@RequestBody ClazzConditionBoExt clazzConditionBo) {
        clazzConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (StringUtils.isBlank(clazzConditionBo.getSchoolYear())) {
            clazzConditionBo.setSchoolYear(SchoolYearUtil.getCurrentSchoolYear());
        }
        if (StringUtils.isNotBlank(clazzConditionBo.getSchoolYear())
            && StringUtils.isNotBlank(clazzConditionBo.getGrade())) {
            clazzConditionBo.setEnrollmentYear(SchoolYearUtil
                .getYearByGradeAndSchoolYear(clazzConditionBo.getGrade(), clazzConditionBo.getSchoolYear())
                .longValue());
        }
        clazzConditionBo.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC,\n"
                + "    CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> clazzInfoVoListMap = baseDataService.getClazzInfoVoList(clazzConditionBo);
        if (clazzInfoVoListMap == null) {
            return AjaxResult.success(Lists.newArrayList());
        }
        return AjaxResult.success(clazzInfoVoListMap);
    }

    @Override
    public AjaxResult listGradesWithClassesAndStudents(Long orgId) {
        final OrganizationVo organizationVo = baseDataService.getOrganizationVoByOrgId(orgId);
        if (organizationVo == null) {
            return AjaxResult.success();
        }
        // 教育级别 获取学段
        final String section = organizationVo.getSection();
        final List<String> sections = SchoolYearUtil.eduLevelSectionListMap.get(section);

        // 根据学段获取年级
        List<GradeEnums> gradeEnums = GradeEnums.getBySections(sections);
        List<Map<String, Object>> list = new ArrayList<>();
        gradeEnums.forEach(x -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("code", x.getCode());
            map.put("label", x.getLabel());

            ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
            clazzConditionBoExt.setGrade(x.getCode());
            clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
            clazzConditionBoExt.setIsDelete(StatusEnum.NOTDELETE.getCode());
            clazzConditionBoExt.setOrganizationId(orgId);
            List<ClazzInfoVo> clazzInfoVoLists =
                (List<ClazzInfoVo>)baseDataService.getClazzInfoVoList(clazzConditionBoExt).get("list");
            if (CollectionUtils.isNotEmpty(clazzInfoVoLists)) {
                clazzInfoVoLists.forEach(clazzInfoVo -> {
                    List<StudentVo> studentVoList = baseDataService.getStudentVoListByClassesId(clazzInfoVo.getId());
                    clazzInfoVo.setStudentVoList(studentVoList);
                });
            }
            map.put("classes", clazzInfoVoLists);
            list.add(map);
        });

        return AjaxResult.success(list);
    }
}
