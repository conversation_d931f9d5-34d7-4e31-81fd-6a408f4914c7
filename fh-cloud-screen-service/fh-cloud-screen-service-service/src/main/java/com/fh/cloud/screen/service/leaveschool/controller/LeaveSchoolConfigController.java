package com.fh.cloud.screen.service.leaveschool.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.fh.app.role.service.role.enums.AppIdEnum;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.enums.LeaveSchoolType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.*;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDto;
import com.fh.cloud.screen.service.leaveschool.entity.vo.*;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolBroadcastInfoService;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolRecordService;
import com.fh.cloud.screen.service.role.servise.UserRoleService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.constants.SystemConstants;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 放学配置表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
@RestController
@Validated
public class LeaveSchoolConfigController implements LeaveSchoolConfigApi {

    @Autowired
    private ILeaveSchoolConfigService leaveSchoolConfigService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private ILeaveSchoolRecordService leaveSchoolRecordService;
    @Autowired
    private ILeaveSchoolBroadcastInfoService leaveSchoolBroadcastInfoService;
    @Value("${leave.school.broadcast.prefix:正在放学}")
    private String leaveSchoolBroadcastSuffix;

    /**
     * 查询放学配置表分页列表
     * 
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<PageInfo<LeaveSchoolConfigVo>>
        getLeaveSchoolConfigPageListByCondition(@RequestBody LeaveSchoolConfigConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<LeaveSchoolConfigVo> pageInfo =
            new PageInfo<>(leaveSchoolConfigService.getLeaveSchoolConfigListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询放学配置表列表
     * 
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<List<LeaveSchoolConfigVo>>
        getLeaveSchoolConfigListByCondition(@RequestBody LeaveSchoolConfigConditionBo condition) {
        List<LeaveSchoolConfigVo> list = leaveSchoolConfigService.getLeaveSchoolConfigListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增放学配置表
     * 
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult addLeaveSchoolConfig(@Validated @RequestBody LeaveSchoolConfigBo leaveSchoolConfigBo) {
        return leaveSchoolConfigService.addLeaveSchoolConfig(leaveSchoolConfigBo);
    }

    /**
     * 修改放学配置表
     * 
     * @param leaveSchoolConfigBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult updateLeaveSchoolConfig(@Validated @RequestBody LeaveSchoolConfigBo leaveSchoolConfigBo) {
        if (null == leaveSchoolConfigBo.getLeaveSchoolConfigId()) {
            return AjaxResult.fail("放学配置表id不能为空");
        }
        return leaveSchoolConfigService.updateLeaveSchoolConfig(leaveSchoolConfigBo);
    }

    /**
     * 查询放学配置表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<LeaveSchoolConfigVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("放学配置表id不能为空");
        }
        LeaveSchoolConfigConditionBo condition = new LeaveSchoolConfigConditionBo();
        condition.setLeaveSchoolConfigId(id);
        LeaveSchoolConfigVo vo = leaveSchoolConfigService.getLeaveSchoolConfigByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除放学配置表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:11:40
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        LeaveSchoolConfigDto leaveSchoolConfigDto = new LeaveSchoolConfigDto();
        leaveSchoolConfigDto.setLeaveSchoolConfigId(id);
        leaveSchoolConfigDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (leaveSchoolConfigService.updateById(leaveSchoolConfigDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 获取放学配置
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 14:31
     **/
    @Override
    public AjaxResult getLeaveSchoolConfig(@RequestBody LeaveSchoolConfigConditionBo condition) {
        if (condition.getOrganizationId() == null) {
            return AjaxResult.fail("组织id不能为空");
        }
        return AjaxResult.success(leaveSchoolConfigService.getLeaveSchoolConfigByCondition(condition));
    }

    /**
     * 获取班主任班级列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 17:37
     **/
    @Override
    public AjaxResult getTeacherClasses() {
        String currentUserOid = baseDataService.getCurrentUserOid();
        List<Long> classesIds = userRoleService.getTeacherClassIds(currentUserOid);
        if (CollectionUtil.isEmpty(classesIds)) {
            return AjaxResult.success(new ArrayList<>());
        }
        ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
        clazzConditionBo.setPageNo(SystemConstants.NO_PAGE);
        clazzConditionBo.setIds(classesIds);
        Map<String, Object> map = baseDataService.getClazzInfoVoList(clazzConditionBo);

        List<LeaveSchoolClazzVo> clazzVos =
            JSONObject.parseArray(JSONObject.toJSONString(map.get("list")), LeaveSchoolClazzVo.class);

        if (CollectionUtil.isEmpty(clazzVos)) {
            return AjaxResult.success(new ArrayList<>());
        }

        // 获取放学状态
        LeaveSchoolRecordConditionBo conditionBo = new LeaveSchoolRecordConditionBo();
        conditionBo.setLeaveSchoolDay(DateKit.getZeroDate(0, new Date()));
        conditionBo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
        conditionBo.setSpaceInfoIds(clazzVos.stream().map(ClazzVo::getId).collect(Collectors.toList()));
        List<LeaveSchoolRecordVo> recordVos = leaveSchoolRecordService.getLeaveSchoolRecordListByCondition(conditionBo);
        Map<Long, LeaveSchoolRecordVo> longLeaveSchoolRecordVoMap =
            recordVos.stream().collect(Collectors.toMap(LeaveSchoolRecordVo::getSpaceInfoId, r -> r, (v1, v2) -> v1));
        for (LeaveSchoolClazzVo clazzVo : clazzVos) {
            LeaveSchoolRecordVo recordVo = longLeaveSchoolRecordVoMap.get(clazzVo.getId());
            if (recordVo == null) {
                clazzVo.setLeaveSchoolType(LeaveSchoolType.NOT_LEAVE_SCHOOL.getCode());
            } else {
                clazzVo.setLeaveSchoolType(recordVo.getLeaveSchoolType());
            }
        }

        return AjaxResult.success(clazzVos);
    }

    @Override
    public AjaxResult getClazzList(@RequestBody ClazzConditionBoExt clazzConditionBoExt) {
        AjaxResult<List<ClazzInfoVo>> ajaxResult = baseDataService.getClazzListWithoutDataAuthority(clazzConditionBoExt);
        List<ClazzInfoVo> list = ajaxResult.getData();
        if (ajaxResult.isFail() || CollectionUtil.isEmpty(list)) {
            return ajaxResult;
        }

        List<Long> classesIds = list.stream().map(ClazzInfoVo::getId).collect(Collectors.toList());
        // 获取放学状态
        LeaveSchoolRecordConditionBo conditionBo = new LeaveSchoolRecordConditionBo();
        conditionBo.setLeaveSchoolDay(DateKit.getZeroDate(0, new Date()));
        conditionBo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
        conditionBo.setSpaceInfoIds(classesIds);
        List<LeaveSchoolRecordVo> recordVos = leaveSchoolRecordService.getLeaveSchoolRecordListByCondition(conditionBo);
        Map<Long, LeaveSchoolRecordVo> map =
            recordVos.stream().collect(Collectors.toMap(LeaveSchoolRecordVo::getSpaceInfoId, r -> r, (v1, v2) -> v1));

        // 查询播报信息
        LeaveSchoolBroadcastInfoConditionBo broadcastInfoConditionBo = new LeaveSchoolBroadcastInfoConditionBo();
        broadcastInfoConditionBo.setOrganizationId(clazzConditionBoExt.getOrganizationId());
        broadcastInfoConditionBo.setCampusId(clazzConditionBoExt.getCampusId());
        List<LeaveSchoolBroadcastInfoVo> broadcastInfoVos =
            leaveSchoolBroadcastInfoService.getLeaveSchoolBroadcastInfoListByCondition(broadcastInfoConditionBo);
        Map<String, LeaveSchoolBroadcastInfoVo> broadcastInfoVoMap = broadcastInfoVos.stream()
            .collect(Collectors.toMap(LeaveSchoolBroadcastInfoVo::getBroadcastContent, b -> b, (v1, v2) -> v1));

        List<LeaveSchoolClazzVo> clazzVos = list.stream().map(c -> {
            LeaveSchoolClazzVo clazzVo = new LeaveSchoolClazzVo();
            BeanUtils.copyProperties(c, clazzVo);
            LeaveSchoolRecordVo recordVo = map.get(clazzVo.getId());
            LeaveSchoolBroadcastInfoVo broadcastInfoVo =
                broadcastInfoVoMap.get(clazzVo.getClassesNameShow() + leaveSchoolBroadcastSuffix);
            if (recordVo == null) {
                clazzVo.setLeaveSchoolType(LeaveSchoolType.NOT_LEAVE_SCHOOL.getCode());
            } else {
                clazzVo.setLeaveSchoolType(recordVo.getLeaveSchoolType());
            }
            if (broadcastInfoVo != null) {
                clazzVo.setBroadcastId(broadcastInfoVo.getBroadcastId());
                clazzVo.setBroadcastUrl(broadcastInfoVo.getBroadcastUrl());
                clazzVo.setPlayTimes(broadcastInfoVo.getPlayTimes());
            }
            return clazzVo;
        }).collect(Collectors.toList());

        List<LeaveSchoolGradeListVo> gradeListVos = list.stream().map(c -> {
            LeaveSchoolGradeListVo gradeListVo = new LeaveSchoolGradeListVo();
            gradeListVo.setOrganizationId(c.getOrganizationId());
            gradeListVo.setGrade(c.getGrade());
            gradeListVo.setGradeName(c.getGradeName());
            return gradeListVo;
        }).distinct().sorted(Comparator.comparing(LeaveSchoolGradeListVo::getGrade)).collect(Collectors.toList());

        for (LeaveSchoolGradeListVo gradeListVo : gradeListVos) {
            gradeListVo.setClazzList(clazzVos.stream().filter(c -> c.getGrade().equals(gradeListVo.getGrade()))
                .collect(Collectors.toList()));
        }

        return AjaxResult.success(gradeListVos);
    }

    /**
     * 获取开了放学的组织列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/12 9:16
     **/
    @Override
    public AjaxResult<List<OrganizationVo>> getLeaveSchoolOrganizationList() {
        return baseDataService.getOrganizationListByAppId(AppIdEnum.LEAVE_SCHOOL.getAppId());
    }
}
