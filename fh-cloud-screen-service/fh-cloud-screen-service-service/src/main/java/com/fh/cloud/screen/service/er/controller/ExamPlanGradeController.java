package com.fh.cloud.screen.service.er.controller;

import com.fh.cloud.screen.service.er.api.ExamPlanGradeApi;
import com.fh.cloud.screen.service.er.entity.dto.ExamPlanGradeDto;
import com.fh.cloud.screen.service.er.service.IExamPlanGradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 考场_考试计划涉及的年级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@RestController
@Validated
public class ExamPlanGradeController implements ExamPlanGradeApi{
	
    @Autowired
    private IExamPlanGradeService examPlanGradeService;

    /**
     * 查询考场_考试计划涉及的年级分页列表
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<PageInfo<ExamPlanGradeVo>> getExamPlanGradePageListByCondition(@RequestBody ExamPlanGradeConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ExamPlanGradeVo> pageInfo = new PageInfo<>(examPlanGradeService.getExamPlanGradeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询考场_考试计划涉及的年级列表
	 * <AUTHOR>
	 * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult<List<ExamPlanGradeVo>> getExamPlanGradeListByCondition(@RequestBody ExamPlanGradeConditionBo condition){
		List<ExamPlanGradeVo> list = examPlanGradeService.getExamPlanGradeListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增考场_考试计划涉及的年级
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
	@Override
    public AjaxResult addExamPlanGrade(@Validated @RequestBody ExamPlanGradeBo examPlanGradeBo){
		return examPlanGradeService.addExamPlanGrade(examPlanGradeBo);
    }

    /**
	 * 修改考场_考试计划涉及的年级
	 * @param examPlanGradeBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult updateExamPlanGrade(@Validated @RequestBody ExamPlanGradeBo examPlanGradeBo) {
		if(null == examPlanGradeBo.getExamPlanGradeId()) {
			return AjaxResult.fail("考场_考试计划涉及的年级id不能为空");
		}
		return examPlanGradeService.updateExamPlanGrade(examPlanGradeBo);
	}

	/**
	 * 查询考场_考试计划涉及的年级详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult<ExamPlanGradeVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("考场_考试计划涉及的年级id不能为空");
		}
		ExamPlanGradeVo vo = examPlanGradeService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除考场_考试计划涉及的年级
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ExamPlanGradeDto examPlanGradeDto = new ExamPlanGradeDto();
		examPlanGradeDto.setExamPlanGradeId(id);
		examPlanGradeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(examPlanGradeService.updateById(examPlanGradeDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
