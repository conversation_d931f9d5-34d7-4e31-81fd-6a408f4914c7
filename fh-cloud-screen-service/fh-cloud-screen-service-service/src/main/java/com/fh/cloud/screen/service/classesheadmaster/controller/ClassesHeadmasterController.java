package com.fh.cloud.screen.service.classesheadmaster.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.classheadmaster.api.ClassesHeadmasterApi;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.baseinfo.entity.bo.ClazzHeadmasterConditionBoExt;
import com.fh.cloud.screen.service.baseinfo.entity.vo.ClassesVoExt;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;
import com.fh.cloud.screen.service.enums.DictType;
import com.fh.cloud.screen.service.enums.TeacherImportTemplateType;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-01-17 9:15
 */
@RestController
@Slf4j
public class ClassesHeadmasterController implements ClassesHeadmasterApi {

    @Resource
    private BaseDataService baseDataService;

    /**
     * 班主任任教班级列表
     *
     * @param clazzHeadmasterConditionBoExt the clazz headmaster condition bo ext
     * @return com.light.core.entity.AjaxResult ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-29 16:15:00
     */
    @ApiOperation(value = "班主任任教班级列表", httpMethod = "POST")
    public AjaxResult headmasterClasses(@RequestBody ClazzHeadmasterConditionBoExt clazzHeadmasterConditionBoExt) throws Exception {
        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
        teacherConditionBo.setType((long) TeacherImportTemplateType.SCHOOL_JX.getValue());
        teacherConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        teacherConditionBo.setOrganizationId(clazzHeadmasterConditionBoExt.getOrganizationId());
        teacherConditionBo.setUserOid(clazzHeadmasterConditionBoExt.getUserOid());
        if (StringUtils.isNotBlank(clazzHeadmasterConditionBoExt.getRealName())) {
            teacherConditionBo.setRealName(FuzzyQueryUtil.transferMean(clazzHeadmasterConditionBoExt.getRealName()));
        }
        Map<String, Object> result = baseDataService.getTeacherListByCondition(teacherConditionBo);
        List<Map> list = (List) result.get("list");
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.fail("获取教师信息失败");
        }
        List<TeacherVo> teacherVos = JSONObject.parseArray(JSONObject.toJSONString(list), TeacherVo.class);
        List<Long> teacherIdList = teacherVos.stream().map(TeacherVo::getId).collect(Collectors.toList());
        clazzHeadmasterConditionBoExt.setTeacherIds(teacherIdList);
        clazzHeadmasterConditionBoExt.setIsDelete(StatusEnum.NOTDELETE.getCode());
        clazzHeadmasterConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult tcslResult = baseDataService.getClassesHeadmasterListByCondition(clazzHeadmasterConditionBoExt);
        Map<String, Object> map = (Map<String, Object>) tcslResult.getData();
        List<Map> chtList = (List) map.get("list");
        List<ClazzHeadmasterVo> clazzHeadmasterVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(chtList)) {
            clazzHeadmasterVos = JSONObject.parseArray(JSONObject.toJSONString(chtList), ClazzHeadmasterVo.class);
        }
        List<ClassesVoExt> classesVoExts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(clazzHeadmasterVos)) {
            //获取年级列表
            List<String> types = new ArrayList<>();
            types.add(DictType.GRADE.getValue());
            List<DictionaryDataVo> dictionaryDataVos = JSONObject.parseArray(JSONObject.toJSONString(baseDataService.listValueByTypes(types)), DictionaryDataVo.class);
            for (ClazzHeadmasterVo clazzHeadmasterVo : clazzHeadmasterVos) {
                ClassesVoExt classesVoExt = new ClassesVoExt();
                classesVoExt.setClassesId(clazzHeadmasterVo.getClassesId());
                classesVoExt.setClassesName(clazzHeadmasterVo.getClazzVo().getClassesName());
                classesVoExt.setGrade(clazzHeadmasterVo.getClazzVo().getGrade());
                DictionaryDataVo grade = dictionaryDataVos.stream().filter(obj -> obj.getDictType().equals(DictType.GRADE.getValue()) && obj.getDictValue().equals(classesVoExt.getGrade())).findFirst().get();
                classesVoExt.setGradeName(grade.getDictLabel());
                classesVoExts.add(classesVoExt);
            }
        }
        result.put("list", classesVoExts);
        return AjaxResult.success(result);
    }

}
