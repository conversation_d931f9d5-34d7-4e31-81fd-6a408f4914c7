package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMediaAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo;

/**
 * 云屏模块库媒体资源审核表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
public interface ScreenModuleLibraryMediaAuditMapper extends BaseMapper<ScreenModuleLibraryMediaAuditDto> {

	List<ScreenModuleLibraryMediaAuditVo> getScreenModuleLibraryMediaAuditListByCondition(ScreenModuleLibraryMediaAuditConditionBo condition);

	ScreenModuleLibraryMediaAuditVo getScreenModuleLibraryMediaAuditByCondition(ScreenModuleLibraryMediaAuditConditionBo condition);

}
