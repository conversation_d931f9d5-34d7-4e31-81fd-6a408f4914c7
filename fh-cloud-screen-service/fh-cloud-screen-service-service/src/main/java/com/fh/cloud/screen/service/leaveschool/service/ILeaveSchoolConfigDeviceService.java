package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDeviceDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 放学配置设备表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
public interface ILeaveSchoolConfigDeviceService extends IService<LeaveSchoolConfigDeviceDto> {

    List<LeaveSchoolConfigDeviceVo> getLeaveSchoolConfigDeviceListByCondition(LeaveSchoolConfigDeviceConditionBo condition);

	AjaxResult addLeaveSchoolConfigDevice(LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo);

	AjaxResult updateLeaveSchoolConfigDevice(LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo);

	LeaveSchoolConfigDeviceVo getLeaveSchoolConfigDeviceByCondition(LeaveSchoolConfigDeviceConditionBo condition);

	/**
	 * 删除后批量新增
	 *
	 * @param leaveSchoolConfigId
	 * @param deviceList
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/8/23 11:48
	 **/
	boolean deleteAndAddLeaveSchoolConfigDeviceList(Long leaveSchoolConfigId, List<LeaveSchoolConfigDeviceBo> deviceList);

	/**
	 * 获取放学配置设备列表
	 *
	 * @param condition
	 * @return java.util.List<com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo>
	 * <AUTHOR>
	 * @date 2023/8/23 14:21
	 **/
	List<LeaveSchoolConfigDeviceVo> getLeaveSchoolConfigDeviceList(LeaveSchoolConfigDeviceConditionBo condition);


}

