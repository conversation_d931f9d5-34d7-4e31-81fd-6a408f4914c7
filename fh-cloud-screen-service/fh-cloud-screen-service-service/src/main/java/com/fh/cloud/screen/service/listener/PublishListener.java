package com.fh.cloud.screen.service.listener;

import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.dto.EventPublishDto;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 云屏发布事件监听
 *
 * <AUTHOR>
 * @date 2022/5/10 16:45
 */
@Component
public class PublishListener {

    @Lazy
    @Autowired
    private MessageService messageService;
    @Lazy
    @Autowired
    private IScreenSceneService screenSceneService;
    @Lazy
    @Autowired
    private IShowDeviceService showDeviceService;

    @Async
    @EventListener
    public void onApplicationEvent(PublishEvent event) {
        EventPublishDto eventPublishDto = (EventPublishDto)event.getSource();
        if (eventPublishDto.getMessageWsType() == null) {
            return;
        }

        if (eventPublishDto.getMessageWsType().equals(MessageWsType.INIT.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                eventPublishDto.getMsg());
        }

        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_CONTENT.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsByContentId(eventPublishDto.getOrganizationId(), eventPublishDto.getContentId(),
                eventPublishDto.getMsg());
            // 推送消息给手机TODO
        }

        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_STUDENT.getValue())) {
            // 此处无用 (在MQ消息接收变更时，推送消息)
            // messageService.sendMessageWsByClassesId(eventPublishDto.getOrganizationId(),
            // eventPublishDto.getClassesId(),
            // eventPublishDto.getMsg());
        }

        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_TEACHER.getValue())) {
            // 此处无用 (在MQ消息接收变更时，推送消息)
        }

        if (eventPublishDto.getMessageWsType().equals(MessageWsType.DEVICE_OPEN.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                eventPublishDto.getMsg());
        }

        if (eventPublishDto.getMessageWsType().equals(MessageWsType.DEVICE_CLOSE.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_SPECIAL.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsByContentSpecialId(eventPublishDto.getOrganizationId(),
                eventPublishDto.getContentSpecialId(), eventPublishDto.getContentSpecialIdNow(),
                eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_SCENE.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsBySceneId(eventPublishDto.getOrganizationId(), eventPublishDto.getSceneId(),
                eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_MEETING.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_ER_PUBLISH.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsByExamPlanId(eventPublishDto.getOrganizationId(),
                eventPublishDto.getExamPlanId(), eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_ER_CANCEL.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsByExamPlanId(eventPublishDto.getOrganizationId(),
                eventPublishDto.getExamPlanId(), eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_FACE_CONFIG.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsBySchool(eventPublishDto.getOrganizationId(),
                eventPublishDto.getDeviceNumbers(), eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_FACE_TEACHER.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWsBySchool(eventPublishDto.getOrganizationId(),
                eventPublishDto.getDeviceNumbers(), eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.MODIFY_FACE_STUDENT.getValue())) {
            // 推送websocket消息给云屏
            if (eventPublishDto.getClassesId() != null) {
                messageService.sendMessageWsByClassesId(eventPublishDto.getOrganizationId(),
                    eventPublishDto.getClassesId(), eventPublishDto.getMsg());
            } else {
                messageService.sendMessageWsBySchool(eventPublishDto.getOrganizationId(),
                    eventPublishDto.getDeviceNumbers(), eventPublishDto.getMsg());
            }
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.SCREEN_CAPTURE.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.SCREEN_CHANGE_SPACE.getValue())) {
            // 切换到新地点，如果没有场景的业务逻辑处理：init 一个海报的场景
            screenSceneService.initScreenSceneByDeviceNumber(eventPublishDto.getDeviceNumbers().get(0));

            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.SCREEN_ACTIVE.getValue())) {
            // 设备激活需要做的额外的业务逻辑处理：init 一个海报的场景
            screenSceneService.initScreenSceneByDeviceNumber(eventPublishDto.getDeviceNumbers().get(0));

            // 推送websocket消息给云屏：场景变更
            MessageVo msg = eventPublishDto.getMsg();
            msg.setMessageType(MessageWsType.MODIFY_SCENE.getValue());
            ShowDevice showDevice = showDeviceService.getByDeviceNum(eventPublishDto.getDeviceNumbers().get(0));
            if (showDevice != null) {
                eventPublishDto.setOrganizationId(showDevice.getOrganizationId());
            }
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(), msg);
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.SCREEN_LOG.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                    eventPublishDto.getMsg());
        }
        if (eventPublishDto.getMessageWsType().equals(MessageWsType.LEAVE_SCHOOL_START.getValue())
                || eventPublishDto.getMessageWsType().equals(MessageWsType.LEAVE_SCHOOL_END.getValue())
                || eventPublishDto.getMessageWsType().equals(MessageWsType.LEAVE_SCHOOL_STATE_CHANGE.getValue())) {
            // 推送websocket消息给云屏
            messageService.sendMessageWs(eventPublishDto.getOrganizationId(), eventPublishDto.getDeviceNumbers(),
                    eventPublishDto.getMsg());
        }
    }
}
