package com.fh.cloud.screen.service.space.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceGroup;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;

import java.util.List;
import java.util.Map;

/**
 * 区域组表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ISpaceGroupService extends IService<SpaceGroup> {

    List<SpaceGroupVo> getSpaceGroupListByCondition(SpaceGroupListConditionBo condition);

    boolean addSpaceGroup(SpaceGroupBo spaceGroupBo);

    boolean updateSpaceGroup(SpaceGroupBo spaceGroupBo);

    Map<String, Object> getDetail(Long spaceGroupId);

    /**
     * 根据spaceGroupIds获取地点组数据
     *
     * @param spaceGroupIds the space group ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:15:26
     */
    List<SpaceGroupVo> listSpaceGroupVoBySpaceGroupIds(List<Long> spaceGroupIds);

    /**
     * 查询所有数据
     *
     * @return
     */
    List<SpaceGroupVo> findAll();

    /**
     * 获取行政教师的地点组信息（底层是根据类型获取，因为行政地点组只有一个）
     *
     * @return space group vo of xz
     */
    SpaceGroupVo getSpaceGroupVoOfXz();
}
