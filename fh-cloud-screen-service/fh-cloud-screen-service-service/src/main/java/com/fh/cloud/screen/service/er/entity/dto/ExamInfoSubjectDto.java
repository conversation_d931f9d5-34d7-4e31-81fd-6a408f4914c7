package com.fh.cloud.screen.service.er.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考场_考试计划里面一次考试科目信息
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("er_exam_info_subject")
public class ExamInfoSubjectDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "exam_info_subject_id", type = IdType.AUTO)
	private Long examInfoSubjectId;

	/**
	 * 考试id
	 */
	@TableField("exam_info_id")
	private Long examInfoId;

	/**
	 * 考试计划id
	 */
	@TableField("exam_plan_id")
	private Long examPlanId;

	/**
	 * 学科code
	 */
	@TableField("subject_code")
	private String subjectCode;

	/**
	 * 学科名称
	 */
	@TableField("subject_name")
	private String subjectName;

	/**
	 * 准考证开始
	 */
	@TableField("at_no_start")
	private String atNoStart;

	/**
	 * 准考证结束
	 */
	@TableField("at_no_end")
	private String atNoEnd;

	/**
	 * 该场考试开始时间
	 */
	@TableField("exam_start_time")
	private Date examStartTime;

	/**
	 * 该场考试结束时间
	 */
	@TableField("exam_end_time")
	private Date examEndTime;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 科目的typeId：1全，2局，3校
	 */
	@TableField("type_id")
	private Long typeId;

}
