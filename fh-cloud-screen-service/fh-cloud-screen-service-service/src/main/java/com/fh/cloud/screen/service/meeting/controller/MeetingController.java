package com.fh.cloud.screen.service.meeting.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.AttendanceConstants;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.enums.MeetingEnums;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.meeting.api.MeetingApi;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingStudentImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingTeacherImportBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingImportVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;
import com.fh.cloud.screen.service.meeting.service.IMeetingService;
import com.fh.cloud.screen.service.meeting.service.IMeetingUserService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.utils.DateKit;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.vo.UserVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 会议表
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@RestController
@Validated
public class MeetingController implements MeetingApi {

    @Autowired
    private IMeetingService meetingService;
    @Resource
    private IMeetingUserService meetingUserService;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private RedisComponent redisComponent;

    /**
     * 查询会议表分页列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult getMeetingPageListByCondition(@RequestBody MeetingConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<MeetingVo> pageInfo = new PageInfo<>(meetingService.getMeetingListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询我参与的会议列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult getMyMeetingPageListByCondition(@RequestBody MeetingConditionBo condition) {
        if (StringUtils.isBlank(condition.getUserOid())) {
            return AjaxResult.fail("参数错误");
        }
        return AjaxResult.success(meetingService.getMyMeetingListByCondition(condition));
    }

    /**
     * 按日期查看会议室列表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult getMeetingListByDate(@RequestBody MeetingConditionBo condition) {
        return AjaxResult.success(meetingService.getMeetingListByDate(condition));
    }

    /**
     * 新增或修改会议表
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult addMeeting(@Validated @RequestBody MeetingBo meetingBo) {
        if (CollectionUtils.isEmpty(meetingBo.getMeetingUserBos())) {
            return AjaxResult.fail("与会人员不能为空");
        }
        return meetingService.addMeeting(meetingBo);
    }

    /**
     * 新增或修改会议表-批量
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult addMeetingBatch(@RequestBody MeetingBo meetingBo) {
        if (meetingBo.getOrganizationId() == null) {
            return AjaxResult.fail("组织ID不能为空");
        }
        if (meetingBo.getSpaceInfoId() == null) {
            return AjaxResult.fail("会议室地点id不能为空");
        }
        if (StringUtils.isBlank(meetingBo.getTitle())) {
            return AjaxResult.fail("会议主题不能为空");
        }
        if (CollectionUtils.isEmpty(meetingBo.getMeetingDateList())) {
            return AjaxResult.fail("会议日期不能为空");
        }
        if (CollectionUtils.isEmpty(meetingBo.getMeetingUserBos())) {
            return AjaxResult.fail("与会人员不能为空");
        }

        return meetingService.addMeetingBatch(meetingBo);
    }

    /**
     * 修改会议表
     * 
     * @param meetingBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult updateMeeting(@RequestBody MeetingBo meetingBo) {
        if (null == meetingBo.getMeetingId()) {
            return AjaxResult.fail("会议表id不能为空");
        }
        return meetingService.updateMeeting(meetingBo);
    }

    /**
     * 查询会议表详情
     * 
     * @param meetingId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult getDetail(@RequestParam("meetingId") Long meetingId) {
        if (null == meetingId) {
            return AjaxResult.fail("会议表id不能为空");
        }
        MeetingVo vo = meetingService.getDetail(meetingId);
        return AjaxResult.success(vo);
    }

    /**
     * 删除会议表
     *
     * @param meetingBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @Override
    public AjaxResult delete(@RequestBody MeetingBo meetingBo) {
        if (null == meetingBo.getMeetingId()) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        // 删除和取消预约，删除已结束，或取消自己以预约
        return meetingService.delete(meetingBo);
    }

    /**
     * 会议签到接口
     *
     * @param meetingUserBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/8/23 9:59
     */
    @Override
    public AjaxResult signIn(@RequestBody MeetingUserBo meetingUserBo) {
        // 1、取出会议
        Long meetingId = meetingUserBo.getMeetingId();
        MeetingVo meetingVo = getMeetingDetailByMeetingIdAndCache(meetingId);
        if (null == meetingVo) {
            return AjaxResult.fail(ConstantsInteger.MEETING_FAIL_CODE, "场地预约不存在");
        }
        if (!MeetingEnums.MEETING_IS_SIGN.getCode().equals(meetingVo.getIsSignIn())) {
            return AjaxResult.fail(ConstantsInteger.MEETING_FAIL_CODE, "本次预约不支持签到");
        }
        Date normalSignTime = null;
        if (meetingVo.getNormalSignInTime() != null) {
            normalSignTime = DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getNormalSignInTime());
        }

        // 2、签到逻辑处理
        List<MeetingUserVo> meetingUserVos = meetingVo.getMeetingUserVos();
        List<MeetingUserVo> filterList = meetingUserVos.stream()
            .filter(x -> x.getUserOid().equals(meetingUserBo.getUserOid())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return AjaxResult.fail(ConstantsInteger.MEETING_FAIL_CODE, "用户不在本次预约人员中");
        }
        MeetingUserVo meetingUserVo = filterList.get(0);
        // 判断本次签到时间
        Date signTime;
        Integer signStatus;
        Date startDateTime = DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getMeetingStartTime());
        Date endDateTime = DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getMeetingEndTime());
        // 开始时间+59秒
        Date startDateTimePlus59 = DateKit.getAfterSeconds(AttendanceConstants.FIFTY_NINE, startDateTime);
        // 正常签到时间+59秒
        Date normalDateTimePlus59 = DateKit.getAfterSeconds(AttendanceConstants.FIFTY_NINE, normalSignTime);
        // 允许签到的时间（暂时是签到前30分钟）
        Date allowSignTime = DateKit.getBeforeSeconds(AttendanceConstants.ALLOW_SIGN_IN_SECOND, startDateTime);

        // 3.签到状态判断处理
        // 签到从59秒开始，判断间隔时间，添加59秒
        Date userSignDateTime = meetingUserBo.getSignTime();
        if (normalDateTimePlus59 != null) {
            startDateTimePlus59 = normalDateTimePlus59;
        }
        if (userSignDateTime.before(startDateTimePlus59) && userSignDateTime.after(allowSignTime)) {
            signTime = userSignDateTime;
            signStatus = MeetingEnums.SIGN_NORMAL.getCode();
        } else if (userSignDateTime.before(endDateTime) && userSignDateTime.after(startDateTimePlus59)) {
            signTime = userSignDateTime;
            signStatus = MeetingEnums.SIGN_LATE.getCode();
        } else {
            return AjaxResult.fail(ConstantsInteger.MEETING_FAIL_CODE, "不在签到范围时间内");
        }

        // 4.数据更新
        Map<String, Object> map = new HashMap<>();
        // 无签到记录
        if (null == meetingUserVo.getStatus() || MeetingEnums.SIGN_NOT.getCode().equals(meetingUserVo.getStatus())) {
            meetingUserVo.setSignTime(signTime);
            meetingUserVo.setStatus(signStatus);
            meetingUserService.update(new UpdateWrapper<MeetingUserDto>().lambda()
                .eq(MeetingUserDto::getMeetingUserId, meetingUserVo.getMeetingUserId())
                .set(MeetingUserDto::getSignTime, signTime).set(MeetingUserDto::getStatus, signStatus));
            map.put("isRepeat", false);
            map.put("attendanceTime", signTime);
        } else {
            // 重复签到
            map.put("isRepeat", true);
            map.put("attendanceTime", meetingUserVo.getSignTime());
        }
        String key = ConstantsRedis.MEETING_REDIS_KEY.concat(meetingId.toString());
        redisComponent.set(key, meetingVo);
        // 有效期到endDay的23:59:59
        redisComponent.expire(key, DateKit.getNextDayMidnightSeconds(endDateTime));
        return AjaxResult.success(map);

    }

    /**
     * 获取教师列表
     *
     * @param teacherConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/29 11:27
     */
    @Override
    public AjaxResult getTeacherListByCondition(@RequestBody TeacherConditionBo teacherConditionBo) {
        return AjaxResult.success(baseDataService.getTeacherListByCondition(teacherConditionBo));
    }

    /**
     * 获取当前及下一个会议（当天)
     *
     * @param meetingConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/20 15:17
     */
    @Override
    public AjaxResult getNowAndNextMeeting(MeetingConditionBo meetingConditionBo) {
        if (null == meetingConditionBo.getSpaceGroupUseType() || null == meetingConditionBo.getSpaceInfoId()) {
            return AjaxResult.fail("参数错误");
        }
        return meetingService.getNowAndNextMeeting(meetingConditionBo);
    }

    /**
     * 获取会议及与会人员详情-缓存
     *
     * @param meetingId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/8/23 9:59
     */
    @Override
    public AjaxResult getDetailCacheByMeetingId(Long meetingId) {
        MeetingVo detail = getMeetingDetailByMeetingIdAndCache(meetingId);
        if (null == detail) {
            return AjaxResult.fail("会议不存在");
        }
        return AjaxResult.success(detail);
    }

    @Override
    public AjaxResult getTeacherImportCheckResult(MeetingImportBo<MeetingTeacherImportBo> meetingImportBo) {
        if (CollectionUtils.isEmpty(meetingImportBo.getList()) || meetingImportBo.getOrganizationId() == null) {
            return AjaxResult.fail("导入数据为空");
        }
        MeetingImportVo meetingImportVo = new MeetingImportVo();
        List<MeetingTeacherImportBo> list = meetingImportBo.getList();
        // 数据过滤前后空格
        list.stream().forEach(mtib -> {
            if(StringUtils.isNotBlank(mtib.getRealName())){
                mtib.setRealName(StringUtils.trim(mtib.getRealName()));
            }
            if(StringUtils.isNotBlank(mtib.getMobilePhone())){
                mtib.setMobilePhone(StringUtils.trim(mtib.getMobilePhone()));
            }
        });
        Long organizationId = meetingImportBo.getOrganizationId();
        Map<String, Long> frequencyNameMap = list.stream().filter(mtib -> StringUtils.isNotBlank(mtib.getRealName()))
            .collect(Collectors.groupingBy(MeetingTeacherImportBo::getRealName, Collectors.counting()));
        // 导入数据里面重复的名称
        List<String> duplicateNames = frequencyNameMap.entrySet().stream().filter(entry -> entry.getValue() > 1)
            .map(Map.Entry::getKey).collect(Collectors.toList());
        Map<String, Long> frequencyNamePhoneMap = list.stream()
            .filter(mtib -> StringUtils.isNotBlank(mtib.getRealName()) && StringUtils.isNotBlank(mtib.getMobilePhone()))
            .collect(Collectors.groupingBy(m1 -> m1.getRealName().concat(m1.getMobilePhone()), Collectors.counting()));
        // 导入数据里面重复的名称+手机号
        List<String> duplicateNamePhones = frequencyNamePhoneMap.entrySet().stream()
            .filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 根据学校id导入和查询检查教师信息
        List<TeacherVo> teacherVoList = baseDataService.getTeacherVoByOrgId(organizationId);
        Map<String, Long> frequencyNameDBMap =
            teacherVoList.stream().filter(mtib -> StringUtils.isNotBlank(mtib.getUserVo().getRealName()))
                .collect(Collectors.groupingBy(mtib -> mtib.getUserVo().getRealName(), Collectors.counting()));
        // 数据库里面已经重复的名称
        List<String> duplicateNamesDB = frequencyNameDBMap.entrySet().stream().filter(entry -> entry.getValue() > 1)
            .map(Map.Entry::getKey).collect(Collectors.toList());
        Map<String,
            Long> frequencyNamePhoneDBMap = teacherVoList.stream()
                .filter(mtib -> StringUtils.isNotBlank(mtib.getUserVo().getRealName())
                    && StringUtils.isNotBlank(mtib.getAccountPhone()))
                .collect(Collectors.groupingBy(m1 -> m1.getUserVo().getRealName().concat(m1.getAccountName()),
                    Collectors.counting()));
        // 数据库里面已经重复的名称+手机号
        List<String> duplicateNamePhonesDB = frequencyNamePhoneDBMap.entrySet().stream()
            .filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        // 数据库里面用户和userOid的关系
        Map<String, String> teacherNameOidMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(teacherVoList)) {
            teacherNameOidMap = teacherVoList.stream().collect(Collectors
                .toMap(teacherVo -> teacherVo.getUserVo().getRealName(), TeacherVo::getUserOid, (v1, v2) -> v1));
        }
        List<String> notExistInfos = Lists.newArrayList();
        List<String> repeatInfos = Lists.newArrayList();
        List<MeetingUserVo> meetingUserVos = Lists.newArrayList();
        // 导入数据重复和数据库重复的行数数据
        Set<Integer> duplicateLines = Sets.newLinkedHashSet();
        for (int i = 0; i < list.size(); i++) {
            MeetingTeacherImportBo meetingTeacherImportBo = list.get(i);
            if (StringUtils.isBlank(meetingTeacherImportBo.getRealName())
                || StringUtils.isBlank(meetingTeacherImportBo.getTeacherType())) {
                continue;
            }
            // 导入数据重复的行号，默认值为标题占用的行数
            Integer dupHeadLine = ConstantsInteger.NUM_4;
            // 数据库数据重复的行号，默认值为标题占用的行数
            Integer dupDBHeadLine = ConstantsInteger.NUM_4;
            // 不存在的行号，默认值为标题占用的行数
            Integer notExistHeadLine = ConstantsInteger.NUM_4;
            boolean hasError = false;
            if (duplicateNames.contains(meetingTeacherImportBo.getRealName())) {
                // 再校验含手机号的是否重复
                if (StringUtils.isNotBlank(meetingTeacherImportBo.getMobilePhone())) {
                    if (duplicateNamePhones.contains(
                        meetingTeacherImportBo.getRealName().concat(meetingTeacherImportBo.getMobilePhone()))) {
                        dupHeadLine = dupHeadLine + i;
                        if (!duplicateLines.contains(dupHeadLine)) {
                            repeatInfos.add("第" + dupHeadLine + "行" + meetingTeacherImportBo.getRealName());
                        }
                        duplicateLines.add(dupHeadLine);
                        hasError = true;
                    }
                } else {
                    dupHeadLine = dupHeadLine + i;
                    if (!duplicateLines.contains(dupHeadLine)) {
                        repeatInfos.add("第" + dupHeadLine + "行" + meetingTeacherImportBo.getRealName());
                    }
                    duplicateLines.add(dupHeadLine);
                    hasError = true;
                }
            }
            if (duplicateNamesDB.contains(meetingTeacherImportBo.getRealName())) {
                // 再校验含手机号的是否重复
                if (StringUtils.isNotBlank(meetingTeacherImportBo.getMobilePhone())) {
                    if (duplicateNamePhonesDB.contains(
                        meetingTeacherImportBo.getRealName().concat(meetingTeacherImportBo.getMobilePhone()))) {
                        dupDBHeadLine = dupDBHeadLine + i;
                        if (!duplicateLines.contains(dupDBHeadLine)) {
                            repeatInfos.add("第" + dupDBHeadLine + "行" + meetingTeacherImportBo.getRealName());
                        }
                        duplicateLines.add(dupDBHeadLine);
                        hasError = true;
                    }
                } else {
                    dupDBHeadLine = dupDBHeadLine + i;
                    if (!duplicateLines.contains(dupDBHeadLine)) {
                        repeatInfos.add("第" + dupDBHeadLine + "行" + meetingTeacherImportBo.getRealName());
                    }
                    duplicateLines.add(dupDBHeadLine);
                    hasError = true;
                }
            }
            if (!teacherNameOidMap.containsKey(meetingTeacherImportBo.getRealName())) {
                notExistHeadLine = notExistHeadLine + i;
                notExistInfos.add("第" + notExistHeadLine + "行" + meetingTeacherImportBo.getRealName());
                hasError = true;
            }
            if (hasError) {
                continue;
            }
            // 有效数据
            MeetingUserVo meetingUserVo = new MeetingUserVo();
            meetingUserVo.setUserOid(teacherNameOidMap.get(meetingTeacherImportBo.getRealName()));
            meetingUserVo.setUserName(meetingTeacherImportBo.getRealName());
            meetingUserVos.add(meetingUserVo);
        }

        meetingImportVo.setNotExistInfos(notExistInfos);
        meetingImportVo.setRepeatInfos(repeatInfos);
        meetingImportVo.setMeetingUserVos(meetingUserVos);
        meetingImportVo.setSuccessNum((long)meetingUserVos.size());
        meetingImportVo.setFailNum((long)(list.size() - meetingUserVos.size()));
        return AjaxResult.success(meetingImportVo);
    }

    @Override
    public AjaxResult getStudentImportCheckResult(MeetingImportBo<MeetingStudentImportBo> meetingImportBo) {
        if (CollectionUtils.isEmpty(meetingImportBo.getList()) || meetingImportBo.getOrganizationId() == null) {
            return AjaxResult.fail("导入数据为空");
        }
        MeetingImportVo meetingImportVo = new MeetingImportVo();
        List<MeetingStudentImportBo> list = meetingImportBo.getList();
        // 数据过滤前后空格
        list.stream().forEach(mtib -> {
            if(StringUtils.isNotBlank(mtib.getRealName())){
                mtib.setRealName(StringUtils.trim(mtib.getRealName()));
            }
            if(StringUtils.isNotBlank(mtib.getGradeName())){
                mtib.setGradeName(StringUtils.trim(mtib.getGradeName()));
            }
            if(StringUtils.isNotBlank(mtib.getClazzName())){
                mtib.setClazzName(StringUtils.trim(mtib.getClazzName()));
                // 判断是否以班结尾，如果是则移除班
                if(mtib.getClazzName().endsWith("班")){
                    mtib.setClazzName(mtib.getClazzName().substring(0, mtib.getClazzName().length() - 1));
                }
            }
        });
        Long organizationId = meetingImportBo.getOrganizationId();
        // 找出所有班级
        ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
        clazzConditionBoExt.setOrganizationId(organizationId);
        AjaxResult<List<ClazzInfoVo>> clazzListDBAjaxResult =
            baseDataService.getClazzListWithoutDataAuthority(clazzConditionBoExt);
        // 数据库班级
        List<ClazzInfoVo> clazzInfoVoListDB = Lists.newArrayList();
        if (clazzListDBAjaxResult.isSuccess() && CollectionUtils.isNotEmpty(clazzListDBAjaxResult.getData())) {
            clazzInfoVoListDB = clazzListDBAjaxResult.getData();
        }
        // 导入的excel里面的班级
        // List<String> gradeClassNameList = list.stream()
        // .filter(msib -> StringUtils.isNotBlank(msib.getGradeName()) && StringUtils.isNotBlank(msib.getClazzName()))
        // .map(msib -> msib.getGradeName().concat(msib.getClazzName())).distinct().collect(Collectors.toList());
        // 找出所有班级的学生<年级班级名称，List<学生姓名>>
        Map<String, List<String>> clazzStudentNameMap = Maps.newHashMap();
        // 班级名称，班级内重复的学生名称列表
        Map<String, List<String>> clazzStudentDuplicateNameDBMap = Maps.newHashMap();
        // 学生姓名map和userOid的关系--> <学生名称,userOid>
        Map<String, String> studentNameOidMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(clazzInfoVoListDB)) {
            for (ClazzInfoVo clazzInfoVo : clazzInfoVoListDB) {
                String gradeClazzName = clazzInfoVo.getGradeName().concat(clazzInfoVo.getClassesName());
                List<StudentVo> studentVoListByClassesId =
                    baseDataService.getStudentVoListByClassesId(clazzInfoVo.getId());
                if (CollectionUtils.isNotEmpty(studentVoListByClassesId)) {
                    List<String> studentNames = studentVoListByClassesId.stream()
                        .filter(mtib -> StringUtils.isNotBlank(mtib.getUserVo().getRealName()))
                        .map(mtib -> mtib.getUserVo().getRealName()).collect(Collectors.toList());
                    clazzStudentNameMap.put(gradeClazzName, studentNames);
                    Map<String,
                        Long> frequencyNameDBMap = studentVoListByClassesId.stream()
                            .filter(mtib -> StringUtils.isNotBlank(mtib.getUserVo().getRealName())).collect(
                                Collectors.groupingBy(mtib -> mtib.getUserVo().getRealName(), Collectors.counting()));
                    // 数据库里面本班级已经重复的名称
                    List<String> duplicateNamesDB = frequencyNameDBMap.entrySet().stream()
                        .filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
                    clazzStudentDuplicateNameDBMap.put(gradeClazzName, duplicateNamesDB);

                    Map<String, String> studentNameOidMapTemp =
                        studentVoListByClassesId.stream().collect(Collectors.toMap(
                            studentVo -> studentVo.getUserVo().getRealName(), StudentVo::getUserOid, (v1, v2) -> v1));
                    studentNameOidMap.putAll(studentNameOidMapTemp);
                }
            }
        }
        // 导入数据里面重复的数据
        Map<String,
            Long> frequencyValueMap = list.stream()
                .filter(mtib -> StringUtils.isNotBlank(mtib.getRealName())
                    && StringUtils.isNotBlank(mtib.getGradeName()) && StringUtils.isNotBlank(mtib.getClazzName()))
                .collect(Collectors.groupingBy(
                    m1 -> m1.getRealName().concat(m1.getGradeName()).concat(m1.getClazzName()), Collectors.counting()));
        // 导入数据里面重复的名称+年级+班级
        List<String> duplicateValues = frequencyValueMap.entrySet().stream().filter(entry -> entry.getValue() > 1)
            .map(Map.Entry::getKey).collect(Collectors.toList());

        // 便利导入的学生数据然后校验
        List<String> notExistInfos = Lists.newArrayList();
        List<String> repeatInfos = Lists.newArrayList();
        List<MeetingUserVo> meetingUserVos = Lists.newArrayList();
        // 导入数据重复和数据库重复的行数数据
        Set<Integer> duplicateLines = Sets.newLinkedHashSet();
        for (int i = 0; i < list.size(); i++) {
            MeetingStudentImportBo meetingStudentImportBo = list.get(i);
            if (StringUtils.isBlank(meetingStudentImportBo.getRealName())
                || StringUtils.isBlank(meetingStudentImportBo.getGradeName())
                || StringUtils.isBlank(meetingStudentImportBo.getClazzName())) {
                continue;
            }
            // 导入数据重复的行号，默认值为标题占用的行数
            Integer dupHeadLine = ConstantsInteger.NUM_5;
            // 数据库数据重复的行号，默认值为标题占用的行数
            Integer dupDBHeadLine = ConstantsInteger.NUM_5;
            // 不存在的行号，默认值为标题占用的行数
            Integer notExistHeadLine = ConstantsInteger.NUM_5;
            boolean hasError = false;
            String gradeClazzName = meetingStudentImportBo.getGradeName().concat(meetingStudentImportBo.getClazzName());
            String lineValue = meetingStudentImportBo.getRealName().concat(gradeClazzName);
            if (clazzStudentNameMap.containsKey(gradeClazzName)) {
                List<String> studentDuplicateNameDBList = clazzStudentDuplicateNameDBMap.get(gradeClazzName);
                List<String> studentNames = clazzStudentNameMap.get(gradeClazzName);
                // 导入数据重复的学生姓名
                if (duplicateValues.contains(lineValue)) {
                    dupHeadLine = dupHeadLine + i;
                    if (!duplicateLines.contains(dupHeadLine)) {
                        repeatInfos.add("第" + dupHeadLine + "行" + meetingStudentImportBo.getRealName());
                    }
                    duplicateLines.add(dupHeadLine);
                    hasError = true;
                }
                // 本班级内重复的学生姓名
                if (studentDuplicateNameDBList.contains(meetingStudentImportBo.getRealName())) {
                    dupDBHeadLine = dupDBHeadLine + i;
                    if (!duplicateLines.contains(dupDBHeadLine)) {
                        repeatInfos.add("第" + dupDBHeadLine + "行" + meetingStudentImportBo.getRealName());
                    }
                    duplicateLines.add(dupDBHeadLine);
                    hasError = true;
                }
                if (!studentNames.contains(meetingStudentImportBo.getRealName())) {
                    notExistHeadLine = notExistHeadLine + i;
                    notExistInfos.add("第" + notExistHeadLine + "行" + meetingStudentImportBo.getRealName());
                    hasError = true;
                }
                if (hasError) {
                    continue;
                }
                // 有效数据
                MeetingUserVo meetingUserVo = new MeetingUserVo();
                meetingUserVo.setUserOid(studentNameOidMap.get(meetingStudentImportBo.getRealName()));
                meetingUserVo.setUserName(meetingStudentImportBo.getRealName());
                meetingUserVos.add(meetingUserVo);
            } else {
                notExistHeadLine = notExistHeadLine + i;
                notExistInfos.add("第" + notExistHeadLine + "行" + meetingStudentImportBo.getRealName());
            }
        }
        meetingImportVo.setNotExistInfos(notExistInfos);
        meetingImportVo.setRepeatInfos(repeatInfos);
        meetingImportVo.setMeetingUserVos(meetingUserVos);
        meetingImportVo.setSuccessNum((long)meetingUserVos.size());
        meetingImportVo.setFailNum((long)(list.size() - meetingUserVos.size()));
        return AjaxResult.success(meetingImportVo);
    }

    /**
     * 通过会议id获取会议详情-byCache
     *
     * @param meetingId 会议id
     * @return com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo
     * <AUTHOR>
     * @date 2023/4/13 14:45
     */
    private MeetingVo getMeetingDetailByMeetingIdAndCache(Long meetingId) {
        String key = ConstantsRedis.MEETING_REDIS_KEY.concat(meetingId.toString());
        Object object = redisComponent.get(key);
        if (object != null) {
            return JSONObject.parseObject(object.toString(), MeetingVo.class);
        } else {
            MeetingVo detail = meetingService.getDetail(meetingId);
            if (null == detail || null == detail.getMeetingId()) {
                return null;
            }
            redisComponent.set(key, detail);
            Date endDay = DateKit.getDateAndTimeCompose(detail.getMeetingDate(), detail.getMeetingEndTime());
            // 有效期到endDay的23:59:59
            redisComponent.expire(key, DateKit.getNextDayMidnightSeconds(endDay));
            return detail;
        }
    }

}
