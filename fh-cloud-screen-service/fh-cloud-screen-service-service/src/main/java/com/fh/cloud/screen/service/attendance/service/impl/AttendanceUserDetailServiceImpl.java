package com.fh.cloud.screen.service.attendance.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserDetailBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserDetailListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;
import com.fh.cloud.screen.service.attendance.mapper.AttendanceUserDetailMapper;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserDetailService;
import com.light.core.enums.StatusEnum;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 考勤用户详情表，需要日终计算接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Service
public class AttendanceUserDetailServiceImpl extends ServiceImpl<AttendanceUserDetailMapper, AttendanceUserDetail>
    implements IAttendanceUserDetailService {

    @Resource
    private AttendanceUserDetailMapper attendanceUserDetailMapper;

    @Override
    public List<AttendanceUserDetailVo>
        getAttendanceUserDetailListByCondition(AttendanceUserDetailListConditionBo condition) {
        return attendanceUserDetailMapper.getAttendanceUserDetailListByCondition(condition);
    }

    @Override
    public boolean addAttendanceUserDetail(AttendanceUserDetailBo attendanceUserDetailBo) {
        return false;
    }

    @Override
    public boolean updateAttendanceUserDetail(AttendanceUserDetailBo attendanceUserDetailBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long attendanceUserDetailId) {
        return null;
    }

    @Override
    public List<AttendanceUserDetailVo> getListByAttendanceUserId(Long attendanceUserId) {
        QueryWrapper<AttendanceUserDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AttendanceUserDetail::getAttendanceUserId, attendanceUserId)
            .eq(AttendanceUserDetail::getIsDelete, StatusEnum.NOTDELETE.getCode())
            .orderByAsc(AttendanceUserDetail::getAttendanceRuleDayIndex);
        final List<AttendanceUserDetail> attendanceUserDetails =
            this.attendanceUserDetailMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(attendanceUserDetails)) {
            return Lists.newArrayList();
        }
        return JSONUtil.toList(JSONUtil.parseArray(attendanceUserDetails), AttendanceUserDetailVo.class);
    }

}