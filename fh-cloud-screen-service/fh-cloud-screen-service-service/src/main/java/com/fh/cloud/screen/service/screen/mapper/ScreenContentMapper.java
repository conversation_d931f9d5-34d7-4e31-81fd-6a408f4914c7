package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContent;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 云屏内容表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface ScreenContentMapper extends BaseMapper<ScreenContent> {

    List<ScreenContentVo> getScreenContentListByCondition(ScreenContentListConditionBo condition);

    /**
     * 查询大于当前时间的第一条记录
     *
     * @param scopeType {@link com.fh.cloud.screen.service.enums.ScreenContentScopeType}
     * @param screenModuleDataId the screen module data id
     * @param campusId the campus id
     * @param classesId the classes id
     * @param updateTime 上一次的查询时间
     * @return integer integer
     * <AUTHOR>
     * @date 2023 -01-16 14:34:55
     */
    Integer countFirstRecord(@Param("scopeType") Integer scopeType,
        @Param("screenModuleDataId") Long screenModuleDataId, @Param("campusId") Long campusId,
        @Param("classesId") Long classesId, @Param("updateTime") Date updateTime);

}
