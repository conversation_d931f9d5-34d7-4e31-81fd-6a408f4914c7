package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditCountVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo;
import org.apache.ibatis.annotations.Param;

/**
 * 云屏场景审核表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
public interface ScreenSceneAuditMapper extends BaseMapper<ScreenSceneAuditDto> {

	List<ScreenSceneAuditVo> getScreenSceneAuditListByCondition(ScreenSceneAuditConditionBo condition);

	/**
	 * 点位发布审核记录查询
	 *
	 * @param condition
	 * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo>
	 * <AUTHOR>
	 * @date 2024/8/9 10:41
	 **/
	List<ScreenSceneAuditVo> getScreenSceneAuditListByPoint(ScreenSceneAuditConditionBo condition);

	ScreenSceneAuditVo getScreenSceneAuditByCondition(ScreenSceneAuditConditionBo condition);

	void deleteToAuditByShowDeviceId(@Param("showDeviceId") Long showDeviceId, @Param("auditType") Integer auditType);

	ScreenSceneAuditCountVo getScreenSceneAuditCount(ScreenSceneAuditConditionBo condition);

}
