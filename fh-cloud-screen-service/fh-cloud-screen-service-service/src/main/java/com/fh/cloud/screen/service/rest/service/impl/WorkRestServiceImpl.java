package com.fh.cloud.screen.service.rest.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRest;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestDay;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGrade;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeVo;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestVo;
import com.fh.cloud.screen.service.rest.mapper.WorkRestMapper;
import com.fh.cloud.screen.service.rest.service.IWorkRestDayService;
import com.fh.cloud.screen.service.rest.service.IWorkRestGradeService;
import com.fh.cloud.screen.service.rest.service.IWorkRestService;
import com.google.common.collect.Lists;
import com.light.core.enums.StatusEnum;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;

/**
 * 作息时间主表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class WorkRestServiceImpl extends ServiceImpl<WorkRestMapper, WorkRest> implements IWorkRestService {

    @Resource
    private WorkRestMapper workRestMapper;
    @Resource
    private IWorkRestDayService workRestDayService;
    @Resource
    private IWorkRestGradeService workRestGradeService;

    @Override
    public List<WorkRestVo> getWorkRestListByCondition(WorkRestListConditionBo condition) {
        List<WorkRestVo> workRestVoList = workRestMapper.getWorkRestListByCondition(condition);
        if (CollectionUtils.isEmpty(workRestVoList)) {
            return workRestVoList;
        }
        List<Long> restIds = workRestVoList.stream().map(WorkRestVo::getWorkRestId).collect(Collectors.toList());
        List<WorkRestGrade> workRestGradeList = workRestGradeService
            .list(new LambdaQueryWrapper<WorkRestGrade>().in(WorkRestGrade::getWorkRestId, restIds));
        List<WorkRestDay> workRestDayList =
            workRestDayService.list(new LambdaQueryWrapper<WorkRestDay>().in(WorkRestDay::getWorkRestId, restIds));

        List<WorkRestGradeVo> workRestGradeVos = workRestGradeList.stream()
            .map(grade -> BeanUtil.toBean(grade, WorkRestGradeVo.class)).collect(Collectors.toList());
        List<WorkRestDayVo> workRestDayVos =
            workRestDayList.stream().map(day -> BeanUtil.toBean(day, WorkRestDayVo.class)).collect(Collectors.toList());

        // 拼接返回数据
        for (WorkRestVo workRestVo : workRestVoList) {
            List<WorkRestGradeVo> restGradeVos = workRestGradeVos.stream()
                .filter(grade -> grade.getWorkRestId().equals(workRestVo.getWorkRestId())).collect(Collectors.toList());
            for (WorkRestGradeVo workRestGradeVo : restGradeVos) {
                List<WorkRestDayVo> restDayVos = workRestDayVos.stream()
                    .filter(day -> day.getWorkRestGradeId().equals(workRestGradeVo.getWorkRestGradeId()))
                    .collect(Collectors.toList());
                Map<Integer, List<WorkRestDayVo>> weekMap =
                    restDayVos.stream().collect(Collectors.groupingBy(WorkRestDayVo::getWeek));
                workRestGradeVo.setWeekMap(weekMap);
            }
            workRestVo.setWorkRestGradeVoList(restGradeVos);
        }

        return workRestVoList;
    }

    @Override
    public Long addWorkRest(WorkRestBo workRestBo) {
        WorkRest workRest = new WorkRest();
        BeanUtils.copyProperties(workRestBo, workRest);
        workRest.setIsDelete(StatusEnum.NOTDELETE.getCode());
        boolean result = save(workRest);
        return result ? workRest.getWorkRestId() : null;
    }

    @Override
    public boolean updateWorkRest(WorkRestBo workRestBo) {
        WorkRest workRest = new WorkRest();
        BeanUtils.copyProperties(workRestBo, workRest);
        return updateById(workRest);
    }

    @Override
    public WorkRestVo getDetail(Long workRestId) {
        if (workRestId == null) {
            return null;
        }

        LambdaQueryWrapper<WorkRest> lqw = new LambdaQueryWrapper<>();
        lqw.eq(WorkRest::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(WorkRest::getWorkRestId, workRestId);
        WorkRest workRest = getOne(lqw);
        WorkRestVo workRestVo = new WorkRestVo();
        BeanUtils.copyProperties(workRest, workRestVo);
        return workRestVo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Long saveOrUpdateWorkRestWithDetail(WorkRestBo workRestBo) {
        Long workRestId = workRestBo.getWorkRestId();
        if (workRestId == null) {
            workRestId = addWorkRest(workRestBo);
        } else {
            updateWorkRest(workRestBo);
        }

        // 子表信息处理
        Long workRestIdFinal = workRestId;
        List<WorkRestGradeBo> workRestGradeBoList = workRestBo.getWorkRestGradeBoList();
        workRestGradeBoList.stream().forEach(workRestGradeBo -> workRestGradeBo.setWorkRestId(workRestIdFinal));
        workRestGradeService.deleteAndSaveBatch(workRestId, workRestGradeBoList);
        List<WorkRestDayBo> workRestDayBoListAll = Lists.newArrayList();
        for (WorkRestGradeBo workRestGradeBo : workRestGradeBoList) {
            if (workRestGradeBo.getWeekMap() == null || workRestGradeBo.getWeekMap().isEmpty()) {
                continue;
            }
            for (Integer week : workRestGradeBo.getWeekMap().keySet()) {
                List<WorkRestDayBo> tempWorkRestDayBoList = workRestGradeBo.getWeekMap().get(week);
                if (CollectionUtils.isEmpty(tempWorkRestDayBoList)) {
                    continue;
                }
                tempWorkRestDayBoList.stream().forEach(workRestDayBo -> {
                    workRestDayBo.setWorkRestId(workRestIdFinal);
                    workRestDayBo.setWorkRestGradeId(workRestGradeBo.getWorkRestGradeId());
                    workRestDayBoListAll.add(workRestDayBo);
                });
            }
        }
        workRestDayService.deleteAndSaveBatch(workRestId, workRestDayBoListAll);
        return workRestId;
    }

    @Override
    public void changeStatus(WorkRestBo workRestBo) {
        if (workRestBo.getStatus().equals(StatusEnum.ENABLE.getCode())) {
            UpdateWrapper<WorkRest> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(true, WorkRest::getOrganizationId, workRestBo.getOrganizationId())
                .eq(true, WorkRest::getStatus, StatusEnum.ENABLE.getCode())
                .set(WorkRest::getStatus, StatusEnum.DISABLE.getCode());
            this.update(null, updateWrapper);

        }
        UpdateWrapper<WorkRest> updateWrapper_en = new UpdateWrapper<>();
        updateWrapper_en.lambda().eq(true, WorkRest::getOrganizationId, workRestBo.getOrganizationId())
            .eq(true, WorkRest::getWorkRestId, workRestBo.getWorkRestId())
            .set(WorkRest::getStatus, workRestBo.getStatus());
        this.update(null, updateWrapper_en);

    }
}