package com.fh.cloud.screen.service.calendar.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarWeek;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;

import java.util.List;

/**
 * 校历上课日星期表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
public interface SchoolCalendarWeekMapper extends BaseMapper<SchoolCalendarWeek> {

    List<SchoolCalendarWeekVo> getSchoolCalendarWeekListByCondition(SchoolCalendarWeekListConditionBo condition);

}
