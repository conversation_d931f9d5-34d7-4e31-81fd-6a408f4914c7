package com.fh.cloud.screen.service.space.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.mapper.SpaceDeviceRelMapper;
import com.fh.cloud.screen.service.space.service.ISpaceDeviceRelService;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.light.core.enums.StatusEnum;
import com.light.user.clazz.entity.vo.ClazzVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 地点和设备关系表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Slf4j
@Service
public class SpaceDeviceRelServiceImpl extends ServiceImpl<SpaceDeviceRelMapper, SpaceDeviceRel>
    implements ISpaceDeviceRelService {

    @Resource
    private SpaceDeviceRelMapper spaceDeviceRelMapper;

    @Resource
    @Lazy
    private ISpaceInfoService iSpaceInfoService;

    @Lazy
    @Resource
    private BaseDataService baseDataService;

    @Lazy
    @Resource
    private ISpaceGroupService spaceGroupService;

    @Override
    public List<SpaceDeviceRelVo> getSpaceDeviceRelListByCondition(SpaceDeviceRelListConditionBo condition) {
        return spaceDeviceRelMapper.getSpaceDeviceRelListByCondition(condition);
    }

    @Override
    public boolean saveSpaceDeviceRel(SpaceDeviceRelBo spaceDeviceRelBo) {

        // 校验 设备是否已经绑定
        final Long showDeviceId = spaceDeviceRelBo.getShowDeviceId();
        final SpaceDeviceRel spaceDevice = this.getByShowDeviceId(showDeviceId);
        if (spaceDevice != null) {
            spaceDeviceRelBo.setId(spaceDevice.getId());
        }
        // 进行绑定
        final SpaceDeviceRel spaceDeviceRel = BeanUtil.toBean(spaceDeviceRelBo, SpaceDeviceRel.class);
        // 获取 地点所属组织机构ID 校区ID
        final Long spaceInfoId = spaceDeviceRelBo.getSpaceInfoId();
        final Integer spaceGroupUseType = spaceDeviceRelBo.getSpaceGroupUseType();
        if (spaceGroupUseType == SpaceGroupUseType.XZ.getValue()) {
            final ClazzVo clazzVo = this.baseDataService.getByClazzId(spaceInfoId);
            if (clazzVo != null) {
                log.info(" ===========SpaceDeviceRelServiceImpl -->saveSpaceDeviceRel xz  地点数据赋值 -===============");
                spaceDeviceRel.setOrganizationId(clazzVo.getOrganizationId());
                spaceDeviceRel.setCampusId(clazzVo.getCampusId());
                if (spaceDeviceRel.getSpaceGroupId() == null) {
                    SpaceGroupVo spaceGroupVoOfXz = spaceGroupService.getSpaceGroupVoOfXz();
                    if (spaceGroupVoOfXz != null) {
                        spaceDeviceRel.setSpaceGroupId(spaceGroupVoOfXz.getSpaceGroupId());
                    }
                }
            }
        }
        //
        if (spaceGroupUseType == SpaceGroupUseType.NOT_XZ.getValue()) {
            final SpaceInfo spaceInfo = this.iSpaceInfoService.getById(spaceInfoId);
            if (spaceInfo != null) {
                log.info(" ===========SpaceDeviceRelServiceImpl -->saveSpaceDeviceRel not xz  地点数据赋值 -===============");
                spaceDeviceRel.setOrganizationId(spaceInfo.getOrganizationId());
                spaceDeviceRel.setCampusId(spaceInfo.getCampusId());
                if (spaceDeviceRel.getSpaceGroupId() == null) {
                    spaceDeviceRel.setSpaceGroupId(spaceInfo.getSpaceGroupId());
                }
            }
        }

        return this.saveOrUpdate(spaceDeviceRel);
    }

    /**
     * 根据设备获取 绑定数量
     *
     * @param deviceId
     * @return
     */
    public int getCountByShowDeviceId(Long deviceId) {
        QueryWrapper<SpaceDeviceRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SpaceDeviceRel::getShowDeviceId, deviceId).eq(SpaceDeviceRel::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        return this.baseMapper.selectCount(queryWrapper);
    }

    /**
     * 根据设备获取 绑定数量
     *
     * @param deviceId
     * @return
     */
    public SpaceDeviceRel getByShowDeviceId(Long deviceId) {
        QueryWrapper<SpaceDeviceRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SpaceDeviceRel::getShowDeviceId, deviceId).eq(SpaceDeviceRel::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean updateSpaceDeviceRel(SpaceDeviceRelBo spaceDeviceRelBo) {
        SpaceDeviceRel spaceDeviceRel = new SpaceDeviceRel();
        BeanUtils.copyProperties(spaceDeviceRelBo, spaceDeviceRel);
        return updateById(spaceDeviceRel);
    }

    @Override
    public Map<String, Object> getDetail(Long spaceDeviceRelId) {
        return null;
    }

    @Override
    public SpaceDeviceRel getByDeviceId(Long deviceId) {

        QueryWrapper<SpaceDeviceRel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SpaceDeviceRel::getShowDeviceId, deviceId).eq(SpaceDeviceRel::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        return this.baseMapper.selectOne(queryWrapper);
    }

}