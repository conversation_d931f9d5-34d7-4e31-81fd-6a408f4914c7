package com.fh.cloud.screen.service.er.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.er.entity.bo.*;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoTeacherDto;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;
import com.fh.cloud.screen.service.er.service.IExamInfoStudentService;
import com.fh.cloud.screen.service.er.service.IExamInfoTeacherService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.er.entity.dto.ExamInfoSubjectDto;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.service.IExamInfoSubjectService;
import com.fh.cloud.screen.service.er.mapper.ExamInfoSubjectMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 考场_考试计划里面一次考试科目信息接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
@Service
public class ExamInfoSubjectServiceImpl extends ServiceImpl<ExamInfoSubjectMapper, ExamInfoSubjectDto>
    implements IExamInfoSubjectService {

    @Resource
    private ExamInfoSubjectMapper examInfoSubjectMapper;
    @Autowired
    private IExamInfoStudentService examInfoStudentService;
    @Autowired
    private IExamInfoTeacherService examInfoTeacherService;
    @Autowired
    private IExamInfoSubjectService examInfoSubjectService;

    @Resource
    private ISpaceInfoService spaceInfoService;

    @Override
    public List<ExamInfoSubjectVo> getExamInfoSubjectListByCondition(ExamInfoSubjectConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return examInfoSubjectMapper.getExamInfoSubjectListByCondition(condition);
    }

    @Override
    public AjaxResult addExamInfoSubject(ExamInfoSubjectBo examInfoSubjectBo) {
        ExamInfoSubjectDto examInfoSubject = new ExamInfoSubjectDto();
        BeanUtils.copyProperties(examInfoSubjectBo, examInfoSubject);
        examInfoSubject.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(examInfoSubject)) {
            // 保存成功则塞入主键
            examInfoSubjectBo.setExamInfoSubjectId(examInfoSubject.getExamInfoSubjectId());
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamInfoSubject(ExamInfoSubjectBo examInfoSubjectBo) {
        ExamInfoSubjectDto examInfoSubject = new ExamInfoSubjectDto();
        BeanUtils.copyProperties(examInfoSubjectBo, examInfoSubject);
        if (updateById(examInfoSubject)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ExamInfoSubjectVo getDetail(Long id) {
        ExamInfoSubjectConditionBo condition = new ExamInfoSubjectConditionBo();
        condition.setExamInfoSubjectId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoSubjectVo> list = examInfoSubjectMapper.getExamInfoSubjectListByCondition(condition);
        ExamInfoSubjectVo vo = new ExamInfoSubjectVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult addExamInfoSubjectBatchWithDetail(List<ExamInfoSubjectBo> examInfoSubjectBos) {
        if (CollectionUtils.isEmpty(examInfoSubjectBos)) {
            return AjaxResult.success();
        }
        List<ExamInfoStudentBo> allStudentList = new ArrayList<>();
        List<ExamInfoTeacherBo> allTeacherList = new ArrayList<>();
        for (ExamInfoSubjectBo examInfoSubjectBo : examInfoSubjectBos) {
            // 添加考试科目信息
            examInfoSubjectBo.setExamInfoSubjectId(null);
            addExamInfoSubject(examInfoSubjectBo);
            Long examInfoSubjectId = examInfoSubjectBo.getExamInfoSubjectId();

            // 添加考试科目的学生
            List<ExamInfoStudentBo> examInfoStudentBos = examInfoSubjectBo.getExamInfoStudentBos();
            if (CollectionUtils.isNotEmpty(examInfoStudentBos)) {
                examInfoStudentBos.stream().forEach(examInfoStudentBo -> {
                    examInfoStudentBo.setExamInfoSubjectId(examInfoSubjectId);
                    examInfoStudentBo.setExamInfoId(examInfoSubjectBo.getExamInfoId());
                    examInfoStudentBo.setExamPlanId(examInfoSubjectBo.getExamPlanId());
                });
                allStudentList.addAll(examInfoStudentBos);
            }

            // 添加考试科目的教师
            if (CollectionUtils.isNotEmpty(examInfoSubjectBo.getExamInfoTeacherBos())) {
                List<ExamInfoTeacherBo> examInfoTeacherBos = examInfoSubjectBo.getExamInfoTeacherBos();
                examInfoTeacherBos.stream().forEach(examInfoTeacherBo -> {
                    examInfoTeacherBo.setExamInfoSubjectId(examInfoSubjectId);
                    examInfoTeacherBo.setExamInfoId(examInfoSubjectBo.getExamInfoId());
                    examInfoTeacherBo.setExamPlanId(examInfoSubjectBo.getExamPlanId());
                });
                allTeacherList.addAll(examInfoTeacherBos);
            }
        }
        // examInfoStudentService.addExamInfoStudentBatch(allStudentList);
        // examInfoTeacherService.addExamInfoTeacherBatch(allTeacherList);
        examInfoStudentService.addExamInfoStudentBatchByXML(allStudentList);
        examInfoTeacherService.addExamInfoTeacherBatchByXML(allTeacherList);
        return AjaxResult.success();
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public AjaxResult updateExamInfoSubjectBatchWithDetail(List<ExamInfoSubjectBo> examInfoSubjectBos) {
        if (CollectionUtils.isEmpty(examInfoSubjectBos)) {
            return AjaxResult.success();
        }
        // 删除
        Long examInfoId = examInfoSubjectBos.get(0).getExamInfoId();
        LambdaUpdateWrapper<ExamInfoSubjectDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExamInfoSubjectDto::getExamInfoId, examInfoId);
        updateWrapper.eq(ExamInfoSubjectDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ExamInfoSubjectDto examInfoSubjectDto = new ExamInfoSubjectDto();
        examInfoSubjectDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        examInfoSubjectMapper.update(examInfoSubjectDto, updateWrapper);

        // 批量删除学生教师和科目关系
        examInfoStudentService.deleteExamInfoStudentBatch(examInfoId);
        examInfoTeacherService.deleteExamInfoTeacherBatch(examInfoId);
        // 批量插入
        if (CollectionUtils.isNotEmpty(examInfoSubjectBos)) {
            examInfoSubjectService.addExamInfoSubjectBatchWithDetail(examInfoSubjectBos);
        }
        return AjaxResult.success();
    }

    /**
     * 通过地点和日期检索考试科目列表
     *
     * @param spaceGroupUseType
     * @param spaceInfoId
     * @param date
     * @return java.util.List<com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo>
     * <AUTHOR>
     * @date 2022/10/17 14:55
     */
    @Override
    public List<ExamInfoSubjectVo> getExamInfoSubjectListBySpaceAndDate(Integer spaceGroupUseType, Long spaceInfoId,
        Date date) {
        return examInfoSubjectMapper.getExamInfoSubjectListBySpaceAndDate(spaceGroupUseType, spaceInfoId, date);
    }

    /**
     * 获取当前考试详情
     * 
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/1/16 10:05
     */
    @Override
    public AjaxResult getNowExamInfoByCondition(ExamInfoSubjectConditionBo conditionBo) {
        // 1、当前地点和定日期开始考试的 科目列表
        conditionBo.setOrderBy("exam_start_time asc");
        List<ExamInfoSubjectVo> examInfoSubjectVos = examInfoSubjectMapper.getNowExamInfoByCondition(conditionBo);
        if (CollectionUtils.isEmpty(examInfoSubjectVos)) {
            return AjaxResult.success();
        }
        // 2、提前30分钟霸屏，获取30分钟后开始的考试
        Date nowDate = conditionBo.getExamStartTime();
        ExamInfoSubjectVo returnVo = null;
        for (int i = 0; i < examInfoSubjectVos.size(); i++) {
            ExamInfoSubjectVo examInfoSubjectVo = examInfoSubjectVos.get(i);
            // 考试开始时间提前30分钟
            Date examStartTime = examInfoSubjectVo.getExamStartTime();
            examStartTime = DateKit.addMinute(examStartTime, ConstantsInteger.MEETING_SCENE_BEFORE_TIME);
            Date examEndTime = examInfoSubjectVo.getExamEndTime();
            // 当前时间在考试时间范围内
            if (nowDate.after(examStartTime) && nowDate.before(examEndTime)) {
                // 判断下一场考试提前30分钟后，是否包含当前时间，是->返回下一场考试信息
                if (i != examInfoSubjectVos.size() - 1) {
                    Date nextExamStartTime = DateKit.addMinute(examInfoSubjectVos.get(i + 1).getExamStartTime(),
                        ConstantsInteger.MEETING_SCENE_BEFORE_TIME);
                    Date nextExamEndTime = examInfoSubjectVos.get(i + 1).getExamEndTime();
                    if (nowDate.after(nextExamStartTime) && nowDate.before(nextExamEndTime)) {
                        returnVo = examInfoSubjectVos.get(i + 1);
                        break;
                    }
                }
                returnVo = examInfoSubjectVo;
                break;
            }
        }
        if (null == returnVo) {
            return AjaxResult.success();
        }
        // 3、封装地点
        // List<Long> spaceInfoIds = new ArrayList<>();
        // spaceInfoIds.add(conditionBo.getSpaceInfoId());
        // if (SpaceGroupUseType.XZ.getValue() == conditionBo.getSpaceGroupUseType()) {
        // List<ClazzInfoVo> classesInfoVos = spaceInfoService.getSpaceInfoVosByIdsXz(spaceInfoIds);
        // Map<Long, String> classesNameMap = classesInfoVos.stream()
        // .collect(Collectors.toMap(ClazzInfoVo::getId, ClazzInfoVo::getClassesNameShow, (k1, k2) -> k1));
        // returnVo.setSubjectName(classesNameMap.get(conditionBo.getSpaceInfoId()));
        // } else if (SpaceGroupUseType.NOT_XZ.getValue() == conditionBo.getSpaceGroupUseType()) {
        // List<SpaceInfoVo> spaceInfoVos = spaceInfoService.getSpaceInfoVosByIdsNotXz(spaceInfoIds);
        // Map<Long, String> spaceInfoNameMap = spaceInfoVos.stream()
        // .collect(Collectors.toMap(SpaceInfoVo::getSpaceInfoId, SpaceInfoVo::getSpaceInfoName, (k1, k2) -> k1));
        // returnVo.setSubjectName(spaceInfoNameMap.get(conditionBo.getSpaceInfoId()));
        // }
        // 4、封装当前考试科目的学生、教师等信息
        ExamInfoStudentConditionBo examInfoStudentConditionBo = new ExamInfoStudentConditionBo();
        examInfoStudentConditionBo.setExamInfoSubjectId(returnVo.getExamInfoSubjectId());
        returnVo.setExamInfoStudentVos(
            examInfoStudentService.getExamInfoStudentListByCondition(examInfoStudentConditionBo));

        ExamInfoTeacherConditionBo examInfoTeacherConditionBo = new ExamInfoTeacherConditionBo();
        examInfoTeacherConditionBo.setExamInfoSubjectId(returnVo.getExamInfoSubjectId());
        returnVo.setExamInfoTeacherVos(
            examInfoTeacherService.getExamInfoTeacherListByCondition(examInfoTeacherConditionBo));
        return AjaxResult.success(returnVo);
    }

    /**
     * 获取组织下所有考试科目信息
     *
     * @param organizationId
     * @return
     */
    @Override
    public List<ExamInfoSubjectVo> getExamInfoSubjectListByOrganizationId(Long organizationId) {
        return examInfoSubjectMapper.getExamInfoSubjectListByOrganizationId(organizationId);
    }

}