package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryUserRelDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryUserRelVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 模块用户关系表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
public interface IScreenModuleLibraryUserRelService extends IService<ScreenModuleLibraryUserRelDto> {

    List<ScreenModuleLibraryUserRelVo>
        getScreenModuleLibraryUserRelListByCondition(ScreenModuleLibraryUserRelConditionBo condition);

    AjaxResult addScreenModuleLibraryUserRel(ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo);

    AjaxResult updateScreenModuleLibraryUserRel(ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo);

    ScreenModuleLibraryUserRelVo getDetail(Long id);

}
