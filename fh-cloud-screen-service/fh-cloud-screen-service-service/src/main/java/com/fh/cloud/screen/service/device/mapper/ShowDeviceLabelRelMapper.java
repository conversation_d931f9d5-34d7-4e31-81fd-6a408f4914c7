package com.fh.cloud.screen.service.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;

/**
 * 设备订阅标签表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
public interface ShowDeviceLabelRelMapper extends BaseMapper<ShowDeviceLabelRelDto> {

	List<ShowDeviceLabelRelVo> getShowDeviceLabelRelListByCondition(ShowDeviceLabelRelConditionBo condition);

}
