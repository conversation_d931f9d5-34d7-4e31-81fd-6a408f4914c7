package com.fh.cloud.screen.service.face.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.face.entity.dto.FaceConfigDto;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 人脸对比参数接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:35
 */
public interface IFaceConfigService extends IService<FaceConfigDto> {

    List<FaceConfigVo> getFaceConfigListByCondition(FaceConfigConditionBo condition);

    AjaxResult addFaceConfig(FaceConfigBo faceConfigBo);

    AjaxResult updateFaceConfig(FaceConfigBo faceConfigBo);

    FaceConfigVo getDetail(Long id);

    /**
     * 根据学校id查询人脸参数配置信息
     *
     * @param organizationId the organization id
     * @return detail by organization id
     */
    FaceConfigVo getDetailByOrganizationId(Long organizationId);

}
