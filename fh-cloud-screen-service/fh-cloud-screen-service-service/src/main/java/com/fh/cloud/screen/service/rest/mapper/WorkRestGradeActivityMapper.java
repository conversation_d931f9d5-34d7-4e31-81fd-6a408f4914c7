package com.fh.cloud.screen.service.rest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGradeActivity;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeActivityVo;

import java.util.List;

/**
 * 作息时间年级活动课设置表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface WorkRestGradeActivityMapper extends BaseMapper<WorkRestGradeActivity> {

    List<WorkRestGradeActivityVo>
        getWorkRestGradeActivityListByCondition(WorkRestGradeActivityListConditionBo condition);

}
