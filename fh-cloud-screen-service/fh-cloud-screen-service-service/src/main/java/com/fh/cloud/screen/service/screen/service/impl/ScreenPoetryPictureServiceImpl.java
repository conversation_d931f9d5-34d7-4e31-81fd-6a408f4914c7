package com.fh.cloud.screen.service.screen.service.impl;

import com.light.core.utils.FuzzyQueryUtil;
import lombok.Synchronized;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryPictureDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo;
import com.fh.cloud.screen.service.screen.service.IScreenPoetryPictureService;
import com.fh.cloud.screen.service.screen.mapper.ScreenPoetryPictureMapper;
import com.light.core.entity.AjaxResult;
/**
 * 共话诗词图片资源表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
@Service
public class ScreenPoetryPictureServiceImpl extends ServiceImpl<ScreenPoetryPictureMapper, ScreenPoetryPictureDto> implements IScreenPoetryPictureService {

	@Resource
	private ScreenPoetryPictureMapper screenPoetryPictureMapper;
	
    @Override
	public List<ScreenPoetryPictureVo> getScreenPoetryPictureListByCondition(ScreenPoetryPictureConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return screenPoetryPictureMapper.getScreenPoetryPictureListByCondition(condition);
	}

	@Override
	public AjaxResult addScreenPoetryPicture(ScreenPoetryPictureBo screenPoetryPictureBo) {
		ScreenPoetryPictureDto screenPoetryPicture = new ScreenPoetryPictureDto();
		BeanUtils.copyProperties(screenPoetryPictureBo, screenPoetryPicture);
		screenPoetryPicture.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(screenPoetryPicture)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScreenPoetryPicture(ScreenPoetryPictureBo screenPoetryPictureBo) {
		ScreenPoetryPictureDto screenPoetryPicture = new ScreenPoetryPictureDto();
		BeanUtils.copyProperties(screenPoetryPictureBo, screenPoetryPicture);
		if(updateById(screenPoetryPicture)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ScreenPoetryPictureVo getScreenPoetryPictureByCondition(ScreenPoetryPictureConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return screenPoetryPictureMapper.getScreenPoetryPictureByCondition(condition);
	}

}