package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenScene;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云屏场景表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ScreenSceneMapper extends BaseMapper<ScreenScene> {

    List<ScreenSceneVo> getScreenSceneListByCondition(ScreenSceneListConditionBo condition);

    /**
     * 根据场景id集合获取数据
     *
     * @param sceneIds the scene ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:18:34
     */
    List<ScreenSceneVo> listScreenSceneVoByScreenSceneIds(@Param("sceneIds") List<Long> sceneIds,
        @Param("isDelete") Integer isDelete);
}
