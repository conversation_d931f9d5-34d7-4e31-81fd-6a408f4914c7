package com.fh.cloud.screen.service.screen.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenSignatureMessageApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureMessageDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo;
import com.fh.cloud.screen.service.screen.service.IScreenSignatureMessageService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * 电子签名寄语资源表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
@RestController
@Validated
public class ScreenSignatureMessageController implements ScreenSignatureMessageApi{
	
    @Autowired
    private IScreenSignatureMessageService screenSignatureMessageService;

    /**
     * 查询电子签名寄语资源表分页列表
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
    @Override
    public AjaxResult<PageInfo<ScreenSignatureMessageVo>> getScreenSignatureMessagePageListByCondition(@RequestBody ScreenSignatureMessageConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ScreenSignatureMessageVo> pageInfo = new PageInfo<>(screenSignatureMessageService.getScreenSignatureMessageListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询电子签名寄语资源表列表
	 * <AUTHOR>
	 * @date 2023-07-14 15:17:15
	 */
	@Override
	public AjaxResult<List<ScreenSignatureMessageVo>> getScreenSignatureMessageListByCondition(@RequestBody ScreenSignatureMessageConditionBo condition){
		List<ScreenSignatureMessageVo> list = screenSignatureMessageService.getScreenSignatureMessageListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增电子签名寄语资源表
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
     */
	@Override
    public AjaxResult addScreenSignatureMessage(@Validated @RequestBody ScreenSignatureMessageBo screenSignatureMessageBo){
		return screenSignatureMessageService.addScreenSignatureMessage(screenSignatureMessageBo);
    }

    /**
	 * 修改电子签名寄语资源表
	 * @param screenSignatureMessageBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
	 */
	@Override
	public AjaxResult updateScreenSignatureMessage(@Validated @RequestBody ScreenSignatureMessageBo screenSignatureMessageBo) {
		if(null == screenSignatureMessageBo.getScreenSignatureMessageId()) {
			return AjaxResult.fail("电子签名寄语资源表id不能为空");
		}
		return screenSignatureMessageService.updateScreenSignatureMessage(screenSignatureMessageBo);
	}

	/**
	 * 查询电子签名寄语资源表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
	 */
	@Override
	public AjaxResult<ScreenSignatureMessageVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("电子签名寄语资源表id不能为空");
		}
		ScreenSignatureMessageConditionBo condition = new ScreenSignatureMessageConditionBo();
		condition.setScreenSignatureMessageId(id);
		ScreenSignatureMessageVo vo = screenSignatureMessageService.getScreenSignatureMessageByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除电子签名寄语资源表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-14 15:17:15
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ScreenSignatureMessageDto screenSignatureMessageDto = new ScreenSignatureMessageDto();
		screenSignatureMessageDto.setScreenSignatureMessageId(id);
		screenSignatureMessageDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(screenSignatureMessageService.updateById(screenSignatureMessageDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
