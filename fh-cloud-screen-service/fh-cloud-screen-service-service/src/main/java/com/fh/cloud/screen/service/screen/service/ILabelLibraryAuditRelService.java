package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.dto.LabelLibraryAuditRelDto;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 标签海报关联表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
public interface ILabelLibraryAuditRelService extends IService<LabelLibraryAuditRelDto> {

    List<LabelLibraryAuditRelVo> getLabelLibraryAuditRelListByCondition(LabelLibraryAuditRelConditionBo condition);

    AjaxResult addLabelLibraryAuditRel(LabelLibraryAuditRelBo labelLibraryAuditRelBo);

    AjaxResult updateLabelLibraryAuditRel(LabelLibraryAuditRelBo labelLibraryAuditRelBo);

    LabelLibraryAuditRelVo getLabelLibraryAuditRelByCondition(LabelLibraryAuditRelConditionBo condition);

    List<LabelVo> getLabelListByLibraryAuditId(Long screenModuleLibraryAuditId);

    /**
     * 更新审核的分类
     *
     * @param screenModuleLibraryAuditId 审核表主键
     * @param labelBos the label bos
     * @return boolean
     * <AUTHOR>
     * @date 2024 -01-26 16:41:40
     */
    boolean updateLabelList(Long screenModuleLibraryAuditId, List<LabelBo> labelBos);

    /**
     * 更新审核的分类。内部调用updateLabelList，因为要有更新逻辑，所以按照screenModuleLibraryAuditId一个一个处理
     *
     * @param screenModuleLibraryAuditId 审核表主键
     * @param labelBos the label bos
     * @return boolean
     * <AUTHOR>
     * @date 2024 -01-26 16:41:40
     */
    boolean updateLabelList(List<Long> screenModuleLibraryAuditIds, List<LabelBo> labelBos);

}
