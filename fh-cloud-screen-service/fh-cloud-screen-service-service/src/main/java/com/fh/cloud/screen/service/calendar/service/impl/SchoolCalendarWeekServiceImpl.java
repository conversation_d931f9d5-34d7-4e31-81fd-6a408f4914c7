package com.fh.cloud.screen.service.calendar.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekSaveUpdateConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarWeek;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.calendar.mapper.SchoolCalendarWeekMapper;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarDayService;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarService;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarWeekService;
import com.fh.cloud.screen.service.enums.SchoolCalendarRedisKeyEnum;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.enums.StatusEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 校历上课日星期表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
@Service
public class SchoolCalendarWeekServiceImpl extends ServiceImpl<SchoolCalendarWeekMapper, SchoolCalendarWeek>
    implements ISchoolCalendarWeekService {

    @Resource
    private SchoolCalendarWeekMapper schoolCalendarWeekMapper;

    @Autowired
    private ISchoolCalendarService schoolCalendarService;

    @Resource
    private ISchoolCalendarDayService schoolCalendarDayService;

    @Resource
    private RedisComponent redisComponent;

    @Override
    public List<SchoolCalendarWeekVo>
        getSchoolCalendarWeekListByCondition(SchoolCalendarWeekListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        SchoolCalendarVo detail = schoolCalendarService.getDetail(condition.getOrganizationId());
        condition.setSchoolCalendarId(detail.getSchoolCalendarId());
        return schoolCalendarWeekMapper.getSchoolCalendarWeekListByCondition(condition);
    }

    @Override
    public boolean addSchoolCalendarWeek(SchoolCalendarWeekBo schoolCalendarWeekBo) {
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSchoolCalendarWeeks(SchoolCalendarWeekSaveUpdateConditionBo conditionBo) {
        SchoolCalendarVo detail = schoolCalendarService.getDetail(conditionBo.getOrganizationId());
        conditionBo.setSchoolCalendarId(detail.getSchoolCalendarId());

        if (null == detail.getSchoolCalendarId()) {
            SchoolCalendarBo schoolCalendarBo = new SchoolCalendarBo();
            schoolCalendarBo.setOrganizationId(conditionBo.getOrganizationId());
            schoolCalendarService.addSchoolCalendar(schoolCalendarBo);
            detail = schoolCalendarService.getDetail(conditionBo.getOrganizationId());
            conditionBo.setSchoolCalendarId(detail.getSchoolCalendarId());
        }

        List<SchoolCalendarWeek> schoolCalendarWeeks = new ArrayList<>();
        for (SchoolCalendarWeekBo schoolCalendarWeekBo : conditionBo.getSchoolCalendarWeekBos()) {
            SchoolCalendarWeek schoolCalendarWeek = new SchoolCalendarWeek();
            BeanUtils.copyProperties(schoolCalendarWeekBo, schoolCalendarWeek);
            if (null == schoolCalendarWeek.getSchoolCalendarId()) {
                schoolCalendarWeek.setSchoolCalendarId(conditionBo.getSchoolCalendarId());
            }
            if (null == schoolCalendarWeek.getSchoolCalendarWeekId()) {
                schoolCalendarWeek.setIsDelete(StatusEnum.NOTDELETE.getCode());
            }
            schoolCalendarWeeks.add(schoolCalendarWeek);
        }
        // 日与周同步上课类型
        updateCalendarDays(schoolCalendarWeeks);
        // 设置缓冲失效
        redisComponent.del(SchoolCalendarRedisKeyEnum.SCHOOL_WEEK_REDIS_KEY.getValue()
            .concat(conditionBo.getOrganizationId().toString()));
        redisComponent.del(SchoolCalendarRedisKeyEnum.SCHOOL_DAY_REDIS_KEY.getValue()
            .concat(conditionBo.getOrganizationId().toString()));
        return saveOrUpdateBatch(schoolCalendarWeeks);
    }

    @Transactional
    public void updateCalendarDays(List<SchoolCalendarWeek> schoolCalendarWeeks) {
        if (CollectionUtils.isEmpty(schoolCalendarWeeks)) {
            return;
        }
        List<SchoolCalendarDay> schoolCalendarDayList = new ArrayList<>();
        List<SchoolCalendarDay> list = schoolCalendarDayService.list(new LambdaQueryWrapper<SchoolCalendarDay>()
            .eq(SchoolCalendarDay::getSchoolCalendarId, schoolCalendarWeeks.get(0).getSchoolCalendarId()));
        for (SchoolCalendarWeek schoolCalendarWeek : schoolCalendarWeeks) {
            for (SchoolCalendarDay schoolCalendarDay : list) {
                if (DateKit.getWeekIdByDate(schoolCalendarDay.getDay()) == schoolCalendarWeek.getWeek()) {
                    schoolCalendarDayList.add(schoolCalendarDay);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(schoolCalendarDayList)) {
            schoolCalendarDayService.removeByIds(schoolCalendarDayList.stream()
                .map(SchoolCalendarDay::getSchoolCalendarDayId).collect(Collectors.toList()));
        }
    }

    @Override
    public Map<String, Object> getDetail(Long schoolCalendarWeekId) {
        return null;
    }

    @Override
    public List<SchoolCalendarWeek> getBySchoolCalendarId(Long schoolCalendarId) {
        QueryWrapper<SchoolCalendarWeek> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SchoolCalendarWeek::getSchoolCalendarId, schoolCalendarId)
            .eq(SchoolCalendarWeek::getIsDelete, StatusEnum.NOTDELETE.getCode());
        return this.schoolCalendarWeekMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Object> getCacheWeekListAndDayListByOrganizationId(Long organizationId) {
        String strOrganizationId = organizationId.toString();
        Map<String, Object> map = new HashMap<>();
        Object weekObject =
            redisComponent.get(SchoolCalendarRedisKeyEnum.SCHOOL_WEEK_REDIS_KEY.getValue().concat(strOrganizationId));
        Object dayObject =
            redisComponent.get(SchoolCalendarRedisKeyEnum.SCHOOL_DAY_REDIS_KEY.getValue().concat(strOrganizationId));

        // 按天
        if (null != dayObject) {
            String json = JSONObject.toJSONString(dayObject);
            List<SchoolCalendarDay> dayVos = JSONObject.parseArray(json, SchoolCalendarDay.class);
            map.put("dayVos", dayVos);
        } else {
            SchoolCalendarVo detail = schoolCalendarService.getDetail(organizationId);
            if (null == detail) {
                return map;
            }
            List<SchoolCalendarDay> dayVos =
                schoolCalendarDayService.getBySchoolCalendarId(detail.getSchoolCalendarId());
            if (CollectionUtils.isNotEmpty(dayVos)) {
                map.put("dayVos", dayVos);
                redisComponent.set(SchoolCalendarRedisKeyEnum.SCHOOL_DAY_REDIS_KEY.getValue().concat(strOrganizationId),
                    dayVos);
            }
        }

        // 按星期
        if (null != weekObject) {
            String json = JSONObject.toJSONString(weekObject);
            List<SchoolCalendarWeekVo> weekVoList = JSONObject.parseArray(json, SchoolCalendarWeekVo.class);
            map.put("weekVos", weekVoList);
        } else {
            SchoolCalendarVo detail = schoolCalendarService.getDetail(organizationId);
            if (null == detail) {
                return map;
            }
            SchoolCalendarWeekListConditionBo condition = new SchoolCalendarWeekListConditionBo();
            condition.setSchoolCalendarId(detail.getSchoolCalendarId());
            List<SchoolCalendarWeekVo> weekVos =
                schoolCalendarWeekMapper.getSchoolCalendarWeekListByCondition(condition);
            if (CollectionUtils.isNotEmpty(weekVos)) {
                map.put("weekVos", weekVos);
                redisComponent.set(
                    SchoolCalendarRedisKeyEnum.SCHOOL_WEEK_REDIS_KEY.getValue().concat(strOrganizationId), weekVos);
            }
        }
        return map;
    }

}