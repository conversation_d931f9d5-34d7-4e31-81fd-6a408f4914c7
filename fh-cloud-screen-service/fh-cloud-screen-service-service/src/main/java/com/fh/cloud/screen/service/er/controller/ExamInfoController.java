package com.fh.cloud.screen.service.er.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.excel.imports.ExcelImportService;
import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.ExamEnums;
import com.fh.cloud.screen.service.er.api.ExamInfoApi;
import com.fh.cloud.screen.service.er.entity.bo.*;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoDto;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStatisticsVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.service.IExamInfoService;
import com.fh.cloud.screen.service.er.service.IExamInfoStudentService;
import com.fh.cloud.screen.service.er.service.IExamInfoSubjectService;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.ListKit;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.vo.TeacherVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考场_考试计划里面一次考试信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@RestController
@Validated
public class ExamInfoController implements ExamInfoApi {

    @Autowired
    private IExamInfoService examInfoService;
    @Autowired
    private IExamInfoSubjectService examInfoSubjectService;

    @Resource
    private ISpaceInfoService spaceInfoService;
    @Resource
    private IClassesInfoService classesInfoService;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private ISpaceGroupService spaceGroupService;
    @Resource
    private IExamInfoStudentService examInfoStudentService;

    /**
     * 查询考场_考试计划里面一次考试信息分页列表
     *
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<PageInfo<ExamInfoVo>> getExamInfoPageListByCondition(@RequestBody ExamInfoConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<ExamInfoVo> list = examInfoService.getExamInfoListByCondition(condition);
        PageInfo<ExamInfoVo> pageInfo = new PageInfo<>(list);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询考场_考试计划里面一次考试信息列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<List<ExamInfoVo>> getExamInfoListByCondition(@RequestBody ExamInfoConditionBo condition) {
        List<ExamInfoVo> list = examInfoService.getExamInfoListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增考场_考试计划里面一次考试信息
     *
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult addExamInfo(@RequestBody ExamInfoBo examInfoBo) {
        return examInfoService.addExamInfoWithDetail(examInfoBo);
    }

    /**
     * 修改考场_考试计划里面一次考试信息
     * 
     * @param examInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult updateExamInfo(@RequestBody ExamInfoBo examInfoBo) {
        if (null == examInfoBo.getExamInfoId()) {
            return AjaxResult.fail("考场_考试计划里面一次考试信息id不能为空");
        }
        return examInfoService.updateExamInfoWithDetail(examInfoBo);
    }

    /**
     * 查询考场_考试计划里面一次考试信息详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<ExamInfoVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("考场_考试计划里面一次考试信息id不能为空");
        }
        ExamInfoVo vo = examInfoService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除考场_考试计划里面一次考试信息
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ExamInfoDto examInfoDto = new ExamInfoDto();
        examInfoDto.setExamInfoId(id);
        examInfoDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (examInfoService.updateById(examInfoDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 考试导入
     *
     * @param file, examPlanId, organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/17 9:58
     */
    @Override
    public AjaxResult importExamInfo(ImportExamInfoModel importExamInfoModel) {
        // 1、拿到导入的数据
        Long organizationId = importExamInfoModel.getOrganizationId();
        MultipartFile file = importExamInfoModel.getFile();
        Long examPlanId = importExamInfoModel.getExamPlanId();
        if (null == examPlanId || null == organizationId) {
            return AjaxResult.fail("参数错误");
        }
        ImportParams params = new ImportParams();
        // 表头设置为2行
        params.setHeadRows(2);
        params.setTitleRows(2);
        // params.setNeedVerify(true);
        ExcelImportResult<ExamInfoImportModel> result;
        try {
            // result = ExcelImportUtil.importExcelMore(file.getInputStream(), ExamInfoImportModel.class, params);
            result = (ExcelImportResult<ExamInfoImportModel>)new ExcelImportService()
                .importExcelByIs(file.getInputStream(), ExamInfoImportModel.class, params, false);
        } catch (Exception e) {
            return AjaxResult.fail("文件格式错误");
        }
        List<ExamInfoImportModel> list = result.getList();
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.fail("空数据");
        }

        // 2、对导入的数据进行判断等处理
        // 校内教师名称集合
        List<String> schoolTeacherNames = new ArrayList<>();
        // 校内学生名称集合
        List<String> schoolStudentNameExts = new ArrayList<>();
        List<String> examRoomNames = new ArrayList<>();
        // 第一次遍历导入的数据
        for (ExamInfoImportModel examInfoImportModel : list) {
            // 校验为空判断
            if (StringUtils.isBlank(examInfoImportModel.getExamRoomName())) {
                return AjaxResult.fail("考场号不能为空");
            }
            examRoomNames.add(examInfoImportModel.getExamRoomName());
            if (StringUtils.isBlank(examInfoImportModel.getSpaceInfoName())) {
                return AjaxResult.fail("考试地点不能为空");
            }
            if (CollectionUtils.isEmpty(examInfoImportModel.getExamInfoImportChildrenModels())) {
                return AjaxResult.fail("科目信息不能为空");
            }
            for (ExamInfoImportChildrenModel examInfoImportChildrenModel : examInfoImportModel
                .getExamInfoImportChildrenModels()) {
                if (StringUtils.isBlank(examInfoImportChildrenModel.getSubjectName())) {
                    return AjaxResult.fail("考试科目不能为空");
                }
                if (StringUtils.isBlank(examInfoImportChildrenModel.getExamTime())) {
                    return AjaxResult.fail("考试时间不能为空");
                }
                String schoolStudentNameConcat = examInfoImportChildrenModel.getSchoolStudentName();
                if (StringUtils.isBlank(schoolStudentNameConcat)
                    && StringUtils.isBlank(examInfoImportChildrenModel.getOutStudentName())) {
                    return AjaxResult.fail("考生不能都为空");
                }
                if (StringUtils.isNotBlank(schoolStudentNameConcat)) {
                    schoolStudentNameExts.addAll(Arrays.asList(schoolStudentNameConcat.split(",")));
                }
                if (StringUtils.isBlank(examInfoImportChildrenModel.getSchoolTeacherName())
                    && StringUtils.isBlank(examInfoImportChildrenModel.getOutTeacherName())) {
                    return AjaxResult.fail("监考老师不能为空");
                }
                if (StringUtils.isNotBlank(examInfoImportChildrenModel.getSchoolTeacherName())) {
                    schoolTeacherNames
                        .addAll(Arrays.asList(examInfoImportChildrenModel.getSchoolTeacherName().split(",")));
                }
            }
            if (examRoomNames.size() != examRoomNames.stream().distinct().count()) {
                return AjaxResult.fail("有重复考场号,导入失败");
            }
        }
        Map<String, List<StudentVo>> studentMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(schoolStudentNameExts)) {
            // 只有姓名的集合
            List<String> schoolStudentNames = Lists.newArrayList();
            for (String schoolStudentNameExt : schoolStudentNameExts) {
                if (StringUtils.isBlank(schoolStudentNameExt)) {
                    continue;
                }
                if (schoolStudentNameExt.contains(ConstString.ywzhx)) {
                    schoolStudentNames
                        .add(schoolStudentNameExt.substring(0, schoolStudentNameExt.indexOf(ConstString.ywzhx)));
                } else {
                    schoolStudentNames.add(schoolStudentNameExt);
                }
            }
            List<StudentVo> studentVos =
                baseDataService.getOrgStudentListByRealNames(organizationId, schoolStudentNames);
            if (CollectionUtils.isNotEmpty(studentVos)) {
                studentMap = studentVos.stream().collect(Collectors.groupingBy(StudentVo::getUserRealName));
            }
        }
        Map<String, List<TeacherVo>> teacherMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(schoolTeacherNames)) {
            List<TeacherVo> teacherVos =
                baseDataService.getOrgTeacherListByRealNames(organizationId, schoolTeacherNames);
            if (CollectionUtils.isNotEmpty(teacherVos)) {
                teacherMap = teacherVos.stream().collect(Collectors.groupingBy(TeacherVo::getUserRealName));
            }
        }

        // 2.1、第二次遍历导入的数据，对导入的数据进行二次处理
        List<ExamInfoBo> examInfoBoList = new ArrayList<>();
        for (ExamInfoImportModel examInfoImportModel : list) {
            ExamInfoBo examInfoBo = new ExamInfoBo();
            examInfoBo.setExamPlanId(examPlanId);
            examInfoBo.setExamRoomName(examInfoImportModel.getExamRoomName());
            examInfoBo.setSpaceInfoName(examInfoImportModel.getSpaceInfoName());
            // 考试科目相关
            List<ExamInfoSubjectBo> examInfoSubjectBos = new ArrayList<>();
            for (ExamInfoImportChildrenModel examInfoImportChildrenModel : examInfoImportModel
                .getExamInfoImportChildrenModels()) {
                // 设置考试科目相关
                ExamInfoSubjectBo examInfoSubjectBo = new ExamInfoSubjectBo();
                examInfoSubjectBo.setSubjectName(examInfoImportChildrenModel.getSubjectName());
                if (StringUtils.isNotBlank(examInfoImportChildrenModel.getAtNo())) {
                    String[] split = examInfoImportChildrenModel.getAtNo().split("至");
                    examInfoSubjectBo.setAtNoStart(split[0]);
                    examInfoSubjectBo.setAtNoEnd(split[1]);
                }
                String[] split = examInfoImportChildrenModel.getExamTime().split(" ");
                String[] splitTime = split[1].split("-");
                Date startDate = DateKit.getDateAndTimeCompose(split[0], splitTime[0]);
                Date endDate = DateKit.getDateAndTimeCompose(split[0], splitTime[1]);
                if (null == startDate || null == endDate) {
                    return AjaxResult.fail("时间转换错误，请按照模板检查并修改时间");
                }
                examInfoSubjectBo.setExamStartTime(startDate);
                examInfoSubjectBo.setExamEndTime(endDate);
                // 设置学生
                List<ExamInfoStudentBo> studentBos = new ArrayList<>();
                String schoolStudentNameConcat = examInfoImportChildrenModel.getSchoolStudentName();
                if (StringUtils.isNotBlank(schoolStudentNameConcat)) {
                    List<String> schoolStudentNameExtTemps = ListKit.array2list(schoolStudentNameConcat.split(","));
                    for (String schoolStudentNameExtTemp : schoolStudentNameExtTemps) {
                        String schoolStudentName = "";
                        String registrationNumber = "";
                        String seatNumber = "";
                        if (schoolStudentNameExtTemp.contains(ConstString.ywzhx)) {
                            String[] splitTemp = registrationNumber.split(ConstString.ywzhx);
                            schoolStudentName = splitTemp[0];
                            if (splitTemp.length > 1) {
                                registrationNumber = splitTemp[1];
                            }
                            if (splitTemp.length > 2) {
                                seatNumber = splitTemp[2];
                            }
                        } else {
                            schoolStudentName = schoolStudentNameExtTemp;
                        }
                        List<StudentVo> studentVos = studentMap.get(schoolStudentName);
                        if (CollectionUtils.isEmpty(studentVos)) {
                            return AjaxResult.fail("校内不存在学生：" + schoolStudentName);
                        }
                        // if (studentVos.size() > 1) {
                        // return AjaxResult.fail("学生：" + string + "在校内重名");
                        // }
                        ExamInfoStudentBo studentBo = new ExamInfoStudentBo();
                        studentBo.setRealName(schoolStudentName);
                        studentBo.setUserFromType(ExamEnums.USER_FORM_SCHOOL.getValue());
                        studentBo.setUserOid(studentVos.get(0).getUserOid());
                        studentBo.setRegistrationNumber(registrationNumber);
                        studentBo.setSeatNumber(seatNumber);
                        studentBos.add(studentBo);
                    }
                }
                String outStudentNameConcat = examInfoImportChildrenModel.getOutStudentName();
                if (StringUtils.isNotBlank(outStudentNameConcat)) {
                    List<String> outStudentNameExtTemps = ListKit.array2list(outStudentNameConcat.split(","));
                    for (String outStudentNameExtTemp : outStudentNameExtTemps) {
                        String outStudentName = "";
                        String registrationNumber = "";
                        String seatNumber = "";
                        if (outStudentNameExtTemp.contains(ConstString.ywzhx)) {
                            String[] splitTemp = registrationNumber.split(ConstString.ywzhx);
                            outStudentName = splitTemp[0];
                            if (splitTemp.length > 1) {
                                registrationNumber = splitTemp[1];
                            }
                            if (splitTemp.length > 2) {
                                seatNumber = splitTemp[2];
                            }
                        } else {
                            outStudentName = outStudentNameExtTemp;
                        }
                        ExamInfoStudentBo studentBo = new ExamInfoStudentBo();
                        studentBo.setRealName(outStudentName);
                        studentBo.setUserFromType(ExamEnums.USER_FORM_OUT.getValue());
                        studentBo.setRegistrationNumber(registrationNumber);
                        studentBo.setSeatNumber(seatNumber);
                        studentBos.add(studentBo);
                    }
                }
                examInfoSubjectBo.setExamInfoStudentBos(studentBos);
                // 设置监考老师
                List<ExamInfoTeacherBo> teacherBos = new ArrayList<>();
                if (StringUtils.isNotBlank(examInfoImportChildrenModel.getSchoolTeacherName())) {
                    String[] strings = examInfoImportChildrenModel.getSchoolTeacherName().split(",");
                    for (String string : strings) {
                        List<TeacherVo> teacherVos = teacherMap.get(string);
                        if (CollectionUtils.isEmpty(teacherVos)) {
                            return AjaxResult.fail("校内不存在教师：" + string);
                        }
                        // if (teacherVos.size() > 1) {
                        // return AjaxResult.fail("教师：" + string + "在校内重名");
                        // }
                        ExamInfoTeacherBo teacherBo = new ExamInfoTeacherBo();
                        teacherBo.setRealName(string);
                        teacherBo.setUserFromType(ExamEnums.USER_FORM_SCHOOL.getValue());
                        teacherBo.setUserOid(teacherVos.get(0).getUserOid());
                        teacherBos.add(teacherBo);
                    }
                }
                if (StringUtils.isNotBlank(examInfoImportChildrenModel.getOutTeacherName())) {
                    String[] strings = examInfoImportChildrenModel.getOutTeacherName().split(",");
                    for (String string : strings) {
                        ExamInfoTeacherBo teacherBo = new ExamInfoTeacherBo();
                        teacherBo.setRealName(string);
                        teacherBo.setUserFromType(ExamEnums.USER_FORM_OUT.getValue());
                        teacherBos.add(teacherBo);
                    }
                }
                // 设置唯一oid用于后续判断时间冲突，排除自身
                examInfoSubjectBo.setUuid(UUID.randomUUID().toString());
                examInfoSubjectBo.setExamInfoTeacherBos(teacherBos);
                examInfoSubjectBos.add(examInfoSubjectBo);
            }
            examInfoBo.setExamInfoSubjectBos(examInfoSubjectBos);
            examInfoBoList.add(examInfoBo);
        }

        // 2.2、匹配地点
        AjaxResult ajaxResult = matchingSpace(examInfoBoList, organizationId);
        List<ExamInfoSubjectVo> examInfoSubjectVos =
            examInfoSubjectService.getExamInfoSubjectListByOrganizationId(organizationId);
        // 校验时间重复 循环考场号--即循环考试地点
        for (ExamInfoBo examInfoBo : examInfoBoList) {
            if (null == examInfoBo.getSpaceInfoId()) {
                if (ajaxResult.isFail()) {
                    return AjaxResult.fail("导入失败,请检查" + examInfoBo.getSpaceInfoName() + "班级是否为当前学年班级");
                }
                return AjaxResult.fail("地点" + examInfoBo.getSpaceInfoName() + "没有匹配到，导入失败。");
            }
            // 循环本地点，新增的所有科目
            for (ExamInfoSubjectBo examSubjectBo : examInfoBo.getExamInfoSubjectBos()) {
                // 判断同一地点下 每个新增的科目时间，与同一地点下新增的所有科目时间比对 重复 --新增bean与新增bean
                for (ExamInfoSubjectBo infoSubjectBean : examInfoBo.getExamInfoSubjectBos()) {
                    if (!infoSubjectBean.getUuid().equals(examSubjectBo.getUuid())
                        && DateKit.isIntersection(infoSubjectBean.getExamStartTime(), infoSubjectBean.getExamEndTime(),
                            examSubjectBo.getExamStartTime(), examSubjectBo.getExamEndTime())) {
                        return AjaxResult.fail("时间冲突");
                    }
                }
                if (CollectionUtils.isEmpty(examInfoSubjectVos)) {
                    continue;
                }
                // 判断地点下 的每个科目的时间，是否与db中组织同一地点下的所有科目时间重复 重复--新增bean与db
                for (ExamInfoSubjectVo examSubjectVo : examInfoSubjectVos) {
                    if (examInfoBo.getSpaceGroupUseType().equals(examSubjectVo.getSpaceGroupUseType())
                        && examInfoBo.getSpaceInfoId().equals(examSubjectVo.getSpaceInfoId())
                        && DateKit.isIntersection(examSubjectVo.getExamStartTime(), examSubjectVo.getExamEndTime(),
                            examSubjectBo.getExamStartTime(), examSubjectBo.getExamEndTime())) {
                        return AjaxResult.fail("时间冲突");
                    }
                }
            }
            // 判断已有考试计划下的的地点与考场号，相同则并入
            if (CollectionUtils.isNotEmpty(examInfoSubjectVos)) {
                for (ExamInfoSubjectVo examSubjectVo : examInfoSubjectVos) {
                    if (examInfoBo.getExamPlanId().equals(examSubjectVo.getExamPlanId())
                        && examInfoBo.getSpaceGroupUseType().equals(examSubjectVo.getSpaceGroupUseType())
                        && examInfoBo.getSpaceInfoId().equals(examSubjectVo.getSpaceInfoId())
                        && examInfoBo.getExamRoomName().equals(examSubjectVo.getExamRoomName())) {
                        examInfoBo.setExamInfoId(examSubjectVo.getExamInfoId());
                    }
                }
            }
        }
        // 3、导入数据入库
        return examInfoService.addExamInfoBatchWithDetail(examInfoBoList);
    }

    /**
     * 根据地点名称，获取组织内的地点spaceGroupUseType、spaceInfoId
     *
     * @param examInfoBoList, organizationId
     * @return ajaxResult success 流程走完，fail 匹配到但是是下一学年
     * <AUTHOR>
     * @date 2022/10/13 9:52
     */
    private AjaxResult matchingSpace(List<ExamInfoBo> examInfoBoList, Long organizationId) {
        AjaxResult ajaxResult = AjaxResult.success();
        // 获取分组列表
        List<SpaceGroupVo> allGroup = spaceGroupService.findAll();
        Map<Long, List<SpaceGroupVo>> allGroupMap =
            allGroup.stream().collect(Collectors.groupingBy(SpaceGroupVo::getSpaceGroupId));
        // 获取组织内普通教室
        ClassesInfoListConditionBo conditionBo = new ClassesInfoListConditionBo();
        conditionBo.setQueryType(ConstantsInteger.APP_QUERY);
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setOrganizationId(organizationId);
        Map<String, Object> map = classesInfoService.getClassesInfoListByCondition(conditionBo);
        List<SpaceInfoVo> spaceInfoVoList = (List<SpaceInfoVo>)map.get("list");
        for (ExamInfoBo examInfoBo : examInfoBoList) {
            for (SpaceInfoVo spaceInfoVo : spaceInfoVoList) {
                if (spaceInfoVo.getSpaceInfoName().equals(examInfoBo.getSpaceInfoName())) {
                    // 排除下一学年班级 入学>今年 或者 入学=今年且当前日期小于8.20
                    boolean isNextClass = spaceInfoVo.getEnrollmentYear() > DateUtil.year(new Date())
                        || (DateUtil.year(new Date()) == spaceInfoVo.getEnrollmentYear()
                            && SchoolYearUtil.getDateBeforeSchoolYear());
                    if (isNextClass) {
                        ajaxResult = AjaxResult.fail();
                        continue;
                    }
                    examInfoBo.setSpaceGroupUseType(spaceInfoVo.getSpaceGroupUseType());
                    examInfoBo.setSpaceInfoId(spaceInfoVo.getSpaceInfoId());
                    examInfoBo.setSpaceGroupId(spaceInfoVo.getSpaceGroupId());
                    if (allGroupMap.get(spaceInfoVo.getSpaceGroupId()).size() != 0) {
                        examInfoBo.setSpaceGroupName(
                            allGroupMap.get(spaceInfoVo.getSpaceGroupId()).get(0).getSpaceGroupName());
                    }
                    break;
                }
            }
        }
        // 获取组织内专用教室
        SpaceInfoListConditionBo condition = new SpaceInfoListConditionBo();
        condition.setOrganizationId(organizationId);
        List<SpaceInfoVo> spaceInfoVos = spaceInfoService.getSpaceInfoListByCondition(condition);
        for (ExamInfoBo examInfoBo : examInfoBoList) {
            for (SpaceInfoVo spaceInfoVo : spaceInfoVos) {
                if (spaceInfoVo.getSpaceInfoName().equals(examInfoBo.getSpaceInfoName())) {
                    examInfoBo.setSpaceGroupUseType(spaceInfoVo.getSpaceGroupUseType());
                    examInfoBo.setSpaceInfoId(spaceInfoVo.getSpaceInfoId());
                    examInfoBo.setSpaceGroupId(spaceInfoVo.getSpaceGroupId());
                    if (allGroupMap.get(spaceInfoVo.getSpaceGroupId()).size() != 0) {
                        examInfoBo.setSpaceGroupName(
                            allGroupMap.get(spaceInfoVo.getSpaceGroupId()).get(0).getSpaceGroupName());
                    }
                    break;
                }
            }
        }
        return ajaxResult;
    }

    @Override
    public AjaxResult<List<ExamInfoSubjectVo>> listSubjectByExamInfoId(Long examInfoId) {
        ExamInfoSubjectConditionBo condition = new ExamInfoSubjectConditionBo();
        condition.setExamInfoId(examInfoId);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoSubjectVo> list = examInfoSubjectService.getExamInfoSubjectListByCondition(condition);
        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult<List<ExamInfoStudentVo>> attendanceMember(ExamInfoConditionBo examInfoConditionBo) {
        List<Integer> attendanceStatusList = Lists.newArrayList();
        if (examInfoConditionBo.getAttendanceSignStatus() != null) {
            if (examInfoConditionBo.getAttendanceSignStatus().equals(ExamEnums.SIGN_ATTENDANCE_NOT.getValue())) {
                attendanceStatusList.add(ExamEnums.SIGN_NOT.getValue());
            }
            if (examInfoConditionBo.getAttendanceSignStatus().equals(ExamEnums.SIGN_ATTENDANCE_YES.getValue())) {
                attendanceStatusList.add(ExamEnums.SIGN_NORMAL.getValue());
                attendanceStatusList.add(ExamEnums.SIGN_NORMAL_LATER.getValue());
            }
        }

        ExamInfoStudentConditionBo condition = new ExamInfoStudentConditionBo();
        condition.setExamInfoSubjectId(examInfoConditionBo.getExamInfoSubjectId());
        condition.setAttendanceStatusList(attendanceStatusList);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoStudentVo> examInfoStudentVos =
            examInfoStudentService.getExamInfoStudentListByCondition(condition);
        return AjaxResult.success(examInfoStudentVos);
    }

    @Override
    public AjaxResult<ExamInfoStatisticsVo> attendanceStatistics(ExamInfoConditionBo examInfoConditionBo) {
        ExamInfoStatisticsVo examInfoStatisticsVo = new ExamInfoStatisticsVo();
        ExamInfoStudentConditionBo condition = new ExamInfoStudentConditionBo();
        condition.setExamInfoSubjectId(examInfoConditionBo.getExamInfoSubjectId());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoStudentVo> examInfoStudentVos =
            examInfoStudentService.getExamInfoStudentListByCondition(condition);
        if (CollectionUtils.isEmpty(examInfoStudentVos)) {
            return AjaxResult.success(examInfoStatisticsVo);
        }

        Long totalNumber = (long)examInfoStudentVos.size();
        Long unSignNumber =
            examInfoStudentVos.stream().filter(examInfoStudentVo -> examInfoStudentVo.getAttendanceStatus() == null
                || examInfoStudentVo.getAttendanceStatus().equals(ExamEnums.SIGN_NOT.getValue())).count();
        Long signNumber = totalNumber - unSignNumber;
        examInfoStatisticsVo.setTotalNumber(totalNumber);
        examInfoStatisticsVo.setUnSignNumber(unSignNumber);
        examInfoStatisticsVo.setSignNumber(signNumber);
        return AjaxResult.success(examInfoStatisticsVo);
    }
}
