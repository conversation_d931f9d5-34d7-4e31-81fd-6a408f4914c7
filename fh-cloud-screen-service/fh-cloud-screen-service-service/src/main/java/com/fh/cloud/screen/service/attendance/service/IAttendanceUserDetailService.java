package com.fh.cloud.screen.service.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserDetailBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserDetailListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;

import java.util.List;
import java.util.Map;

/**
 * 考勤用户详情表，需要日终计算接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface IAttendanceUserDetailService extends IService<AttendanceUserDetail> {

    List<AttendanceUserDetailVo> getAttendanceUserDetailListByCondition(AttendanceUserDetailListConditionBo condition);

    boolean addAttendanceUserDetail(AttendanceUserDetailBo attendanceUserDetailBo);

    boolean updateAttendanceUserDetail(AttendanceUserDetailBo attendanceUserDetailBo);

    Map<String, Object> getDetail(Long attendanceUserDetailId);

    /**
     * 根据 考勤记录获取 考勤详情
     * 
     * @param attendanceUserId
     * @return
     */
    List<AttendanceUserDetailVo> getListByAttendanceUserId(Long attendanceUserId);
}
