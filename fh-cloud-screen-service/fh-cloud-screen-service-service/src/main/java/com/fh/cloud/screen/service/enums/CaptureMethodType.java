package com.fh.cloud.screen.service.enums;

/**
 * 云屏截图upload方式
 *
 * <AUTHOR>
 */
public enum CaptureMethodType {
    /**
     * 更新：表示更新【用户发起的截图请求】数据
     */
    UPDATE("update"),
    /***
     * 新增：表示云屏端主动提交一个截图数据
     */
    CREATE("create"),

    ;

    private String value;

    CaptureMethodType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}