package com.fh.cloud.screen.service.task.handler;

import com.fh.cloud.screen.service.meeting.service.IMeetingLongService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 长期预约会议任务
 * 
 * <AUTHOR>
 * @date 2023/12/12 15:32
 */
@Component
@Slf4j
public class MeetingLongTask {

    @Resource
    private IMeetingLongService meetingLongService;

    /**
     * 长期预约会议生成当天的会议
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2023 -12-12 15:34:58
     */
    @XxlJob("MeetingLongTask-generate")
    public void generateMeeting() throws Exception {
        log.info("MeetingLongTask-generate start");
        try {
            // 生成会议的日期，格式为：yyyy-MM-dd
            String date = "";
            String param = XxlJobHelper.getJobParam();
            if (StringUtils.isNotBlank(param)) {
                String[] methodParams = param.split(",");
                date = methodParams[0];
            }
            Date nowDay = new Date();
            if (StringUtils.isNotBlank(date)) {
                nowDay = DateKit.string2Date(date, "yyyy-MM-dd");
            }
            meetingLongService.generateMeetingFromMeetingLong(nowDay);
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            log.error("MeetingLongTask-generate error:", e);
            XxlJobHelper.handleFail("MeetingLongTask-generate error:" + e.getLocalizedMessage());
        }
        log.info("MeetingLongTask-generate end");
    }
}
