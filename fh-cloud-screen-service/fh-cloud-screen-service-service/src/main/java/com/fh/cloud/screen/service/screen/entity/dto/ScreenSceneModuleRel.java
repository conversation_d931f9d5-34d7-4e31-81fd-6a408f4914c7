package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏场景模块关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_scene_module_rel")
public class ScreenSceneModuleRel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * FK场景ID
     */
    @TableField("screen_scene_id")
    private Long screenSceneId;

    /**
     * FK模块ID
     */
    @TableField("screen_module_data_id")
    private Long screenModuleDataId;

    /**
     * FK模块库ID
     */
    @TableField("screen_module_library_id")
    private Long screenModuleLibraryId;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 海报多选id，使用逗号分割
     */
    @TableField("screen_module_library_sel_ids")
    private String screenModuleLibrarySelIds;
}
