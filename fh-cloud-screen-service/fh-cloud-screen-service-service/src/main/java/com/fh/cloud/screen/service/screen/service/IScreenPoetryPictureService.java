package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryPictureDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 共话诗词图片资源表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:25
 */
public interface IScreenPoetryPictureService extends IService<ScreenPoetryPictureDto> {

    List<ScreenPoetryPictureVo> getScreenPoetryPictureListByCondition(ScreenPoetryPictureConditionBo condition);

	AjaxResult addScreenPoetryPicture(ScreenPoetryPictureBo screenPoetryPictureBo);

	AjaxResult updateScreenPoetryPicture(ScreenPoetryPictureBo screenPoetryPictureBo);

	ScreenPoetryPictureVo getScreenPoetryPictureByCondition(ScreenPoetryPictureConditionBo condition);

}

