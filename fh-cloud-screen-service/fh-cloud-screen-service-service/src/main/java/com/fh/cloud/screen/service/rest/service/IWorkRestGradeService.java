package com.fh.cloud.screen.service.rest.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGrade;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeVo;

/**
 * 作息时间年级表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IWorkRestGradeService extends IService<WorkRestGrade> {

    List<WorkRestGradeVo> getWorkRestGradeListByCondition(WorkRestGradeListConditionBo condition);

    boolean addWorkRestGrade(WorkRestGradeBo workRestGradeBo);

    boolean updateWorkRestGrade(WorkRestGradeBo workRestGradeBo);

    WorkRestGradeVo getDetail(Long workRestGradeId);

    /**
     * 批量添加，同时将主键id添加到workRestGradeBoList并返回
     * @param workRestId
     * @param workRestGradeBoList
     * @return
     */
    List<WorkRestGradeBo> deleteAndSaveBatch(Long workRestId, List<WorkRestGradeBo> workRestGradeBoList);

    /**
     * 按照workRestId删除
     * @param workRestId
     */
    void deleteByWorkRestId(Long workRestId);
}
