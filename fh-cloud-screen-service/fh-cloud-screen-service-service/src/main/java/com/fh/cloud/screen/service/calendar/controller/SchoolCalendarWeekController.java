package com.fh.cloud.screen.service.calendar.controller;

import com.fh.cloud.screen.service.calendar.api.SchoolCalendarWeekApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekSaveUpdateConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarWeekService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 校历上课日星期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
@RestController
@RequestMapping("/calendar/week")
@Validated
@Api(tags = "校历上课日星期表")
public class SchoolCalendarWeekController implements SchoolCalendarWeekApi {

    @Autowired
    private ISchoolCalendarWeekService schoolCalendarWeekService;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 查询校历上课日星期表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:46
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询校历上课日星期表列表", httpMethod = "POST")
    public AjaxResult getSchoolCalendarWeekListByCondition(@RequestBody SchoolCalendarWeekListConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);
        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<SchoolCalendarWeekVo> pageInfo =
                new PageInfo<>(schoolCalendarWeekService.getSchoolCalendarWeekListByCondition(condition));
            map.put("count", pageInfo.getTotal());
            map.put("schoolCalendarWeekList", pageInfo.getList());
        } else {
            List<SchoolCalendarWeekVo> schoolCalendarWeekListByCondition =
                schoolCalendarWeekService.getSchoolCalendarWeekListByCondition(condition);
            map.put("schoolCalendarWeekList", schoolCalendarWeekListByCondition);
        }
        return AjaxResult.success(map);
    }

    /**
     * 新增校历上课日星期表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:46
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增校历上课日星期表", httpMethod = "POST")
    public AjaxResult addSchoolCalendarWeek(@Validated @RequestBody SchoolCalendarWeekBo schoolCalendarWeekBo) {
        boolean save = schoolCalendarWeekService.addSchoolCalendarWeek(schoolCalendarWeekBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 保存或修改校历上课日星期表
     *
     * @param conditionBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:46
     */
    @PostMapping("/save-or-update")
    @ApiOperation(value = "保存或修改校历上课日星期表", httpMethod = "POST")
    public AjaxResult
        saveOrUpdateSchoolCalendarWeek(@Validated @RequestBody SchoolCalendarWeekSaveUpdateConditionBo conditionBo) {
        boolean saveOrUpdate = schoolCalendarWeekService.saveOrUpdateSchoolCalendarWeeks(conditionBo);
        if (saveOrUpdate) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询校历上课日星期表详情
     *
     * @param schoolCalendarWeekId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:46
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询校历上课日星期表详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择数据") Long schoolCalendarWeekId) {
        Map<String, Object> map = schoolCalendarWeekService.getDetail(schoolCalendarWeekId);
        return AjaxResult.success(map);
    }

    // /**
    // * 删除校历上课日星期表
    // *
    // * @param schoolCalendarWeekId
    // * @return
    // * @returnType AjaxResult
    // * <AUTHOR>
    // * @date 2022-04-26 16:05:46
    // */
    // @GetMapping("/delete")
    // @ApiOperation(value = "删除校历上课日星期表", httpMethod = "GET")
    // public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long schoolCalendarWeekId) {
    // SchoolCalendarWeekBo schoolCalendarWeekBo = new SchoolCalendarWeekBo();
    // schoolCalendarWeekBo.setSchoolCalendarWeekId(schoolCalendarWeekId);
    // schoolCalendarWeekBo.setIsDelete(StatusEnum.ISDELETE.getCode());
    // boolean delete = schoolCalendarWeekService.updateSchoolCalendarWeek(schoolCalendarWeekBo);
    // if (delete) {
    // return AjaxResult.success("删除成功");
    // }
    // return AjaxResult.fail();
    // }
}
