package com.fh.cloud.screen.service.calendar.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendar;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarWeek;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayOfMonthVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.calendar.mapper.SchoolCalendarMapper;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarDayService;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarService;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarWeekService;
import com.fh.cloud.screen.service.enums.SchoolCalendarEnum;
import com.fh.cloud.screen.service.utils.DateKit;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 校历主表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@Service
public class SchoolCalendarServiceImpl extends ServiceImpl<SchoolCalendarMapper, SchoolCalendar>
    implements ISchoolCalendarService {

    @Resource
    private SchoolCalendarMapper schoolCalendarMapper;
    @Resource
    @Lazy
    private ISchoolCalendarWeekService iSchoolCalendarWeekService;
    @Resource
    @Lazy
    private ISchoolCalendarDayService iSchoolCalendarDayService;

    @Override
    public List<SchoolCalendarVo> getSchoolCalendarListByCondition(SchoolCalendarListConditionBo condition) {
        return schoolCalendarMapper.getSchoolCalendarListByCondition(condition);
    }

    @Override
    public boolean addSchoolCalendar(SchoolCalendarBo schoolCalendarBo) {
        SchoolCalendar schoolCalendar = new SchoolCalendar();
        BeanUtils.copyProperties(schoolCalendarBo, schoolCalendar);
        schoolCalendar.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(schoolCalendar);
    }

    @Override
    public boolean updateSchoolCalendar(SchoolCalendarBo schoolCalendarBo) {
        return false;
    }

    @Override
    public SchoolCalendarVo getDetail(Long organizationId) {
        LambdaQueryWrapper<SchoolCalendar> schoolCalendarLambdaQueryWrapper = new LambdaQueryWrapper<>();
        schoolCalendarLambdaQueryWrapper.eq(SchoolCalendar::getOrganizationId, organizationId);
        List<SchoolCalendar> schoolCalendars = schoolCalendarMapper.selectList(schoolCalendarLambdaQueryWrapper);
        SchoolCalendarVo schoolCalendarVo = new SchoolCalendarVo();
        if (CollectionUtils.isNotEmpty(schoolCalendars)) {
            BeanUtils.copyProperties(schoolCalendars.get(0), schoolCalendarVo);
            // 兼容处理重复数据的问题
            if (schoolCalendars.size() > 1) {
                for (int i = 1; i < schoolCalendars.size(); i++) {
                    SchoolCalendar entity = schoolCalendars.get(i);
                    entity.setIsDelete(StatusEnum.ISDELETE.getCode());
                    schoolCalendarMapper.updateById(entity);
                }
            }
        }
        return schoolCalendarVo;
    }

    @Override
    public List<SchoolCalendarDayOfMonthVo> getDayInfoByMonthAndOrgId(String attendanceMonth, Long organizationId) {

        // 初始化月份日期 默认 周六 周日不上课
        List<SchoolCalendarDayOfMonthVo> schoolCalendarDayOfMonthVos = this.initDayInfo(attendanceMonth);

        // 获取学校校历主表信息
        final SchoolCalendarVo detail = this.getDetail(organizationId);
        if (detail == null) {
            return schoolCalendarDayOfMonthVos;
        }

        // 策略由上至下执行 最高级策略最后执行
        // 处理校历 周表上课策略
        final Long schoolCalendarId = detail.getSchoolCalendarId();
        schoolCalendarDayOfMonthVos = this.buildWeekType(schoolCalendarDayOfMonthVos, schoolCalendarId);

        // 处理校历 日表上课策略
        schoolCalendarDayOfMonthVos = this.buildDayType(schoolCalendarDayOfMonthVos, schoolCalendarId, attendanceMonth);

        return schoolCalendarDayOfMonthVos;
    }

    /**
     * 处理校历周表上课策略
     *
     * @param schoolCalendarDayOfMonthVos the schoolCalendarDayOfMonthVos 已初始化的默认校历信息
     * @param schoolCalendarId the schoolCalendarId 校历主表ID
     * @return
     */
    private List<SchoolCalendarDayOfMonthVo> buildWeekType(List<SchoolCalendarDayOfMonthVo> schoolCalendarDayOfMonthVos,
        Long schoolCalendarId) {
        List<SchoolCalendarWeek> weeks = this.iSchoolCalendarWeekService.getBySchoolCalendarId(schoolCalendarId);
        if (CollectionUtil.isNotEmpty(weeks)) {
            // 分组 KEY： 周号（1... 7） value: SchoolCalendarWeek
            final Map<Integer, SchoolCalendarWeek> weekCalendarMap = weeks.stream()
                .collect(Collectors.toMap(SchoolCalendarWeek::getWeek, Function.identity(), (k1, k2) -> k2));
            schoolCalendarDayOfMonthVos = schoolCalendarDayOfMonthVos.stream().map(x -> {
                final SchoolCalendarWeek schoolCalendarWeek = weekCalendarMap.get(x.getWeek());
                if (schoolCalendarWeek != null) {
                    x.setStatus(schoolCalendarWeek.getType());
                }
                return x;
            }).collect(Collectors.toList());
        }
        return schoolCalendarDayOfMonthVos;
    }

    /**
     * 处理 校历天 上课策略
     *
     * @param schoolCalendarDayOfMonthVos the schoolCalendarDayOfMonthVos 已初始化的默认校历信息
     * @param schoolCalendarId the schoolCalendarId 校历主表ID
     * @param month the month 月份
     * @return
     */
    private List<SchoolCalendarDayOfMonthVo> buildDayType(List<SchoolCalendarDayOfMonthVo> schoolCalendarDayOfMonthVos,
        Long schoolCalendarId, String month) {
        List<SchoolCalendarDay> days =
            this.iSchoolCalendarDayService.getBySchoolCalendarIdAndMonth(schoolCalendarId, month);

        if (CollectionUtil.isNotEmpty(days)) {
            // 分组 KEY：日期 yyyy--MM-dd value: SchoolCalendarDay
            final Map<String, SchoolCalendarDay> dayCalendarMap = days.stream()
                .collect(Collectors.toMap(x -> DateUtil.formatDate(x.getDay()), Function.identity(), (k1, k2) -> k2));
            schoolCalendarDayOfMonthVos = schoolCalendarDayOfMonthVos.stream().map(x -> {
                final SchoolCalendarDay schoolCalendarDay = dayCalendarMap.get(x.getDayDate());
                if (schoolCalendarDay != null) {
                    x.setStatus(schoolCalendarDay.getType());
                }
                return x;
            }).collect(Collectors.toList());
        }
        return schoolCalendarDayOfMonthVos;
    }

    /**
     * 初始化 校历（月） 默认 周六、周日不上课 周一至周五 上课
     *
     * @param attendanceMonth the attendance month 月份
     * @return
     */
    private List<SchoolCalendarDayOfMonthVo> initDayInfo(String attendanceMonth) {

        List<SchoolCalendarDayOfMonthVo> list = Lists.newArrayList();

        // 获取最后一天
        final DateTime monthDate = DateUtil.parse(attendanceMonth, "yyyy-MM");
        final int endValue = DateUtil.getEndValue(monthDate.toCalendar(), DateField.DAY_OF_MONTH.getValue());
        for (int i = 1; i <= endValue; i++) {
            String day = i < 10 ? "0" + i : i + "";
            String date = attendanceMonth + "-" + day;

            final DateTime dateTime = DateUtil.parseDate(date);
            // 获取周 （ 如果 周日 week = 0）
            int week = (dateTime.dayOfWeekEnum().getValue() - 1);
            if (week == 0) {
                week = 7;
            }

            // 装载
            SchoolCalendarDayOfMonthVo vo = new SchoolCalendarDayOfMonthVo();
            vo.setDayDate(date);
            vo.setDay(i);
            vo.setStatus(week > 5 ? SchoolCalendarEnum.TYPE_IS.getValue() : SchoolCalendarEnum.TYPE_NOT.getValue());
            vo.setWeek(week);
            list.add(vo);
        }

        return list;

    }

    /**
     * 默认上课，check
     *
     * @param organizationId the organization id
     * @return boolean boolean
     * <AUTHOR>
     * @date 2023 -06-15 15:13:40
     */
    @Override
    public boolean checkNotWork(Long organizationId) {
        // 最终判定的结果
        boolean notWorkFlag = false;
        Map<String, Object> SchoolCalendarMap =
            iSchoolCalendarWeekService.getCacheWeekListAndDayListByOrganizationId(organizationId);
        // 星期的设置的数据
        List<SchoolCalendarWeekVo> weekVos = (List<SchoolCalendarWeekVo>)SchoolCalendarMap.get("weekVos");
        // 按天的设置的数据
        List<SchoolCalendarDay> dayVos = (List<SchoolCalendarDay>)SchoolCalendarMap.get("dayVos");
        if (CollectionUtils.isEmpty(weekVos)) {
            return false;
        }

        // 星期设置的逻辑判断
        Date nowDay = new Date();
        Integer week = DateKit.getWeekIdByDate(nowDay);
        Optional<SchoolCalendarWeekVo> anyWeek = weekVos.stream().filter(weekVo -> weekVo.getWeek().equals(week))
            .filter(weekVo -> weekVo.getType().equals(SchoolCalendarEnum.TYPE_NOT.getValue())).findAny();
        if (anyWeek != null) {
            notWorkFlag = anyWeek.isPresent();
        }
        // 没用按天设置的则直接以按星期设置的返回
        if (CollectionUtils.isEmpty(dayVos)) {
            return notWorkFlag;
        }

        // 按天设置的逻辑判断，覆盖按星期设置的逻辑
        Optional<SchoolCalendarDay> anyDay =
            dayVos.stream().filter(dayVo -> DateKit.checkOneDay(dayVo.getDay(), nowDay))
                .filter(dayVo -> dayVo.getType().equals(SchoolCalendarEnum.TYPE_NOT.getValue())).findAny();
        if (anyDay != null) {
            notWorkFlag = anyDay.isPresent();
        }
        return notWorkFlag;

    }
}