package com.fh.cloud.screen.service.facebody;

import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyun.facebody20191230.Client;
import com.aliyun.facebody20191230.models.AddFaceEntityResponse;
import com.aliyun.facebody20191230.models.AddFaceResponse;
import com.aliyun.facebody20191230.models.CreateFaceDbResponse;
import com.aliyun.facebody20191230.models.DeleteFaceDbResponse;
import com.aliyun.facebody20191230.models.DeleteFaceEntityResponse;
import com.aliyun.facebody20191230.models.ListFaceDbsResponse;
import com.aliyun.facebody20191230.models.ListFaceDbsResponseBody;
import com.aliyun.facebody20191230.models.SearchFaceResponse;
import com.aliyun.facebody20191230.models.SearchFaceResponseBody;
import com.aliyun.tea.TeaException;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.google.common.collect.Lists;

import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

/**
 * 阿里云人脸识别产品faceBody的service
 * 
 * <AUTHOR>
 * @date 2023/5/16 16:04
 */
@Slf4j
@Service
public class FaceBodyService {

    @Autowired(required = false)
    private Client client;
    @Autowired(required = false)
    private FaceBodyService faceBodyService;

    /**
     * 封装事务：添加人脸entity，添加人脸
     *
     * @param userOid the user oid
     * @param imageUrl the image url
     * @param realName the real name
     * @return boolean
     * <AUTHOR>
     * @date 2023 -05-17 16:37:45
     */
    public boolean addFaceTransaction(String userOid, String imageUrl, String realName) {
        try {
            boolean addEntityResult = faceBodyService.addFaceEntity(ConstantsConfig.faceDbName, userOid);
            if (!addEntityResult) {
                return false;
            }
            boolean addFaceResult = faceBodyService.addFace(ConstantsConfig.faceDbName, userOid, imageUrl, realName);
            return addFaceResult;
        } catch (Exception e) {
            log.error("addFaceTransaction error:", e);
            return false;
        }
    }

    /**
     * 封装事务：删除人脸
     *
     * @param userOid the user oid
     * @return boolean boolean
     * <AUTHOR>
     * @date 2023 -05-17 16:41:06
     */
    public boolean delFaceTransaction(String userOid) {
        try {
            boolean result = faceBodyService.deleteFaceEntity(ConstantsConfig.faceDbName, userOid);
            return result;
        } catch (Exception e) {
            log.error("delFaceTransaction error:", e);
            return false;
        }
    }

    /**
     * 封装事务：查询人脸
     *
     * @param imageUrl the image url
     * @param threshold the threshold
     * @return list list
     * <AUTHOR>
     * @date 2023 -05-17 16:41:03
     */
    public List<String> listFaceTransaction(String imageUrl, String threshold) {
        return faceBodyService.searchFace(ConstantsConfig.faceDbName, imageUrl, null, null, threshold);
    }

    // ----------------------------下面是调用案例云的方法----------------------------//

    /**
     * 创建人脸库
     *
     * @param name 阿里云上面的人脸库名称
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public boolean createFaceDb(String name) {
        com.aliyun.facebody20191230.models.CreateFaceDbRequest createFaceDbRequest =
            new com.aliyun.facebody20191230.models.CreateFaceDbRequest().setName(name);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            CreateFaceDbResponse createFaceDbResponse = client.createFaceDbWithOptions(createFaceDbRequest, runtime);
            if (createFaceDbResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                return true;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("createFaceDb Exception:", e);
        }
        return false;
    }

    /**
     * 查询出所有的人脸数据库的名称列表
     *
     * @return the list
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public List<String> listFaceDbs() {
        List<String> resultList = Lists.newArrayList();
        com.aliyun.facebody20191230.models.ListFaceDbsRequest listFaceDbsRequest =
            new com.aliyun.facebody20191230.models.ListFaceDbsRequest();
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            ListFaceDbsResponse listFaceDbsResponse = client.listFaceDbsWithOptions(listFaceDbsRequest, runtime);
            if (listFaceDbsResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                List<ListFaceDbsResponseBody.ListFaceDbsResponseBodyDataDbList> dbList =
                    listFaceDbsResponse.getBody().getData().getDbList();
                if (CollectionUtils.isEmpty(dbList)) {
                    return resultList;
                }
                resultList = dbList.stream().map(ListFaceDbsResponseBody.ListFaceDbsResponseBodyDataDbList::getName)
                    .collect(Collectors.toList());
                return resultList;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("listFaceDbs Exception:", e);
        }
        return resultList;
    }

    /**
     * 删除人脸库
     *
     * @param name 阿里云上面的人脸库名称
     * @return the boolean
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public boolean deleteFaceDb(String name) {
        com.aliyun.facebody20191230.models.DeleteFaceDbRequest deleteFaceDbRequest =
            new com.aliyun.facebody20191230.models.DeleteFaceDbRequest().setName(name);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            DeleteFaceDbResponse deleteFaceDbResponse = client.deleteFaceDbWithOptions(deleteFaceDbRequest, runtime);
            if (deleteFaceDbResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                return true;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("deleteFaceDb Exception:", e);
        }
        return false;
    }

    /**
     * 添加人脸样本：通常指添加用户唯一标识到数据库里面
     *
     * @param name 阿里云上面的人脸库名称
     * @param entityId 用户唯一标志
     * @return the boolean
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public boolean addFaceEntity(String name, String entityId) {
        com.aliyun.facebody20191230.models.AddFaceEntityRequest addFaceEntityRequest =
            new com.aliyun.facebody20191230.models.AddFaceEntityRequest().setDbName(name).setEntityId(entityId);
        com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
        try {
            AddFaceEntityResponse addFaceEntityResponse =
                client.addFaceEntityWithOptions(addFaceEntityRequest, runtime);
            if (addFaceEntityResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                return true;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("addFaceEntity Exception:", e);
        }
        return false;
    }

    /**
     * 添加人脸数据
     *
     * @param name 阿里云上面的人脸库名称
     * @param entityId 用户唯一标志
     * @param imageUrl 图片地址
     * @param extraData 额外信息，通常存储用户名称
     * @return the boolean
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public boolean addFace(String name, String entityId, String imageUrl, String extraData) {
        try {
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openConnection().getInputStream();
            com.aliyun.facebody20191230.models.AddFaceAdvanceRequest addFaceAdvanceRequest =
                new com.aliyun.facebody20191230.models.AddFaceAdvanceRequest().setDbName(name).setEntityId(entityId)
                    .setImageUrlObject(inputStream).setExtraData(extraData);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            AddFaceResponse addFaceResponse = client.addFaceAdvance(addFaceAdvanceRequest, runtime);
            if (addFaceResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                return true;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("addFace Exception:", e);
        }
        return false;
    }

    /**
     * 搜索人脸
     *
     * @param name 阿里云上面的人脸库名称
     * @param imageUrl 图片地址，来识别的用户图片地址
     * @param limit 搜索结果数量限制
     * @param threshold 阈值
     * @return 符合的entityId list
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public List<String> searchFace(String name, String imageUrl, Integer limit, Long maxFaceNum, String threshold) {
        List<String> resultList = Lists.newArrayList();
        if (limit == null) {
            limit = ConstantsConfig.aliFaceLimit;
        }
        if (maxFaceNum == null) {
            maxFaceNum = ConstantsConfig.aliFaceMaxNum;
        }

        try {
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openConnection().getInputStream();
            com.aliyun.facebody20191230.models.SearchFaceAdvanceRequest searchFaceAdvanceRequest =
                new com.aliyun.facebody20191230.models.SearchFaceAdvanceRequest().setDbName(name)
                    .setImageUrlObject(inputStream).setLimit(limit).setMaxFaceNum(maxFaceNum);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            SearchFaceResponse searchFaceResponse = client.searchFaceAdvance(searchFaceAdvanceRequest, runtime);
            if (searchFaceResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                List<SearchFaceResponseBody.SearchFaceResponseBodyDataMatchList> matchList =
                    searchFaceResponse.getBody().getData().getMatchList();
                if (CollectionUtils.isEmpty(matchList)) {
                    return resultList;
                }
                for (SearchFaceResponseBody.SearchFaceResponseBodyDataMatchList searchFaceResponseBodyDataMatchList : matchList) {
                    for (SearchFaceResponseBody.SearchFaceResponseBodyDataMatchListFaceItems faceItem : searchFaceResponseBodyDataMatchList
                        .getFaceItems()) {
                        if (faceItem == null) {
                            continue;
                        }
                        String entityId = faceItem.getEntityId();
                        BigDecimal bdL = new BigDecimal(faceItem.confidence * 0.01);
                        BigDecimal bdR = new BigDecimal(Double.valueOf(threshold));
                        if (bdL.compareTo(bdR) >= 0 && !resultList.contains(entityId)) {
                            resultList.add(entityId);
                        }
                    }
                }
                return resultList;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("searchFace Exception:", e);
        }
        return resultList;
    }

    /**
     * 删除人脸样本，会同时删除样本的人脸数据
     *
     * @param name 阿里云上面的人脸库名称
     * @param entityId 用户唯一标志
     * @param imageUrl 图片地址
     * @param extraData 额外信息，通常存储用户名称
     * @return the boolean
     * <AUTHOR>
     * @date 2023 -05-16 16:14:53
     */
    public boolean deleteFaceEntity(String name, String entityId) {
        try {
            com.aliyun.facebody20191230.models.DeleteFaceEntityRequest deleteFaceEntityRequest =
                new com.aliyun.facebody20191230.models.DeleteFaceEntityRequest().setDbName(name).setEntityId(entityId);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            DeleteFaceEntityResponse deleteFaceEntityResponse =
                client.deleteFaceEntityWithOptions(deleteFaceEntityRequest, runtime);
            if (deleteFaceEntityResponse.getStatusCode().equals(HttpStatus.HTTP_OK)) {
                return true;
            }
        } catch (TeaException teaException) {
            log.error(com.aliyun.teautil.Common.toJSONString(teaException));
            log.error(teaException.getCode());
        } catch (Exception e) {
            log.error("deleteFaceEntity Exception:", e);
        }
        return false;
    }
}
