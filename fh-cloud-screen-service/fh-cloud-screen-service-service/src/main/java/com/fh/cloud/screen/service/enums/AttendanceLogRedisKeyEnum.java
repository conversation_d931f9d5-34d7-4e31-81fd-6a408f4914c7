package com.fh.cloud.screen.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AttendanceLogRedisKeyEnum {

    /**
     * 班级打卡记录缓存，最终的缓存格式是：attendance:log:student:classesId:day:班级id:yyyyMMddHHmmss
     */
    ATTENDANCE_LOG_CLASS_STUDENT_DAY_KEY("attendance:log:student:classesId:day", "班级学生指定日期打卡记录"),
    ATTENDANCE_LOG_ORG_TEACHER_DAY_KEY("attendance:log:teacher:orgId:day", "学校老师指定日期打卡记录");

    private final String value;

    private final String desc;

}
