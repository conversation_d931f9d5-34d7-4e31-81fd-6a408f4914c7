package com.fh.cloud.screen.service.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLogDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 设备日志表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-10 15:08:02
 */
public interface IShowDeviceLogService extends IService<ShowDeviceLogDto> {

    List<ShowDeviceLogVo> getShowDeviceLogListByCondition(ShowDeviceLogConditionBo condition);

	Long addShowDeviceLog(ShowDeviceLogBo showDeviceLogBo);

	AjaxResult updateShowDeviceLog(ShowDeviceLogBo showDeviceLogBo);

	ShowDeviceLogVo getShowDeviceLogByCondition(ShowDeviceLogConditionBo condition);

	ShowDeviceLogVo getDetail(Long id);

	/**
	 * 根据设备号查询最后一条日志信息
	 *
	 * @param deviceNumber
	 * @return
	 */
	ShowDeviceLogVo getDetailByNumber(String deviceNumber);
}

