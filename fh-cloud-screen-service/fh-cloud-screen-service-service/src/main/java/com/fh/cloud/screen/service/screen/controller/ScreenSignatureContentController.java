package com.fh.cloud.screen.service.screen.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenSignatureContentApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureContentDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo;
import com.fh.cloud.screen.service.screen.service.IScreenSignatureContentService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 电子签名表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
@RestController
@Validated
public class ScreenSignatureContentController implements ScreenSignatureContentApi {

    @Autowired
    private IScreenSignatureContentService screenSignatureContentService;

    /**
     * 查询电子签名表分页列表
     * 
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult<PageInfo<ScreenSignatureContentVo>>
        getScreenSignatureContentPageListByCondition(@RequestBody ScreenSignatureContentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenSignatureContentVo> pageInfo =
            new PageInfo<>(screenSignatureContentService.getScreenSignatureContentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询电子签名表列表
     * 
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult<List<ScreenSignatureContentVo>>
        getScreenSignatureContentListByCondition(@RequestBody ScreenSignatureContentConditionBo condition) {
        List<ScreenSignatureContentVo> list =
            screenSignatureContentService.getScreenSignatureContentListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增电子签名表
     * 
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult
        addScreenSignatureContent(@Validated @RequestBody ScreenSignatureContentBo screenSignatureContentBo) {
        return screenSignatureContentService.addScreenSignatureContent(screenSignatureContentBo);
    }

    /**
     * 修改电子签名表
     * 
     * @param screenSignatureContentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult
        updateScreenSignatureContent(@Validated @RequestBody ScreenSignatureContentBo screenSignatureContentBo) {
        if (null == screenSignatureContentBo.getScreenSignatureContentId()) {
            return AjaxResult.fail("电子签名表id不能为空");
        }
        return screenSignatureContentService.updateScreenSignatureContent(screenSignatureContentBo);
    }

    /**
     * 查询电子签名表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult<ScreenSignatureContentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("电子签名表id不能为空");
        }
        ScreenSignatureContentConditionBo condition = new ScreenSignatureContentConditionBo();
        condition.setScreenSignatureContentId(id);
        ScreenSignatureContentVo vo = screenSignatureContentService.getScreenSignatureContentByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除电子签名表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-07-12 09:50:58
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenSignatureContentDto screenSignatureContentDto = new ScreenSignatureContentDto();
        screenSignatureContentDto.setScreenSignatureContentId(id);
        screenSignatureContentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenSignatureContentService.updateById(screenSignatureContentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
