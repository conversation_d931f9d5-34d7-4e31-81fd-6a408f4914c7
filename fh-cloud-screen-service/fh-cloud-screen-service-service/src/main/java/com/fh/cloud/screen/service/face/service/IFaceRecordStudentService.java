package com.fh.cloud.screen.service.face.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordStudentDto;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordUploadResultVo;
import com.light.core.entity.AjaxResult;

/**
 * 学生人脸库接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
public interface IFaceRecordStudentService extends IService<FaceRecordStudentDto> {

    List<FaceRecordStudentVo> getFaceRecordStudentListByCondition(FaceRecordStudentConditionBo condition);

    AjaxResult addFaceRecordStudent(FaceRecordStudentBo faceRecordStudentBo);

    AjaxResult updateFaceRecordStudent(FaceRecordStudentBo faceRecordStudentBo);

    AjaxResult updateFaceRecordStudentBatch(List<FaceRecordStudentBo> faceRecordStudentBoList);

    FaceRecordStudentVo getDetail(Long id);

    /**
     * 导入学生人脸数据
     *
     * @param faceRecordStudentConditionBos
     * @return
     */
    AjaxResult<FaceRecordUploadResultVo> upload(List<FaceRecordStudentConditionBo> faceRecordStudentConditionBos);

    /**
     * 更新用户姓名
     * 
     * @param userOid
     * @param realName
     * @return
     */
    AjaxResult updateRealNameByUserOid(String userOid, String realName);
}
