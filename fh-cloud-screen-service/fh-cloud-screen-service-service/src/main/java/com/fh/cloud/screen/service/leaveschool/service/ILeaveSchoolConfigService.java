package com.fh.cloud.screen.service.leaveschool.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo;
import com.light.core.entity.AjaxResult;

import java.util.Date;
import java.util.List;

/**
 * 放学配置表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
public interface ILeaveSchoolConfigService extends IService<LeaveSchoolConfigDto> {

    List<LeaveSchoolConfigVo> getLeaveSchoolConfigListByCondition(LeaveSchoolConfigConditionBo condition);

	AjaxResult addLeaveSchoolConfig(LeaveSchoolConfigBo leaveSchoolConfigBo);

	AjaxResult updateLeaveSchoolConfig(LeaveSchoolConfigBo leaveSchoolConfigBo);

	LeaveSchoolConfigVo getLeaveSchoolConfigByCondition(LeaveSchoolConfigConditionBo condition);

	/**
	 * 根据组织、地点获取放学场景
	 *
	 * @param organizationId
	 * @param spaceInfoId
	 * @param spaceGroupUseType
	 * @return com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo
	 * <AUTHOR>
	 * @date 2023/8/24 20:10
	 **/
	LeaveSchoolConfigVo getLeaveSchoolConfigBySpaceInfo(Long organizationId,
														Long spaceInfoId,
														Integer spaceGroupUseType,
														Date nowDay,
														Long showDeviceId);

}

