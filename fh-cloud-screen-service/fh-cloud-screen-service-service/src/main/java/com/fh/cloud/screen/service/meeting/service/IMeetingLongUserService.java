package com.fh.cloud.screen.service.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongUserDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongUserVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 长期预约表人员表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
public interface IMeetingLongUserService extends IService<MeetingLongUserDto> {

    List<MeetingLongUserVo> getMeetingLongUserListByCondition(MeetingLongUserConditionBo condition);

	AjaxResult addMeetingLongUser(MeetingLongUserBo meetingLongUserBo);

	AjaxResult updateMeetingLongUser(MeetingLongUserBo meetingLongUserBo);

	MeetingLongUserVo getMeetingLongUserByCondition(MeetingLongUserConditionBo condition);

}

