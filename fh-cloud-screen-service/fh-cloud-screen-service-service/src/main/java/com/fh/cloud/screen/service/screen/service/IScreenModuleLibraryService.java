package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 模块库表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface IScreenModuleLibraryService extends IService<ScreenModuleLibrary> {

    List<ScreenModuleLibraryVo> getScreenModuleLibraryListByCondition(ScreenModuleLibraryListConditionBo condition);

    boolean addScreenModuleLibrary(ScreenModuleLibraryBo screenModuleLibraryBo);

    boolean updateScreenModuleLibrary(ScreenModuleLibraryBo screenModuleLibraryBo);

    ScreenModuleLibraryVo getDetail(Long screenmodulelibraryid);

    /**
     * 海报条件查询主题列表（内部含业务逻辑、仅仅适用于特定方法调用，如果单独查询主题请调用其他方法）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 16:43
     */
    AjaxResult getPosterList(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 原始的查询海报查询方法，会顺带查询出标签
     * @param conditionBo
     * @return
     */
    List<ScreenModuleLibraryVo> getPosterListOriginal(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 海报条件查询主题列表，只根据已选择的海报主题查询
     *
     * @param conditionBo the condition bo
     * @return the poster list sel
     * <AUTHOR>
     * @date 2023 -06-05 14:41:08
     */
    AjaxResult getPosterListSel(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 设备关联标签关联海报主题列表(订阅海报列表)
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 9:42
     */
    AjaxResult getDevicePosterListByDeviceNumber(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 运营海报库列表（未配置标签、已配置标签）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 13:53
     */
    AjaxResult getLabelPosterListByCondition(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 运营海报库统计信息（未配置标签、已配置标签）
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 13:53
     */
    AjaxResult getLabelPosterStatistics(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 新增或编辑主题海报
     *
     * @param screenModuleLibraryBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/9 14:04
     */
    AjaxResult updateLibrary(ScreenModuleLibraryBo screenModuleLibraryBo);

    /**
     * 我创建的海报主题列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/31 11:44
     */
    AjaxResult getPersonPosters(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 删除主题列表及主题对应的海报图片
     *
     * @param libraryIds 主题id集合
     * @return boolean
     * <AUTHOR>
     * @date 2023/5/16 14:33
     */
    boolean deleteScreenModuleLibraryAndLibraryMediaByLibraryIds(List<Long> libraryIds);

    /**
     * 查询校本海报数量
     *
     * @param conditionBo the condition bo
     * @return poster school num
     * <AUTHOR>
     * @date 2024 -07-01 15:16:57
     */
    List<ScreenModuleLibraryNumVo> getPosterSchoolNum(ScreenModuleLibraryListConditionBo conditionBo);

    /**
     * 查询校本海报海报数
     *
     * @param conditionBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo>
     * <AUTHOR>
     * @date 2024/8/2 14:25
     **/
    List<ScreenModuleLibraryNumVo> getPosterMediaSchoolNum(ScreenModuleLibraryListConditionBo conditionBo);
}
