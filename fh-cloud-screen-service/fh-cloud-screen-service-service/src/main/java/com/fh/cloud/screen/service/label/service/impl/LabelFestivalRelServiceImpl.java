package com.fh.cloud.screen.service.label.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.label.entity.bo.LabelBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo;
import com.fh.cloud.screen.service.label.mapper.LabelFestivalRelMapper;
import com.fh.cloud.screen.service.label.service.ILabelFestivalRelService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 标签节日关联表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@Service
public class LabelFestivalRelServiceImpl extends ServiceImpl<LabelFestivalRelMapper, LabelFestivalRelDto>
    implements ILabelFestivalRelService {

    @Resource
    private LabelFestivalRelMapper labelFestivalRelMapper;

    @Override
    public List<LabelFestivalRelVo> getLabelFestivalRelListByCondition(LabelFestivalRelConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return labelFestivalRelMapper.getLabelFestivalRelListByCondition(condition);
    }

    @Override
    public AjaxResult addLabelFestivalRel(LabelFestivalRelBo labelFestivalRelBo) {
        List<LabelBo> labelBos = labelFestivalRelBo.getLabelBos();
        // 删除已绑定
        LambdaUpdateWrapper<LabelFestivalRelDto> luw = new LambdaUpdateWrapper<>();
        luw.eq(LabelFestivalRelDto::getFestivalCode, labelFestivalRelBo.getFestivalCode());
        luw.set(LabelFestivalRelDto::getIsDelete, StatusEnum.ISDELETE.getCode());
        this.update(luw);
        if (CollectionUtils.isEmpty(labelBos)) {
            return AjaxResult.success("保存成功");
        }
        // 添加新绑定
        List<LabelFestivalRelDto> addList = new ArrayList<>();
        for (LabelBo labelBo : labelBos) {
            LabelFestivalRelDto labelFestivalRelDto = new LabelFestivalRelDto();
            labelFestivalRelDto.setFestivalCode(labelFestivalRelBo.getFestivalCode());
            labelFestivalRelDto.setLabelId(labelBo.getLabelId());
            labelFestivalRelDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
            addList.add(labelFestivalRelDto);
        }
        if (saveBatch(addList)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateLabelFestivalRel(LabelFestivalRelBo labelFestivalRelBo) {
        LabelFestivalRelDto labelFestivalRel = new LabelFestivalRelDto();
        BeanUtils.copyProperties(labelFestivalRelBo, labelFestivalRel);
        if (updateById(labelFestivalRel)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public LabelFestivalRelVo getDetail(Long id) {
        LabelFestivalRelConditionBo condition = new LabelFestivalRelConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<LabelFestivalRelVo> list = labelFestivalRelMapper.getLabelFestivalRelListByCondition(condition);
        LabelFestivalRelVo vo = new LabelFestivalRelVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

}