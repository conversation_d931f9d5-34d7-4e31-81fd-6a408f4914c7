package com.fh.cloud.screen.service.rest.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.rest.api.WorkRestApi;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestListConditionBo;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestVo;
import com.fh.cloud.screen.service.rest.service.IWorkRestService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 作息时间主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Slf4j
@RestController
public class WorkRestController implements WorkRestApi {

    @Autowired
    private IWorkRestService workRestService;

    /**
     * 查询作息时间主表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询作息时间主表列表", httpMethod = "POST")
    public AjaxResult getWorkRestListByCondition(@RequestBody WorkRestListConditionBo condition) {
        if (null == condition.getOrganizationId()) {
            return AjaxResult.fail("组织id不能为空");
        }

        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("update_time desc");
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", workRestService.getWorkRestListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<WorkRestVo> pageInfo = new PageInfo<>(workRestService.getWorkRestListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 新增作息时间主表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "新增作息时间主表", httpMethod = "POST")
    public AjaxResult addWorkRest(@RequestBody WorkRestBo workRestBo) {
        Long workRestId = workRestService.addWorkRest(workRestBo);
        if (workRestId != null) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改作息时间主表
     * 
     * @param workRestBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "修改作息时间主表", httpMethod = "POST")
    public AjaxResult updateWorkRest(@RequestBody WorkRestBo workRestBo) {
        if (null == workRestBo.getWorkRestId()) {
            return AjaxResult.fail("作息时间主表id不能为空");
        }
        boolean update = workRestService.updateWorkRest(workRestBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询作息时间主表详情
     * 
     * @param workRestId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询作息时间主表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("workRestId") Long workRestId) {
        WorkRestVo workRestVo = workRestService.getDetail(workRestId);
        return AjaxResult.success(workRestVo);
    }

    /**
     * 删除作息时间主表
     * 
     * @param workRestId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "删除作息时间主表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("workRestId") Long workRestId) {
        WorkRestBo workRestBo = new WorkRestBo();
        workRestBo.setWorkRestId(workRestId);
        workRestBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = workRestService.updateWorkRest(workRestBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    @ApiOperation(value = "保存作息时间主表", httpMethod = "POST")
    @Override
    public AjaxResult saveWorkRestWithDetail(WorkRestBo workRestBo) {
        try {
            Long workRestId = workRestService.saveOrUpdateWorkRestWithDetail(workRestBo);
            return AjaxResult.success(workRestId);
        } catch (Exception e) {
            log.error("saveWorkRestWithDetail error:", e);
            return AjaxResult.fail();
        }
    }

    @ApiOperation(value = "启用禁用作息时间", httpMethod = "POST")
    @Override
    public AjaxResult changeStatus(WorkRestBo workRestBo) {
        if (workRestBo.getOrganizationId() == null || workRestBo.getWorkRestId() == null
            || workRestBo.getStatus() == null) {
            return AjaxResult.fail("参数错误");
        }

        try {
            workRestService.changeStatus(workRestBo);
            return AjaxResult.success();
        } catch (Exception e) {
            log.error("changeStatus error:", e);
            return AjaxResult.fail();
        }
    }
}
