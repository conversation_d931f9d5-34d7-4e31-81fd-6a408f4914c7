package com.fh.cloud.screen.service.screen.controller;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.attendance.service.IAttendanceLogService;
import com.fh.cloud.screen.service.baseinfo.entity.vo.OrganizationVoExt;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarWeekService;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.consts.ConstantsRedisScreenIndex;
import com.fh.cloud.screen.service.enums.*;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.service.IExamInfoSubjectService;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigService;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;
import com.fh.cloud.screen.service.meeting.service.IMeetingService;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneThirdService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.StringKit;
import com.light.user.campus.entity.vo.CampusVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.screen.api.ScreenBusinessApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ClazzVoExt;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenIndexVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentService;
import com.fh.cloud.screen.service.screen.service.IScreenContentSpecialService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleDataService;
import com.fh.cloud.screen.service.screen.service.IScreenSceneService;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.task.dto.DelayDemoDto;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.redis.component.RedisComponent;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.entity.vo.UserOrgVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 云屏业务相关controller(首页、菜单等上层业务)
 *
 * <AUTHOR>
 * @date 2022 /6/8 16:48
 */
@RestController
@Api(value = "", tags = "云屏首页管理")
public class ScreenBusinessController implements ScreenBusinessApi {

    @Autowired
    private ISpaceInfoService spaceInfoService;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Autowired
    private IScreenSceneService screenSceneService;
    @Autowired
    private IScreenModuleDataService screenModuleDataService;
    @Autowired
    private IScreenContentService screenContentService;
    @Autowired
    private IScreenContentSpecialService screenContentSpecialService;
    @Autowired
    private ISpaceGroupService spaceGroupService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private RedisComponent redisComponent;
    @Autowired
    private IMeetingService meetingService;
    @Autowired
    private IExamInfoSubjectService examInfoSubjectService;
    @Autowired
    private IScreenSceneThirdService screenSceneThirdService;
    @Autowired
    private ILeaveSchoolConfigService leaveSchoolConfigService;
    @Autowired
    private ISchoolCalendarWeekService schoolCalendarWeekService;

    /**
     * 云屏link转截图开关开启标识：默认关闭，需要nacos配置才是开启.1开，0关
     */
    @Value("${screen.content.link.image.enable:0}")
    private String screenContentLinkImageEnable;

    @ApiOperation(value = "查询云屏内容表列表", httpMethod = "POST")
    @Override
    public AjaxResult screenIndex(ScreenBusinessBo screenBusinessBo) {
        List<ScreenIndexVo> screenIndexVos = Lists.newArrayListWithCapacity(ConstantsInteger.NUM_3);
        Long organizationId = screenBusinessBo.getOrganizationId();
        Long campusId = screenBusinessBo.getCampusId();
        Long spaceInfoId = screenBusinessBo.getSpaceInfoId();
        Integer spaceGroupUseType = screenBusinessBo.getSpaceGroupUseType();
        // 参数校验
        if (screenBusinessBo == null || spaceGroupUseType == null || organizationId == null) {
            return AjaxResult.fail("参数错误");
        }
        Date nowDate = new Date();
        // 横竖版式
        ShowDevice showDevice = showDeviceService.getByDeviceNum(screenBusinessBo.getDeviceNumber());
        Long showDeviceId = showDevice.getShowDeviceId();
        Integer devicePattern = showDevice.getDevicePattern();
        Integer superviseState = showDevice.getSuperviseState();
        // 查地点组
        Long spaceGroupId = null;
        if (spaceGroupUseType.equals(SpaceGroupUseType.XZ.getValue())) {
            SpaceGroupVo spaceGroupVoOfXz = spaceGroupService.getSpaceGroupVoOfXz();
            if (spaceGroupVoOfXz != null) {
                spaceGroupId = spaceGroupVoOfXz.getSpaceGroupId();
            }
        } else if (spaceGroupUseType.equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            SpaceInfo spaceInfo = spaceInfoService.getById(spaceInfoId);
            if (spaceInfo != null) {
                spaceGroupId = spaceInfo.getSpaceGroupId();
            }
        }

        // 1、添加常规场景数据
        addNormalScreenScene(screenIndexVos, organizationId, campusId, spaceInfoId, spaceGroupUseType, nowDate,
            showDeviceId, devicePattern, spaceGroupId, superviseState);

        // 2、添加特殊(即紧急发布)场景数据
        addSpecialScreenScene(screenIndexVos, organizationId, campusId, nowDate, spaceGroupId);

        // 3、添加功能场景数据
        addFunctionalScreenScene(screenIndexVos, organizationId, spaceInfoId, spaceGroupUseType, nowDate, showDeviceId);

        // 4、获取地点信息（校区 地点）
        if (screenBusinessBo.isGetSpaceInfo()) {
            setScreenSceneVo(screenIndexVos);
        }

        // 5、计算出当前场景
        if (screenBusinessBo.isCalCurrentScene()) {
            calCurrentSceneToResult(screenIndexVos);
        }

        return AjaxResult.success(screenIndexVos);
    }

    // 封装返回参数
    private void setScreenSceneVo(List<ScreenIndexVo> screenIndexVos) {
        Map<Long, Long> campusMap = new HashMap<>();
        Map<Integer, Set<Long>> spaceInfoMap = new HashMap<>();
        for (ScreenIndexVo indexVo : screenIndexVos) {
            if (CollectionUtils.isNotEmpty(indexVo.getScreenSceneVos())) {
                for (ScreenSceneVo screenSceneVo : indexVo.getScreenSceneVos()) {
                    getCampusIdAndSpaceInfoId(campusMap, spaceInfoMap, screenSceneVo);
                }
            }
        }
        Map<Long, CampusVo> campusVoMap = new HashMap<>();
        // 获取校区
        if (CollectionUtils.isNotEmpty(campusMap.keySet())) {
            Set<Long> organizationIds = new HashSet<>();
            for (Long campusId : campusMap.keySet()) {
                organizationIds.add(campusMap.get(campusId));
            }
            Set<CampusVo> campusVoSet = new HashSet<>();
            for (Long organizationId : organizationIds) {
                CampusListConditionBo conditionBo = new CampusListConditionBo();
                conditionBo.setOrganizationId(organizationId);
                conditionBo.setPageNo(SystemConstants.NO_PAGE);
                List<CampusVo> campusVos = baseDataService.getCampusVoByCondition(conditionBo);
                if (CollectionUtils.isNotEmpty(campusVos)) {
                    campusVoSet.addAll(campusVos);
                }
            }
            if (CollectionUtils.isNotEmpty(campusVoSet)) {
                campusVoMap = campusVoSet.stream().collect(Collectors.toMap(CampusVo::getId, c -> c));
            }
        }
        Map<Long, String> classMap = new HashMap<>();
        Map<Long, String> spaceMap = new HashMap<>();
        // 获取地点
        if (CollectionUtils.isNotEmpty(spaceInfoMap.keySet())) {
            for (Integer spaceGroupUseType : spaceInfoMap.keySet()) {
                if (SpaceGroupUseType.XZ.getValue() == spaceGroupUseType) {
                    Set<Long> classesIds = spaceInfoMap.get(spaceGroupUseType);
                    if (CollectionUtils.isNotEmpty(classesIds)) {
                        ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
                        clazzConditionBo.setIds(classesIds);
                        AjaxResult classesResult = baseDataService.getClassesListByClassesIds(clazzConditionBo);
                        if (classesResult.isSuccess() && classesResult.getData() != null) {
                            Map classesResultMap =
                                JSON.parseObject(JSON.toJSONString(classesResult.getData()), Map.class);
                            List<ClazzInfoVo> clazzInfoVos = JSONArray
                                .parseArray(JSONArray.toJSONString(classesResultMap.get("list")), ClazzInfoVo.class);
                            Map<Long, String> clazzMap = clazzInfoVos.stream().collect(Collectors.toMap(
                                ClazzInfoVo::getId,
                                x -> SchoolYearUtil.gradeMap.get(x.getGrade()).concat(x.getClassesName()).concat("班")));
                            classMap.putAll(clazzMap);
                        }
                    }
                } else {
                    // 非行政地点
                    Set<Long> spaceInfoIds = spaceInfoMap.get(spaceGroupUseType);
                    List<SpaceInfo> spaceInfos = spaceInfoService
                        .list(new LambdaQueryWrapper<SpaceInfo>().in(SpaceInfo::getSpaceInfoId, spaceInfoIds));
                    if (CollectionUtils.isNotEmpty(spaceInfos)) {
                        Map<Long, String> spaceNameMap = spaceInfos.stream()
                            .collect(Collectors.toMap(SpaceInfo::getSpaceInfoId, SpaceInfo::getSpaceInfoName));
                        spaceMap.putAll(spaceNameMap);
                    }
                }
            }
        }
        // 封装名称
        for (ScreenIndexVo indexVo : screenIndexVos) {
            if (CollectionUtils.isNotEmpty(indexVo.getScreenSceneVos())) {
                for (ScreenSceneVo screenSceneVo : indexVo.getScreenSceneVos()) {
                    if (screenSceneVo.getCampusId() != null && screenSceneVo.getCampusId() != ConstantsLong.NUM_0
                        && campusVoMap.containsKey(screenSceneVo.getCampusId())) {
                        screenSceneVo.setCampusName(campusVoMap.get(screenSceneVo.getCampusId()).getName());
                    }
                    if (screenSceneVo.getSpaceInfoId() != null
                        && screenSceneVo.getSpaceInfoId() != ConstantsLong.NUM_0) {
                        if (SpaceGroupUseType.XZ.getValue() == screenSceneVo.getSpaceGroupUseType()
                            && classMap.containsKey(screenSceneVo.getSpaceInfoId())) {
                            screenSceneVo.setSpaceInfoName(classMap.get(screenSceneVo.getSpaceInfoId()));
                        } else if (SpaceGroupUseType.NOT_XZ.getValue() == screenSceneVo.getSpaceGroupUseType()
                            && spaceMap.containsKey(screenSceneVo.getSpaceInfoId())) {
                            screenSceneVo.setSpaceInfoName(spaceMap.get(screenSceneVo.getSpaceInfoId()));
                        }
                    }
                }
            }
        }
    }

    // 获取校区和地点id
    private void getCampusIdAndSpaceInfoId(Map<Long, Long> campusMap, Map<Integer, Set<Long>> spaceInfoMap,
        ScreenSceneVo vo) {
        if (vo.getCampusId() != null && vo.getCampusId() != ConstantsLong.NUM_0) {
            campusMap.put(vo.getCampusId(), vo.getOrganizationId());
        }
        if (vo.getSpaceInfoId() != null && vo.getSpaceInfoId() != ConstantsLong.NUM_0) {
            if (spaceInfoMap.containsKey(vo.getSpaceGroupUseType())) {
                Set<Long> spaceInfoIds = spaceInfoMap.get(vo.getSpaceGroupUseType());
                spaceInfoIds.add(vo.getSpaceInfoId());
            } else {
                Set<Long> spaceInfoIds = new HashSet<>();
                spaceInfoIds.add(vo.getSpaceInfoId());
                spaceInfoMap.put(vo.getSpaceGroupUseType(), spaceInfoIds);
            }
        }
    }

    /**
     * 3、功能场景，添加至screenIndexVos
     * 
     * @param screenIndexVos 返回的场景列表
     * @param organizationId 组织id
     * @param spaceInfoId 地点id
     * @param spaceGroupUseType 地点组类型
     * @param nowDate 当天
     * @Param showDeviceId 设备id （放学场景需要）
     */
    private void addFunctionalScreenScene(List<ScreenIndexVo> screenIndexVos, Long organizationId, Long spaceInfoId,
        Integer spaceGroupUseType, Date nowDate, Long showDeviceId) {
        // 3.1获取当天会议
        MeetingConditionBo meetingConditionBo = new MeetingConditionBo();
        meetingConditionBo.setOrganizationId(organizationId);
        meetingConditionBo.setMeetingDate(DateKit.getZeroDate(0, nowDate));
        meetingConditionBo.setSpaceGroupUseType(spaceGroupUseType);
        meetingConditionBo.setSpaceInfoId(spaceInfoId);
        meetingConditionBo.setNotEnd(StatusEnum.YES.getCode());
        List<MeetingVo> meetingVos = meetingService.getMeetingListByCondition(meetingConditionBo);
        // 3.2根据地点获取当天考试
        List<ExamInfoSubjectVo> examInfoSubjectVos =
            examInfoSubjectService.getExamInfoSubjectListBySpaceAndDate(spaceGroupUseType, spaceInfoId, nowDate);
        // 3.3获取放学场景数据
        LeaveSchoolConfigVo leaveSchoolConfigVo = leaveSchoolConfigService
            .getLeaveSchoolConfigBySpaceInfo(organizationId, spaceInfoId, spaceGroupUseType, nowDate, showDeviceId);
        // 3.4获取第三方对接的功能场景:仅支持按地点或按设备关联查询，showDeviceId在下面具体的业务里面再做判断处理
        List<ScreenSceneThirdVo> screenSceneThirdVos = screenSceneThirdService
            .getScreenSceneThirdVoListBySpaceAndDeviceAndDate(spaceGroupUseType, spaceInfoId, null, nowDate);
        // 3.5组装功能场景数据
        if (CollectionUtils.isNotEmpty(screenSceneThirdVos) || CollectionUtils.isNotEmpty(meetingVos)
            || CollectionUtils.isNotEmpty(examInfoSubjectVos) || leaveSchoolConfigVo != null) {
            ScreenIndexVo functionalScreenIndexVo = new ScreenIndexVo();
            functionalScreenIndexVo.setScreenPriority(ScreenPriorityType.FUNCTIONAL.getValue());
            List<ScreenSceneVo> screenSceneVoList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(screenSceneThirdVos)) {
                screenSceneThirdVos.forEach(screenSceneThirdVo -> {
                    // 课后服务
                    if (screenSceneThirdVo.getScreenSceneThirdType() != null && screenSceneThirdVo
                        .getScreenSceneThirdType().equals(ScreenSceneThirdType.AFTER_SCHOOL.getValue())) {
                        ScreenSceneVo afterSchoolScreenSceneVo = new ScreenSceneVo();
                        afterSchoolScreenSceneVo.setScreenSceneName(screenSceneThirdVo.getScreenSceneThirdName());
                        afterSchoolScreenSceneVo.setUpdateTime(screenSceneThirdVo.getUpdateTime());
                        afterSchoolScreenSceneVo.setScreenPriority(ScreenPriorityType.FUNCTIONAL.getValue());
                        Integer delayMinute = screenSceneThirdVo.getScreenSceneThirdDelayMinute();
                        afterSchoolScreenSceneVo.setStartTime(screenSceneThirdVo.getScreenSceneThirdStartTime());
                        // 提前30分钟霸屏
                        if (delayMinute != null) {
                            afterSchoolScreenSceneVo
                                .setStartTime(DateKit.addMinute(afterSchoolScreenSceneVo.getStartTime(), delayMinute));
                        }
                        afterSchoolScreenSceneVo.setEndTime(screenSceneThirdVo.getScreenSceneThirdEndTime());
                        afterSchoolScreenSceneVo
                            .setScreenSceneThirdShowUrl(screenSceneThirdVo.getScreenSceneThirdShowUrl());
                        afterSchoolScreenSceneVo
                            .setScreenSceneThirdPostUrl(screenSceneThirdVo.getScreenSceneThirdPostUrl());
                        afterSchoolScreenSceneVo.setOrganizationId(organizationId);
                        afterSchoolScreenSceneVo.setSpaceGroupUseType(spaceGroupUseType);
                        afterSchoolScreenSceneVo.setSpaceInfoId(spaceInfoId);
                        afterSchoolScreenSceneVo.setFunctionalType(ScreenScreenEnums.FUN_TYPE_AFTER_SCHOOL.getCode());
                        screenSceneVoList.add(afterSchoolScreenSceneVo);
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(meetingVos)) {
                meetingVos.forEach(meetingVo -> {
                    ScreenSceneVo meetingScreenSceneVo = new ScreenSceneVo();
                    meetingScreenSceneVo.setScreenSceneName("会议场景");
                    meetingScreenSceneVo.setUpdateTime(meetingVo.getUpdateTime());
                    meetingScreenSceneVo.setScreenPriority(ScreenPriorityType.FUNCTIONAL.getValue());
                    // 提前30分钟霸屏
                    Date starDateTime =
                        DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getMeetingStartTime());
                    meetingScreenSceneVo
                        .setStartTime(DateKit.addMinute(starDateTime, ConstantsInteger.MEETING_SCENE_BEFORE_TIME));
                    meetingScreenSceneVo.setEndTime(
                        DateKit.getDateAndTimeCompose(meetingVo.getMeetingDate(), meetingVo.getMeetingEndTime()));
                    meetingScreenSceneVo.setOrganizationId(meetingVo.getOrganizationId());
                    meetingScreenSceneVo.setFunctionalType(ScreenScreenEnums.FUN_TYPE_MEETING.getCode());
                    screenSceneVoList.add(meetingScreenSceneVo);
                });
            }
            if (CollectionUtils.isNotEmpty(examInfoSubjectVos)) {
                examInfoSubjectVos.forEach(examInfoSubjectVo -> {
                    ScreenSceneVo screenSceneVo = new ScreenSceneVo();
                    screenSceneVo.setScreenSceneName("考试场景");
                    screenSceneVo.setUpdateTime(examInfoSubjectVo.getPlanUpdateTime());
                    screenSceneVo.setScreenPriority(ScreenPriorityType.FUNCTIONAL.getValue());
                    // 提前30分钟霸屏
                    screenSceneVo.setStartTime(DateKit.addMinute(examInfoSubjectVo.getExamStartTime(),
                        ConstantsInteger.MEETING_SCENE_BEFORE_TIME));
                    screenSceneVo.setEndTime(examInfoSubjectVo.getExamEndTime());
                    screenSceneVo.setOrganizationId(organizationId);
                    screenSceneVo.setSpaceGroupUseType(spaceGroupUseType);
                    screenSceneVo.setSpaceInfoId(spaceInfoId);
                    screenSceneVo.setFunctionalType(ScreenScreenEnums.FUN_TYPE_EXAM.getCode());
                    screenSceneVoList.add(screenSceneVo);
                });
            }
            // 放学场景 判断是否是上课日
            AjaxResult ajaxResult = getSchoolCalendarDaysByOrganization(organizationId, new Date());
            if (ajaxResult.isSuccess()) {
                if (leaveSchoolConfigVo != null
                    && CollectionUtils.isNotEmpty(leaveSchoolConfigVo.getConfigDetailList())) {
                    leaveSchoolConfigVo.getConfigDetailList().forEach(detail -> {
                        ScreenSceneVo screenSceneVo = new ScreenSceneVo();
                        screenSceneVo.setScreenSceneName("放学场景");
                        screenSceneVo.setUpdateTime(leaveSchoolConfigVo.getUpdateTime());
                        screenSceneVo.setScreenPriority(ScreenPriorityType.FUNCTIONAL.getValue());
                        try {
                            Calendar calendar = Calendar.getInstance();// 获取Calendar实例
                            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");// 定义日期格式
                            String dateString = dateFormat.format(calendar.getTime());// 获取当前时间并格式化为字符串
                            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            screenSceneVo.setStartTime(
                                format.parse(dateString + " " + sdf.format(detail.getLeaveSchoolStartTime())));
                            screenSceneVo.setEndTime(
                                format.parse(dateString + " " + sdf.format(detail.getLeaveSchoolEndTime())));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        screenSceneVo.setOrganizationId(leaveSchoolConfigVo.getOrganizationId());
                        screenSceneVo.setSpaceGroupUseType(spaceGroupUseType);
                        screenSceneVo.setSpaceInfoId(spaceInfoId);
                        screenSceneVo.setFunctionalType(ScreenScreenEnums.FUN_TYPE_LEAVE_SCHOOL.getCode());
                        screenSceneVoList.add(screenSceneVo);
                    });
                }
            }
            // 根据更新时间倒序
            List<ScreenSceneVo> screenScenes = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(screenSceneVoList)) {
                screenScenes = screenSceneVoList.stream()
                    .sorted(Comparator.comparing(ScreenSceneVo::getUpdateTime).reversed()).collect(Collectors.toList());
            }
            functionalScreenIndexVo.setScreenSceneVos(screenScenes);
            screenIndexVos.add(functionalScreenIndexVo);
        }
    }

    /**
     * 2、特殊(即紧急发布)场景数据填充，添加至screenIndexVos
     * 
     * @param screenIndexVos 返回的场景列表
     * @param organizationId 组织id
     * @param campusId 校区id
     * @param nowDate 当天
     * @param spaceGroupId 地点组id
     */
    private void addSpecialScreenScene(List<ScreenIndexVo> screenIndexVos, Long organizationId, Long campusId,
        Date nowDate, Long spaceGroupId) {
        List<ScreenContentSpecialVo> screenContentSpecialVos = screenContentSpecialService
            .listScreenContentSpecialVosBySpaceGroupId(organizationId, campusId, spaceGroupId, nowDate);
        if (CollectionUtils.isNotEmpty(screenContentSpecialVos)) {
            for (ScreenContentSpecialVo screenContentSpecialVo : screenContentSpecialVos) {
                ScreenIndexVo specialScreenIndexVo = new ScreenIndexVo();
                specialScreenIndexVo.setScreenPriority(ScreenPriorityType.SPECIAL.getValue());
                ScreenSceneVo specialScreenSceneVo = new ScreenSceneVo();
                specialScreenSceneVo.setScreenSceneName("紧急场景");
                specialScreenSceneVo.setUpdateTime(screenContentSpecialVo.getUpdateTime());
                specialScreenSceneVo.setScreenPriority(ScreenPriorityType.SPECIAL.getValue());
                specialScreenSceneVo.setStartTime(screenContentSpecialVo.getStartTime());
                specialScreenSceneVo.setEndTime(screenContentSpecialVo.getEndTime());
                specialScreenSceneVo.setScreenContentSpecialVos(Lists.newArrayList(screenContentSpecialVo));
                specialScreenIndexVo.setScreenSceneVos(Lists.newArrayList(specialScreenSceneVo));
                specialScreenSceneVo.setCampusId(screenContentSpecialVo.getCampusId());
                specialScreenSceneVo.setOrganizationId(screenContentSpecialVo.getOrganizationId());
                screenIndexVos.add(specialScreenIndexVo);
            }
        }
    }

    /**
     * 1、常规场景数据，添加至screenIndexVos
     * 
     * @param screenIndexVos 返回的场景列表
     * @param organizationId 组织id
     * @param campusId 校区id
     * @param spaceInfoId 地点id
     * @param spaceGroupUseType 地点类型
     * @param nowDate 当天
     * @param showDeviceId 设备id
     * @param devicePattern 设备横竖屏类型
     * @param spaceGroupId 地点组id
     */
    private void addNormalScreenScene(List<ScreenIndexVo> screenIndexVos, Long organizationId, Long campusId,
        Long spaceInfoId, Integer spaceGroupUseType, Date nowDate, Long showDeviceId, Integer devicePattern,
        Long spaceGroupId, Integer superviseState) {
        // 根据设备、地点、地点组查询场景（兼容按点位发布），同时过滤横竖屏
        ScreenSceneListConditionBo screenSceneListConditionBo = new ScreenSceneListConditionBo();
        screenSceneListConditionBo.setOrganizationId(organizationId);
        screenSceneListConditionBo.setCampusId(campusId);
        screenSceneListConditionBo.setPageNo(SystemConstants.NO_PAGE);
        screenSceneListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        screenSceneListConditionBo.setShowDeviceId(showDeviceId);
        screenSceneListConditionBo.setSpaceInfoId(spaceInfoId);
        screenSceneListConditionBo.setSpaceGroupUseType(spaceGroupUseType);
        screenSceneListConditionBo.setSpaceGroupId(spaceGroupId);
        screenSceneListConditionBo.setScreenDevicePattern(devicePattern);
        screenSceneListConditionBo.setSuperviseState(superviseState);
        List<ScreenSceneVo> screenSceneVos =
            screenSceneService.listScreenSceneOfAllByDevice(screenSceneListConditionBo);
        // 根据场景查询模块
        List<Long> screenSceneIds =
            screenSceneVos.stream().map(ScreenSceneVo::getScreenSceneId).collect(Collectors.toList());
        List<ScreenModuleDataVo> screenModuleDataVos = screenModuleDataService.listByScreenSceneIds(screenSceneIds);
        Map<Long, List<ScreenModuleDataVo>> screenSceneIdMap =
            screenModuleDataVos.stream().collect(Collectors.groupingBy(ScreenModuleDataVo::getScreenSceneId));
        screenSceneVos.forEach(screenSceneVo -> screenSceneVo
            .setScreenModuleDataVos(screenSceneIdMap.get(screenSceneVo.getScreenSceneId())));
        List<Long> screenModuleDataIds =
            screenModuleDataVos.stream().map(ScreenModuleDataVo::getScreenModuleDataId).collect(Collectors.toList());
        // 根据模块查询内容数据
        ScreenContentListConditionBo screenContentListConditionBo = new ScreenContentListConditionBo();
        screenContentListConditionBo.setScreenIndexShow(true);
        screenContentListConditionBo.setOrganizationId(organizationId);
        screenContentListConditionBo.setCampusId(campusId);
        screenContentListConditionBo.setClassesId(spaceInfoId);
        screenContentListConditionBo.setPageNo(SystemConstants.NO_PAGE);
        screenContentListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        screenContentListConditionBo.setScreenModuleDataIds(screenModuleDataIds);
        screenContentListConditionBo.setScreenContentStatus(ScreenContentStatusType.PUBLISH.getValue());
        screenContentListConditionBo.setNowDate(nowDate);
        screenContentListConditionBo.setOrderBy("content_nice asc,update_time desc");
        List<ScreenContentVo> screenContentVos =
            screenContentService.getScreenContentListByConditionWithDetail(screenContentListConditionBo);
        // 是否隐藏link转换的图片
        if (SystemConstants.NO.equals(screenContentLinkImageEnable)) {
            screenContentService.hideLinkImage(screenContentVos);
        }
        Map<Long, List<ScreenContentVo>> screenModuleIdMap =
            screenContentVos.stream().collect(Collectors.groupingBy(ScreenContentVo::getScreenModuleDataId));
        screenModuleDataVos.forEach(screenModuleDataVo -> screenModuleDataVo
            .setScreenContentVos(screenModuleIdMap.get(screenModuleDataVo.getScreenModuleDataId())));
        // 按照场景轮播key组装场景层级结构：组装完成后，后续的screenSceneVos将有层级，如需遍历处理则需要在此之前处理。
        Map<String, List<ScreenSceneVo>> screenSceneGroupMap = screenSceneVos.stream()
            .collect(Collectors.groupingBy(ScreenSceneVo::getScreenPlayKey, LinkedHashMap::new, Collectors.toList()));
        // screenSceneVos替换为新的多层的screenSceneVos
        List<ScreenSceneVo> screenSceneVosWithGroup = Lists.newArrayList();
        for (String screenPlayKey : screenSceneGroupMap.keySet()) {
            List<ScreenSceneVo> screenSceneVosTmp = screenSceneGroupMap.get(screenPlayKey);
            ScreenSceneVo screenSceneVoTemp = new ScreenSceneVo();
            BeanUtils.copyProperties(screenSceneVosTmp.get(0), screenSceneVoTemp);
            screenSceneVoTemp.setScreenSceneLayout(null);
            screenSceneVoTemp.setScreenModuleDataVos(null);
            screenSceneVoTemp.setScreenPlayIndex(null);
            screenSceneVoTemp.setScreenPlayName(null);
            screenSceneVoTemp.setChildren(screenSceneVosTmp);
            screenSceneVosWithGroup.add(screenSceneVoTemp);
        }
        screenSceneVos = screenSceneVosWithGroup;
        // 封装返回信息
        LinkedHashMap<Integer, List<ScreenSceneVo>> screenSceneMap = screenSceneVos.stream()
            .collect(Collectors.groupingBy(ScreenSceneVo::getScreenPriority, LinkedHashMap::new, Collectors.toList()));
        for (Integer screenPriority : screenSceneMap.keySet()) {
            ScreenIndexVo screenIndexVo = new ScreenIndexVo();
            screenIndexVo.setScreenPriority(screenPriority);
            screenIndexVo.setScreenSceneVos(screenSceneMap.get(screenPriority));
            screenIndexVos.add(screenIndexVo);
        }
    }

    @Override
    public AjaxResult orgInfo(ScreenBusinessBo screenBusinessBo) {
        OrganizationVoExt organizationVo =
            baseDataService.getOrganizationVoByOrgId(screenBusinessBo.getOrganizationId());
        organizationVo.setCampusList(null);
        return AjaxResult.success(organizationVo);
    }

    @Override
    public AjaxResult orgInfoWithCache(ScreenBusinessBo screenBusinessBo) {
        String cacheKey = StringKit.getMessage(ConstantsRedisScreenIndex.ORG_INFO,
            String.valueOf(screenBusinessBo.getOrganizationId()));
        // 从缓存里面拉取数据
        if (redisComponent.hasKey(cacheKey)) {
            OrganizationVoExt organizationVoExt =
                JSONObject.parseObject((String)redisComponent.get(cacheKey), OrganizationVoExt.class);
            return AjaxResult.success(organizationVoExt);
        }
        // 缓存没有从数据库拉取数据，同时更新到缓存
        AjaxResult organizationVoExtAjaxResult = orgInfo(screenBusinessBo);
        if (organizationVoExtAjaxResult.isSuccess() && organizationVoExtAjaxResult.getData() != null) {
            String value = JSONObject.toJSONString(organizationVoExtAjaxResult.getData());
            redisComponent.set(cacheKey, value, ConstantsRedisScreenIndex.ORG_INFO_EXPIRE_IN);
        }
        return organizationVoExtAjaxResult;
    }

    @Override
    public AjaxResult clazzInfo(ScreenBusinessBo screenBusinessBo) {
        if (screenBusinessBo.getSpaceGroupUseType() != null
            && screenBusinessBo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            return AjaxResult.success();
        }
        ClazzVo clazzVo = baseDataService.getByClazzId(screenBusinessBo.getSpaceInfoId());
        if (clazzVo == null) {
            return AjaxResult.success();
        }
        ClazzVoExt clazzVoExt = new ClazzVoExt();
        BeanUtils.copyProperties(clazzVo, clazzVoExt);
        String grade = SchoolYearUtil.gradeMap.get(clazzVoExt.getGrade());
        String gradeClassesName = grade.concat(clazzVoExt.getClassesName()).concat("班");
        clazzVoExt.setGradeClassesName(gradeClassesName);

        List<ClazzHeadmasterVo> clazzHeadmasterVos = baseDataService.listClazzHeadmasterVoByClassesId(clazzVo.getId());
        if (CollectionUtils.isEmpty(clazzHeadmasterVos)) {
            return AjaxResult.success(clazzVoExt);
        }
        List<Long> teacherIds =
            clazzHeadmasterVos.stream().map(ClazzHeadmasterVo::getTeacherId).collect(Collectors.toList());
        List<TeacherVo> teacherVoByTeacherIds = baseDataService.getTeacherVoByTeacherIds(teacherIds);
        List<String> headmasters = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(teacherVoByTeacherIds)) {
            // 过滤出转出教师
            List<TeacherVo> teacherVos = new ArrayList<>();
            for (TeacherVo teacherVoByTeacherId : teacherVoByTeacherIds) {
                List<UserOrgVo> userOrgList = teacherVoByTeacherId.getUserVo().getUserOrgList();
                if (CollectionUtils.isNotEmpty(userOrgList)) {
                    for (UserOrgVo userOrgVo : userOrgList) {
                        if (screenBusinessBo.getOrganizationId().equals(userOrgVo.getOrganizationId())) {
                            teacherVos.add(teacherVoByTeacherId);
                        }
                    }
                }
            }
            headmasters =
                teacherVos.stream().map(teacherVo -> teacherVo.getUserVo().getRealName()).collect(Collectors.toList());
        }
        clazzVoExt.setHeadmasters(headmasters);
        return AjaxResult.success(clazzVoExt);
    }

    @Override
    public AjaxResult clazzInfoWithCache(ScreenBusinessBo screenBusinessBo) {
        String cacheKey = StringKit.getMessage(ConstantsRedisScreenIndex.CLAZZ_INFO,
            String.valueOf(screenBusinessBo.getOrganizationId()), String.valueOf(screenBusinessBo.getSpaceInfoId()));
        // 从缓存里面拉取数据
        if (redisComponent.hasKey(cacheKey)) {
            ClazzVoExt clazzVoExt = JSONObject.parseObject((String)redisComponent.get(cacheKey), ClazzVoExt.class);
            return AjaxResult.success(clazzVoExt);
        }
        // 缓存没有从数据库拉取数据，同时更新到缓存
        AjaxResult clazzVoExtAjaxResult = clazzInfo(screenBusinessBo);
        if (clazzVoExtAjaxResult.isSuccess() && clazzVoExtAjaxResult.getData() != null) {
            String value = JSONObject.toJSONString(clazzVoExtAjaxResult.getData());
            redisComponent.set(cacheKey, value, ConstantsRedisScreenIndex.CLAZZ_INFO_EXPIRE_IN);
        }
        return clazzVoExtAjaxResult;
    }

    /**
     * 根据学校id查询演示的课后延迟服务，废弃-20230411
     * 
     * @param organizationId
     * @return
     */
    @Deprecated
    private DelayDemoDto getDelayDemoDto(Long organizationId) {
        if (organizationId == null) {
            return null;
        }
        String cacheKey = StringUtils.join(ConstantsRedis.DEMO_DELAY_CACHE_PREFIX, organizationId);
        Object o = redisComponent.get(cacheKey);
        if (o != null) {
            return JSONObject.parseObject((String)o, DelayDemoDto.class);
        }
        return null;
    }

    /**
     * 根据学校id查询会议的课后延迟服务
     * 
     * @param organizationId
     * @return
     */
    private DelayDemoDto getMeetingDemoDto(Long organizationId) {
        if (organizationId == null) {
            return null;
        }
        String cacheKey = StringUtils.join(ConstantsRedis.DEMO_MEETING_CACHE_PREFIX, organizationId);
        Object o = redisComponent.get(cacheKey);
        if (o != null) {
            return JSONObject.parseObject((String)o, DelayDemoDto.class);
        }
        return null;
    }

    /**
     * 在返回的数据里添加当前场景（只给screenIndexVos添加当前场景，其他不动）
     * 
     * <AUTHOR>
     * @param screenIndexVos the screen scene index vos
     */
    private void calCurrentSceneToResult(List<ScreenIndexVo> screenIndexVos) {
        if (CollectionUtils.isEmpty(screenIndexVos)) {
            return;
        }
        Date now = new Date();

        // 获取当前时间在内的场景列表
        List<ScreenSceneVo> currentTimeScreenSceneVos = Lists.newArrayList();
        List<ScreenSceneVo> noTimeScreenSceneVos = Lists.newArrayList();
        for (ScreenIndexVo screenIndexVo : screenIndexVos) {
            if (CollectionUtils.isEmpty(screenIndexVo.getScreenSceneVos())) {
                continue;
            }
            for (ScreenSceneVo screenSceneVo : screenIndexVo.getScreenSceneVos()) {
                if (screenSceneVo.getStartTime() == null && screenSceneVo.getEndTime() == null) {
                    noTimeScreenSceneVos.add(screenSceneVo);
                } else if (screenSceneVo.getStartTime().before(now) && screenSceneVo.getEndTime().after(now)) {
                    currentTimeScreenSceneVos.add(screenSceneVo);
                }
            }
        }
        // 没有时间的场景加在当前时间场景集合的末尾
        currentTimeScreenSceneVos.addAll(noTimeScreenSceneVos);

        // 按照优先级排序(ASC-默认)
        if (CollectionUtils.isEmpty(currentTimeScreenSceneVos)) {
            return;
        }
        List<ScreenSceneVo> screenSceneVoOrderByPriority = currentTimeScreenSceneVos.stream()
            .sorted(Comparator.comparing(ScreenSceneVo::getScreenPriority)).collect(Collectors.toList());
        Integer screenPriority = screenSceneVoOrderByPriority.get(0).getScreenPriority();
        List<ScreenSceneVo> screenSceneVoOfFirstPriority =
            screenSceneVoOrderByPriority.stream().filter(screenSceneVo -> screenSceneVo.getScreenPriority() != null
                && screenSceneVo.getScreenPriority().equals(screenPriority)).collect(Collectors.toList());

        // 甄选出当前场景
        ScreenSceneVo currentScreenSceneVo = null;
        // 第一个优先级里面,当前有自定义时间的常规场景按照场景更新时间排序Desc
        List<ScreenSceneVo> screenSceneVoOrderByUpdateTimeDescCustom = screenSceneVoOfFirstPriority.stream()
            .filter(screenSceneVo -> screenSceneVo.getStartTime() != null && screenSceneVo.getEndTime() != null)
            .sorted(Comparator.comparing(ScreenSceneVo::getUpdateTime).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(screenSceneVoOrderByUpdateTimeDescCustom)) {
            currentScreenSceneVo = screenSceneVoOrderByUpdateTimeDescCustom.get(0);
        }
        // 第一个优先级里面,当前没有自定义时间的按照场景更新时间排序Desc
        if (currentScreenSceneVo == null) {
            List<ScreenSceneVo> screenSceneVoOrderByUpdateTimeDesc = screenSceneVoOfFirstPriority.stream()
                .filter(screenSceneVo -> (screenSceneVo.getStartTime() == null || screenSceneVo.getEndTime() == null))
                .sorted(Comparator.comparing(ScreenSceneVo::getUpdateTime).reversed()).collect(Collectors.toList());
            currentScreenSceneVo = screenSceneVoOrderByUpdateTimeDesc.get(0);
        }

        // 修改参数screenIndexVos里面的数据返回
        ScreenSceneVo currentScreenSceneVoFinal = currentScreenSceneVo;
        screenIndexVos.stream().filter(screenIndexVo -> screenIndexVo.getScreenPriority() != null
            && screenIndexVo.getScreenPriority().equals(screenPriority)).forEach(screenIndexVo -> {
                screenIndexVo.setCurrentScreenSceneVo(currentScreenSceneVoFinal);
                screenIndexVo.setScreenSceneVos(null);
            });
    }

    /**
     * 根据组织id和日期判断当前是否是上课日
     *
     * @param organization 组织id
     * @param date 日期
     * @return AjaxResult success 上课日，周几week。fail 不是上课日
     * <AUTHOR>
     */
    private AjaxResult getSchoolCalendarDaysByOrganization(Long organization, Date date) {
        // 联合校历，判断当前日期是否属于上课日
        Map<String, Object> schoolCalendarMap =
            schoolCalendarWeekService.getCacheWeekListAndDayListByOrganizationId(organization);
        List<SchoolCalendarWeekVo> weekVos = (List<SchoolCalendarWeekVo>)schoolCalendarMap.get("weekVos");
        List<SchoolCalendarDay> dayVos = (List<SchoolCalendarDay>)schoolCalendarMap.get("dayVos");
        if (org.apache.commons.collections.CollectionUtils.isEmpty(weekVos)) {
            return AjaxResult.fail("校历不存在");
        }
        // 不上课标志
        boolean notWorkFlag = false;
        Integer week = DateKit.getWeekIdByDate(date);
        Integer firstWeek = week;
        SchoolCalendarWeekVo schoolCalendarWeekVo =
            weekVos.stream().filter(weekVo -> weekVo.getWeek().equals(firstWeek)).findFirst().get();
        // 上课周不上课，true
        if (SchoolCalendarEnum.TYPE_IS.getValue() != schoolCalendarWeekVo.getType()) {
            notWorkFlag = true;
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dayVos)) {
            for (SchoolCalendarDay dayVo : dayVos) {
                // 考勤当天
                if (DateKit.checkOneDay(dayVo.getDay(), date)) {
                    // 不上课 true
                    if (SchoolCalendarEnum.TYPE_IS.getValue() != dayVo.getType()) {
                        notWorkFlag = true;
                    } else {
                        notWorkFlag = false;
                        week = dayVo.getWeek();
                    }
                    break;
                }
            }
        }
        if (notWorkFlag) {
            return AjaxResult.fail("不在考勤时间范围内");
        }
        return AjaxResult.success(week);
    }
}
