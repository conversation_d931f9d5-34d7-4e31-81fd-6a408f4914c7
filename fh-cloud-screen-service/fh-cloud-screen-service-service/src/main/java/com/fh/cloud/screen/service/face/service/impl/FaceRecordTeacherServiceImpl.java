package com.fh.cloud.screen.service.face.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.enums.FaceBrandType;
import com.fh.cloud.screen.service.enums.FaceStatusType;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordUploadResultVo;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;
import com.fh.cloud.screen.service.facebody.FaceBodyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.enums.FaceSourceType;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordTeacherDto;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.fh.cloud.screen.service.face.mapper.FaceRecordTeacherMapper;
import com.fh.cloud.screen.service.face.service.IFaceRecordTeacherService;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.StringUtils;
import com.light.user.teacher.entity.vo.TeacherVo;

/**
 * 教师人脸库接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
@Service
public class FaceRecordTeacherServiceImpl extends ServiceImpl<FaceRecordTeacherMapper, FaceRecordTeacherDto>
    implements IFaceRecordTeacherService {

    @Resource
    private FaceRecordTeacherMapper faceRecordTeacherMapper;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private IFaceConfigService faceConfigService;
    @Autowired
    private FaceBodyService faceBodyService;

    @Override
    public List<FaceRecordTeacherVo> getFaceRecordTeacherListByCondition(FaceRecordTeacherConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return faceRecordTeacherMapper.getFaceRecordTeacherListByCondition(condition);
    }

    @Override
    public AjaxResult addFaceRecordTeacher(FaceRecordTeacherBo faceRecordTeacherBo) {
        FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
        BeanUtils.copyProperties(faceRecordTeacherBo, faceRecordTeacher);
        faceRecordTeacher.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(faceRecordTeacher)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateFaceRecordTeacher(FaceRecordTeacherBo faceRecordTeacherBo) {
        FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
        BeanUtils.copyProperties(faceRecordTeacherBo, faceRecordTeacher);
        if (updateById(faceRecordTeacher)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateFaceRecordTeacherBatch(List<FaceRecordTeacherBo> faceRecordTeacherBoList) {
        List<FaceRecordTeacherDto> batchUpdateDtos = new ArrayList<>();
        for (FaceRecordTeacherBo faceRecordTeacherBo : faceRecordTeacherBoList) {
            FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
            BeanUtils.copyProperties(faceRecordTeacherBo, faceRecordTeacher);
            batchUpdateDtos.add(faceRecordTeacher);
        }
        if (updateBatchById(batchUpdateDtos)) {
            return AjaxResult.success("保存成功");
        }
        return AjaxResult.fail("保存失败");
    }

    @Override
    public FaceRecordTeacherVo getDetail(Long id) {
        FaceRecordTeacherConditionBo condition = new FaceRecordTeacherConditionBo();
        condition.setFaceRecordTeacherId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<FaceRecordTeacherVo> list = faceRecordTeacherMapper.getFaceRecordTeacherListByCondition(condition);
        FaceRecordTeacherVo vo = new FaceRecordTeacherVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public AjaxResult<FaceRecordUploadResultVo> upload(List<FaceRecordTeacherConditionBo> faceRecordTeacherConditionBos) {
        // 参数处理：校验->默认字段设置
        if (CollectionUtils.isEmpty(faceRecordTeacherConditionBos)) {
            return AjaxResult.success();
        }
        faceRecordTeacherConditionBos.forEach(faceRecordTeacherConditionBo -> {
            if (StringUtils.isNotBlank(faceRecordTeacherConditionBo.getFaceMediaNameOri())) {
                faceRecordTeacherConditionBo
                    .setFaceMediaName(StringUtils.substring(faceRecordTeacherConditionBo.getFaceMediaNameOri(), 0,
                        faceRecordTeacherConditionBo.getFaceMediaNameOri().indexOf(ConstString.dot)));
                if (StringUtils.isBlank(faceRecordTeacherConditionBo.getRealName())) {
                    faceRecordTeacherConditionBo.setRealName(faceRecordTeacherConditionBo.getFaceMediaName());
                }
            }
        });
        Long organizationId = faceRecordTeacherConditionBos.get(0).getOrganizationId();
        Integer sourceType = faceRecordTeacherConditionBos.get(0).getSourceType();

        // 封装导入db的数据
        List<FaceRecordTeacherDto> faceRecordTeacherDtos = Lists.newArrayList();
        List<FaceRecordTeacherVo> faceRecordTeacherVosSuccess = Lists.newArrayList();
        List<FaceRecordTeacherVo> faceRecordTeacherVosFail = Lists.newArrayList();
        if (sourceType.equals(FaceSourceType.IMPORT.getValue())) {
            // realName,FaceRecordTeacherConditionBo
            Map<String, FaceRecordTeacherConditionBo> faceRecordTeacherConditionBoMapRealName =
                faceRecordTeacherConditionBos.stream().filter(
                    faceRecordTeacherConditionBo -> StringUtils.isNotBlank(faceRecordTeacherConditionBo.getRealName()))
                    .collect(Collectors.toMap(FaceRecordTeacherConditionBo::getRealName, a -> a, (k1, k2) -> k1));
            List<String> realNameList = Lists.newArrayList(faceRecordTeacherConditionBoMapRealName.keySet());
            // realName，TeacherVo
            List<TeacherVo> teacherVosFromDB = baseDataService.getTeacherVoByOrgId(organizationId);
            Map<String,
                List<TeacherVo>> realNameTeacherVoMap = teacherVosFromDB.stream()
                    .filter(teacherVo -> teacherVo.getUserVo() != null
                        && StringUtils.isNotBlank(teacherVo.getUserVo().getRealName()))
                    .collect(Collectors.groupingBy(teacherVo -> teacherVo.getUserVo().getRealName()));
            // userOid， FaceRecordTeacherVo
            FaceRecordTeacherConditionBo faceRecordTeacherConditionBo = new FaceRecordTeacherConditionBo();
            faceRecordTeacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
            faceRecordTeacherConditionBo.setOrganizationId(organizationId);
            faceRecordTeacherConditionBo.setRealNames(realNameList);
            List<FaceRecordTeacherVo> faceRecordTeacherVos =
                getFaceRecordTeacherListByCondition(faceRecordTeacherConditionBo);
            Map<String,
                FaceRecordTeacherVo> userOidFaceRecordTeacherVoMap = faceRecordTeacherVos.stream()
                    .filter(faceRecordTeacherVo -> StringUtils.isNotBlank(faceRecordTeacherVo.getUserOid()))
                    .collect(Collectors.toMap(FaceRecordTeacherVo::getUserOid, a -> a, (k1, k2) -> k1));
            // 封装更新数据或者新增数据入待更新list
            for (String realName : realNameList) {
                FaceRecordTeacherVo tempResultVo= new FaceRecordTeacherVo();
                BeanUtils.copyProperties(faceRecordTeacherConditionBoMapRealName.get(realName),tempResultVo);
                if (!realNameTeacherVoMap.containsKey(realName)) {
                    faceRecordTeacherVosFail.add(tempResultVo);
                    continue;
                }
                faceRecordTeacherVosSuccess.add(tempResultVo);
                List<TeacherVo> teacherVosTemp = realNameTeacherVoMap.get(realName);
                for (TeacherVo teacherVo : teacherVosTemp) {
                    // 该userOid的人脸数据已经有了则更新
                    if (userOidFaceRecordTeacherVoMap.containsKey(teacherVo.getUserOid())) {
                        FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
                        FaceRecordTeacherVo faceRecordTeacherVoTemp =
                            userOidFaceRecordTeacherVoMap.get(teacherVo.getUserOid());
                        FaceRecordTeacherConditionBo recordTeacherConditionBo =
                            faceRecordTeacherConditionBoMapRealName.get(realName);
                        recordTeacherConditionBo
                            .setFaceRecordTeacherId(faceRecordTeacherVoTemp.getFaceRecordTeacherId());
                        recordTeacherConditionBo.setFaceStatus(FaceStatusType.UPLOAD_ING.getValue());
                        BeanUtils.copyProperties(recordTeacherConditionBo, faceRecordTeacher);
                        faceRecordTeacher.setUpdateTime(new Date());
                        faceRecordTeacherDtos.add(faceRecordTeacher);
                    } else {
                        FaceRecordTeacherConditionBo faceRecordTeacherConditionBoAdd =
                            faceRecordTeacherConditionBoMapRealName.get(realName);
                        faceRecordTeacherConditionBoAdd.setUserOid(teacherVo.getUserOid());
                        FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
                        BeanUtils.copyProperties(faceRecordTeacherConditionBoAdd, faceRecordTeacher);
                        faceRecordTeacherDtos.add(faceRecordTeacher);
                    }
                }
            }
        }
        if (sourceType.equals(FaceSourceType.SINGLE.getValue())) {
            FaceRecordTeacherConditionBo faceRecordTeacherConditionBo = faceRecordTeacherConditionBos.get(0);
            String userOid = faceRecordTeacherConditionBo.getUserOid();
            FaceRecordTeacherConditionBo faceRecordTeacherConditionBoQuery = new FaceRecordTeacherConditionBo();
            faceRecordTeacherConditionBoQuery.setPageNo(SystemConstants.NO_PAGE);
            faceRecordTeacherConditionBoQuery.setOrganizationId(organizationId);
            faceRecordTeacherConditionBoQuery.setUserOid(userOid);
            List<FaceRecordTeacherVo> faceRecordTeacherVos =
                getFaceRecordTeacherListByCondition(faceRecordTeacherConditionBoQuery);
            if (CollectionUtils.isNotEmpty(faceRecordTeacherVos)) {
                FaceRecordTeacherVo faceRecordTeacherVoTemp = faceRecordTeacherVos.get(0);
                FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
                FaceRecordTeacherConditionBo recordTeacherConditionBo = faceRecordTeacherConditionBo;
                recordTeacherConditionBo.setFaceRecordTeacherId(faceRecordTeacherVoTemp.getFaceRecordTeacherId());
                // 设置状态为上传中
                recordTeacherConditionBo.setFaceStatus(FaceStatusType.UPLOAD_ING.getValue());
                BeanUtils.copyProperties(recordTeacherConditionBo, faceRecordTeacher);
                faceRecordTeacher.setUpdateTime(new Date());
                faceRecordTeacherDtos.add(faceRecordTeacher);
            } else {
                FaceRecordTeacherConditionBo faceRecordTeacherConditionBoAdd = faceRecordTeacherConditionBo;
                FaceRecordTeacherDto faceRecordTeacher = new FaceRecordTeacherDto();
                BeanUtils.copyProperties(faceRecordTeacherConditionBoAdd, faceRecordTeacher);
                faceRecordTeacherDtos.add(faceRecordTeacher);
            }
        }

        // 返回的结果
        FaceRecordUploadResultVo faceRecordUploadResultVo= new FaceRecordUploadResultVo();
        faceRecordUploadResultVo.setFaceRecordTeacherVosSuccess(faceRecordTeacherVosSuccess);
        faceRecordUploadResultVo.setFaceRecordTeacherVosFail(faceRecordTeacherVosFail);
        if(CollectionUtils.isEmpty(faceRecordTeacherDtos)){
            return AjaxResult.success(faceRecordUploadResultVo);
        }
        // 导入数据
        saveOrUpdateBatch(faceRecordTeacherDtos);

        // 阿里云人脸库建模
        FaceConfigVo faceConfigVo = faceConfigService.getDetailByOrganizationId(organizationId);
        if (faceConfigVo != null) {
            if (faceConfigVo.getFaceBrandType() != null
                && faceConfigVo.getFaceBrandType().equals(FaceBrandType.ALI.getValue())) {
                faceRecordTeacherDtos.forEach(
                    faceRecordTeacherDto -> faceBodyService.addFaceTransaction(faceRecordTeacherDto.getUserOid(),
                        faceRecordTeacherDto.getFaceMediaUrl(), faceRecordTeacherDto.getRealName()));
            }
        }

        // 返回
        return AjaxResult.success(faceRecordUploadResultVo);
    }

    @Override
    public AjaxResult updateRealNameByUserOid(String userOid, String realName) {
        if (StringUtils.isBlank(userOid) || StringUtils.isBlank(realName)) {
            return AjaxResult.fail("参数不允许为空");
        }
        faceRecordTeacherMapper.updateRealNameByUserOid(userOid, realName);
        return AjaxResult.success();
    }
}
