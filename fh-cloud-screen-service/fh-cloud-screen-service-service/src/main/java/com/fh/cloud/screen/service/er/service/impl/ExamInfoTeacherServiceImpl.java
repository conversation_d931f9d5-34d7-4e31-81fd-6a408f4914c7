package com.fh.cloud.screen.service.er.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherConditionBo;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoTeacherDto;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;
import com.fh.cloud.screen.service.er.mapper.ExamInfoTeacherMapper;
import com.fh.cloud.screen.service.er.service.IExamInfoTeacherService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 考场_考试计划里面一次考试的老师接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Service
public class ExamInfoTeacherServiceImpl extends ServiceImpl<ExamInfoTeacherMapper, ExamInfoTeacherDto>
    implements IExamInfoTeacherService {

    @Resource
    private ExamInfoTeacherMapper examInfoTeacherMapper;
    @Autowired
    private IExamInfoTeacherService examInfoTeacherService;

    @Override
    public List<ExamInfoTeacherVo> getExamInfoTeacherListByCondition(ExamInfoTeacherConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return examInfoTeacherMapper.getExamInfoTeacherListByCondition(condition);
    }

    @Override
    public AjaxResult addExamInfoTeacher(ExamInfoTeacherBo examInfoTeacherBo) {
        ExamInfoTeacherDto examInfoTeacher = new ExamInfoTeacherDto();
        BeanUtils.copyProperties(examInfoTeacherBo, examInfoTeacher);
        examInfoTeacher.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(examInfoTeacher)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamInfoTeacher(ExamInfoTeacherBo examInfoTeacherBo) {
        ExamInfoTeacherDto examInfoTeacher = new ExamInfoTeacherDto();
        BeanUtils.copyProperties(examInfoTeacherBo, examInfoTeacher);
        if (updateById(examInfoTeacher)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ExamInfoTeacherVo getDetail(Long id) {
        ExamInfoTeacherConditionBo condition = new ExamInfoTeacherConditionBo();
        condition.setExamInfoTeacherId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoTeacherVo> list = examInfoTeacherMapper.getExamInfoTeacherListByCondition(condition);
        ExamInfoTeacherVo vo = new ExamInfoTeacherVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public AjaxResult addExamInfoTeacherBatch(List<ExamInfoTeacherBo> examInfoTeacherBos) {
        if (CollectionUtils.isEmpty(examInfoTeacherBos)) {
            return AjaxResult.success();
        }
        List<ExamInfoTeacherDto> examInfoTeacherDtos = examInfoTeacherBos.stream().map(examInfoTeacherBo -> {
            ExamInfoTeacherDto examInfoTeacher = new ExamInfoTeacherDto();
            BeanUtils.copyProperties(examInfoTeacherBo, examInfoTeacher);
            examInfoTeacher.setIsDelete(StatusEnum.NOTDELETE.getCode());
            examInfoTeacher.setExamInfoTeacherId(null);
            return examInfoTeacher;
        }).collect(Collectors.toList());

        boolean saveBatch = saveBatch(examInfoTeacherDtos);
        if (saveBatch) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 批量添加
     *
     * @param examInfoTeacherBos
     * @return
     */
    @Override
    public AjaxResult addExamInfoTeacherBatchByXML(List<ExamInfoTeacherBo> examInfoTeacherBos) {
        if (CollectionUtils.isEmpty(examInfoTeacherBos)) {
            return AjaxResult.success();
        }
        examInfoTeacherMapper.addExamInfoTeacherBatchByXML(examInfoTeacherBos);
        return AjaxResult.success("保存成功");
    }

    @Override
    public AjaxResult updateExamInfoTeacherBatch(List<ExamInfoTeacherBo> examInfoTeacherBos) {
        // 删除
        if (CollectionUtils.isEmpty(examInfoTeacherBos)) {
            return AjaxResult.success();
        }
        Long examInfoId = examInfoTeacherBos.get(0).getExamInfoId();
        LambdaUpdateWrapper<ExamInfoTeacherDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExamInfoTeacherDto::getExamInfoId, examInfoId);
        updateWrapper.eq(ExamInfoTeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ExamInfoTeacherDto examInfoTeacherDto = new ExamInfoTeacherDto();
        examInfoTeacherDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        examInfoTeacherMapper.update(examInfoTeacherDto, updateWrapper);

        // 新增
        AjaxResult ajaxResult = examInfoTeacherService.addExamInfoTeacherBatch(examInfoTeacherBos);
        return ajaxResult;
    }

    @Override
    public AjaxResult deleteExamInfoTeacherBatch(Long examInfoId) {
        if (examInfoId == null) {
            return AjaxResult.success();
        }

        LambdaUpdateWrapper<ExamInfoTeacherDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExamInfoTeacherDto::getExamInfoId, examInfoId);
        updateWrapper.eq(ExamInfoTeacherDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ExamInfoTeacherDto examInfoTeacherDto = new ExamInfoTeacherDto();
        examInfoTeacherDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        examInfoTeacherMapper.update(examInfoTeacherDto, updateWrapper);
        return AjaxResult.success();
    }
}