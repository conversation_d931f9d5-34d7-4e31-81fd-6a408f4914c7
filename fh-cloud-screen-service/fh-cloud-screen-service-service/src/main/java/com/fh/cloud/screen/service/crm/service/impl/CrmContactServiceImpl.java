package com.fh.cloud.screen.service.crm.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.nacos.common.utils.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactConditionBo;
import com.fh.cloud.screen.service.crm.entity.dto.CrmContactDto;
import com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo;
import com.fh.cloud.screen.service.crm.mapper.CrmContactMapper;
import com.fh.cloud.screen.service.crm.service.ICrmContactService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * CRM商讯联系人表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@Service
public class CrmContactServiceImpl extends ServiceImpl<CrmContactMapper, CrmContactDto> implements ICrmContactService {

    @Resource
    private CrmContactMapper crmContactMapper;

    @Override
    public List<CrmContactVo> getCrmContactListByCondition(CrmContactConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return crmContactMapper.getCrmContactListByCondition(condition);
    }

    @Override
    public AjaxResult addCrmContact(CrmContactBo crmContactBo) {
        CrmContactDto crmContact = new CrmContactDto();
        BeanUtils.copyProperties(crmContactBo, crmContact);
        crmContact.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(crmContact)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateCrmContact(CrmContactBo crmContactBo) {
        CrmContactDto crmContact = new CrmContactDto();
        BeanUtils.copyProperties(crmContactBo, crmContact);
        if (updateById(crmContact)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public CrmContactVo getCrmContactByCondition(CrmContactConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        CrmContactVo vo = crmContactMapper.getCrmContactByCondition(condition);
        return vo;
    }

    @Override
    public AjaxResult addCrmContactBatch(Long crmInfoId, List<CrmContactBo> crmContactBoList) {
        if (CollectionUtils.isEmpty(crmContactBoList)) {
            return AjaxResult.success();
        }

        List<CrmContactDto> crmContactDtos = crmContactBoList.stream().map(crmContactBo -> {
            CrmContactDto crmContact = new CrmContactDto();
            BeanUtils.copyProperties(crmContactBo, crmContact);
            crmContact.setIsDelete(StatusEnum.NOTDELETE.getCode());
            crmContact.setCrmInfoId(crmInfoId);
            return crmContact;
        }).collect(Collectors.toList());
        return saveBatch(crmContactDtos) ? AjaxResult.success() : AjaxResult.fail("保存失败");
    }
}