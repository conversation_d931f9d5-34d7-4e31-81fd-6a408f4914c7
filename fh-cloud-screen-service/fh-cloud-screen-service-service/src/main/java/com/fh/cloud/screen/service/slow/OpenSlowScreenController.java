package com.fh.cloud.screen.service.slow;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.dto.UserCard;
import com.fh.cloud.screen.service.card.entity.vo.UserCardVo;
import com.fh.cloud.screen.service.card.service.IUserCardService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.DeviceStatusType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordStudentDto;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordTeacherDto;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.fh.cloud.screen.service.face.service.IFaceRecordStudentService;
import com.fh.cloud.screen.service.face.service.IFaceRecordTeacherService;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneThirdService;
import com.fh.cloud.screen.service.slow.api.OpenSlowScreenApi;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.dto.SyllabusInfoDto;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.fh.cloud.screen.service.syllabus.service.ISyllabusInfoService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.campus.entity.vo.CampusVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 开放平台使用的controller
 * 
 * <AUTHOR>
 * @date 2023/3/29 19:16
 */
@RestController
public class OpenSlowScreenController implements OpenSlowScreenApi {
    @Autowired
    private ISpaceInfoService spaceInfoService;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Autowired
    private IClassesInfoService classesInfoService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private IScreenSceneThirdService screenSceneThirdService;
    @Autowired
    private IFaceRecordStudentService faceRecordStudentService;
    @Autowired
    private IFaceRecordTeacherService faceRecordTeacherService;
    @Autowired
    private IUserCardService userCardService;
    @Autowired
    private ISyllabusInfoService syllabusInfoService;

    @Override
    public AjaxResult<PageInfo<SpaceInfoVo>> querySpacePageList(SpaceInfoListConditionBo condition) {
        // 给最大的数据权限
        condition.setQueryType(ConstantsInteger.APP_QUERY);
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(ConstantsInteger.NUM_2);
        map.put("total", null);
        map.put("list", null);

        if (SpaceGroupUseType.XZ.getValue() == condition.getSpaceGroupUseType()) {
            Map<String, Object> classesInfoMap = new HashMap<>();
            // feign的分页查询无需调用PageHelper.startPage
            // PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            ClassesInfoListConditionBo classesInfoListConditionBo = new ClassesInfoListConditionBo();
            BeanUtils.copyProperties(condition, classesInfoListConditionBo);
            classesInfoMap = classesInfoService.getClassesInfoListByConditionSlow(classesInfoListConditionBo);
            map.put("total", classesInfoMap.get("total"));
            map.put("list", classesInfoMap.get("list"));
        } else if (SpaceGroupUseType.NOT_XZ.getValue() == condition.getSpaceGroupUseType()) {
            PageInfo<SpaceInfoVo> pageInfo = new PageInfo<>();
            List<SpaceInfoVo> list = new ArrayList<>();
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            List<SpaceInfoVo> spaceInfoListByConditionSlow =
                spaceInfoService.getSpaceInfoListByConditionSlow(condition);
            pageInfo = new PageInfo<>(spaceInfoListByConditionSlow);
            list = JSONObject.parseArray(JSONObject.toJSONString(pageInfo.getList()), SpaceInfoVo.class);
            if (CollectionUtils.isNotEmpty(list)) {
                AjaxResult campusResult =
                    baseDataService.getCampusListByOrganizationId(list.get(0).getOrganizationId());
                if (campusResult != null && !campusResult.isFail()) {
                    Map<String, Object> resultMap = (Map<String, Object>)campusResult.getData();
                    List<CampusVo> campusVos =
                        JSONObject.parseArray(JSONObject.toJSONString(resultMap.get("list")), CampusVo.class);
                    for (CampusVo campusVo : campusVos) {
                        for (SpaceInfoVo spaceInfoVo : list) {
                            if (campusVo.getId().equals(spaceInfoVo.getCampusId())) {
                                spaceInfoVo.setCampusName(campusVo.getName());
                            }
                        }
                    }
                }
            }
            map.put("total", pageInfo.getTotal());
            map.put("list", list);
        }
        if (!condition.isQuerySpaceWithDevice()) {
            return AjaxResult.success(map);
        }

        // 查询设备
        List<SpaceInfoVo> list = (List<SpaceInfoVo>)map.get("list");
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(map);
        }
        List<ShowDeviceVo> showDeviceVos =
            showDeviceService.listShowDeviceBindByOrganizationId(condition.getOrganizationId());
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(map);
        }
        // 设备异常状态检测
        List<String> showDeviceNumbers = messageService.listOnLineDeviceNumber(condition.getOrganizationId());
        showDeviceVos.stream().forEach(showDeviceVo -> {
            if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            }
        });
        Map<String, List<ShowDeviceVo>> spaceDeviceMap = showDeviceVos.stream().collect(Collectors.groupingBy(
            showDeviceVo -> showDeviceVo.getSpaceInfoId() + ConstString.jh + showDeviceVo.getSpaceGroupUseType()));
        for (SpaceInfoVo spaceInfoVo : list) {
            if (spaceInfoVo.getSpaceInfoId() == null) {
                continue;
            }
            String key = spaceInfoVo.getSpaceInfoId() + ConstString.jh + spaceInfoVo.getSpaceGroupUseType();
            spaceInfoVo.setShowDeviceVoList(spaceDeviceMap.get(key));
        }
        return AjaxResult.success(map);

    }

    @Override
    public AjaxResult<List<SpaceInfoVo>> querySpaceList(SpaceInfoListConditionBo condition) {
        // 给最大的数据权限
        condition.setQueryType(ConstantsInteger.APP_QUERY);
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(ConstantsInteger.NUM_2);
        map.put("total", null);
        map.put("list", null);

        if (SpaceGroupUseType.XZ.getValue() == condition.getSpaceGroupUseType()) {
            Map<String, Object> classesInfoMap = new HashMap<>();
            ClassesInfoListConditionBo classesInfoListConditionBo = new ClassesInfoListConditionBo();
            BeanUtils.copyProperties(condition, classesInfoListConditionBo);
            classesInfoMap = classesInfoService.getClassesInfoListByConditionSlow(classesInfoListConditionBo);
            map.put("total", classesInfoMap.get("total"));
            map.put("list", classesInfoMap.get("list"));
        } else if (SpaceGroupUseType.NOT_XZ.getValue() == condition.getSpaceGroupUseType()) {
            PageInfo<SpaceInfoVo> pageInfo = new PageInfo<>();
            List<SpaceInfoVo> list = new ArrayList<>();
            List<SpaceInfoVo> spaceInfoListByConditionSlow =
                spaceInfoService.getSpaceInfoListByConditionSlow(condition);
            pageInfo = new PageInfo<>(spaceInfoListByConditionSlow);
            list = JSONObject.parseArray(JSONObject.toJSONString(pageInfo.getList()), SpaceInfoVo.class);
            if (CollectionUtils.isNotEmpty(list)) {
                AjaxResult campusResult =
                    baseDataService.getCampusListByOrganizationId(list.get(0).getOrganizationId());
                if (campusResult != null && !campusResult.isFail()) {
                    Map<String, Object> resultMap = (Map<String, Object>)campusResult.getData();
                    List<CampusVo> campusVos =
                        JSONObject.parseArray(JSONObject.toJSONString(resultMap.get("list")), CampusVo.class);
                    for (CampusVo campusVo : campusVos) {
                        for (SpaceInfoVo spaceInfoVo : list) {
                            if (campusVo.getId().equals(spaceInfoVo.getCampusId())) {
                                spaceInfoVo.setCampusName(campusVo.getName());
                            }
                        }
                    }
                }
            }
            map.put("total", pageInfo.getTotal());
            map.put("list", list);
        }
        if (!condition.isQuerySpaceWithDevice()) {
            return AjaxResult.success(map);
        }

        // 查询设备
        List<SpaceInfoVo> list = (List<SpaceInfoVo>)map.get("list");
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(map);
        }
        List<ShowDeviceVo> showDeviceVos =
            showDeviceService.listShowDeviceBindByOrganizationId(condition.getOrganizationId());
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(map);
        }
        // 设备异常状态检测
        List<String> showDeviceNumbers = messageService.listOnLineDeviceNumber(condition.getOrganizationId());
        showDeviceVos.stream().forEach(showDeviceVo -> {
            if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            }
        });
        Map<String, List<ShowDeviceVo>> spaceDeviceMap = showDeviceVos.stream().collect(Collectors.groupingBy(
            showDeviceVo -> showDeviceVo.getSpaceInfoId() + ConstString.jh + showDeviceVo.getSpaceGroupUseType()));
        for (SpaceInfoVo spaceInfoVo : list) {
            if (spaceInfoVo.getSpaceInfoId() == null) {
                continue;
            }
            String key = spaceInfoVo.getSpaceInfoId() + ConstString.jh + spaceInfoVo.getSpaceGroupUseType();
            spaceInfoVo.setShowDeviceVoList(spaceDeviceMap.get(key));
        }
        return AjaxResult.success(map);

    }

    @Override
    public AjaxResult<PageInfo<ScreenSceneThirdVo>> queryThirdScenePageList(ScreenSceneThirdConditionBo condition) {
        final Page<OrganizationVo> page =
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        screenSceneThirdService.getScreenSceneThirdListByCondition(condition);
        return AjaxResult.success(page.toPageInfo());
    }

    @Override
    public AjaxResult<List<ScreenSceneThirdVo>> queryThirdSceneList(ScreenSceneThirdConditionBo condition) {
        return AjaxResult.success(screenSceneThirdService.getScreenSceneThirdListByCondition(condition));
    }

    @Override
    public AjaxResult<String> syncFaceRecordsStudent(List<FaceRecordStudentBo> faceRecordStudentBoList) {
        if (CollectionUtils.isEmpty(faceRecordStudentBoList)) {
            return AjaxResult.success();
        }
        Long organizationId = faceRecordStudentBoList.get(0).getOrganizationId();
        // 已存在的数据
        FaceRecordStudentConditionBo faceRecordStudentConditionBo = new FaceRecordStudentConditionBo();
        faceRecordStudentConditionBo.setOrganizationId(organizationId);
        List<FaceRecordStudentVo> faceRecordStudentListExist =
            faceRecordStudentService.getFaceRecordStudentListByCondition(faceRecordStudentConditionBo);
        Map<String, FaceRecordStudentVo> faceRecordStudentExistMap = faceRecordStudentListExist.stream()
            .collect(Collectors.toMap(FaceRecordStudentVo::getUserOid, Function.identity(), (k1, k2) -> k2));

        // 需要修改或更新的数据
        List<FaceRecordStudentDto> saveDtos = Lists.newArrayList();
        for (FaceRecordStudentBo faceRecordStudentBo : faceRecordStudentBoList) {
            String userOid = faceRecordStudentBo.getUserOid();
            FaceRecordStudentDto faceRecordStudentDto = new FaceRecordStudentDto();
            if (faceRecordStudentExistMap.containsKey(userOid)) {
                FaceRecordStudentVo faceRecordStudentVo = faceRecordStudentExistMap.get(userOid);
                if (StringUtils.isNotBlank(faceRecordStudentBo.getRealName())
                    && faceRecordStudentBo.getRealName().equals(faceRecordStudentVo.getRealName())
                    && StringUtils.isNotBlank(faceRecordStudentBo.getFaceMediaUrl())
                    && faceRecordStudentBo.getFaceMediaUrl().equals(faceRecordStudentVo.getFaceMediaUrl())) {
                    continue;
                }
                if (!faceRecordStudentBo.getFaceMediaUrl().equals(faceRecordStudentVo.getFaceMediaUrl())) {
                    faceRecordStudentBo.setFaceMediaId(UUID.fastUUID().toString());
                }
                BeanUtils.copyProperties(faceRecordStudentVo, faceRecordStudentDto);
            } else {
                faceRecordStudentBo.setFaceMediaId(UUID.fastUUID().toString());
                BeanUtils.copyProperties(faceRecordStudentBo, faceRecordStudentDto);
            }
            saveDtos.add(faceRecordStudentDto);
        }
        if (CollectionUtils.isEmpty(saveDtos)) {
            return AjaxResult.success(true);
        }
        boolean result = faceRecordStudentService.saveOrUpdateBatch(saveDtos);
        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult<String> syncFaceRecordsTeacher(List<FaceRecordTeacherBo> faceRecordTeacherBoList) {
        if (CollectionUtils.isEmpty(faceRecordTeacherBoList)) {
            return AjaxResult.success();
        }
        Long organizationId = faceRecordTeacherBoList.get(0).getOrganizationId();
        // 已存在的数据
        FaceRecordTeacherConditionBo faceRecordTeacherConditionBo = new FaceRecordTeacherConditionBo();
        faceRecordTeacherConditionBo.setOrganizationId(organizationId);
        List<FaceRecordTeacherVo> faceRecordTeacherListExist =
            faceRecordTeacherService.getFaceRecordTeacherListByCondition(faceRecordTeacherConditionBo);
        Map<String, FaceRecordTeacherVo> faceRecordTeacherExistMap = faceRecordTeacherListExist.stream()
            .collect(Collectors.toMap(FaceRecordTeacherVo::getUserOid, Function.identity(), (k1, k2) -> k2));

        // 需要修改或更新的数据
        List<FaceRecordTeacherDto> saveDtos = Lists.newArrayList();
        for (FaceRecordTeacherBo faceRecordTeacherBo : faceRecordTeacherBoList) {
            String userOid = faceRecordTeacherBo.getUserOid();
            FaceRecordTeacherDto faceRecordTeacherDto = new FaceRecordTeacherDto();
            if (faceRecordTeacherExistMap.containsKey(userOid)) {
                FaceRecordTeacherVo faceRecordTeacherVo = faceRecordTeacherExistMap.get(userOid);
                if (StringUtils.isNotBlank(faceRecordTeacherBo.getRealName())
                    && faceRecordTeacherBo.getRealName().equals(faceRecordTeacherVo.getRealName())
                    && StringUtils.isNotBlank(faceRecordTeacherBo.getFaceMediaUrl())
                    && faceRecordTeacherBo.getFaceMediaUrl().equals(faceRecordTeacherVo.getFaceMediaUrl())) {
                    continue;
                }
                if (!faceRecordTeacherBo.getFaceMediaUrl().equals(faceRecordTeacherVo.getFaceMediaUrl())) {
                    faceRecordTeacherBo.setFaceMediaId(UUID.fastUUID().toString());
                }
                BeanUtils.copyProperties(faceRecordTeacherVo, faceRecordTeacherDto);
            } else {
                faceRecordTeacherBo.setFaceMediaId(UUID.fastUUID().toString());
                BeanUtils.copyProperties(faceRecordTeacherBo, faceRecordTeacherDto);
            }
            saveDtos.add(faceRecordTeacherDto);
        }
        if (CollectionUtils.isEmpty(saveDtos)) {
            return AjaxResult.success(true);
        }
        boolean result = faceRecordTeacherService.saveOrUpdateBatch(saveDtos);
        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult<String> syncCards(List<UserCardBo> userCardBos) {
        if (CollectionUtils.isEmpty(userCardBos)) {
            return AjaxResult.success();
        }
        UserCardListConditionBo userCardListConditionBo = new UserCardListConditionBo();
        userCardListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        userCardListConditionBo.setPageNo(SystemConstants.NO_PAGE);
        List<UserCardVo> userCardVos = userCardService.getUserCardListByCondition(userCardListConditionBo);
        // （userOid,UserCardVo）
        Map<String, UserCardVo> userCardVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userCardVos)) {
            userCardVoMap = userCardVos.stream()
                .collect(Collectors.toMap(UserCardVo::getUserOid, Function.identity(), (k1, k2) -> k2));
        }

        List<UserCard> userCardListBatchSaveOrUpdate = Lists.newArrayList();
        for (UserCardBo userCardBo : userCardBos) {
            if (StringUtils.isBlank(userCardBo.getUserOid())) {
                continue;
            }
            UserCard userCard = new UserCard();
            if (userCardVoMap.containsKey(userCardBo.getUserOid())) {
                UserCardVo userCardVo = userCardVoMap.get(userCardBo.getUserOid());
                BeanUtils.copyProperties(userCardVo, userCard);
            } else {
                BeanUtils.copyProperties(userCardBo, userCard);
            }
            userCardListBatchSaveOrUpdate.add(userCard);
        }
        if (CollectionUtils.isEmpty(userCardListBatchSaveOrUpdate)) {
            return AjaxResult.success(true);
        }
        boolean result = userCardService.saveOrUpdateBatch(userCardListBatchSaveOrUpdate);
        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult<String> syncSyllabus(List<SyllabusInfoBo> syllabusInfoBos) {
        if (CollectionUtils.isEmpty(syllabusInfoBos)) {
            return AjaxResult.success(true);
        }

        Long organizationId = syllabusInfoBos.get(0).getOrganizationId();
        // 先查询这个学校的课表
        SyllabusInfoConditionBo syllabusInfoConditionBo = new SyllabusInfoConditionBo();
        syllabusInfoConditionBo.setPageNo(SystemConstants.NO_PAGE);
        syllabusInfoConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        syllabusInfoConditionBo.setOrganizationId(organizationId);
        syllabusInfoConditionBo.setStatus(StatusEnum.ENABLE.getCode());
        List<SyllabusInfoVo> syllabusInfoVos =
            syllabusInfoService.getSyllabusInfoListByCondition(syllabusInfoConditionBo);
        // 本校已有课表的map（classesName#weekId#sort,SyllabusInfoVo）
        Map<String, SyllabusInfoVo> syllabusInfoVoMap = syllabusInfoVos.stream()
            .collect(Collectors.toMap(syllabusInfoVo -> syllabusInfoVo.getClassesName() + ConstString.jh
                + syllabusInfoVo.getWeekId() + ConstString.jh + syllabusInfoVo.getSort(), Function.identity(),
                (k1, k2) -> k2));

        List<SyllabusInfoDto> syllabusInfoDtos = Lists.newArrayList();
        for (SyllabusInfoBo syllabusInfoBo : syllabusInfoBos) {
            if (StringUtils.isBlank(syllabusInfoBo.getClassesName()) || syllabusInfoBo.getWeekId() == null
                || syllabusInfoBo.getSort() == null) {
                continue;
            }
            String key = syllabusInfoBo.getClassesName() + ConstString.jh + syllabusInfoBo.getWeekId() + ConstString.jh
                + syllabusInfoBo.getSort();

            SyllabusInfoDto syllabusInfoDto = new SyllabusInfoDto();
            if (syllabusInfoVoMap.containsKey(key)) {
                SyllabusInfoVo syllabusInfoVo = syllabusInfoVoMap.get(key);
                syllabusInfoBo.setSyllabusId(syllabusInfoVo.getSyllabusId());
                BeanUtils.copyProperties(syllabusInfoBo, syllabusInfoDto);
            } else {
                BeanUtils.copyProperties(syllabusInfoBo, syllabusInfoDto);
            }
            syllabusInfoDtos.add(syllabusInfoDto);
        }
        if (CollectionUtils.isEmpty(syllabusInfoDtos)) {
            return AjaxResult.success(true);
        }
        boolean result = syllabusInfoService.saveOrUpdateBatch(syllabusInfoDtos);
        return AjaxResult.success(result);
    }
}
