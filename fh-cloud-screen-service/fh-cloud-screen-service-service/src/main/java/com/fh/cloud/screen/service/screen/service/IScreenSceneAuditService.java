package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 云屏场景审核表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
public interface IScreenSceneAuditService extends IService<ScreenSceneAuditDto> {

    List<ScreenSceneAuditVo> getScreenSceneAuditListByCondition(ScreenSceneAuditConditionBo condition);

	AjaxResult addScreenSceneAudit(ScreenSceneAuditBo screenSceneAuditBo);

	AjaxResult updateScreenSceneAudit(ScreenSceneAuditBo screenSceneAuditBo);

	ScreenSceneAuditVo getScreenSceneAuditByCondition(ScreenSceneAuditConditionBo condition);

	/**
	 * 新增待审核记录并删除之前待审核的记录
	 *
	 * @param screenSceneBo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/11/30 11:32
	 **/
	boolean addAndDeleteScreenSceneAudit(ScreenSceneBo screenSceneBo);

	/**
	 * 审核
	 *
	 * @param screenSceneAuditBo
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/11/30 14:57
	 **/
	AjaxResult audit(ScreenSceneAuditBo screenSceneAuditBo);
	
	/**
	 * 待发布云屏数据
	 *
	 * @param showDeviceId
	 * @return com.light.core.entity.AjaxResult 
	 * <AUTHOR>
	 * @date 2023/11/30 16:22
	 **/
	AjaxResult screenToAudit(Long screenSceneAuditId);

	/**
	 * 获取审核数量
	 *
	 * @param condition
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/12/1 10:11
	 **/
	AjaxResult getScreenSceneAuditCount(ScreenSceneAuditConditionBo condition);

}

