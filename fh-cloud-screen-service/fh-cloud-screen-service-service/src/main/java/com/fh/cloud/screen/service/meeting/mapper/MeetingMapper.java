package com.fh.cloud.screen.service.meeting.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;

/**
 * 会议表Mapper
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
public interface MeetingMapper extends BaseMapper<MeetingDto> {

    List<MeetingVo> getMeetingListByCondition(MeetingConditionBo condition);

    List<MeetingVo> getMeetingRelationList(MeetingConditionBo condition);
}
