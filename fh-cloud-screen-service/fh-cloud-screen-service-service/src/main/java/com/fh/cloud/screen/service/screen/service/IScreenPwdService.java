package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPwd;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo;

import java.util.List;
import java.util.Map;

/**
 * 云屏密码接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IScreenPwdService extends IService<ScreenPwd> {

    List<ScreenPwdVo> getScreenPwdListByCondition(ScreenPwdListConditionBo condition);

    boolean addScreenPwd(ScreenPwdBo screenPwdBo);

    boolean updateScreenPwd(ScreenPwdBo screenPwdBo);

    Map<String, Object> getDetail(Long screenPwdId);

    Object getByOrgId(Long orgId);
}
