package com.fh.cloud.screen.service.rabbitmq.constant;

/**
 * <AUTHOR>
 */
public interface UserRabbitConstant {

    /**
     * 用户交换机 （也可以到更细粒度 ：学生交换机 老师交换机）
     */
    public final static String USER_EXCHANGE = "user.exchange";

    /**
     * 学生更新 queue 事件
     */
    public final static String STUDENT_UPDATE_QUEUE = "student.update.queue";

    /**
     * 学生新增 queue 事件
     */
    public final static String STUDENT_ADD_QUEUE = "student.add.queue";


    /**
     * 老师更新 queue 事件
     */
    public final static String TEACHER_UPDATE_QUEUE = "teacher.update.queue";

    /**
     * 老师新增 queue 事件
     */
    public final static String TEACHER_ADD_QUEUE = "teacher.add.queue";
}
