package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryCollectDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 海报收藏表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
public interface IScreenModuleLibraryCollectService extends IService<ScreenModuleLibraryCollectDto> {

    List<ScreenModuleLibraryCollectVo>
        getScreenModuleLibraryCollectListByCondition(ScreenModuleLibraryCollectConditionBo condition);

    AjaxResult addScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo);

    AjaxResult updateScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo);

    ScreenModuleLibraryCollectVo getDetail(Long id);

    /**
     * 获取热门海报排行前多少个的LibraryIds
     *
     * @param limit page size
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2023/3/23 16:40
     */
    List<Long> getHotPosterModuleLibraryIds(Integer limit);

    AjaxResult delScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo);
}
