package com.fh.cloud.screen.service.screen.service.impl;

import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.baseinfo.entity.vo.OrganizationVoExt;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ScreenModuleConstants;
import com.fh.cloud.screen.service.enums.DevicePatternType;
import com.fh.cloud.screen.service.enums.ScreenScenePublishType;
import com.fh.cloud.screen.service.enums.ScreenSceneType;
import com.fh.cloud.screen.service.enums.SuperviseStateType;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigService;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenIndexVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleDataService;
import com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel;
import com.fh.cloud.screen.service.space.service.ISpaceDeviceRelService;
import com.fh.cloud.screen.service.utils.RandomUtil;
import com.fh.cloud.screen.service.utils.StringKit;
import com.light.core.constants.DateTimeFormatConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.format.DateTimeFormatters;
import org.springframework.context.annotation.Lazy;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.ScreenPriorityType;
import com.fh.cloud.screen.service.enums.ScreenScreenEnums;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenScene;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenSceneMapper;
import com.fh.cloud.screen.service.screen.service.IScreenSceneModuleRelService;
import com.fh.cloud.screen.service.screen.service.IScreenSceneService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.light.core.enums.StatusEnum;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 云屏场景表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class ScreenSceneServiceImpl extends ServiceImpl<ScreenSceneMapper, ScreenScene> implements IScreenSceneService {

    @Resource
    private ScreenSceneMapper screenSceneMapper;
    @Lazy
    @Autowired
    private IScreenSceneService screenSceneService;
    @Lazy
    @Autowired
    private IScreenSceneModuleRelService screenSceneModuleRelService;
    @Lazy
    @Autowired
    private IShowDeviceService showDeviceService;
    @Lazy
    @Autowired
    private ISpaceDeviceRelService spaceDeviceRelService;
    @Lazy
    @Autowired
    private IScreenModuleDataService screenModuleDataService;
    @Lazy
    @Autowired
    private BaseDataService baseDataService;
    @Resource
    private ILeaveSchoolConfigService leaveSchoolConfigService;

    @Override
    public List<ScreenSceneVo> getScreenSceneListByCondition(ScreenSceneListConditionBo condition) {
        return screenSceneMapper.getScreenSceneListByCondition(condition);
    }

    @Override
    public boolean addScreenScene(ScreenSceneBo screenSceneBo) {
        ScreenScene screenScene = new ScreenScene();
        BeanUtils.copyProperties(screenSceneBo, screenScene);
        screenScene.setIsDelete(StatusEnum.NOTDELETE.getCode());
        boolean save = save(screenScene);
        screenSceneBo.setScreenSceneId(screenScene.getScreenSceneId());
        return save;
    }

    @Override
    public boolean updateScreenScene(ScreenSceneBo screenSceneBo) {
        ScreenScene screenScene = new ScreenScene();
        BeanUtils.copyProperties(screenSceneBo, screenScene);
        return updateById(screenScene);
    }

    @Override
    public ScreenSceneVo getDetail(Long screenSceneId) {
        if (screenSceneId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenScene> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenScene::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenScene::getScreenSceneId, screenSceneId);
        ScreenScene screenScene = getOne(lqw);
        ScreenSceneVo screenSceneVo = new ScreenSceneVo();
        BeanUtils.copyProperties(screenScene, screenSceneVo);
        return screenSceneVo;
    }

    @Override
    public List<ScreenSceneVo> listScreenSceneVoByScreenSceneIds(List<Long> sceneIds, Integer isDelete) {
        if (CollectionUtil.isEmpty(sceneIds)) {
            return Lists.newArrayList();
        }
        return screenSceneMapper.listScreenSceneVoByScreenSceneIds(sceneIds, isDelete);
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Long saveOrUpdateScreenSceneWithModule(ScreenSceneBo screenSceneBo) {
        Long screenSceneId = screenSceneBo.getScreenSceneId();
        List<ScreenModuleDataBo> screenModuleDatas = screenSceneBo.getScreenModuleDatas();

        // save和update逻辑分离，方便扩展
        if (screenSceneId == null) {
            // 如果是单个设备发布常规场景，检查常规场景是否已存在，已存在则更新
            if (ScreenSceneType.NORMAL.getValue() == screenSceneBo.getScreenSceneType() && screenSceneBo.getShowDeviceId() != null) {
                ScreenSceneListConditionBo condition = new ScreenSceneListConditionBo();
                condition.setShowDeviceId(screenSceneBo.getShowDeviceId());
                condition.setScreenSceneType(ScreenSceneType.NORMAL.getValue());
                condition.setScreenDevicePattern(screenSceneBo.getScreenDevicePattern());
                condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
                List<ScreenSceneVo> existScenes = screenSceneMapper.getScreenSceneListByCondition(condition);
                // 已存在，更新常规场景并返回场景screenSceneId
                if (CollectionUtil.isNotEmpty(existScenes)) {
                    ScreenSceneVo existScene = existScenes.get(ConstantsInteger.NUM_0);
                    screenSceneId = existScene.getScreenSceneId();
                    screenSceneBo.setScreenSceneId(screenSceneId);
                    screenSceneService.updateScreenScene(screenSceneBo);
                    screenSceneModuleRelService.deleteAndAddScreenSceneModuleRelBatch(screenSceneId, screenModuleDatas);
                    return screenSceneId;
                }
            }

            screenSceneService.addScreenScene(screenSceneBo);
            screenSceneId = screenSceneBo.getScreenSceneId();
            screenSceneModuleRelService.deleteAndAddScreenSceneModuleRelBatch(screenSceneId, screenModuleDatas);

            // 新增时候按照全校校区关系删除冲突的场景

        } else {
            screenSceneService.updateScreenScene(screenSceneBo);
            if (null == screenSceneBo.getSyncUpdateModule()
                || !screenSceneBo.getSyncUpdateModule().equals(ScreenScreenEnums.SYNC_NOT.getCode())) {
                screenSceneModuleRelService.deleteAndAddScreenSceneModuleRelBatch(screenSceneId, screenModuleDatas);
            }
        }
        return screenSceneId;
    }

    @Override
    public List<ScreenSceneVo> getScreenSceneListByConditionWithModule(ScreenSceneListConditionBo condition) {
        List<ScreenSceneVo> screenSceneVos = screenSceneService.getScreenSceneListByCondition(condition);
        if (CollectionUtil.isEmpty(screenSceneVos)) {
            return Lists.newArrayList();
        }

        List<Long> screenSceneIds =
            screenSceneVos.stream().map(ScreenSceneVo::getScreenSceneId).collect(Collectors.toList());
        List<ScreenSceneModuleRelVo> screenSceneModuleRelVos =
            screenSceneModuleRelService.listByScreenSceneIds(screenSceneIds);
        if (CollectionUtil.isEmpty(screenSceneModuleRelVos)) {
            return screenSceneVos;
        }
        Map<Long, List<ScreenSceneModuleRelVo>> screenSceneIdMap =
            screenSceneModuleRelVos.stream().collect(Collectors.groupingBy(ScreenSceneModuleRelVo::getScreenSceneId));
        screenSceneVos.forEach(screenSceneVo -> {
            Long screenSceneId = screenSceneVo.getScreenSceneId();
            List<ScreenSceneModuleRelVo> screenSceneModuleRelVosTemp = screenSceneIdMap.get(screenSceneId);
            screenSceneVo.setScreenSceneModuleRelVos(screenSceneModuleRelVosTemp);
        });
        // 查询场景的模块信息并返回，目前仅H5移动端为了兼容代码格式使用
        if (condition.isQueryScreenModuleDataVos()) {
            List<ScreenModuleDataVo> screenModuleDataVos = screenModuleDataService.listByScreenSceneIds(screenSceneIds);
            if (CollectionUtils.isNotEmpty(screenModuleDataVos)) {
                Map<Long, List<ScreenModuleDataVo>> screenModuleDataVoMap =
                    screenModuleDataVos.stream().collect(Collectors.groupingBy(ScreenModuleDataVo::getScreenSceneId));
                screenSceneVos.forEach(screenSceneVo -> screenSceneVo
                    .setScreenModuleDataVos(screenModuleDataVoMap.get(screenSceneVo.getScreenSceneId())));
            }
        }

        // 获取功能场景（当前仅获取放学）
        if (condition.getQueryFunctionalScreenScene() != null && condition.getQueryFunctionalScreenScene()) {
            addFunctionalScreenScene(screenSceneVos,
                    condition.getOrganizationId(),
                    condition.getSpaceInfoId(),
                    condition.getSpaceGroupUseType(),
                    new Date(),
                    condition.getShowDeviceId());
        }

        return screenSceneVos;
    }

    /**
     * 获取功能场景数据（当前仅获取放学场景）
     *
     * @param screenSceneVos
     * @param organizationId
     * @param spaceInfoId
     * @param spaceGroupUseType
     * @param nowDate
     * @param showDeviceId
     * @return void
     * <AUTHOR>
     * @date 2024/9/24 11:18
     **/
    private void addFunctionalScreenScene(List<ScreenSceneVo> screenSceneVos, Long organizationId, Long spaceInfoId,
                                          Integer spaceGroupUseType, Date nowDate, Long showDeviceId) {
        // 3.3获取放学场景数据
        LeaveSchoolConfigVo leaveSchoolConfigVo =
                leaveSchoolConfigService.getLeaveSchoolConfigBySpaceInfo(organizationId, spaceInfoId, spaceGroupUseType, nowDate, showDeviceId);
        // 3.5组装功能场景数据
        if (leaveSchoolConfigVo != null
                && CollectionUtils.isNotEmpty(leaveSchoolConfigVo.getConfigDetailList())) {
            leaveSchoolConfigVo.getConfigDetailList().forEach(detail -> {
                ScreenSceneVo screenSceneVo = new ScreenSceneVo();
                screenSceneVo.setScreenSceneName("放学场景");
                screenSceneVo.setUpdateTime(leaveSchoolConfigVo.getUpdateTime());
                screenSceneVo.setScreenPriority(ScreenPriorityType.FUNCTIONAL.getValue());
                try {
                    Calendar calendar = Calendar.getInstance();//获取Calendar实例
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//定义日期格式
                    String dateString = dateFormat.format(calendar.getTime());//获取当前时间并格式化为字符串
                    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    screenSceneVo.setStartTime(format.parse(dateString + " " + sdf.format(detail.getLeaveSchoolStartTime())));
                    screenSceneVo.setEndTime(format.parse(dateString + " " + sdf.format(detail.getLeaveSchoolEndTime())));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                screenSceneVo.setOrganizationId(leaveSchoolConfigVo.getOrganizationId());
                screenSceneVo.setSpaceGroupUseType(spaceGroupUseType);
                screenSceneVo.setSpaceInfoId(spaceInfoId);
                screenSceneVo.setFunctionalType(ScreenScreenEnums.FUN_TYPE_LEAVE_SCHOOL.getCode());
                screenSceneVos.add(screenSceneVo);
            });
        }
    }

    @Override
    public List<ScreenSceneVo> listScreenSceneOfAllByDevice(ScreenSceneListConditionBo condition) {
        List<ScreenSceneVo> resultVoList = Lists.newArrayList();

        // 1、按照设备发布（设备有值）
        ScreenSceneListConditionBo conditionTemp = new ScreenSceneListConditionBo();
        BeanUtils.copyProperties(condition, conditionTemp);
        List<ScreenSceneVo> screenSceneVosByDevice = Lists.newArrayList();
        if (conditionTemp.getShowDeviceId() != null) {
            screenSceneVosByDevice = screenSceneService.getScreenSceneListByCondition(conditionTemp);
        }

        // 2、按照地点发布（地点有值）
        conditionTemp.setShowDeviceId(ConstantsLong.NUM_0);
        List<ScreenSceneVo> screenSceneVosBySpace = Lists.newArrayList();
        if (conditionTemp.getSpaceInfoId() != null) {
            screenSceneVosBySpace = screenSceneService.getScreenSceneListByCondition(conditionTemp);
        }

        // 3、按照地点组发布
        conditionTemp.setShowDeviceId(ConstantsLong.NUM_0);
        conditionTemp.setSpaceInfoId(ConstantsLong.NUM_0);
        conditionTemp.setSpaceGroupUseType(null);
        List<ScreenSceneVo> screenSceneVosByGroup = Lists.newArrayList();
        if (conditionTemp.getSpaceGroupId() != null) {
            screenSceneVosByGroup = screenSceneService.getScreenSceneListByCondition(conditionTemp);
        }

        // 4、监管教育局发布，且设备是被监管的设备则添加教育局场景
        List<ScreenSceneVo> screenSceneVosByParentOrganization = Lists.newArrayList();
        if (conditionTemp.getOrganizationId() != null && conditionTemp.getOrganizationId() != ConstantsLong.NUM_0) {
            OrganizationVoExt parentOrganization =
                baseDataService.getSuperviseParentOrganization(conditionTemp.getOrganizationId());
            if (parentOrganization != null && condition.getSuperviseState() != null
                && condition.getSuperviseState().equals(SuperviseStateType.MONITOR_YES.getValue())) {
                conditionTemp.setOrganizationId(null);
                conditionTemp.setParentOrganizationId(parentOrganization.getId());
                conditionTemp.setSpaceGroupId(ConstantsLong.NUM_0);
                conditionTemp.setCampusId(null);
                screenSceneVosByParentOrganization = screenSceneService.getScreenSceneListByCondition(conditionTemp);
            }
        }

        // 5、合并设置场景优先级、排序返回
        resultVoList = Lists.newArrayList(Iterables.concat(screenSceneVosByDevice, screenSceneVosBySpace,
            screenSceneVosByGroup, screenSceneVosByParentOrganization));
        // 判断有没有教育局场景
        int screenSceneByParentOrganization = resultVoList.stream()
            .filter(x -> x.getParentOrganizationId() != ConstantsLong.NUM_0).collect(Collectors.toList()).size();
        boolean haveScreenSceneByParentOrganization = screenSceneByParentOrganization > 0;
        resultVoList.stream().forEach(screenSceneVo -> {
            screenSceneVo.setScreenPriority(ScreenPriorityType.NORMAL.getValue());
            // 校区#地点组id#场景类型#场景名称#轮播名称
            String playKey = Joiner.on(ConstString.jh).join(screenSceneVo.getCampusId(),
                screenSceneVo.getSpaceGroupId(), screenSceneVo.getScreenSceneType(),
                screenSceneVo.getScreenSceneName() == null ? "" : screenSceneVo.getScreenSceneName(),
                screenSceneVo.getScreenPlayName() == null ? "" : screenSceneVo.getScreenPlayName());
            screenSceneVo.setScreenPlayKey(playKey);
            // 如果是监管教育局场景，优先级最高，排在周期场景之前
            if (screenSceneVo.getParentOrganizationId() != ConstantsLong.NUM_0) {
                screenSceneVo.setUpdateTime(new Date());
            }
            // 没有教育局场景情况下，如果场景是周期场景，则将场景更新时间设置为当前时间（保证周期场景时间顺位优先级是最高的）
            if (!haveScreenSceneByParentOrganization && (screenSceneVo.getStartDate() != null
                || screenSceneVo.getEndDate() != null || StringUtils.isNotBlank(screenSceneVo.getWeeks()))) {
                screenSceneVo.setUpdateTime(new Date());
            }
        });
        Date currentDate = new Date();
        resultVoList = resultVoList.stream().filter(resultVo -> {
            // 不满足条件的场景返回false，排除掉。
            if (resultVo.getStartDate() != null && DateKit.ifLessThenDateFormat(resultVo.getStartDate(), null)) {
                return false;
            }
            if (resultVo.getEndDate() != null && DateKit.ifMoreThenDateFormat(resultVo.getEndDate(), null)) {
                return false;
            }
            if (StringUtils.isNotBlank(resultVo.getWeeks())
                && !resultVo.getWeeks().contains(String.valueOf(DateKit.getWeekIdByDate(currentDate)))) {
                return false;
            }
            return true;
        }).sorted(Comparator.comparing(ScreenSceneVo::getScreenPriority).thenComparing(ScreenSceneVo::getUpdateTime)
            .reversed().thenComparing(ScreenSceneVo::getScreenIndex).thenComparing(ScreenSceneVo::getScreenPlayIndex))
            .collect(Collectors.toList());
        resultVoList.forEach(screenSceneVo -> {
            screenSceneVo.setStartTime(DateKit.transferYMD2CurrentDay(screenSceneVo.getStartTime()));
            screenSceneVo.setEndTime(DateKit.transferYMD2CurrentDay(screenSceneVo.getEndTime()));
        });
        return resultVoList;
    }

    @Override
    public void initScreenSceneByDeviceNumber(String deviceNumber) {
        if (StringUtils.isBlank(deviceNumber)) {
            return;
        }
        ShowDevice showDevice = showDeviceService.getByDeviceNum(deviceNumber);
        if (showDevice == null) {
            return;
        }
        Long showDeviceId = showDevice.getShowDeviceId();
        SpaceDeviceRel spaceDeviceRel = spaceDeviceRelService.getByDeviceId(showDeviceId);
        if (spaceDeviceRel == null) {
            return;
        }
        Long organizationId = showDevice.getOrganizationId();
        Integer devicePattern = showDevice.getDevicePattern();
        Long campusId = spaceDeviceRel.getCampusId();
        Long spaceInfoId = spaceDeviceRel.getSpaceInfoId();
        Integer spaceGroupUseType = spaceDeviceRel.getSpaceGroupUseType();
        Long spaceGroupId = spaceDeviceRel.getSpaceGroupId();

        // check该学校该设备是否有场景,有场景则不做初始化
        ScreenSceneListConditionBo screenSceneListConditionBo = new ScreenSceneListConditionBo();
        screenSceneListConditionBo.setOrganizationId(organizationId);
        screenSceneListConditionBo.setScreenDevicePattern(devicePattern);
        screenSceneListConditionBo.setCampusId(0L);
        screenSceneListConditionBo.setSpaceInfoId(spaceInfoId);
        screenSceneListConditionBo.setSpaceGroupUseType(spaceGroupUseType);
        screenSceneListConditionBo.setSpaceGroupId(spaceGroupId);
        screenSceneListConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        screenSceneListConditionBo.setShowDeviceId(showDeviceId);
        screenSceneListConditionBo.setPublishType(ScreenScenePublishType.POINT.getValue());
        List<ScreenSceneVo> screenSceneListByCondition =
            screenSceneService.getScreenSceneListByCondition(screenSceneListConditionBo);
        if (CollectionUtils.isNotEmpty(screenSceneListByCondition)) {
            return;
        }

        // do初始化场景+模块
        List<Integer> devicePatterns =
            Lists.newArrayList(DevicePatternType.VERTICAL.getValue(), DevicePatternType.HORIZONTAL.getValue());
        for (Integer pattern : devicePatterns) {
            ScreenSceneBo screenSceneBo = new ScreenSceneBo();
            screenSceneBo.setOrganizationId(organizationId);
            screenSceneBo.setCampusId(0L);
            screenSceneBo.setSpaceInfoId(spaceInfoId);
            screenSceneBo.setSpaceGroupUseType(spaceGroupUseType);
            screenSceneBo.setShowDeviceId(showDeviceId);
            screenSceneBo.setSpaceGroupId(spaceGroupId);
            screenSceneBo.setScreenSceneType(ScreenSceneType.NORMAL.getValue());
            screenSceneBo.setScreenDevicePattern(pattern);
            screenSceneBo.setPublishType(ScreenScenePublishType.POINT.getValue());
            String layout = "";
            Long screenModuleDataId = null;
            if (pattern != null && pattern.equals(DevicePatternType.HORIZONTAL.getValue())) {
                layout = ConstantsConfig.DEFAULT_SCENE_LAYOUT_HORIZONTAL;
            }
            if (pattern != null && pattern.equals(DevicePatternType.VERTICAL.getValue())) {
                layout = ConstantsConfig.DEFAULT_SCENE_LAYOUT_VERTICAL;
            }
            screenModuleDataId = screenModuleDataService.getScreenModuleDataIdByScreenModuleLibraryId(
                ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID, organizationId);
            if (screenModuleDataId == null) {
                screenModuleDataService.initScreenModuleData(organizationId);
            }
            screenModuleDataId = screenModuleDataService.getScreenModuleDataIdByScreenModuleLibraryId(
                ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID, organizationId);
            layout = StringKit.getMessage(layout, String.valueOf(screenModuleDataId), String.valueOf(organizationId),
                RandomUtil.randomStringAll(ConstantsInteger.NUM_16));

            screenSceneBo.setScreenSceneLayout(layout);
            ScreenModuleDataBo screenModuleDataBo = new ScreenModuleDataBo();
            screenModuleDataBo.setScreenModuleDataId(screenModuleDataId);
            screenModuleDataBo.setScreenModuleLibraryId(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID);
            screenModuleDataBo
                .setScreenModuleLibrarySelIds(String.valueOf(ScreenModuleConstants.POSTER_SCREEN_MODULE_PARENT_ID));
            List<ScreenModuleDataBo> screenModuleDataBos = Lists.newArrayList(screenModuleDataBo);
            screenSceneBo.setScreenModuleDatas(screenModuleDataBos);
            screenSceneService.saveOrUpdateScreenSceneWithModule(screenSceneBo);
        }
    }

    @Override
    public void updateScreenSceneDeviceFullTypeNull(Long screenSceneId) {
        if (screenSceneId == null) {
            return;
        }
        LambdaUpdateWrapper<ScreenScene> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ScreenScene::getScreenSceneId, screenSceneId);
        updateWrapper.isNotNull(ScreenScene::getDeviceFullType);
        updateWrapper.set(ScreenScene::getDeviceFullType, null);
        updateWrapper.set(ScreenScene::getUpdateTime, new Date());
        update(updateWrapper);
    }

    @Override
    public void updateScreenSceneStartDateNull(Long screenSceneId) {
        if (screenSceneId == null) {
            return;
        }
        LambdaUpdateWrapper<ScreenScene> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ScreenScene::getScreenSceneId, screenSceneId);
        updateWrapper.isNotNull(ScreenScene::getStartDate);
        updateWrapper.set(ScreenScene::getStartDate, null);
        updateWrapper.set(ScreenScene::getUpdateTime, new Date());
        update(updateWrapper);
    }

    @Override
    public void updateScreenSceneEndDateNull(Long screenSceneId) {
        if (screenSceneId == null) {
            return;
        }
        LambdaUpdateWrapper<ScreenScene> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ScreenScene::getScreenSceneId, screenSceneId);
        updateWrapper.isNotNull(ScreenScene::getEndDate);
        updateWrapper.set(ScreenScene::getEndDate, null);
        updateWrapper.set(ScreenScene::getUpdateTime, new Date());
        update(updateWrapper);
    }

    @Override
    public void updateScreenSceneWeeksNull(Long screenSceneId) {
        if (screenSceneId == null) {
            return;
        }
        LambdaUpdateWrapper<ScreenScene> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ScreenScene::getScreenSceneId, screenSceneId);
        updateWrapper.isNotNull(ScreenScene::getWeeks);
        updateWrapper.ne(ScreenScene::getWeeks, "");
        updateWrapper.set(ScreenScene::getWeeks, null);
        updateWrapper.set(ScreenScene::getUpdateTime, new Date());
        update(updateWrapper);
    }
}