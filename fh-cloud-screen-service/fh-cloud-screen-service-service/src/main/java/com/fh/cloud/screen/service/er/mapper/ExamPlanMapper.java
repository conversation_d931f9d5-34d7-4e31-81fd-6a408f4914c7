package com.fh.cloud.screen.service.er.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.er.entity.dto.ExamPlanDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo;

/**
 * 考场_考试计划Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamPlanMapper extends BaseMapper<ExamPlanDto> {

	List<ExamPlanVo> getExamPlanListByCondition(ExamPlanConditionBo condition);

}
