package com.fh.cloud.screen.service.cs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.cs.entity.dto.CsContactDto;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactConditionBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactBo;
import com.fh.cloud.screen.service.cs.entity.vo.CsContactVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * Cultural-Station文化小站联系表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
public interface ICsContactService extends IService<CsContactDto> {

    List<CsContactVo> getCsContactListByCondition(CsContactConditionBo condition);

	AjaxResult addCsContact(CsContactBo csContactBo);

	AjaxResult updateCsContact(CsContactBo csContactBo);

	CsContactVo getCsContactByCondition(CsContactConditionBo condition);

}

