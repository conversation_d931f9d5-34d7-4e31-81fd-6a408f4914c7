package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPwd;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenPwdMapper;
import com.fh.cloud.screen.service.screen.service.IScreenPwdService;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 云屏密码接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class ScreenPwdServiceImpl extends ServiceImpl<ScreenPwdMapper, ScreenPwd> implements IScreenPwdService {

    @Resource
    private ScreenPwdMapper screenPwdMapper;

    @Override
    public List<ScreenPwdVo> getScreenPwdListByCondition(ScreenPwdListConditionBo condition) {
        return screenPwdMapper.getScreenPwdListByCondition(condition);
    }

    @Override
    public boolean addScreenPwd(ScreenPwdBo screenPwdBo) {
        ScreenPwd screenPwd = new ScreenPwd();
        BeanUtils.copyProperties(screenPwdBo, screenPwd);
        screenPwd.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(screenPwd);
    }

    @Override
    public boolean updateScreenPwd(ScreenPwdBo screenPwdBo) {
        ScreenPwd screenPwd = new ScreenPwd();
        BeanUtils.copyProperties(screenPwdBo, screenPwd);
        return updateById(screenPwd);
    }

    @Override
    public Map<String, Object> getDetail(Long screenPwdId) {
        return null;
    }

    @Override
    public Object getByOrgId(Long orgId) {

        QueryWrapper<ScreenPwd> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScreenPwd::getOrganizationId, orgId).eq(ScreenPwd::getIsDelete,
            StatusEnum.NOTDELETE.getCode());

        return this.baseMapper.selectOne(queryWrapper);
    }

}