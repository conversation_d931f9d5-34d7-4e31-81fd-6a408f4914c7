package com.fh.cloud.screen.service.cs.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.cs.api.CsLikeApi;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeConditionBo;
import com.fh.cloud.screen.service.cs.entity.dto.CsLikeDto;
import com.fh.cloud.screen.service.cs.entity.vo.CsLikeVo;
import com.fh.cloud.screen.service.cs.service.ICsLikeService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * Cultural-Station文化小站喜欢记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
@RestController
@Validated
public class CsLikeController implements CsLikeApi{
	
    @Autowired
    private ICsLikeService csLikeService;

    /**
     * 查询Cultural-Station文化小站喜欢记录表分页列表
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
     */
    @Override
    public AjaxResult<PageInfo<CsLikeVo>> getCsLikePageListByCondition(@RequestBody CsLikeConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CsLikeVo> pageInfo = new PageInfo<>(csLikeService.getCsLikeListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询Cultural-Station文化小站喜欢记录表列表
	 * <AUTHOR>
	 * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult<List<CsLikeVo>> getCsLikeListByCondition(@RequestBody CsLikeConditionBo condition){
		List<CsLikeVo> list = csLikeService.getCsLikeListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增Cultural-Station文化小站喜欢记录表
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
     */
	@Override
    public AjaxResult addCsLike(@Validated @RequestBody CsLikeBo csLikeBo){
		return csLikeService.addCsLike(csLikeBo);
    }

    /**
	 * 修改Cultural-Station文化小站喜欢记录表
	 * @param csLikeBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult updateCsLike(@Validated @RequestBody CsLikeBo csLikeBo) {
		if(null == csLikeBo.getId()) {
			return AjaxResult.fail("Cultural-Station文化小站喜欢记录表id不能为空");
		}
		return csLikeService.updateCsLike(csLikeBo);
	}

	/**
	 * 查询Cultural-Station文化小站喜欢记录表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult<CsLikeVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("Cultural-Station文化小站喜欢记录表id不能为空");
		}
		CsLikeConditionBo condition = new CsLikeConditionBo();
		condition.setId(id);
		CsLikeVo vo = csLikeService.getCsLikeByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除Cultural-Station文化小站喜欢记录表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2024-01-19 10:36:39
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CsLikeDto csLikeDto = new CsLikeDto();
		csLikeDto.setId(id);
		csLikeDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(csLikeService.updateById(csLikeDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
