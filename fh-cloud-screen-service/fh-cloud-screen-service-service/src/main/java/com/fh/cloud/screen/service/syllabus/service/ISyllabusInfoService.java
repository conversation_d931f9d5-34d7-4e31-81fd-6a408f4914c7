package com.fh.cloud.screen.service.syllabus.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.syllabus.entity.dto.SyllabusInfoDto;
import com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 课表信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-09-18 15:22:20
 */
public interface ISyllabusInfoService extends IService<SyllabusInfoDto> {

    List<SyllabusInfoVo> getSyllabusInfoListByCondition(SyllabusInfoConditionBo condition);

	AjaxResult addSyllabusInfo(SyllabusInfoBo syllabusInfoBo);

	AjaxResult updateSyllabusInfo(SyllabusInfoBo syllabusInfoBo);

	SyllabusInfoVo getSyllabusInfoByCondition(SyllabusInfoConditionBo condition);

}

