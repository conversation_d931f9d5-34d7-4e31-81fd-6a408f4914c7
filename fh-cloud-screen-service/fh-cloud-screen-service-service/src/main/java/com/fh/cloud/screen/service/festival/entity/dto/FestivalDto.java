package com.fh.cloud.screen.service.festival.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 节日表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:17:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("festival")
public class FestivalDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "festival_id", type = IdType.AUTO)
	private Long festivalId;

	/**
	 * 类型：1传统节日、2国际节日、 3二十四节气、4自定义节点、5一般节日
	 */
	@TableField("type")
	private Integer type;

	/**
	 * 年份
	 */
	@TableField("year")
	private Integer year;

	/**
	 * 名称
	 */
	@TableField("name")
	private String name;

	/**
	 * 用于外部关联key
	 */
	@TableField("festival_code")
	private String festivalCode;

	/**
	 * 节日当天（type属于1、2、3）
	 */
	@TableField("festival_day")
	private Date festivalDay;
	/**
	 * 开始天（海报启用）
	 */
	@TableField("start_day")
	private Date startDay;

	/**
	 * 结束天（海报启用）
	 */
	@TableField("end_day")
	private Date endDay;

	/**
	 * 预置类型：1预置，2自定义
	 */
	@TableField("preset_type")
	private Integer presetType;

	/**
	 * 自定义节点对应学校，默认0：通用所有学校
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
