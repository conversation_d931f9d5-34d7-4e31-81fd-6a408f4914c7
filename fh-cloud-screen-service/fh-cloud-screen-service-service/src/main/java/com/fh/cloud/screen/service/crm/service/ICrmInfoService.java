package com.fh.cloud.screen.service.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.crm.entity.dto.CrmInfoDto;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoConditionBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoBo;
import com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * CRM商讯管理表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
public interface ICrmInfoService extends IService<CrmInfoDto> {

    List<CrmInfoVo> getCrmInfoListByCondition(CrmInfoConditionBo condition);

	Long addCrmInfo(CrmInfoBo crmInfoBo);

	AjaxResult updateCrmInfo(CrmInfoBo crmInfoBo);

	CrmInfoVo getCrmInfoByCondition(CrmInfoConditionBo condition);

	/**
	 * 新增CRM商讯管理表
	 * @param crmInfoBo
	 * @return
	 */
	AjaxResult addCrmInfoWithContact(CrmInfoBo crmInfoBo);
}

