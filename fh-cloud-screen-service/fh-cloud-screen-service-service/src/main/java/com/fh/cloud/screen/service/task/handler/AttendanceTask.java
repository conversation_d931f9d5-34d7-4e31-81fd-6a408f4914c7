package com.fh.cloud.screen.service.task.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fh.cloud.screen.service.attendance.entity.bo.*;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo;
import com.fh.cloud.screen.service.attendance.service.IAttendanceLogService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserService;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarWeekService;
import com.fh.cloud.screen.service.consts.AttendanceConstants;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.AttendanceRuleEnums;
import com.fh.cloud.screen.service.enums.AttendanceUserRecordTypeEnums;
import com.fh.cloud.screen.service.enums.SchoolCalendarEnum;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.core.constants.SystemConstants;
import com.light.core.enums.StatusEnum;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@Component
@Slf4j
public class AttendanceTask {
    @Resource
    private IAttendanceLogService attendanceLogService;

    @Resource
    private IAttendanceUserService attendanceUserService;

    @Resource
    private IAttendanceRuleService attendanceRuleService;

    @Resource
    private ISchoolCalendarWeekService schoolCalendarWeekService;

    private static final SimpleDateFormat monthDateFormat = new SimpleDateFormat("yyyy-MM");

    @Autowired
    private BaseDataService baseDataService;

    /**
     * 计算前一天考勤流水 00:05
     *
     * <AUTHOR>
     * @date 2022/6/21 18:02
     */
    @XxlJob("AttendanceTask")
    public void attendanceTaskHandler() {
        log.info("AttendanceTask start");

        // 1、取出考勤当天所有日志
        Date nowDate = DateKit.getZeroDate(-1, new Date());
        AttendanceLogListConditionBo conditionBo = new AttendanceLogListConditionBo();
        conditionBo.setAttendanceTime(DateKit.getStringDay(nowDate));
        List<AttendanceLogVo> attendanceLogList = attendanceLogService.getAttendanceLogListByCondition(conditionBo);

        // 2 循环所有组织
        // 要新增的用户考勤数据
        List<AttendanceUser> attendanceUserDtoList = new ArrayList<>();
        // <规则，用户考勤log集合>
        Map<Long, List<AttendanceLogVo>> groupMap =
            attendanceLogList.stream().collect(Collectors.groupingBy(AttendanceLogVo::getAttendanceRuleId));

        // 取出所有考勤规则
        AttendanceRuleListConditionBo condition = new AttendanceRuleListConditionBo();
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<AttendanceRuleVo> attendanceRuleVos = attendanceRuleService.getAttendanceRuleListByCondition(condition);
        if (CollectionUtils.isEmpty(attendanceRuleVos)) {
            return;
        }
        for (AttendanceRuleVo attendanceRuleVo : attendanceRuleVos) {
            List<AttendanceLogVo> attendanceLogVos = groupMap.get(attendanceRuleVo.getAttendanceRuleId());
            List<AttendanceUser> attendanceUsers = onceAttendanceRuleCalculate(attendanceRuleVo.getOrganizationId(),
                attendanceRuleVo.getAttendanceType(), nowDate, attendanceLogVos);
            // 当天设置规则db，但是缓存并没有存入缓存规则,所以返回为null不做处理
            if (null != attendanceUsers) {
                attendanceUserDtoList.addAll(attendanceUsers);
            }
        }
        // 新增用户考勤记录
        if (CollectionUtils.isEmpty(attendanceUserDtoList)) {
            return;
        }
        attendanceUserService.saveBatchAndDetail(attendanceUserDtoList);
        log.info("attendanceTask  end");
    }

    /**
     * 多组考勤规则和用户考勤流水计算
     *
     * @param organizationId 学校id
     * @param attendanceType 考勤类型
     * @param nowDate 考勤规则计算的日期
     * @param attendanceLogVos 考勤规则和日期对应的考勤流水
     * @return java.util.List<com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser>
     * <AUTHOR>
     * @date 2022/6/24 15:44
     */
    private List<AttendanceUser> onceAttendanceRuleCalculate(Long organizationId, Integer attendanceType, Date nowDate,
        List<AttendanceLogVo> attendanceLogVos) {
        // 教师考勤规则或学生考勤规则
        AttendanceRuleAddBo ruleBo =
            attendanceRuleService.getAttendanceRuleByOrganizationIdAndType(organizationId, attendanceType);
        if (null == ruleBo || null == ruleBo.getAttendanceRuleDays()) {
            return null;
        }
        List<AttendanceUser> attendanceUserList = new ArrayList<>();
        // 规则存在则计算
        if (CollectionUtils.isEmpty(ruleBo.getAttendanceRuleDays())) {
            return attendanceUserList;
        }
        // 获取校历，校验考勤日规则
        Map<String, Object> SchoolCalendarMap =
            schoolCalendarWeekService.getCacheWeekListAndDayListByOrganizationId(ruleBo.getOrganizationId());
        List<SchoolCalendarWeekVo> weekVos = (List<SchoolCalendarWeekVo>)SchoolCalendarMap.get("weekVos");
        List<SchoolCalendarDay> dayVos = (List<SchoolCalendarDay>)SchoolCalendarMap.get("dayVos");
        if (CollectionUtils.isEmpty(weekVos)) {
            return null;
        }
        // 不上课标志
        boolean notWorkFlag = false;
        Integer week = DateKit.getWeekIdByDate(nowDate);
        Integer firstWeek = week;
        SchoolCalendarWeekVo schoolCalendarWeekVo =
            weekVos.stream().filter(weekVo -> weekVo.getWeek().equals(firstWeek)).findFirst().get();
        // 上课周不上课，true
        if (SchoolCalendarEnum.TYPE_IS.getValue() != schoolCalendarWeekVo.getType()) {
            notWorkFlag = true;
        }
        if (CollectionUtils.isNotEmpty(dayVos)) {
            for (SchoolCalendarDay dayVo : dayVos) {
                // 考勤当天
                if (DateKit.checkOneDay(dayVo.getDay(), nowDate)) {
                    // 不上课 true
                    if (SchoolCalendarEnum.TYPE_IS.getValue() != dayVo.getType()) {
                        notWorkFlag = true;
                    } else {
                        notWorkFlag = false;
                        week = dayVo.getWeek();
                    }
                    break;
                }
            }
        }
        if (notWorkFlag) {
            return null;
        }
        // 过滤出当天考勤规则
        Integer finalWeek = week;
        List<AttendanceRuleDayBo> attendanceRuleDays = ruleBo.getAttendanceRuleDays().stream()
            .filter(day -> day.getWeek().equals(finalWeek)).collect(Collectors.toList());

        // 对打卡的组织内的用户再次以用户oid分组 分别于考勤规则结合计算
        Map<String, List<AttendanceLogVo>> userSignGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(attendanceLogVos)) {
            userSignGroupMap = attendanceLogVos.stream().collect(Collectors.groupingBy(AttendanceLogVo::getUserOid));
        }

        // 获取组织的全部学生或者全部教师
        Set<String> userKey;
        List<StudentVo> studentVoList = null;
        List<TeacherVo> teacherVoList = null;
        Map<String, List<StudentVo>> studentMap = null;
        Map<String, List<TeacherVo>> teacherMap = null;
        if (ruleBo.getAttendanceType().equals(AttendanceRuleEnums.RULE_STUDENT.getCode())) {
            StudentConditionBo studentConditionBo = new StudentConditionBo();
            studentConditionBo.setOrganizationId(ruleBo.getOrganizationId());
            studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
            studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            studentVoList = baseDataService.getStudentVoListSlow(studentConditionBo);
            studentMap = studentVoList.stream().collect(Collectors.groupingBy(StudentVo::getUserOid));
            userKey = studentMap.keySet();
        } else {
            TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
            teacherConditionBo.setOrganizationId(ruleBo.getOrganizationId());
            teacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
            teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            teacherVoList = baseDataService.getTeacherVoList(teacherConditionBo);
            teacherMap = teacherVoList.stream().collect(Collectors.groupingBy(TeacherVo::getUserOid));
            userKey = teacherMap.keySet();
        }

        // 遍历所有学生或教师，(如有未打卡，同样保存）
        for (String userOidKey : userKey) {
            List<AttendanceLogVo> userGroupLogVos = userSignGroupMap.get(userOidKey);
            List<AttendanceRuleDayBo> ruleDays = new ArrayList<>(attendanceRuleDays);
            // 学生列表需要判断学生考勤规则，年级不一致情况，过滤出对应年级的考勤规则
            if (CollectionUtils.isNotEmpty(studentVoList)) {
                String grade = studentMap.get(userOidKey).get(0).getClazzVo().getGrade();
                if (StrUtil.isNotBlank(grade)
                    && AttendanceRuleEnums.GRADE_SAME_NOT.getCode().equals(ruleBo.getGradeSameType())) {
                    ruleDays =
                        ruleDays.stream().filter(day -> day.getGrade().equals(grade)).collect(Collectors.toList());
                }
            }
            // 接收计算返回的多个ruleUserDetail
            List<AttendanceUserDetail> transferUserDetailList = new ArrayList<>();

            // 多组考勤规则对单个用户的计算结果(一天的规则组别顺序不一样，前后间隔时间不一样，分别传入间隔时间处理)
            for (int i = 0; i < ruleDays.size(); i++) {
                AttendanceRuleDayBo ruleDayBo = ruleDays.get(i);
                AttendanceUserDetail attendanceUserDetail;

                // 用户无考勤流水，记录缺卡
                if (CollectionUtils.isEmpty(userGroupLogVos)) {
                    attendanceUserDetail = new AttendanceUserDetail();
                    attendanceUserDetail.setAttendanceRuleDayIndex(ruleDayBo.getAttendanceRuleDayIndex());
                    if (ruleDayBo.getSignInTime() == null || ruleDayBo.getSignOutTime() == null) {
                        if (ruleDayBo.getSignInTime() != null) {
                            attendanceUserDetail.setSignInRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
                            attendanceUserDetail.setOnlySign(AttendanceRuleEnums.ONLY_SIGN_IN.getCode());
                        }
                        if (ruleDayBo.getSignOutTime() != null) {
                            attendanceUserDetail
                                .setSignOutRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
                            attendanceUserDetail.setOnlySign(AttendanceRuleEnums.ONLY_SING_OUT.getCode());
                        }
                    } else {
                        attendanceUserDetail.setSignInRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
                        attendanceUserDetail.setSignOutRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
                    }
                    transferUserDetailList.add(attendanceUserDetail);
                } else {
                    // 一组单个签到或签退规则对应的用户考勤流水计算
                    if (ruleDayBo.getSignInTime() == null || ruleDayBo.getSignOutTime() == null) {
                        // 一组单个规则，可能缺卡
                        attendanceUserDetail =
                            checkOneOneRule(ruleDays, i, userGroupLogVos, nowDate, ruleBo.getAttendanceModeNum());
                        attendanceUserDetail.setAttendanceRuleDayIndex(ruleDayBo.getAttendanceRuleDayIndex());
                        transferUserDetailList.add(attendanceUserDetail);
                        continue;
                    }

                    // 一组完整签到签退规则对应的用户考勤流水计算
                    long beforeSignIn = 0L;
                    long afterSignOut = 0L;
                    if (i == 0) {
                        if (AttendanceRuleEnums.ATTENDANCE_MODE_NUM_1.getCode().equals(ruleBo.getAttendanceModeNum())) {
                            beforeSignIn = AttendanceConstants.MAX_SECOND;
                            afterSignOut = AttendanceConstants.MAX_SECOND;
                        } else {
                            beforeSignIn = AttendanceConstants.MAX_SECOND;
                            afterSignOut = AttendanceConstants.MIN_SECOND;
                        }
                    } else if (i == ruleDays.size() - 1) {
                        beforeSignIn = AttendanceConstants.MIN_SECOND;
                        afterSignOut = AttendanceConstants.MAX_SECOND;
                    } else {
                        beforeSignIn = AttendanceConstants.MIN_SECOND;
                        afterSignOut = AttendanceConstants.MIN_SECOND;
                    }
                    Integer signLeftMinute = ruleDayBo.getSignLeftMinute();
                    Integer signRightMinute = ruleDayBo.getSignRightMinute();
                    // update by sunqb:这里的日终计算判断逻辑同打卡时候的判断
                    if (signLeftMinute != null) {
                        beforeSignIn = signLeftMinute * 60;
                    }
                    if (signRightMinute != null) {
                        afterSignOut = signRightMinute * 60;
                    }
                    attendanceUserDetail = onceRule(ruleDayBo, userGroupLogVos, nowDate, beforeSignIn, afterSignOut);
                    attendanceUserDetail.setAttendanceRuleDayIndex(ruleDayBo.getAttendanceRuleDayIndex());
                    transferUserDetailList.add(attendanceUserDetail);
                }
            }

            // 多条detail合并为一个用户考勤
            if (CollectionUtils.isNotEmpty(transferUserDetailList)) {
                AttendanceUser attendanceUser = new AttendanceUser();
                attendanceUser.setAttendanceRuleId(ruleBo.getAttendanceRuleId());
                attendanceUser.setAttendanceType(ruleBo.getAttendanceType());
                attendanceUser.setAttendanceDate(nowDate);
                attendanceUser.setAttendanceDay(DateKit.getStringDay(nowDate));
                attendanceUser.setAttendanceMonth(monthDateFormat.format(nowDate));
                attendanceUser.setUserOid(userOidKey);
                attendanceUser.setIsDelete(StatusEnum.NOTDELETE.getCode());
                attendanceUser.setAttendanceRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
                attendanceUser.setCreateBy(userOidKey);
                attendanceUser.setUpdateBy(userOidKey);
                attendanceUser.setCreateTime(nowDate);
                attendanceUser.setUpdateTime(nowDate);
                attendanceUser.setAttendanceUserDetailList(transferUserDetailList);
                for (AttendanceUserDetail detail : transferUserDetailList) {
                    // 一组单个签到或签退计算结果，判断当天是否包含异常
                    if (detail.getOnlySign() != null) {
                        if (detail.getSignInRecordType() != null
                            && !detail.getSignInRecordType().equals(AttendanceUserRecordTypeEnums.NORMAL.getCode())) {
                            attendanceUser.setAttendanceRecordType(AttendanceUserRecordTypeEnums.EXCEPTION.getCode());
                        } else if (detail.getSignOutRecordType() != null
                            && !detail.getSignOutRecordType().equals(AttendanceUserRecordTypeEnums.NORMAL.getCode())) {
                            attendanceUser.setAttendanceRecordType(AttendanceUserRecordTypeEnums.EXCEPTION.getCode());
                        }
                    }
                    // 一组完整签到签退规则计算结果，判断当天是否包含异常
                    else if (detail.getSignInRecordType() == null || detail.getSignOutRecordType() == null
                        || !detail.getSignInRecordType().equals(AttendanceUserRecordTypeEnums.NORMAL.getCode())
                        || !detail.getSignOutRecordType().equals(AttendanceUserRecordTypeEnums.NORMAL.getCode())) {
                        attendanceUser.setAttendanceRecordType(AttendanceUserRecordTypeEnums.EXCEPTION.getCode());
                    }
                }
                attendanceUserList.add(attendanceUser);
            }
        }
        return attendanceUserList;
    }

    /**
     * 一组考勤规则与单个用户流水 计算并保存
     *
     * @param attendanceRuleDayBo, 考勤规则详情
     * @param attendanceLogVos 当前考勤规则适用的个人的当天打卡流水
     * @param nowDate 当天
     * @return void 合并成一条detail
     * <AUTHOR>
     * @date 2022/6/10 10:22
     */
    private AttendanceUserDetail onceRule(AttendanceRuleDayBo attendanceRuleDayBo,
        List<AttendanceLogVo> attendanceLogVos, Date nowDate, long beforeSignIn, long afterSignOut) {
        // 规则签到时间拼接当前日期
        Date ruleSignInDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), nowDate);
        Date ruleSignOutDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), nowDate);

        // 针对单个用户考勤时间计算
        boolean signInFlag;
        boolean signOutFlag;
        Long maxSignInTime = null;
        Long maxSignOutTime = null;
        String signInAddress = null;
        String signOutAddress = null;
        Date signInTime = null;
        Date signOutTime = null;
        String signInDeviceNumber = null;
        String signOutDeviceNumber = null;
        Integer signInAttendanceMethod = null;
        Integer signOutAttendanceMethod = null;
        String signInFaceMediaId = null;
        String signOutFaceMediaId = null;

        // 匹配一组考勤的dto
        AttendanceUserDetail attendanceUserDetailBo = new AttendanceUserDetail();
        // 一个用户比对完成，存入detailBean
        attendanceUserDetailBo.setAttendanceRuleDayIndex(attendanceRuleDayBo.getAttendanceRuleDayIndex());
        attendanceUserDetailBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        attendanceUserDetailBo.setCreateBy(attendanceLogVos.get(0).getCreateBy());
        attendanceUserDetailBo.setUpdateBy(attendanceLogVos.get(0).getCreateBy());
        attendanceUserDetailBo.setCreateTime(nowDate);
        attendanceUserDetailBo.setUpdateTime(nowDate);
        // 一组规则，是否有对应考勤标志
        boolean groupSignInFlag = false;
        boolean groupSignOutFlag = false;
        // 一个规则比对一个用户当天所有流水--> 时间再范围内即使打卡成功 return 对应一条考勤的userDetail
        // 修改：同上，加一个规则内时间打卡，算早退，如果之前没有，迟到。如果有迟到，早退，后面有正常数据，取消早退
        // 根据时间排序
        attendanceLogVos = attendanceLogVos.stream().sorted(Comparator.comparing(AttendanceLogVo::getAttendanceTime))
            .collect(Collectors.toList());
        for (AttendanceLogVo userGroupLogVo : attendanceLogVos) {
            // 单个流水，是否匹配考勤规则标志
            signInFlag = false;
            signOutFlag = false;
            // 按照考勤规则时间，比对用户打卡时间
            Date attendanceTime = userGroupLogVo.getAttendanceTime();
            // 签到从59秒开始，判断间隔时间，添加59秒
            beforeSignIn = beforeSignIn + 59;
            Date ruleSignInDate59 = DateKit.getAfterSeconds(59, ruleSignInDate);
            // 签到 ----> 规则时间-签到时间 =相差秒数
            long diffSignInMin = DateKit.getDiffSecDate(attendanceTime, ruleSignInDate59);
            if (0 <= diffSignInMin && diffSignInMin <= beforeSignIn) {
                signInFlag = true;
                groupSignInFlag = true;
                // 第二次且间隔时间小，则忽略掉(签到取最早，第一次address等属性赋值，后续不是最小，sign=flase，后续if不成立不赋值)
                if (null != maxSignInTime && diffSignInMin < maxSignInTime) {
                    signInFlag = false;
                } else {
                    maxSignInTime = diffSignInMin;
                }
                signInAddress = userGroupLogVo.getAddress();
                signInTime = userGroupLogVo.getAttendanceTime();
                signInDeviceNumber = userGroupLogVo.getDeviceNumber();
                signInAttendanceMethod = userGroupLogVo.getAttendanceMethod();
                signInFaceMediaId = userGroupLogVo.getFaceMediaId();
            }

            // 签退 ----> 签到时间-规则时间 =相差秒数
            long diffSignOutMin = DateKit.getDiffSecDate(ruleSignOutDate, attendanceTime);
            if (0 <= diffSignOutMin && diffSignOutMin <= afterSignOut) {
                signOutFlag = true;
                groupSignOutFlag = true;
                // 第二次且间隔时间小，则忽略掉（签到取最晚，后续条件一直成功）
                if (null != maxSignOutTime && diffSignOutMin < maxSignOutTime) {
                    signOutFlag = false;
                } else {
                    maxSignOutTime = diffSignOutMin;
                }
                signOutAddress = userGroupLogVo.getAddress();
                signOutTime = userGroupLogVo.getAttendanceTime();
                signOutDeviceNumber = userGroupLogVo.getDeviceNumber();
                signOutAttendanceMethod = userGroupLogVo.getAttendanceMethod();
                signOutFaceMediaId = userGroupLogVo.getFaceMediaId();
            }
            // 迟到或早退 当前规则分钟00秒+59秒，之后算迟到早退
            if (attendanceTime.before(ruleSignOutDate) && attendanceTime.after(ruleSignInDate59)) {
                if (null == attendanceUserDetailBo.getSignInRecordType()) {
                    // 第一次迟到
                    attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.LATE.getCode());
                    attendanceUserDetailBo.setSignInTime(attendanceTime);
                    attendanceUserDetailBo.setSignInAddress(userGroupLogVo.getAddress());
                    attendanceUserDetailBo.setSignInDeviceNumber(userGroupLogVo.getDeviceNumber());
                    attendanceUserDetailBo.setSignInAttendanceMethod(userGroupLogVo.getAttendanceMethod());
                    attendanceUserDetailBo.setSignInFaceMediaId(userGroupLogVo.getFaceMediaId());
                } else {
                    // 第二次早退
                    attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.EARLY.getCode());
                    attendanceUserDetailBo.setSignOutTime(attendanceTime);
                    attendanceUserDetailBo.setSignOutAddress(userGroupLogVo.getAddress());
                    attendanceUserDetailBo.setSignOutDeviceNumber(userGroupLogVo.getDeviceNumber());
                    attendanceUserDetailBo.setSignOutAttendanceMethod(userGroupLogVo.getAttendanceMethod());
                    attendanceUserDetailBo.setSignOutFaceMediaId(userGroupLogVo.getFaceMediaId());
                }
            }

            // 根据考勤组匹配考勤数据：其中至少有一条满足条件，则针对一组考记录附上用户考勤类型标志(签到取第一条)
            if (groupSignInFlag && signInFlag && null == attendanceUserDetailBo.getSignInTime()) {
                attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
                attendanceUserDetailBo.setSignInTime(signInTime);
                attendanceUserDetailBo.setSignInAddress(signInAddress);
                attendanceUserDetailBo.setSignInDeviceNumber(signInDeviceNumber);
                attendanceUserDetailBo.setSignInAttendanceMethod(signInAttendanceMethod);
                attendanceUserDetailBo.setSignInFaceMediaId(signInFaceMediaId);
            }
            if (groupSignOutFlag && signOutFlag) {
                // 之前为空，或者是早退-->则是异常。之前有异常，或者之前是迟到，则采用之前的
                if (null == attendanceUserDetailBo.getSignOutRecordType() || attendanceUserDetailBo
                    .getSignOutRecordType().equals(AttendanceUserRecordTypeEnums.EARLY.getCode())) {
                    attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
                }
                attendanceUserDetailBo.setSignOutTime(signOutTime);
                attendanceUserDetailBo.setSignOutAddress(signOutAddress);
                attendanceUserDetailBo.setSignOutDeviceNumber(signOutDeviceNumber);
                attendanceUserDetailBo.setSignOutAttendanceMethod(signOutAttendanceMethod);
                attendanceUserDetailBo.setSignOutFaceMediaId(signOutFaceMediaId);
            }
            // 按时间排序，最晚签到有效正常，最早无迟到正常。此处代码无用
            // if (groupSignInFlag && groupSignOutFlag) {
            // attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
            // attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
            // }
        }

        // 无签到、签退、迟到、早退记录为缺卡
        if (null == attendanceUserDetailBo.getSignInRecordType()) {
            attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
        }
        if (null == attendanceUserDetailBo.getSignOutRecordType()) {
            attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
        }
        return attendanceUserDetailBo;
    }

    /**
     * 设置一组单个签到或签退考勤规则的开始及结束时间范围，按照完整考勤时间范围前后两小时，中间30分钟计算
     *
     * @param ruleDayBos 考勤规则列表
     * @param ruleDayBosIndex 当前规则在考勤规则的位置
     * @param attendanceLogVos 用户当天考勤流水
     * @param nowDate 计算的日期
     * @param attendanceModeNum 一天几次考勤
     * @return com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail
     * <AUTHOR>
     * @date 2022/11/18 14:30
     */
    private AttendanceUserDetail checkOneOneRule(List<AttendanceRuleDayBo> ruleDayBos, Integer ruleDayBosIndex,
        List<AttendanceLogVo> attendanceLogVos, Date nowDate, Integer attendanceModeNum) {
        AttendanceRuleDayBo attendanceRuleDayBo = ruleDayBos.get(ruleDayBosIndex);
        Date endDate;
        Date startDate;
        Date ruleDate = null;
        if (attendanceRuleDayBo.getSignInTime() != null) {
            ruleDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), nowDate);
        }
        if (attendanceRuleDayBo.getSignOutTime() != null) {
            ruleDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), nowDate);
        }

        if (ruleDayBosIndex == 0) {
            if (AttendanceRuleEnums.ATTENDANCE_MODE_NUM_1.getCode().equals(attendanceModeNum)) {
                startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MAX_MINUTES);
                endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MAX_MINUTES);
            } else {
                startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MAX_MINUTES);
                endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MIN_MINUTES);
            }
        } else if (ruleDayBosIndex == ruleDayBos.size() - 1) {
            startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MIN_MINUTES);
            endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MAX_MINUTES);
        } else {
            startDate = DateKit.addMinute(ruleDate, -AttendanceConstants.MIN_MINUTES);
            endDate = DateKit.addMinute(ruleDate, AttendanceConstants.MIN_MINUTES);
        }
        Integer signLeftMinute = attendanceRuleDayBo.getSignLeftMinute();
        Integer signRightMinute = attendanceRuleDayBo.getSignRightMinute();
        // update by sunqb：数据库如果配置了自定义左右扩张时间，则用数据库的时间作为规则
        if (signLeftMinute != null && signRightMinute != ConstantsInteger.NUM_0) {
            startDate = DateKit.addMinute(ruleDate, -signLeftMinute);
        }
        if (signRightMinute != null && signRightMinute != ConstantsInteger.NUM_0) {
            endDate = DateKit.addMinute(ruleDate, signRightMinute);
        }
        return checkSignType(attendanceLogVos, attendanceRuleDayBo, startDate, endDate, nowDate);
    }

    /**
     * 设置一组单个签到或签退的考勤规则的开始及结束时间范围
     *
     * @param attendanceLogVos 考勤规则列表
     * @param attendanceRuleDayBo 用户当天考勤流水
     * @param beforeDate 规则开始范围
     * @param endDate 规则结束范围
     * @param nowDate 规则日期
     * @return com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail
     * <AUTHOR>
     * @date 2022/11/18 14:33
     */
    private AttendanceUserDetail checkSignType(List<AttendanceLogVo> attendanceLogVos,
        AttendanceRuleDayBo attendanceRuleDayBo, Date beforeDate, Date endDate, Date nowDate) {
        // 匹配一组考勤的dto
        AttendanceUserDetail attendanceUserDetailBo = new AttendanceUserDetail();
        // 一个用户比对完成，存入detailBean
        attendanceUserDetailBo.setAttendanceRuleDayIndex(attendanceRuleDayBo.getAttendanceRuleDayIndex());
        attendanceUserDetailBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        attendanceUserDetailBo.setCreateBy(attendanceLogVos.get(0).getCreateBy());
        attendanceUserDetailBo.setUpdateBy(attendanceLogVos.get(0).getCreateBy());

        // 一组规则，是否有对应考勤标志
        boolean groupSignInFlag = false;
        boolean groupSignOutFlag = false;

        String signInAddress = null;
        String signOutAddress = null;
        Date signInTime = null;
        Date signOutTime = null;
        String signInDeviceNumber = null;
        String signOutDeviceNumber = null;
        Integer signInAttendanceMethod = null;
        Integer signOutAttendanceMethod = null;
        String signInFaceMediaId = null;
        String signOutFaceMediaId = null;
        Date ruleSignInDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignInTime(), nowDate);
        Date ruleSignOutDate = DateKit.transferYMD2Day(attendanceRuleDayBo.getSignOutTime(), nowDate);
        // 签到00+59s
        Date ruleSignInDate59 = DateKit.getAfterSeconds(59, ruleSignInDate);
        for (AttendanceLogVo attendanceLogVo : attendanceLogVos) {
            Date signTime = attendanceLogVo.getAttendanceTime();
            // 单组签到规则
            if (attendanceRuleDayBo.getSignInTime() != null) {
                attendanceUserDetailBo.setOnlySign(AttendanceRuleEnums.ONLY_SIGN_IN.getCode());
                // 正常签到
                if ((signTime.before(ruleSignInDate59) || signTime == ruleSignInDate59) && signTime.after(beforeDate)) {
                    attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
                    signInAddress = attendanceLogVo.getAddress();
                    signInTime = attendanceLogVo.getAttendanceTime();
                    signInDeviceNumber = attendanceLogVo.getDeviceNumber();
                    signInAttendanceMethod = attendanceLogVo.getAttendanceMethod();
                    signInFaceMediaId = attendanceLogVo.getFaceMediaId();
                    // 签到时间地点设备
                    groupSignInFlag = true;
                    break;
                }
                // 迟到
                if ((signTime.after(ruleSignInDate59)) && signTime.before(endDate)) {
                    attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.LATE.getCode());
                    attendanceUserDetailBo.setSignInAddress(attendanceLogVo.getAddress());
                    attendanceUserDetailBo.setSignInTime(attendanceLogVo.getAttendanceTime());
                    attendanceUserDetailBo.setSignInDeviceNumber(attendanceLogVo.getDeviceNumber());
                    attendanceUserDetailBo.setSignInAttendanceMethod(attendanceLogVo.getAttendanceMethod());
                    attendanceUserDetailBo.setSignInFaceMediaId(attendanceLogVo.getFaceMediaId());
                }
            }
            // 单组签退规则
            if (attendanceRuleDayBo.getSignOutTime() != null) {
                attendanceUserDetailBo.setOnlySign(AttendanceRuleEnums.ONLY_SING_OUT.getCode());
                // 早退
                if (signTime.before(ruleSignOutDate) && signTime.after(beforeDate)) {
                    attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.EARLY.getCode());
                    attendanceUserDetailBo.setSignOutAddress(attendanceLogVo.getAddress());
                    attendanceUserDetailBo.setSignOutTime(attendanceLogVo.getAttendanceTime());
                    attendanceUserDetailBo.setSignOutDeviceNumber(attendanceLogVo.getDeviceNumber());
                    attendanceUserDetailBo.setSignOutAttendanceMethod(attendanceLogVo.getAttendanceMethod());
                    attendanceUserDetailBo.setSignOutFaceMediaId(attendanceLogVo.getFaceMediaId());
                }
                // 正常签退
                if ((signTime.after(ruleSignOutDate) || signTime == beforeDate) && signTime.before(endDate)) {
                    attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
                    signOutAddress = attendanceLogVo.getAddress();
                    signOutTime = attendanceLogVo.getAttendanceTime();
                    signOutDeviceNumber = attendanceLogVo.getDeviceNumber();
                    signOutAttendanceMethod = attendanceLogVo.getAttendanceMethod();
                    signOutFaceMediaId = attendanceLogVo.getFaceMediaId();
                    groupSignOutFlag = true;
                }
            }
        }
        // 多种流水整合，有正常数据以正常数据为准
        if (groupSignInFlag) {
            attendanceUserDetailBo.setOnlySign(AttendanceRuleEnums.ONLY_SIGN_IN.getCode());
            attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
            attendanceUserDetailBo.setSignInAddress(signInAddress);
            attendanceUserDetailBo.setSignInTime(signInTime);
            attendanceUserDetailBo.setSignInDeviceNumber(signInDeviceNumber);
            attendanceUserDetailBo.setSignInAttendanceMethod(signInAttendanceMethod);
            attendanceUserDetailBo.setSignInFaceMediaId(signInFaceMediaId);
        }
        if (groupSignOutFlag) {
            attendanceUserDetailBo.setOnlySign(AttendanceRuleEnums.ONLY_SING_OUT.getCode());
            attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());
            attendanceUserDetailBo.setSignOutAddress(signOutAddress);
            attendanceUserDetailBo.setSignOutTime(signOutTime);
            attendanceUserDetailBo.setSignOutDeviceNumber(signOutDeviceNumber);
            attendanceUserDetailBo.setAttendanceRuleDayIndex(signOutAttendanceMethod);
            attendanceUserDetailBo.setSignOutFaceMediaId(signOutFaceMediaId);
        }
        // 都未匹配，缺卡
        if (attendanceUserDetailBo.getSignInRecordType() == null
            && attendanceUserDetailBo.getSignOutRecordType() == null) {
            if (attendanceRuleDayBo.getSignInTime() != null) {
                attendanceUserDetailBo.setSignInRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
                attendanceUserDetailBo.setOnlySign(AttendanceRuleEnums.ONLY_SIGN_IN.getCode());
            }
            if (attendanceRuleDayBo.getSignOutTime() != null) {
                attendanceUserDetailBo.setSignOutRecordType(AttendanceUserRecordTypeEnums.LOSE_SIGN.getCode());
                attendanceUserDetailBo.setOnlySign(AttendanceRuleEnums.ONLY_SING_OUT.getCode());
            }
        }
        return attendanceUserDetailBo;
    }

    /**
     * 单个考勤规则和日期的考勤流水刷入用户考勤表
     *
     * @param attendanceRuleId 考勤规则id
     * @param range 获取指定天数之后的日期
     * @return void
     * <AUTHOR>
     * @date 2022/6/24 15:07
     */
    @XxlJob("oneAttendanceTask")
    public void oneAttendanceTask() {
        log.info("oneAttendanceTask start");
        String jobParam = XxlJobHelper.getJobParam();
        String[] split = jobParam.split(",");
        if (StringUtils.isBlank(jobParam) || split.length != 3) {
            XxlJobHelper.handleFail("参数不足");
            return;
        }

        Long organizationId = Long.valueOf(split[0]);
        Integer attendanceType = Integer.valueOf(split[1]);
        Integer range = Integer.valueOf(split[2]);
        // 取出当前考勤规则指定天的所有日志
        Date nowDate = DateKit.getZeroDate(range, new Date());

        AttendanceRuleAddBo attendanceRuleByOrganizationIdAndType =
            attendanceRuleService.getAttendanceRuleByOrganizationIdAndType(organizationId, attendanceType);
        Long attendanceRuleId = attendanceRuleByOrganizationIdAndType.getAttendanceRuleId();
        AttendanceLogListConditionBo conditionBo = new AttendanceLogListConditionBo();
        conditionBo.setAttendanceTime(DateKit.getStringDay(nowDate));
        conditionBo.setAttendanceRuleId(attendanceRuleId);
        List<AttendanceLogVo> attendanceLogList = attendanceLogService.getAttendanceLogListByCondition(conditionBo);
        List<AttendanceUser> attendanceUsers =
            onceAttendanceRuleCalculate(organizationId, attendanceType, nowDate, attendanceLogList);
        attendanceUserService.saveBatchAndDetail(attendanceUsers);
        XxlJobHelper.handleSuccess();
        log.info("oneAttendanceTask  end");
    }
}
