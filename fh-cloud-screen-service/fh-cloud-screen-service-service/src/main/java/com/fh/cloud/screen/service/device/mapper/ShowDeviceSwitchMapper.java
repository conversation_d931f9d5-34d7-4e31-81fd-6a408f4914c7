package com.fh.cloud.screen.service.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceSwitch;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceSwitchVo;

import java.util.List;

/**
 * 开关机设置Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ShowDeviceSwitchMapper extends BaseMapper<ShowDeviceSwitch> {

    List<ShowDeviceSwitchVo> getShowDeviceSwitchListByCondition(ShowDeviceSwitchListConditionBo condition);

}
