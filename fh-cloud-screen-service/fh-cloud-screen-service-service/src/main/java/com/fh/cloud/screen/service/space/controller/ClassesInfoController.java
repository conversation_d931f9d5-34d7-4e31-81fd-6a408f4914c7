package com.fh.cloud.screen.service.space.controller;

import com.fh.cloud.screen.service.space.api.ClassesInfoApi;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 行政区域内容扩展信息表（班级扩展信息）
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 16:11:44
 */
@RestController
@Validated
public class ClassesInfoController implements ClassesInfoApi {

    @Autowired
    private IClassesInfoService classesInfoService;

    /**
     * 新增行政区域内容扩展信息表（班级扩展信息）
     *
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @ApiOperation(value = "新增行政区域内容扩展信息表（班级扩展信息）", httpMethod = "POST")
    public AjaxResult addClassesInfo(@RequestBody ClassesInfoBo classesInfoBo) {
        boolean save = classesInfoService.addClassesInfo(classesInfoBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改行政区域内容扩展信息表（班级扩展信息）
     *
     * @param classesInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @ApiOperation(value = "修改行政区域内容扩展信息表（班级扩展信息）", httpMethod = "POST")
    public AjaxResult updateClassesInfo(@RequestBody ClassesInfoBo classesInfoBo) {
        if (null == classesInfoBo.getClassesInfoId()) {
            return AjaxResult.fail("行政区域内容扩展信息表（班级扩展信息）id不能为空");
        }
        boolean update = classesInfoService.updateClassesInfo(classesInfoBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询行政区域内容扩展信息表（班级扩展信息）详情
     *
     * @param classesInfoId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @ApiOperation(value = "查询行政区域内容扩展信息表（班级扩展信息）详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("classesInfoId") Long classesInfoId) {
        ClassesInfoVo classesInfoVo = classesInfoService.getDetail(classesInfoId);
        return AjaxResult.success(classesInfoVo);
    }

    /**
     * 删除行政区域内容扩展信息表（班级扩展信息）
     *
     * @param classesInfoId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-05-10 16:11:44
     */
    @ApiOperation(value = "删除行政区域内容扩展信息表（班级扩展信息）", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("classesInfoId") Long classesInfoId) {
        ClassesInfoBo classesInfoBo = new ClassesInfoBo();
        classesInfoBo.setClassesInfoId(classesInfoId);
        classesInfoBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = classesInfoService.updateClassesInfo(classesInfoBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult<ClassesInfoVo> getById(@PathVariable("classInfoId") Long classInfoId) {
        return AjaxResult.success(this.classesInfoService.selectById(classInfoId));
    }

    @Override
    public AjaxResult<ClassesInfoVo> getByClassesId(@PathVariable("classesId") Long classesId) {
        return AjaxResult.success(this.classesInfoService.getByClassesId(classesId));
    }
}
