package com.fh.cloud.screen.service.er.controller;

import com.fh.cloud.screen.service.enums.ExamPlanType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.er.api.ExamPlanApi;
import com.fh.cloud.screen.service.er.entity.dto.ExamPlanDto;
import com.fh.cloud.screen.service.er.service.IExamPlanService;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考场_考试计划
 *
 * <AUTHOR>
 * @email sunqb @ppm.cn
 * @date 2022 -09-29 14:35:17
 */
@RestController
@Validated
public class ExamPlanController implements ExamPlanApi {

    @Autowired
    private IExamPlanService examPlanService;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 查询考场_考试计划分页列表
     *
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<PageInfo<ExamPlanVo>> getExamPlanPageListByCondition(@RequestBody ExamPlanConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<ExamPlanVo> list = examPlanService.getExamPlanListByCondition(condition);
        PageInfo<ExamPlanVo> pageInfo = new PageInfo<>(list);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询考场_考试计划列表
     *
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<List<ExamPlanVo>> getExamPlanListByCondition(@RequestBody ExamPlanConditionBo condition) {
        List<ExamPlanVo> list = examPlanService.getExamPlanListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增考场_考试计划
     *
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult addExamPlan(@Validated @RequestBody ExamPlanBo examPlanBo) {
        return examPlanService.addExamPlan(examPlanBo);
    }

    /**
     * 修改考场_考试计划
     *
     * @param examPlanBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult updateExamPlan(@Validated @RequestBody ExamPlanBo examPlanBo) {
        if (null == examPlanBo.getExamPlanId()) {
            return AjaxResult.fail("考场_考试计划id不能为空");
        }
        return examPlanService.updateExamPlan(examPlanBo);
    }

    /**
     * 查询考场_考试计划详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<ExamPlanVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("考场_考试计划id不能为空");
        }
        ExamPlanVo vo = examPlanService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除考场_考试计划
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ExamPlanDto examPlanDto = new ExamPlanDto();
        examPlanDto.setExamPlanId(id);
        examPlanDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (examPlanService.updateById(examPlanDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 发布或者取消发布_考试计划
     *
     * @param examPlanBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult pubsubExamPlan(@RequestBody ExamPlanBo examPlanBo) {
        if (null == examPlanBo.getExamPlanId()) {
            return AjaxResult.fail("考场_考试计划id不能为空");
        }
        examPlanService.updateExamPlan(examPlanBo);
        // 发布
        if (examPlanBo.getExamPlanType().equals(ExamPlanType.PUBLISHED.getValue())) {
            // publish event
            applicationContext
                .publishEvent(PublishEvent.produceErPublishEvent(MessageWsType.MODIFY_ER_PUBLISH.getValue(),
                    examPlanBo.getOrganizationId(), examPlanBo.getExamPlanId(), null));
        }
        // 取消发布
        else if (examPlanBo.getExamPlanType().equals(ExamPlanType.NOT_PUBLISHED.getValue())) {
            // publish event
            applicationContext
                .publishEvent(PublishEvent.produceErPublishEvent(MessageWsType.MODIFY_ER_CANCEL.getValue(),
                    examPlanBo.getOrganizationId(), examPlanBo.getExamPlanId(), null));
        }
        return AjaxResult.success();
    }

}
