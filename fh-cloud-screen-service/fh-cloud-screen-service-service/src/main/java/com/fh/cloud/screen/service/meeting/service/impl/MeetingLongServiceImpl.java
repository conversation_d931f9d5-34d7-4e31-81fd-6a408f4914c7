package com.fh.cloud.screen.service.meeting.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.enums.MeetingEnums;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingDto;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;
import com.fh.cloud.screen.service.meeting.service.IMeetingService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.ListKit;
import com.fh.cloud.screen.service.utils.StringKit;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongDto;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongUserDto;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongUserVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo;
import com.fh.cloud.screen.service.meeting.mapper.MeetingLongMapper;
import com.fh.cloud.screen.service.meeting.service.IMeetingLongService;
import com.fh.cloud.screen.service.meeting.service.IMeetingLongUserService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 长期预约表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
@Service
public class MeetingLongServiceImpl extends ServiceImpl<MeetingLongMapper, MeetingLongDto>
    implements IMeetingLongService {

    @Resource
    private MeetingLongMapper meetingLongMapper;
    @Resource
    private IMeetingLongUserService meetingLongUserService;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private IMeetingService meetingService;
    @Resource
    private IMeetingLongService meetingLongService;

    @Override
    public List<MeetingLongVo> getMeetingLongListByCondition(MeetingLongConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        List<MeetingLongVo> meetingLongRelationList = meetingLongMapper.getMeetingRelationList(condition);

        // 封装用户姓名
        if (CollectionUtils.isNotEmpty(meetingLongRelationList)) {
            List<String> userOids =
                meetingLongRelationList.stream().map(MeetingLongVo::getUserOid).distinct().collect(Collectors.toList());
            Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOids);
            for (MeetingLongVo meetingLongVo : meetingLongRelationList) {
                meetingLongVo.setUserName(userNameMap.get(meetingLongVo.getUserOid()));
            }
        }
        return meetingLongRelationList;
    }

    @Override
    public AjaxResult addMeetingLong(MeetingLongBo meetingLongBo) {
        MeetingLongDto meetingLong = new MeetingLongDto();
        BeanUtils.copyProperties(meetingLongBo, meetingLong);
        meetingLong.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // 预约时间冲突校验
        MeetingVo meetingVo = checkMeetingDateRepeat(meetingLong);
        if (null != meetingVo) {
            return AjaxResult.fail(
                "与" + meetingVo.getTitle() + "预约时间" + DateKit.date2String(meetingVo.getMeetingDate(), "yyyy-MM-dd")
                    + " " + DateKit.getStringTime(meetingVo.getMeetingStartTime()) + "-"
                    + DateKit.getStringTime(meetingVo.getMeetingEndTime()) + "冲突");
        }
        MeetingLongVo meetingLongVo = checkMeetingLongDateRepeat(meetingLong);
        if (null != meetingLongVo) {
            return AjaxResult.fail(
                "与长期预约" + meetingLongVo.getTitle() + "预约时间" + DateKit.getStringTime(meetingLongVo.getMeetingStartTime())
                    + "-" + DateKit.getStringTime(meetingLongVo.getMeetingEndTime()) + "冲突");
        }

        // 记录db会议已存在人员
        Map<String, MeetingLongUserDto> dbMeetingLongUserMap = new HashMap<>();
        if (null != meetingLong.getMeetingLongId()) {
            LambdaQueryWrapper<MeetingLongUserDto> lqw = new LambdaQueryWrapper<MeetingLongUserDto>()
                .eq(MeetingLongUserDto::getMeetingLongId, meetingLong.getMeetingLongId())
                .eq(MeetingLongUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            List<MeetingLongUserDto> dbMeetingLongUsers = meetingLongUserService.list(lqw);
            if (CollectionUtils.isNotEmpty(dbMeetingLongUsers)) {
                dbMeetingLongUserMap = dbMeetingLongUsers.stream()
                    .collect(Collectors.toMap(MeetingLongUserDto::getUserOid, x -> x, (k1, k2) -> k2));
                meetingLongUserService.remove(lqw);
            }
        }
        // 新增或修改会议
        meetingLong.setStatus(MeetingEnums.MEETING_STATUS_NOT.getCode());
        if (saveOrUpdate(meetingLong)) {
            List<MeetingLongUserBo> meetingLongUserBos = meetingLongBo.getMeetingLongUserBos();
            Map<String, MeetingLongUserDto> finalDbMeetingLongUserMap = dbMeetingLongUserMap;
            List<MeetingLongUserDto> meetingLongUserDtoList = meetingLongUserBos.stream().map(meetingLongUserBo -> {
                MeetingLongUserDto meetingLongUserDto = new MeetingLongUserDto();
                BeanUtils.copyProperties(meetingLongUserBo, meetingLongUserDto);
                MeetingLongUserDto existMeetingLongUser =
                    finalDbMeetingLongUserMap.get(meetingLongUserDto.getUserOid());
                meetingLongUserDto.setStatus(MeetingEnums.SIGN_NOT.getCode());
                meetingLongUserDto.setMeetingLongId(meetingLong.getMeetingLongId());
                if (null != existMeetingLongUser) {
                    meetingLongUserDto.setStatus(existMeetingLongUser.getStatus());
                    meetingLongUserDto.setSignTime(existMeetingLongUser.getSignTime());
                }
                return meetingLongUserDto;
            }).collect(Collectors.toList());
            meetingLongUserService.saveBatch(meetingLongUserDtoList);
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateMeetingLong(MeetingLongBo meetingLongBo) {
        MeetingLongDto meetingLong = new MeetingLongDto();
        BeanUtils.copyProperties(meetingLongBo, meetingLong);
        if (updateById(meetingLong)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public MeetingLongVo getMeetingLongByCondition(MeetingLongConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        MeetingLongVo vo = meetingLongMapper.getMeetingLongByCondition(condition);
        if (vo == null) {
            return null;
        }

        // 查询出该预约规则下的人
        Long meetingLongId = condition.getMeetingLongId();
        LambdaQueryWrapper<MeetingLongUserDto> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MeetingLongUserDto::getMeetingLongId, meetingLongId);
        lambdaQueryWrapper.eq(MeetingLongUserDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        List<MeetingLongUserDto> meetingLongUserDtos = meetingLongUserService.list(lambdaQueryWrapper);
        if (CollectionUtils.isNotEmpty(meetingLongUserDtos)) {
            List<String> userOids =
                meetingLongUserDtos.stream().map(MeetingLongUserDto::getUserOid).collect(Collectors.toList());
            Map<String, String> userNameMap = baseDataService.getRealNameByUserOids(userOids);
            List<MeetingLongUserVo> meetingLongUserVos = meetingLongUserDtos.stream().map(meetingUserDto -> {
                MeetingLongUserVo meetingUserVo = new MeetingLongUserVo();
                BeanUtils.copyProperties(meetingUserDto, meetingUserVo);
                meetingUserVo.setUserName(userNameMap.get(meetingUserVo.getUserOid()));
                return meetingUserVo;
            }).collect(Collectors.toList());
            vo.setMeetingLongUserVos(meetingLongUserVos);
        }
        return vo;
    }

    /**
     * 从长期预约表生成会议表
     * 
     * @param nowDay ：指定生成某天（格式：yyyy-MM-dd）的会议，必填
     * @return
     */
    @Override
    public boolean generateMeetingFromMeetingLong(Date nowDay) {
        if (nowDay == null) {
            return false;
        }

        // 查询包含当天日期的长期预约会议
        MeetingLongConditionBo meetingLongConditionBo = new MeetingLongConditionBo();
        meetingLongConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        meetingLongConditionBo.setContainsDate(nowDay);
        List<MeetingLongVo> meetingLongVoList =
            meetingLongService.getMeetingLongListByCondition(meetingLongConditionBo);
        if (CollectionUtils.isEmpty(meetingLongVoList)) {
            return true;
        }

        // 根据长期预约会议生成当天的会议
        for (MeetingLongVo meetingLongVo : meetingLongVoList) {
            // 长期预约的星期不包含当天直接跳过
            List<String> weeksList = StringKit.splitString2List(meetingLongVo.getWeeks(), null);
            String nowDayWeek = String.valueOf(DateKit.getWeekIdByDate(new Date()));
            if (!weeksList.contains(nowDayWeek)) {
                continue;
            }

            // 本次会议查询人员
            Long meetingLongId = meetingLongVo.getMeetingLongId();
            MeetingLongUserConditionBo meetingLongUserConditionBo = new MeetingLongUserConditionBo();
            meetingLongUserConditionBo.setMeetingLongId(meetingLongId);
            meetingLongUserConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            List<MeetingLongUserVo> meetingLongUserVoList =
                meetingLongUserService.getMeetingLongUserListByCondition(meetingLongUserConditionBo);

            // 封装当天会议信息并入库
            MeetingBo meetingBo = new MeetingBo();
            BeanUtils.copyProperties(meetingLongVo, meetingBo);
            meetingBo.setMeetingDate(nowDay);
            meetingBo.setTriggerExecute(true);
            meetingBo.setCreateTime(new Date());
            List<MeetingUserBo> meetingUserBos = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(meetingLongUserVoList)) {
                meetingUserBos = meetingLongUserVoList.stream().map(meetingLongUserVo -> {
                    MeetingUserBo meetingUserBo = new MeetingUserBo();
                    BeanUtils.copyProperties(meetingLongUserVo, meetingUserBo);
                    return meetingUserBo;
                }).collect(Collectors.toList());
                meetingBo.setMeetingUserBos(meetingUserBos);
            }
            meetingService.addMeeting(meetingBo);
        }
        return true;
    }

    /**
     * 会议时间冲突校验
     *
     * @param meeting the meeting
     * @return the meeting vo
     * <AUTHOR>
     * @date 2023 -12-12 10:11:06
     */
    private MeetingVo checkMeetingDateRepeat(MeetingLongDto meeting) {
        List<String> weeksList = StringKit.splitString2List(meeting.getWeeks(), null);
        MeetingConditionBo meetingConditionBo = new MeetingConditionBo();
        meetingConditionBo.setOrganizationId(meeting.getOrganizationId());
        meetingConditionBo.setMeetingStartDate(meeting.getMeetingStartDate());
        meetingConditionBo.setMeetingEndDate(meeting.getMeetingEndDate());
        meetingConditionBo.setSpaceGroupUseType(meeting.getSpaceGroupUseType());
        meetingConditionBo.setSpaceInfoId(meeting.getSpaceInfoId());
        meetingConditionBo.setQueryRepeat(true);
        List<MeetingVo> meetingVos = meetingService.getMeetingListByCondition(meetingConditionBo);

        if (CollectionUtils.isNotEmpty(meetingVos)) {
            // 只校验时间，不校验日期
            Date date = new Date();
            for (MeetingVo vo : meetingVos) {
                // 长期预约的校验需要用包含日期的日期时间段来校验
                Date meetingStartDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingStartTime());
                Date meetingEndDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingEndTime());
                String nowDayWeek = String.valueOf(DateKit.getWeekIdByDate(vo.getMeetingDate()));

                Date startDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingStartTime());
                Date endDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingEndTime());
                // 时间校验有重复
                boolean timeCheckRepeat = false;
                // 开始时间相等
                if (meetingStartDateTime.equals(startDateTime)) {
                    timeCheckRepeat = true;
                }
                // 当前开始时间在 已有开始、结束时间范围内
                if (meetingStartDateTime.after(startDateTime) && meetingStartDateTime.before(endDateTime)) {
                    timeCheckRepeat = true;
                }
                // 当前结束时间，在已有开始、结束时间范围内。
                if (meetingEndDateTime.after(startDateTime) && meetingEndDateTime.before(endDateTime)) {
                    timeCheckRepeat = true;
                }
                // 当前时间段，包含已有时间段
                if (meetingStartDateTime.before(startDateTime)
                    && (meetingEndDateTime.after(endDateTime) || meetingEndDateTime.equals(endDateTime))) {
                    timeCheckRepeat = true;
                }
                // 星期校验重复
                boolean weekCheckRepeat = false;
                if (weeksList.contains(nowDayWeek)) {
                    weekCheckRepeat = true;
                }

                // 时间校验重复且星期校验重复则返回vo
                if (timeCheckRepeat && weekCheckRepeat) {
                    return vo;
                }
            }
        }
        return null;
    }

    /**
     * 长期会议时间冲突校验
     *
     * @param meeting the meeting
     * @return the meeting vo
     * <AUTHOR>
     * @date 2023 -12-12 09:44:41
     */
    private MeetingLongVo checkMeetingLongDateRepeat(MeetingLongDto meeting) {
        List<String> weeksList = StringKit.splitString2List(meeting.getWeeks(), null);
        MeetingLongConditionBo meetingLongConditionBo = new MeetingLongConditionBo();
        meetingLongConditionBo.setOrganizationId(meeting.getOrganizationId());
        meetingLongConditionBo.setMeetingStartDate(meeting.getMeetingStartDate());
        meetingLongConditionBo.setMeetingEndDate(meeting.getMeetingEndDate());
        meetingLongConditionBo.setSpaceGroupUseType(meeting.getSpaceGroupUseType());
        meetingLongConditionBo.setSpaceInfoId(meeting.getSpaceInfoId());
        meetingLongConditionBo.setQueryRepeat(true);
        List<MeetingLongVo> meetingLongVos = meetingLongService.getMeetingLongListByCondition(meetingLongConditionBo);

        if (CollectionUtils.isNotEmpty(meetingLongVos)) {
            // 只比较时间，日期忽略
            Date date = new Date();
            Date meetingStartDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingStartTime());
            Date meetingEndDateTime = DateKit.getDateAndTimeCompose(date, meeting.getMeetingEndTime());
            for (MeetingLongVo vo : meetingLongVos) {
                if (vo.getMeetingLongId().equals(meeting.getMeetingLongId())) {
                    continue;
                }
                List<String> weeksListTemp = StringKit.splitString2List(vo.getWeeks(), null);
                Date startDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingStartTime());
                Date endDateTime = DateKit.getDateAndTimeCompose(date, vo.getMeetingEndTime());
                // 时间校验有重复
                boolean timeCheckRepeat = false;
                // 开始时间相等
                if (meetingStartDateTime.equals(startDateTime)) {
                    timeCheckRepeat = true;
                }
                // 当前开始时间在 已有开始、结束时间范围内
                if (meetingStartDateTime.after(startDateTime) && meetingStartDateTime.before(endDateTime)) {
                    timeCheckRepeat = true;
                }
                // 当前结束时间，在已有开始、结束时间范围内。
                if (meetingEndDateTime.after(startDateTime) && meetingEndDateTime.before(endDateTime)) {
                    timeCheckRepeat = true;
                }
                // 当前时间段，包含已有时间段
                if (meetingStartDateTime.before(startDateTime)
                    && (meetingEndDateTime.after(endDateTime) || meetingEndDateTime.equals(endDateTime))) {
                    timeCheckRepeat = true;
                }
                // 星期校验重复
                boolean weekCheckRepeat = false;
                if (ListKit.checkRepeat(weeksList, weeksListTemp)) {
                    weekCheckRepeat = true;
                }

                // 时间校验重复且星期校验重复则返回vo
                if (timeCheckRepeat && weekCheckRepeat) {
                    return vo;
                }
            }
        }
        return null;
    }
}