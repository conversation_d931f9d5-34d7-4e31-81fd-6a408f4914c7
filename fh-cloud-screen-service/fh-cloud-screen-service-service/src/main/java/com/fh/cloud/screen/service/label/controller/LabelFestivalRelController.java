package com.fh.cloud.screen.service.label.controller;

import com.fh.cloud.screen.service.label.api.LabelFestivalRelApi;
import com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto;
import com.fh.cloud.screen.service.label.service.ILabelFestivalRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelFestivalRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
/**
 * 标签节日关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@RestController
@Validated
public class LabelFestivalRelController implements LabelFestivalRelApi{
	
    @Autowired
    private ILabelFestivalRelService labelFestivalRelService;

    /**
     * 查询标签节日关联表分页列表
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult<PageInfo<LabelFestivalRelVo>> getLabelFestivalRelPageListByCondition(@RequestBody LabelFestivalRelConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<LabelFestivalRelVo> pageInfo = new PageInfo<>(labelFestivalRelService.getLabelFestivalRelListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询标签节日关联表列表
	 * <AUTHOR>
	 * @date 2023-02-27 10:16:32
	 */
	@Override
	public AjaxResult<List<LabelFestivalRelVo>> getLabelFestivalRelListByCondition(@RequestBody LabelFestivalRelConditionBo condition){
		List<LabelFestivalRelVo> list = labelFestivalRelService.getLabelFestivalRelListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增标签节日关联表
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
	@Override
    public AjaxResult addLabelFestivalRel(@Validated @RequestBody LabelFestivalRelBo labelFestivalRelBo){
		return labelFestivalRelService.addLabelFestivalRel(labelFestivalRelBo);
    }

    /**
	 * 修改标签节日关联表
	 * @param labelFestivalRelBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
	 */
	@Override
	public AjaxResult updateLabelFestivalRel(@Validated @RequestBody LabelFestivalRelBo labelFestivalRelBo) {
		if(null == labelFestivalRelBo.getId()) {
			return AjaxResult.fail("标签节日关联表id不能为空");
		}
		return labelFestivalRelService.updateLabelFestivalRel(labelFestivalRelBo);
	}

	/**
	 * 查询标签节日关联表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
	 */
	@Override
	public AjaxResult<LabelFestivalRelVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("标签节日关联表id不能为空");
		}
		LabelFestivalRelVo vo = labelFestivalRelService.getDetail(id);
		return AjaxResult.success(vo);
	}
    
    /**
	 * 删除标签节日关联表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		LabelFestivalRelDto labelFestivalRelDto = new LabelFestivalRelDto();
		labelFestivalRelDto.setId(id);
		labelFestivalRelDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(labelFestivalRelService.updateById(labelFestivalRelDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}
}
