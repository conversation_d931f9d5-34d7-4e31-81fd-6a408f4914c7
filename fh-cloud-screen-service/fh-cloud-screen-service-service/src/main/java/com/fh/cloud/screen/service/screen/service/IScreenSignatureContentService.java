package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureContentDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 电子签名表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
public interface IScreenSignatureContentService extends IService<ScreenSignatureContentDto> {

    List<ScreenSignatureContentVo> getScreenSignatureContentListByCondition(ScreenSignatureContentConditionBo condition);

	AjaxResult addScreenSignatureContent(ScreenSignatureContentBo screenSignatureContentBo);

	AjaxResult updateScreenSignatureContent(ScreenSignatureContentBo screenSignatureContentBo);

	ScreenSignatureContentVo getScreenSignatureContentByCondition(ScreenSignatureContentConditionBo condition);

}

