package com.fh.cloud.screen.service.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserShowVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface AttendanceUserMapper extends BaseMapper<AttendanceUser> {

    List<AttendanceUserVo> getAttendanceUserListByCondition(AttendanceUserListConditionBo condition);

    List<AttendanceUserVo> getSignInListByUserOidsAndDateDay(@Param("userOids") Collection<String> userOids,
        @Param("date") String date);

    List<AttendanceUserShowVo> getAttendanceUserExportListByCondition(AttendanceUserListConditionBo condition);
}
