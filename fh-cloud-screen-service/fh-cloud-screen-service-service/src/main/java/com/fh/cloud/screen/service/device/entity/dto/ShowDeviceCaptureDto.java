package com.fh.cloud.screen.service.device.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备抓图表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("show_device_capture")
public class ShowDeviceCaptureDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 学校id，0表示公共配置
     */
    @TableField("show_device_id")
    private Long showDeviceId;

    /**
     * 学校id，0表示公共配置，冗余存储
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 设备号,冗余存储
     */
    @TableField("device_number")
    private String deviceNumber;

    /**
     * 抓图状态：1抓取中，2抓取成功，3抓取失败
     */
    @TableField("device_capture_status")
    private Integer deviceCaptureStatus;

    /**
     * 文件oid
     */
    @TableField("device_capture_file_oid")
    private String deviceCaptureFileOid;

    /**
     * 截图地址
     */
    @TableField("device_capture_media_url")
    private String deviceCaptureMediaUrl;

    /**
     * 截图压缩图地址
     */
    @TableField("device_capture_media_url_compress")
    private String deviceCaptureMediaUrlCompress;

    /**
     * 截图名称（不包含后缀）
     */
    @TableField("device_capture_media_name")
    private String deviceCaptureMediaName;

    /**
     * 截图名称（包含后缀）
     */
    @TableField("device_capture_media_name_ori")
    private String deviceCaptureMediaNameOri;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
