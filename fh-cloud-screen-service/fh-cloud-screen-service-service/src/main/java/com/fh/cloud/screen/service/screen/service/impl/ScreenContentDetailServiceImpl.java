package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentDetail;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenContentDetailMapper;
import com.fh.cloud.screen.service.screen.service.IScreenContentDetailService;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云屏内容详情表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Service
public class ScreenContentDetailServiceImpl extends ServiceImpl<ScreenContentDetailMapper, ScreenContentDetail>
    implements IScreenContentDetailService {

    @Resource
    private ScreenContentDetailMapper screenContentDetailMapper;
    @Lazy
    @Autowired
    private IScreenContentDetailService screenContentDetailService;

    @Override
    public List<ScreenContentDetailVo>
        getScreenContentDetailListByCondition(ScreenContentDetailListConditionBo condition) {
        return screenContentDetailMapper.getScreenContentDetailListByCondition(condition);
    }

    @Override
    public boolean addScreenContentDetail(ScreenContentDetailBo screenContentDetailBo) {
        ScreenContentDetail screenContentDetail = new ScreenContentDetail();
        BeanUtils.copyProperties(screenContentDetailBo, screenContentDetail);
        screenContentDetail.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(screenContentDetail);
    }

    @Override
    public boolean updateScreenContentDetail(ScreenContentDetailBo screenContentDetailBo) {
        ScreenContentDetail screenContentDetail = new ScreenContentDetail();
        BeanUtils.copyProperties(screenContentDetailBo, screenContentDetail);
        return updateById(screenContentDetail);
    }

    @Override
    public ScreenContentDetailVo getDetail(Long screenContentDetailId) {
        if (screenContentDetailId == null) {
            return null;
        }

        LambdaQueryWrapper<ScreenContentDetail> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenContentDetail::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenContentDetail::getScreenContentDetailId, screenContentDetailId);
        ScreenContentDetail screenContentDetail = getOne(lqw);
        ScreenContentDetailVo screenContentDetailVo = new ScreenContentDetailVo();
        BeanUtils.copyProperties(screenContentDetail, screenContentDetailVo);
        return screenContentDetailVo;
    }

    @Override
    public List<ScreenContentDetailVo> listScreenContentDetailByScreenContentIds(List<Long> screenContentIds) {
        if (CollectionUtils.isEmpty(screenContentIds)) {
            return Lists.newArrayList();
        }
        return screenContentDetailMapper.listScreenContentDetailByScreenContentIds(screenContentIds);
    }

    @Override
    public boolean saveOrUpdateScreenContentDetailBatch(List<ScreenContentDetailBo> screenContentDetailBos) {
        if (CollectionUtils.isEmpty(screenContentDetailBos)) {
            return true;
        }

        List<ScreenContentDetail> screenContentDetails = screenContentDetailBos.stream().map(screenContentDetailBo -> {
            if (screenContentDetailBo.getScreenContentDetailId() == null) {
                screenContentDetailBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            }
            ScreenContentDetail screenContentDetail = new ScreenContentDetail();
            BeanUtils.copyProperties(screenContentDetailBo, screenContentDetail);
            return screenContentDetail;
        }).collect(Collectors.toList());
        return saveOrUpdateBatch(screenContentDetails);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean deleteAndAddScreenContentDetailBatch(Long screenContentId,
        List<ScreenContentDetailBo> screenContentDetailBos) {
        // 删除
        screenContentDetailService.deleteByScreenContentId(screenContentId);

        // 批量新增
        screenContentDetailBos.stream()
            .forEach(screenContentDetailBo -> screenContentDetailBo.setScreenContentDetailId(null));
        screenContentDetailService.saveOrUpdateScreenContentDetailBatch(screenContentDetailBos);
        return true;
    }

    /**
     * 根据screenContentId删除
     *
     * @param screenContentId the screen content id
     * <AUTHOR>
     * @date 2022 -05-09 16:11:23
     */
    @Override
    public void deleteByScreenContentId(Long screenContentId) {
        LambdaUpdateWrapper<ScreenContentDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ScreenContentDetail::getScreenContentId, screenContentId);
        ScreenContentDetail screenContentDetail = new ScreenContentDetail();
        screenContentDetail.setIsDelete(StatusEnum.ISDELETE.getCode());
        screenContentDetailMapper.update(screenContentDetail, updateWrapper);
    }

    @Override
    public void updateFirstImgUrlByScreenContentId(Long screenContentId, String screenContentMediaUrl,
        String screenContentMediaId) {
        LambdaQueryWrapper<ScreenContentDetail> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenContentDetail::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenContentDetail::getScreenContentId, screenContentId);
        lqw.last("limit 1");
        ScreenContentDetail screenContentDetail = getOne(lqw);
        if (screenContentDetail == null) {
            return;
        }
        screenContentDetail.setScreenContentMediaUrl(screenContentMediaUrl);
        screenContentDetail.setScreenContentMediaId(screenContentMediaId);
        updateById(screenContentDetail);
    }
}