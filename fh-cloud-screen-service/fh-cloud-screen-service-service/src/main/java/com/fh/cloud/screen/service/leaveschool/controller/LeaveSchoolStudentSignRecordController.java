package com.fh.cloud.screen.service.leaveschool.controller;

import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolStudentSignRecordApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordSaveBatchBo;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolStudentSignRecordDto;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolStudentSignRecordService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 学生进出记录表
 *
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:58:52
 */
@RestController
@Validated
public class LeaveSchoolStudentSignRecordController implements LeaveSchoolStudentSignRecordApi{
	
    @Autowired
    private ILeaveSchoolStudentSignRecordService leaveSchoolStudentSignRecordService;

    /**
     * 查询学生进出记录表分页列表
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
    @Override
    public AjaxResult<PageInfo<LeaveSchoolStudentSignRecordVo>> getLeaveSchoolStudentSignRecordPageListByCondition(@RequestBody LeaveSchoolStudentSignRecordConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<LeaveSchoolStudentSignRecordVo> pageInfo = new PageInfo<>(leaveSchoolStudentSignRecordService.getLeaveSchoolStudentSignRecordListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询学生进出记录表列表
	 * <AUTHOR>
	 * @date 2025-04-10 10:58:52
	 */
	@Override
	public AjaxResult<List<LeaveSchoolStudentSignRecordVo>> getLeaveSchoolStudentSignRecordListByCondition(@RequestBody LeaveSchoolStudentSignRecordConditionBo condition){
		List<LeaveSchoolStudentSignRecordVo> list = leaveSchoolStudentSignRecordService.getLeaveSchoolStudentSignRecordListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增学生进出记录表
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
     */
	@Override
    public AjaxResult addLeaveSchoolStudentSignRecord(@Validated @RequestBody LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo){
		return leaveSchoolStudentSignRecordService.addLeaveSchoolStudentSignRecord(leaveSchoolStudentSignRecordBo);
    }

    /**
	 * 修改学生进出记录表
	 * @param leaveSchoolStudentSignRecordBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
	 */
	@Override
	public AjaxResult updateLeaveSchoolStudentSignRecord(@Validated @RequestBody LeaveSchoolStudentSignRecordBo leaveSchoolStudentSignRecordBo) {
		if(null == leaveSchoolStudentSignRecordBo.getLeaveSchoolStudentSignRecordId()) {
			return AjaxResult.fail("学生进出记录表id不能为空");
		}
		return leaveSchoolStudentSignRecordService.updateLeaveSchoolStudentSignRecord(leaveSchoolStudentSignRecordBo);
	}

	/**
	 * 查询学生进出记录表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
	 */
	@Override
	public AjaxResult<LeaveSchoolStudentSignRecordVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("学生进出记录表id不能为空");
		}
		LeaveSchoolStudentSignRecordConditionBo condition = new LeaveSchoolStudentSignRecordConditionBo();
		condition.setLeaveSchoolStudentSignRecordId(id);
		LeaveSchoolStudentSignRecordVo vo = leaveSchoolStudentSignRecordService.getLeaveSchoolStudentSignRecordByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除学生进出记录表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-04-10 10:58:52
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		LeaveSchoolStudentSignRecordDto leaveSchoolStudentSignRecordDto = new LeaveSchoolStudentSignRecordDto();
		leaveSchoolStudentSignRecordDto.setLeaveSchoolStudentSignRecordId(id);
		leaveSchoolStudentSignRecordDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(leaveSchoolStudentSignRecordService.updateById(leaveSchoolStudentSignRecordDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	@Override
	public AjaxResult addBatch(LeaveSchoolStudentSignRecordSaveBatchBo leaveSchoolStudentSignRecordSaveBatchBo) {
		List<LeaveSchoolStudentSignRecordDto> entities
				= leaveSchoolStudentSignRecordSaveBatchBo.getLeaveSchoolStudentSignRecordBoList().stream().map(bo -> {
					LeaveSchoolStudentSignRecordDto entity = new LeaveSchoolStudentSignRecordDto();
					BeanUtils.copyProperties(bo, entity);
					return entity;
				}).collect(Collectors.toList());
		leaveSchoolStudentSignRecordService.saveBatch(entities);
		return AjaxResult.success();
	}

}
