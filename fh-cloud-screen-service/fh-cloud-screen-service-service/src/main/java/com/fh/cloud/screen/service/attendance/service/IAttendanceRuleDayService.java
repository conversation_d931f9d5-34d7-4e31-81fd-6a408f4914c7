package com.fh.cloud.screen.service.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRuleDay;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo;

import java.util.List;
import java.util.Map;

/**
 * 考勤规则天表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface IAttendanceRuleDayService extends IService<AttendanceRuleDay> {

    List<AttendanceRuleDayVo> getAttendanceRuleDayListByCondition(AttendanceRuleDayListConditionBo condition);

    boolean addAttendanceRuleDay(AttendanceRuleDayBo attendanceRuleDayBo);

    boolean updateAttendanceRuleDay(AttendanceRuleDayBo attendanceRuleDayBo);

    Map<String, Object> getDetail(Long attendanceRuleDayId);

}
