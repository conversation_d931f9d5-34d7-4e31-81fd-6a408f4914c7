package com.fh.cloud.screen.service.screen.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.enums.ScreenModuleLibraryCollectEnums;
import com.fh.cloud.screen.service.label.service.ILabelService;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryCollectDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryCollectService;
import com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryCollectMapper;
import com.light.core.entity.AjaxResult;

/**
 * 海报收藏表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@Service
public class ScreenModuleLibraryCollectServiceImpl
    extends ServiceImpl<ScreenModuleLibraryCollectMapper, ScreenModuleLibraryCollectDto>
    implements IScreenModuleLibraryCollectService {

    @Resource
    private ScreenModuleLibraryCollectMapper screenModuleLibraryCollectMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Resource
    private ILabelService labelService;

    @Override
    public List<ScreenModuleLibraryCollectVo>
        getScreenModuleLibraryCollectListByCondition(ScreenModuleLibraryCollectConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenModuleLibraryCollectMapper.getScreenModuleLibraryCollectListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        screenModuleLibraryCollectBo.setUserOid(baseDataService.getCurrentUserOid());
        ScreenModuleLibraryCollectDto screenModuleLibraryCollect = new ScreenModuleLibraryCollectDto();
        BeanUtils.copyProperties(screenModuleLibraryCollectBo, screenModuleLibraryCollect);
        // 通过标签获取是否关联校本标签
        Long organizationId =
            labelService.getLabelOrganizationIdByLibraryId(screenModuleLibraryCollect.getScreenModuleLibraryId());

        if (0L != organizationId) {
            screenModuleLibraryCollect.setIsSchoolPoster(ScreenModuleLibraryCollectEnums.IS_SCHOOL_POSTER.getValue());
        } else {
            screenModuleLibraryCollect.setIsSchoolPoster(ScreenModuleLibraryCollectEnums.NOT_SCHOOL_POSTER.getValue());
        }
        screenModuleLibraryCollect.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenModuleLibraryCollect)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        ScreenModuleLibraryCollectDto screenModuleLibraryCollect = new ScreenModuleLibraryCollectDto();
        BeanUtils.copyProperties(screenModuleLibraryCollectBo, screenModuleLibraryCollect);
        if (updateById(screenModuleLibraryCollect)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenModuleLibraryCollectVo getDetail(Long id) {
        ScreenModuleLibraryCollectConditionBo condition = new ScreenModuleLibraryCollectConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ScreenModuleLibraryCollectVo> list =
            screenModuleLibraryCollectMapper.getScreenModuleLibraryCollectListByCondition(condition);
        ScreenModuleLibraryCollectVo vo = new ScreenModuleLibraryCollectVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public List<Long> getHotPosterModuleLibraryIds(Integer limit) {
        return screenModuleLibraryCollectMapper.getHotPosterModuleLibraryIds(limit);
    }

    @Override
    public AjaxResult delScreenModuleLibraryCollect(ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        screenModuleLibraryCollectBo.setUserOid(baseDataService.getCurrentUserOid());
        LambdaQueryWrapper<ScreenModuleLibraryCollectDto> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ScreenModuleLibraryCollectDto::getOrganizationId,
            screenModuleLibraryCollectBo.getOrganizationId());
        lambdaQueryWrapper.eq(ScreenModuleLibraryCollectDto::getUserOid, screenModuleLibraryCollectBo.getUserOid());
        lambdaQueryWrapper.eq(ScreenModuleLibraryCollectDto::getScreenModuleLibraryId,
            screenModuleLibraryCollectBo.getScreenModuleLibraryId());
        this.remove(lambdaQueryWrapper);
        return AjaxResult.success();
    }

}