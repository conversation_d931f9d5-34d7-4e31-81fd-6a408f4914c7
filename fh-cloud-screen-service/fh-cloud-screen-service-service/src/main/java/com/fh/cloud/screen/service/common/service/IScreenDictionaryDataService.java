package com.fh.cloud.screen.service.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataBo;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.common.entity.dto.DictionaryData;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/30 16:59
 */
public interface IScreenDictionaryDataService extends IService<DictionaryData> {

    List<DictionaryDataVo> getDictionaryDataListByCondition(DictionaryDataListConditionBo condition);

    /**
     * 根据dictType 查询字典数据<字典value,字典lable名称>
     *
     * @param dictType
     * @return
     */
    Map<String, String> getDictionaryMapByType(String dictType);

    /**
     * 新增海报分组数据
     *
     * @param dictionaryDataBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/12 10:38
     */
    AjaxResult addDictionaryData(DictionaryDataBo dictionaryDataBo);

    AjaxResult updateDictionaryData(DictionaryDataBo dictionaryDataBo);
}
