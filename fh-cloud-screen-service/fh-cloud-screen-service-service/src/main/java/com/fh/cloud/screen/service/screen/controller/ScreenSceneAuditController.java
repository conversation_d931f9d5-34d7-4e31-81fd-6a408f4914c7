package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.enums.ScreenScenePublishType;
import com.fh.cloud.screen.service.screen.api.ScreenSceneAuditApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneAuditDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneAuditService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 云屏场景审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-11-30 10:18:23
 */
@RestController
@Validated
public class ScreenSceneAuditController implements ScreenSceneAuditApi{
	
    @Autowired
    private IScreenSceneAuditService screenSceneAuditService;

    /**
     * 查询云屏场景审核表分页列表
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ScreenSceneAuditVo>> getScreenSceneAuditPageListByCondition(@RequestBody ScreenSceneAuditConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ScreenSceneAuditVo> pageInfo = new PageInfo<>(screenSceneAuditService.getScreenSceneAuditListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询云屏场景审核表列表
	 * <AUTHOR>
	 * @date 2023-11-30 10:18:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ScreenSceneAuditVo>> getScreenSceneAuditListByCondition(@RequestBody ScreenSceneAuditConditionBo condition){
		List<ScreenSceneAuditVo> list = screenSceneAuditService.getScreenSceneAuditListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增云屏场景审核表
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addScreenSceneAudit(@Validated @RequestBody ScreenSceneAuditBo screenSceneAuditBo){
		return screenSceneAuditService.addScreenSceneAudit(screenSceneAuditBo);
    }

    /**
	 * 修改云屏场景审核表
	 * @param screenSceneAuditBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateScreenSceneAudit(@Validated @RequestBody ScreenSceneAuditBo screenSceneAuditBo) {
		if(null == screenSceneAuditBo.getScreenSceneAuditId()) {
			return AjaxResult.fail("云屏场景审核表id不能为空");
		}
		return screenSceneAuditService.updateScreenSceneAudit(screenSceneAuditBo);
	}

	/**
	 * 查询云屏场景审核表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ScreenSceneAuditVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("云屏场景审核表id不能为空");
		}
		ScreenSceneAuditConditionBo condition = new ScreenSceneAuditConditionBo();
		condition.setScreenSceneAuditId(id);
		ScreenSceneAuditVo vo = screenSceneAuditService.getScreenSceneAuditByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除云屏场景审核表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-11-30 10:18:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ScreenSceneAuditDto screenSceneAuditDto = new ScreenSceneAuditDto();
		screenSceneAuditDto.setScreenSceneAuditId(id);
		screenSceneAuditDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(screenSceneAuditService.updateById(screenSceneAuditDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

	/**
	 * 审核
	 *
	 * @param screenSceneAuditBo
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/11/30 13:46
	 **/
	@Override
	public AjaxResult screenSceneAudit(@RequestBody ScreenSceneAuditBo screenSceneAuditBo) {
		if (screenSceneAuditBo.getScreenSceneAuditId() == null) {
			return AjaxResult.fail("请选择需要审核的数据");
		}
		if (screenSceneAuditBo.getAuditType() == null) {
			return AjaxResult.fail("审核状态为空");
		}
		return screenSceneAuditService.audit(screenSceneAuditBo);
	}

	/**
	 * 批量审核
	 *
	 * @param screenSceneAuditBo
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/11/30 15:20
	 **/
	@Override
	public AjaxResult screenSceneAuditBatch(@RequestBody ScreenSceneAuditBo screenSceneAuditBo) {
		if (CollectionUtils.isEmpty(screenSceneAuditBo.getScreenSceneAuditIds())) {
			return AjaxResult.fail("请选择需要审核的数据");
		}
		if (screenSceneAuditBo.getAuditType() == null) {
			return AjaxResult.fail("审核状态为空");
		}
		for (Long screenSceneAuditId : screenSceneAuditBo.getScreenSceneAuditIds()) {
			ScreenSceneAuditBo bo = new ScreenSceneAuditBo();
			bo.setScreenSceneAuditId(screenSceneAuditId);
			bo.setAuditType(screenSceneAuditBo.getAuditType());
			bo.setAuditUser(screenSceneAuditBo.getAuditUser());
			bo.setReason(screenSceneAuditBo.getReason());
			screenSceneAuditService.audit(bo);
		}
		return AjaxResult.success();
	}

	/**
	 * 待发布云屏数据
	 *
	 * @param showDeviceId
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/11/30 16:20
	 **/
	@Override
	public AjaxResult screenToAudit(@RequestParam("screenSceneAuditId") Long screenSceneAuditId) {
		return screenSceneAuditService.screenToAudit(screenSceneAuditId);
	}

	/**
	 * 获取审核数量
	 *
	 * @param condition
	 * @return com.light.core.entity.AjaxResult
	 * <AUTHOR>
	 * @date 2023/12/1 9:25
	 **/
	@Override
	public AjaxResult getScreenSceneAuditCount(@RequestBody ScreenSceneAuditConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if (condition.isSearchDeviceScreenScene()) {
			condition.setPublishType(ScreenScenePublishType.POINT.getValue());
		} else {
			condition.setPublishType(ScreenScenePublishType.GLOBAL.getValue());
		}
		return screenSceneAuditService.getScreenSceneAuditCount(condition);
	}



}
