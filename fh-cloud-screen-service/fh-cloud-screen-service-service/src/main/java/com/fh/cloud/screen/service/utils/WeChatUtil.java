package com.fh.cloud.screen.service.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;

import java.text.MessageFormat;

/**
 * @Author: liuzeyu
 * @CreateTime: 2024-09-26  16:10
 */
@Slf4j
public class WeChatUtil {
    private static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={0}&secret={1}";

    public static String getAccessToken(String appId, String appSecret) throws Exception {
        String url = MessageFormat.format(TOKEN_URL, appId, appSecret);
        HttpGet request = new HttpGet(url);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String result = httpClient.execute(request, httpResponse ->
                EntityUtils.toString(httpResponse.getEntity()));
        log.info("获取微信TOKEN返回：" + result + "，请求url：" + url);
        // 解析JSON获取access_token，这里假设已经通过某种方式(如Jackson, Gson)解析
        // 这里简单用String.split()模拟解析
        JSONObject tokenJson = JSONObject.parseObject(result);
        String token = tokenJson.get("access_token").toString();
        log.info("获取到的TOKEN : "+token);
        return token;
    }

    public static void sendTemplateMessage(String accessToken, WxMpTemplateMessage wxMpTemplateMessage) throws Exception {
        String messageUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
        HttpPost request = new HttpPost(messageUrl);
        request.setHeader(new BasicHeader("Content-Type", "application/json; charset=UTF-8"));
        request.setEntity(new StringEntity(wxMpTemplateMessage.toJson(), "UTF-8"));

        CloseableHttpClient httpClient = HttpClients.createDefault();
        String result = httpClient.execute(request, httpResponse ->
                EntityUtils.toString(httpResponse.getEntity()));
        log.info("发送结果: " + result);
    }

}
