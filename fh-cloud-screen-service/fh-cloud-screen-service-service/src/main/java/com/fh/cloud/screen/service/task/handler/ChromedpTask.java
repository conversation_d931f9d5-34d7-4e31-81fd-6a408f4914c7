package com.fh.cloud.screen.service.task.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fh.cloud.screen.service.chromedp.service.ChromedpService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * url转图片截图的task(暂定5分钟一次)
 * 
 * <AUTHOR>
 * @date 2022/12/29 14:37
 */
@Component
@Slf4j
public class ChromedpTask {
    @Autowired
    private ChromedpService chromedpService;

    /**
     * 手动推送初始化云屏的接口
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("ChromedpTask-consume")
    public void consume() throws Exception {
        log.info("ChromedpTask-consume  start");
        try {
            chromedpService.consumeChromedpTask();
            XxlJobHelper.handleSuccess();
        } catch (Exception e) {
            XxlJobHelper.handleFail("ChromedpTask-consume error:" + e.getLocalizedMessage());
        }
        log.info("ChromedpTask-consume  end");
    }

}
