package com.fh.cloud.screen.service.space.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceGroup;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.mapper.SpaceGroupMapper;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.light.core.enums.StatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 区域组表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Service
public class SpaceGroupServiceImpl extends ServiceImpl<SpaceGroupMapper, SpaceGroup> implements ISpaceGroupService {

    @Resource
    private SpaceGroupMapper spaceGroupMapper;

    @Override
    public List<SpaceGroupVo> getSpaceGroupListByCondition(SpaceGroupListConditionBo condition) {
        return spaceGroupMapper.getSpaceGroupListByCondition(condition);
    }

    @Override
    public boolean addSpaceGroup(SpaceGroupBo spaceGroupBo) {
        return false;
    }

    @Override
    public boolean updateSpaceGroup(SpaceGroupBo spaceGroupBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long spaceGroupId) {
        return null;
    }

    @Override
    public List<SpaceGroupVo> listSpaceGroupVoBySpaceGroupIds(List<Long> spaceGroupIds) {
        if (CollectionUtils.isEmpty(spaceGroupIds)) {
            return Lists.newArrayList();
        }

        return spaceGroupMapper.listSpaceGroupVoBySpaceGroupIds(spaceGroupIds);
    }

    @Override
    public List<SpaceGroupVo> findAll() {
        QueryWrapper<SpaceGroup> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SpaceGroup::getIsDelete, StatusEnum.NOTDELETE.getCode());
        final List<SpaceGroup> spaceGroups = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(spaceGroups)) {
            return spaceGroups.stream().map(val -> BeanUtil.toBean(val, SpaceGroupVo.class))
                .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public SpaceGroupVo getSpaceGroupVoOfXz() {
        LambdaQueryWrapper<SpaceGroup> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SpaceGroup::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(SpaceGroup::getSpaceGroupUseType, SpaceGroupUseType.XZ.getValue());
        SpaceGroup spaceGroup = getOne(lqw);
        SpaceGroupVo spaceGroupVo = new SpaceGroupVo();
        BeanUtils.copyProperties(spaceGroup, spaceGroupVo);
        return spaceGroupVo;
    }
}