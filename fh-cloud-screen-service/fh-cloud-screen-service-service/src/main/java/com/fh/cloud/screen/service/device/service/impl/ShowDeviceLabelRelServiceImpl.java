package com.fh.cloud.screen.service.device.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceLabelRelService;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceLabelRelMapper;
import com.light.core.entity.AjaxResult;
/**
 * 设备订阅标签表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
@Service
public class ShowDeviceLabelRelServiceImpl extends ServiceImpl<ShowDeviceLabelRelMapper, ShowDeviceLabelRelDto> implements IShowDeviceLabelRelService {

	@Resource
	private ShowDeviceLabelRelMapper showDeviceLabelRelMapper;
	
    @Override
	public List<ShowDeviceLabelRelVo> getShowDeviceLabelRelListByCondition(ShowDeviceLabelRelConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return showDeviceLabelRelMapper.getShowDeviceLabelRelListByCondition(condition);
	}

	@Override
	public AjaxResult addShowDeviceLabelRel(ShowDeviceLabelRelBo showDeviceLabelRelBo) {
		ShowDeviceLabelRelDto showDeviceLabelRel = new ShowDeviceLabelRelDto();
		BeanUtils.copyProperties(showDeviceLabelRelBo, showDeviceLabelRel);
		showDeviceLabelRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(showDeviceLabelRel)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateShowDeviceLabelRel(ShowDeviceLabelRelBo showDeviceLabelRelBo) {
		ShowDeviceLabelRelDto showDeviceLabelRel = new ShowDeviceLabelRelDto();
		BeanUtils.copyProperties(showDeviceLabelRelBo, showDeviceLabelRel);
		if(updateById(showDeviceLabelRel)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ShowDeviceLabelRelVo getDetail(Long id) {
		ShowDeviceLabelRelConditionBo condition = new ShowDeviceLabelRelConditionBo();
		condition.setId(id);
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		List<ShowDeviceLabelRelVo> list = showDeviceLabelRelMapper.getShowDeviceLabelRelListByCondition(condition);
		ShowDeviceLabelRelVo vo = new ShowDeviceLabelRelVo();
		if(!CollectionUtils.isEmpty(list)) {
			vo = list.get(0);
		}
		return vo;
	}

}