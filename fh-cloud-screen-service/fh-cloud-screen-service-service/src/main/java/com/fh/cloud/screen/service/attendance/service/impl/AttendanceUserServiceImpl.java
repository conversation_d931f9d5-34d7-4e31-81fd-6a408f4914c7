package com.fh.cloud.screen.service.attendance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserAllDayVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserResultVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserShowVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.attendance.mapper.AttendanceUserMapper;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserDetailService;
import com.fh.cloud.screen.service.attendance.service.IAttendanceUserService;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.enums.AttendanceUserRecordTypeEnums;
import com.fh.cloud.screen.service.enums.AttendanceUserTypeEnums;
import com.fh.cloud.screen.service.enums.GradeEnums;
import com.google.common.collect.ImmutableList;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.UnifiedException;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.xxl.job.core.util.DateUtil;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考勤用户表（一个人一天的考勤记录），需要日终计算接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Service
public class AttendanceUserServiceImpl extends ServiceImpl<AttendanceUserMapper, AttendanceUser>
    implements IAttendanceUserService {

    @Resource
    private AttendanceUserMapper attendanceUserMapper;
    @Resource
    private BaseDataService baseDataService;
    @Resource
    private IAttendanceUserDetailService attendanceUserDetailService;
    @Resource
    private IAttendanceUserService attendanceUserService;
    @Resource
    private IAttendanceRuleService attendanceRuleService;

    @Override
    public List<AttendanceUserVo> getAttendanceUserListByCondition(AttendanceUserListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return this.attendanceUserMapper.getAttendanceUserListByCondition(condition);
    }

    @Override
    public AttendanceUserCensusVo getByDateCondition(AttendanceUserListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        // 状态
        final Integer attendanceRecordType = condition.getAttendanceRecordType();

        AttendanceUserCensusVo attendanceUserCensusVo = new AttendanceUserCensusVo();

        final String attendanceDay = condition.getAttendanceDay();
        if (StrUtil.isEmpty(attendanceDay)) {
            throw new UnifiedException("日期参数不能为空");
        }
        // 获取所有用户基础信息
        List<AttendanceUserVo> basicAttendanceUserVos = this.buildAttendanceUserVo(condition);
        if (CollectionUtil.isEmpty(basicAttendanceUserVos)) {
            return attendanceUserCensusVo;
        }

        // 查询用户打卡信息数据
        final List<String> userOids =
            basicAttendanceUserVos.stream().map(AttendanceUserVo::getUserOid).collect(Collectors.toList());
        condition.setUserOids(userOids);
        condition.setAttendanceRecordType(null);
        final List<AttendanceUserVo> attendanceUserVos =
            this.attendanceUserMapper.getAttendanceUserListByCondition(condition);

        // covert to map key: userOid val : current
        final Map<String, AttendanceUserVo> mapUserVo =
            attendanceUserVos.stream().collect(Collectors.toMap(AttendanceUserVo::getUserOid, v -> v, (v1, v2) -> v1));

        // 填充打卡状态 （主要未打卡填充）
        basicAttendanceUserVos.parallelStream().forEach(x -> {
            final AttendanceUserVo attendanceUserVo = mapUserVo.get(x.getUserOid());
            x.setAttendanceRecordType(AttendanceUserRecordTypeEnums.EXCEPTION.getCode());
            if (attendanceUserVo != null) {
                x.setAttendanceUserId(attendanceUserVo.getAttendanceUserId());
                x.setAttendanceRecordType(attendanceUserVo.getAttendanceRecordType());
            }
        });

        // 状态筛选
        if (attendanceRecordType != null) {

            basicAttendanceUserVos = basicAttendanceUserVos.stream()
                .filter(x -> x.getAttendanceRecordType().equals(attendanceRecordType)).collect(Collectors.toList());
        }

        final Map<Integer, Long> recordTypeCount = basicAttendanceUserVos.stream()
            .collect(Collectors.groupingBy(AttendanceUserVo::getAttendanceRecordType, Collectors.counting()));

        final int size = basicAttendanceUserVos.size();
        // 赋值数据
        final Long normalCount = recordTypeCount.getOrDefault(AttendanceUserRecordTypeEnums.NORMAL.getCode(), 0L);
        attendanceUserCensusVo.setNormalCount(normalCount);
        attendanceUserCensusVo.setExceptionCount(size - normalCount);
        attendanceUserCensusVo.setList(basicAttendanceUserVos);
        attendanceUserCensusVo.setCount(size);

        return attendanceUserCensusVo;
    }

    @Override
    public AttendanceUserResultVo getListExportByDateCondition(AttendanceUserListConditionBo condition) {
        AttendanceUserResultVo attendanceUserResultVo = new AttendanceUserResultVo();
        final Integer attendanceRecordType = condition.getAttendanceRecordType();
        final String attendanceDay = condition.getAttendanceDay();
        if (StrUtil.isEmpty(attendanceDay)) {
            throw new UnifiedException("日期参数不能为空");
        }
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<AttendanceUserVo> basicAttendanceUserVos = this.buildAttendanceUserVo(condition);
        if (CollectionUtil.isEmpty(basicAttendanceUserVos)) {
            return attendanceUserResultVo;
        }
        // 当前机构的考勤规则
        AttendanceRuleAddBo attendanceRule = attendanceRuleService
            .getAttendanceRuleByOrganizationIdAndType(condition.getOrganizationId(), condition.getAttendanceType());
        Integer attendanceModeNum = attendanceRule.getAttendanceModeNum();
        if (attendanceModeNum == null) {
            return attendanceUserResultVo;
        }
        final List<String> userOids =
            basicAttendanceUserVos.stream().map(AttendanceUserVo::getUserOid).collect(Collectors.toList());

        List<AttendanceUserAllDayVo> attendanceUserAllDayVos = Lists.newArrayList();
        // 每组考勤
        for (int i = ConstantsInteger.NUM_1; i <= attendanceModeNum; i++) {
            AttendanceUserAllDayVo attendanceUserAllDayVo = new AttendanceUserAllDayVo();
            attendanceUserAllDayVo.setAttendanceRuleDayIndex(i);
            List<AttendanceUserShowVo> currentBasicAttendanceUserVos = Lists.newArrayList();
            // 复制basicAttendanceUserVos给currentBasicAttendanceUserVos
            List<AttendanceUserShowVo> finalCurrentBasicAttendanceUserVos = currentBasicAttendanceUserVos;
            basicAttendanceUserVos.stream().forEach(x -> {
                AttendanceUserShowVo attendanceUserShowVo = new AttendanceUserShowVo();
                BeanUtil.copyProperties(x, attendanceUserShowVo);
                finalCurrentBasicAttendanceUserVos.add(attendanceUserShowVo);
            });
            // 查询用户打卡信息数据
            condition.setUserOids(userOids);
            condition.setAttendanceRecordType(null);
            condition.setAttendanceRuleDayIndex(i);
            final List<AttendanceUserShowVo> attendanceUserShowVoList =
                this.attendanceUserMapper.getAttendanceUserExportListByCondition(condition);
            final Map<String, AttendanceUserShowVo> mapUserVo = attendanceUserShowVoList.stream()
                .collect(Collectors.toMap(AttendanceUserShowVo::getUserOid, Function.identity(), (v1, v2) -> v1));
            // 填充打卡状态 （主要未打卡填充）
            currentBasicAttendanceUserVos.parallelStream().forEach(currentBasicAttendanceUserVo -> {
                final AttendanceUserShowVo attendanceUserShowVo =
                    mapUserVo.get(currentBasicAttendanceUserVo.getUserOid());
                currentBasicAttendanceUserVo.setAttendanceRecordType(AttendanceUserRecordTypeEnums.EXCEPTION.getCode());
                if (attendanceUserShowVo != null) {
                    currentBasicAttendanceUserVo
                        .setAttendanceRecordType(attendanceUserShowVo.getAttendanceRecordType());
                    currentBasicAttendanceUserVo.setAttendanceDay(attendanceUserShowVo.getAttendanceDay());
                    currentBasicAttendanceUserVo.setSignInAddress(attendanceUserShowVo.getSignInAddress());
                    currentBasicAttendanceUserVo.setSignInTime(attendanceUserShowVo.getSignInTime());
                    currentBasicAttendanceUserVo
                        .setSignInAttendanceMethod(attendanceUserShowVo.getSignInAttendanceMethod());
                    currentBasicAttendanceUserVo.setSignInRecordType(attendanceUserShowVo.getSignInRecordType());
                    currentBasicAttendanceUserVo.setSignOutAddress(attendanceUserShowVo.getSignOutAddress());
                    currentBasicAttendanceUserVo.setSignOutTime(attendanceUserShowVo.getSignOutTime());
                    currentBasicAttendanceUserVo
                        .setSignOutAttendanceMethod(attendanceUserShowVo.getSignOutAttendanceMethod());
                    currentBasicAttendanceUserVo.setSignOutRecordType(attendanceUserShowVo.getSignOutRecordType());
                }
            });
            // 状态筛选
            if (attendanceRecordType != null) {
                currentBasicAttendanceUserVos = currentBasicAttendanceUserVos.stream()
                    .filter(x -> x.getAttendanceRecordType().equals(attendanceRecordType)).collect(Collectors.toList());
            }
            // 赋值数据
            attendanceUserAllDayVo.setAttendanceUserShowVos(currentBasicAttendanceUserVos);
            if (CollectionUtils.isNotEmpty(currentBasicAttendanceUserVos)) {
                attendanceUserAllDayVos.add(attendanceUserAllDayVo);
            }

        }
        attendanceUserResultVo.setAttendanceUserAllDayVos(attendanceUserAllDayVos);
        return attendanceUserResultVo;
    }

    /**
     * 条件获取考勤数据 （老师、学生）
     *
     * @param condition
     * @return
     */
    private List<AttendanceUserVo> buildAttendanceUserVo(AttendanceUserListConditionBo condition) {
        List<AttendanceUserVo> list = Lists.newArrayList();
        // 获取用户信息
        // 学生信息
        if (condition.getAttendanceType().equals(AttendanceUserTypeEnums.STUDENT.getCode())) {
            list = this.buildStudent(condition);
        } else if (condition.getAttendanceType().equals(AttendanceUserTypeEnums.TEACHER.getCode())) {
            list = this.buildTeacher(condition);
        }
        return list;
    }

    /**
     * 处理 老师基础信息
     * 
     * @param condition
     * @return
     */
    private List<AttendanceUserVo> buildTeacher(AttendanceUserListConditionBo condition) {
        List<AttendanceUserVo> list;
        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setOrganizationId(condition.getOrganizationId());
        teacherConditionBo.setRealName(condition.getRealName());
        teacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        final List<TeacherVo> teacherVoList = this.baseDataService.getTeacherVoList(teacherConditionBo);
        if (CollectionUtil.isEmpty(teacherVoList)) {
            return Lists.newArrayList();
        }
        // 填充组装
        list = teacherVoList.stream().map(x -> {
            AttendanceUserVo vo = new AttendanceUserVo();
            vo.setAttendanceDay(condition.getAttendanceDay());
            vo.setUserOid(x.getUserOid());
            vo.setRealName(x.getUserVo().getRealName());
            return vo;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 填充学生 基础信息
     * 
     * @param condition
     * @return
     */
    private List<AttendanceUserVo> buildStudent(AttendanceUserListConditionBo condition) {
        List<AttendanceUserVo> list;
        // 数据查询
        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        studentConditionBo.setRealName(condition.getRealName());
        studentConditionBo.setGrade(condition.getGrade());
        studentConditionBo.setClassesId(condition.getClassesId());
        studentConditionBo.setOrganizationId(condition.getOrganizationId());
        final List<StudentVo> studentVoList = baseDataService.getStudentVoList(studentConditionBo);
        if (CollectionUtil.isEmpty(studentVoList)) {
            return Lists.newArrayList();
        }

        // 填充组装
        list = studentVoList.stream().map(x -> {
            AttendanceUserVo vo = new AttendanceUserVo();
            vo.setAttendanceDay(condition.getAttendanceDay());
            vo.setUserOid(x.getUserOid());
            vo.setGrade(x.getGrade());
            vo.setGradeName(this.getGradeName(x.getGrade()));
            vo.setClassesName(x.getClazzVo().getClassesName());
            vo.setRealName(x.getUserVo().getRealName());
            return vo;
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 获取年级名称
     * 
     * @param code
     * @return
     */
    private String getGradeName(String code) {
        if (StrUtil.isNotEmpty(code)) {
            final GradeEnums gradeEnums = GradeEnums.getByCode(code);
            if (gradeEnums != null) {
                return gradeEnums.getLabel();
            }
        }
        return null;
    }

    @Override
    public boolean addAttendanceUser(AttendanceUserBo attendanceUserBo) {
        return false;
    }

    @Override
    public boolean updateAttendanceUser(AttendanceUserBo attendanceUserBo) {
        return false;
    }

    @Override
    public Map<String, Object> getDetail(Long attendanceUserId) {
        return null;
    }

    @Override
    public AjaxResult changeState(AttendanceUserBo bo) {

        final String attendanceDay = bo.getAttendanceDay();
        AttendanceUser attendanceUser = this.getByUserOidAndDay(bo.getUserOid(), attendanceDay);
        if (attendanceUser != null
            && attendanceUser.getAttendanceRecordType().equals(AttendanceUserRecordTypeEnums.NORMAL.getCode())) {
            return AjaxResult.fail("非异常状态，不能修改");
        }

        if (attendanceUser == null) {
            attendanceUser = new AttendanceUser();
            BeanUtil.copyProperties(bo, attendanceUser);
            // 处理月份
            final Date date = DateUtil.parseDate(attendanceDay);
            attendanceUser.setAttendanceMonth(DateUtil.format(date, "yyyy-MM"));
            attendanceUser.setAttendanceDate(date);
        }

        attendanceUser.setAttendanceRecordType(AttendanceUserRecordTypeEnums.NORMAL.getCode());

        this.saveOrUpdate(attendanceUser);

        return AjaxResult.success();
    }

    /**
     * 批量保存
     *
     * @param attendanceUserDtoList
     * @return void
     * <AUTHOR>
     * @date 2022/6/22 11:12
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAndDetail(List<AttendanceUser> attendanceUserDtoList) {
        boolean saveBatch = attendanceUserService.saveBatch(attendanceUserDtoList);
        List<AttendanceUserDetail> detailList = new ArrayList<>();
        for (AttendanceUser attendanceUser : attendanceUserDtoList) {
            for (AttendanceUserDetail attendanceUserDetail : attendanceUser.getAttendanceUserDetailList()) {
                attendanceUserDetail.setAttendanceUserId(attendanceUser.getAttendanceUserId());
                detailList.add(attendanceUserDetail);
            }
        }
        // 保存子表
        attendanceUserDetailService.saveBatch(detailList);
    }

    @Override
    public List<AttendanceUserVo> getSignInListByUserOidsAndDateDay(Collection<String> userOids, String date) {
        return this.attendanceUserMapper.getSignInListByUserOidsAndDateDay(userOids, date);
    }

    @Override
    public AttendanceUser getByAttendanceUserId(Long attendanceUserId) {
        QueryWrapper<AttendanceUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AttendanceUser::getAttendanceUserId, attendanceUserId).eq(AttendanceUser::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        return this.attendanceUserMapper.selectOne(queryWrapper);
    }

    private Integer getCountByUserOidAndDay(String userOid, String attendanceDay) {
        QueryWrapper<AttendanceUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AttendanceUser::getUserOid, userOid)
            .eq(AttendanceUser::getAttendanceDay, attendanceDay)
            .eq(AttendanceUser::getIsDelete, StatusEnum.NOTDELETE.getCode());

        return this.attendanceUserMapper.selectCount(queryWrapper);
    }

    /**
     * 根据用户OID 和 日期(yyyy-MM-dd) 查询数据
     *
     * @param userOid
     * @param attendanceDay
     * @return
     */
    public AttendanceUser getByUserOidAndDay(String userOid, String attendanceDay) {
        QueryWrapper<AttendanceUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AttendanceUser::getUserOid, userOid)
            .eq(AttendanceUser::getAttendanceDay, attendanceDay)
            .eq(AttendanceUser::getIsDelete, StatusEnum.NOTDELETE.getCode());

        return this.attendanceUserMapper.selectOne(queryWrapper);
    }

}