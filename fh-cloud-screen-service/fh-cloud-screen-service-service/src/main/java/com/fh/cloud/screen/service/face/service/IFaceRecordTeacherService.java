package com.fh.cloud.screen.service.face.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordTeacherDto;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordUploadResultVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 教师人脸库接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
public interface IFaceRecordTeacherService extends IService<FaceRecordTeacherDto> {

    List<FaceRecordTeacherVo> getFaceRecordTeacherListByCondition(FaceRecordTeacherConditionBo condition);

    AjaxResult addFaceRecordTeacher(FaceRecordTeacherBo faceRecordTeacherBo);

    AjaxResult updateFaceRecordTeacher(FaceRecordTeacherBo faceRecordTeacherBo);

    AjaxResult updateFaceRecordTeacherBatch(List<FaceRecordTeacherBo> faceRecordTeacherBoList);

    FaceRecordTeacherVo getDetail(Long id);

    /**
     * 导入教师人脸数据
     *
     * @param faceRecordTeacherConditionBos
     * @return
     */
    AjaxResult<FaceRecordUploadResultVo> upload(List<FaceRecordTeacherConditionBo> faceRecordTeacherConditionBos);

    /**
     * 更新用户姓名
     *
     * @param userOid
     * @param realName
     * @return
     */
    AjaxResult updateRealNameByUserOid(String userOid, String realName);

}
