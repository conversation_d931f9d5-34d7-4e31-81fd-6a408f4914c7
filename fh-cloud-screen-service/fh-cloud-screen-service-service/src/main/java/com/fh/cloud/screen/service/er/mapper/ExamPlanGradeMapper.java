package com.fh.cloud.screen.service.er.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.er.entity.dto.ExamPlanGradeDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo;

/**
 * 考场_考试计划涉及的年级Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamPlanGradeMapper extends BaseMapper<ExamPlanGradeDto> {

	List<ExamPlanGradeVo> getExamPlanGradeListByCondition(ExamPlanGradeConditionBo condition);

}
