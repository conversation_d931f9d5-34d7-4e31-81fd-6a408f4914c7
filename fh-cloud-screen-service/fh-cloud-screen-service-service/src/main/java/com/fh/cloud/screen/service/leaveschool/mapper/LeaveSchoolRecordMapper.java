package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordVo;

/**
 * 放学记录表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
public interface LeaveSchoolRecordMapper extends BaseMapper<LeaveSchoolRecordDto> {

	List<LeaveSchoolRecordVo> getLeaveSchoolRecordListByCondition(LeaveSchoolRecordConditionBo condition);

	LeaveSchoolRecordVo getLeaveSchoolRecordByCondition(LeaveSchoolRecordConditionBo condition);

}
