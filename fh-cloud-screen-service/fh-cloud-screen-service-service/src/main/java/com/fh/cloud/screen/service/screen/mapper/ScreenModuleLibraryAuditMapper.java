package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryAuditDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo;
import org.apache.ibatis.annotations.Param;

/**
 * 模块库审核表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:39
 */
public interface ScreenModuleLibraryAuditMapper extends BaseMapper<ScreenModuleLibraryAuditDto> {

	List<ScreenModuleLibraryAuditVo> getScreenModuleLibraryAuditListByCondition(ScreenModuleLibraryAuditConditionBo condition);

	ScreenModuleLibraryAuditVo getScreenModuleLibraryAuditByCondition(ScreenModuleLibraryAuditConditionBo condition);

	List<ScreenModuleLibraryAuditVo> getScreenModuleLibraryAuditWithLabelNameListByCondition(ScreenModuleLibraryAuditConditionBo condition);

	/**
	 * 根据条件查询模块库审核信息
	 * @param condition
	 * @return
	 */
	ScreenModuleLibraryAuditVo getScreenModuleLibraryAuditWithLabelNameByCondition(ScreenModuleLibraryAuditConditionBo condition);

}
