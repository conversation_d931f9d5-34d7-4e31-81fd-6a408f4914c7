package com.fh.cloud.screen.service.baseinfo.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fh.app.role.service.role.api.OrganizationAppRelApi;
import com.fh.app.role.service.role.api.OrganizationPackageRelApi;
import com.fh.app.role.service.role.api.OrganizationSuperviseApi;
import com.fh.app.role.service.role.entity.bo.OrganizationAppRelConditionBo;
import com.fh.app.role.service.role.entity.bo.OrganizationPackageRelConditionBo;
import com.fh.app.role.service.role.entity.bo.OrganizationSuperviseConditionBo;
import com.fh.app.role.service.role.entity.vo.OrganizationAppRelVo;
import com.fh.app.role.service.role.entity.vo.OrganizationPackageRelVo;
import com.fh.app.role.service.role.entity.vo.OrganizationSuperviseVo;
import com.fh.app.role.service.role.enums.OrganizationSuperviseState;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.baseinfo.BaseDataType;
import com.fh.cloud.screen.service.baseinfo.entity.vo.OrganizationVoExt;
import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.*;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.role.servise.UserRoleService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.subject.entity.bo.SubjectBo;
import com.fh.cloud.screen.service.utils.HtmlUtil;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.base.attachment.api.AttachmentApi;
import com.light.base.attachment.entity.bo.MultipartFormData;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.category.api.CategoryApi;
import com.light.base.category.entity.bo.CategoryConditionBo;
import com.light.base.category.entity.vo.CategoryVo;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.base.dictionary.service.DictionaryDataApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.inspect.InspectService;
import com.light.core.inspect.config.InspectProperties;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.security.service.CurrentUserService;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.campus.api.CampusApi;
import com.light.user.campus.entity.bo.CampusConditionBo;
import com.light.user.campus.entity.vo.CampusVo;
import com.light.user.campus.service.CampusApiService;
import com.light.user.clazz.api.ClazzHeadmasterApi;
import com.light.user.clazz.entity.bo.ClazzConditionBo;
import com.light.user.clazz.entity.bo.ClazzHeadmasterConditionBo;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.clazz.service.ClazzApiService;
import com.light.user.organization.api.OrganizationSetupApi;
import com.light.user.organization.api.OrganizationTermApi;
import com.light.user.organization.entity.bo.OrganizationConditionBo;
import com.light.user.organization.entity.bo.OrganizationTermDelSaveBo;
import com.light.user.organization.entity.vo.OrganizationSetupVo;
import com.light.user.organization.entity.vo.OrganizationTermVo;
import com.light.user.organization.entity.vo.OrganizationVo;
import com.light.user.organization.service.OrganizationApiService;
import com.light.user.slow.api.OpenSlowApi;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.student.service.StudentApiService;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.teacher.service.TeacherApiService;
import com.light.user.user.api.KeeperRelationApi;
import com.light.user.user.api.UserApi;
import com.light.user.user.entity.vo.KeeperRelationVo;
import com.light.user.user.entity.vo.UserVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公共cloud服务的实现(http调用user和base的服务)
 *
 * <AUTHOR>
 * @date 2022/6/1 10:28
 */
public class BaseDataCloudServiceImpl implements BaseDataService {

    @Resource
    private UserApi userApi;
    @Resource
    private CampusApi campusApi;
    @Resource
    private ClazzApiService clazzApiService;
    @Resource
    private StudentApiService studentApiService;
    @Resource
    private TeacherApiService teacherApiService;
    @Resource
    private CampusApiService campusApiService;
    @Resource
    private OrganizationApiService organizationApiService;
    @Resource
    private ClazzHeadmasterApi clazzHeadmasterApi;
    @Resource
    private AttachmentApi attachmentApi;
    @Resource
    private CurrentUserService currentUserService;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private OrganizationTermApi organizationTermApi;
    @Resource
    private CategoryApi categoryApi;
    @Resource
    private OrganizationSetupApi organizationSetupApi;
    @Resource
    private OpenSlowApi openSlowApi;
    @Resource
    private InspectService inspectService;
    @Autowired
    private InspectProperties inspectProperties;
    @Resource
    private OrganizationPackageRelApi organizationPackageRelApi;

    @Autowired
    private DictionaryDataApiService dictionaryDataApiService;
    @Resource
    private OrganizationSuperviseApi organizationSuperviseApi;
    @Resource
    private KeeperRelationApi keeperRelationApi;
    @Resource
    private OrganizationAppRelApi organizationAppRelApi;

    @Override
    public String name() {
        return BaseDataType.CLOUD_BASE.getName();
    }

    @Override
    public String echo() {
        return "BaseDataCloudServiceImpl...";
    }

    @Override
    public Map<String, String> getRealNameByUserOids(List<String> userOids) {
        if (CollectionUtils.isEmpty(userOids)) {
            return Maps.newHashMap();
        }
        Map<String, String> userNameMap = new HashMap<>();
        AjaxResult<List<UserVo>> ajaxResult = userApi.getByOidList(userOids);
        if (ajaxResult.isSuccess() && CollectionUtils.isNotEmpty(ajaxResult.getData())) {
            userNameMap =
                ajaxResult.getData().stream().collect(Collectors.toMap(UserVo::getUserOid, UserVo::getRealName));
        }
        return userNameMap;
    }

    @Override
    public OrganizationVoExt getOrganizationVoByOrgId(Long orgId) {
        final AjaxResult detailResult = this.organizationApiService.getDetail(orgId);
        final Object data = detailResult.getData();
        if (detailResult.isFail() || data == null) {
            return BaseDataService.super.getOrganizationVoByOrgId(orgId);
        }
        Map<String, Object> map = (Map<String, Object>)data;
        final Object organizationVoData = map.get("organizationVo");
        if (organizationVoData == null) {
            return BaseDataService.super.getOrganizationVoByOrgId(orgId);
        }
        final OrganizationVoExt organizationVoExt =
            JSONUtil.toBean(JSONUtil.toJsonStr(organizationVoData), OrganizationVoExt.class);
        //
        AjaxResult<OrganizationSetupVo> byOrgId = organizationSetupApi.getByOrgId(organizationVoExt.getId());
        OrganizationSetupVo setupData = byOrgId.getData();
        if (setupData != null) {
            organizationVoExt.setLogo(setupData.getLogo());
            organizationVoExt.setWebName(setupData.getWebName());
            organizationVoExt.setOtherConfig(setupData.getOtherConfig());
        }
        return organizationVoExt;
    }

    /**
     * 获取组织机构列表
     *
     * @param orgIds
     * @return
     */
    @Override
    public List<OrganizationVo> getOrganizationVoList(List<Long> orgIds) {
        OrganizationConditionBo condition = new OrganizationConditionBo();
        condition.setOrganizationIds(orgIds);
        condition.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = organizationApiService.getOrganizationListByCondition(condition);
        if (ajaxResult.isFail()) {
            return null;
        }
        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        return JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), OrganizationVo.class);
    }

    @Override
    public AjaxResult getCampusListByOrganizationId(Long organizationId) {
        CampusConditionBo campusConditionBo = new CampusConditionBo();
        campusConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        campusConditionBo.setPageNo(SystemConstants.NO_PAGE);
        campusConditionBo.setOrganizationId(organizationId);
        return campusApi.getCampusListByCondition(campusConditionBo);
    }

    @Override
    public Map<String, Object> getClazzInfoVoList(ClazzConditionBoExt clazzConditionBo) {
        // 剔除毕业班级、添加排序
        clazzConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        clazzConditionBo.setOrderBy(
            "grade Asc, CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> map = new HashMap<>(4);
        map.put("list", null);
        map.put("total", 0);
        AjaxResult ajaxResult = null;

        // app 检索全部
        if (ConstantsInteger.APP_QUERY.equals(clazzConditionBo.getQueryType())) {
            ajaxResult = clazzApiService.getClassesListByCondition(clazzConditionBo);
        } else {
            Integer userDataAuthority = userRoleService.getUserMaxDataAuthority(clazzConditionBo.getAppId());
            if (ConstantsInteger.USER_DATA_AUTHORITY_CLASS.equals(userDataAuthority)) {
                // 班级则获取任教班级及任教班主任的合集 任教科目及班主任
                List<Long> classIds = new ArrayList<>();
                String currentOid = currentUserService.getCurrentOid();
                if (null == clazzConditionBo.getTeachingScope()
                    || TeachingScopeEnum.TEACHING_All.getValue() == clazzConditionBo.getTeachingScope()) {
                    classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
                    classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
                } else if (TeachingScopeEnum.TEACHING_CLASS.getValue() == clazzConditionBo.getTeachingScope()) {
                    classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
                } else if (TeachingScopeEnum.TEACHING_SUBJECT.getValue() == clazzConditionBo.getTeachingScope()) {
                    classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
                }
                if (CollectionUtils.isEmpty(classIds)) {
                    return map;
                }
                clazzConditionBo.setIds(classIds);
                ajaxResult = clazzApiService.getClassesListByCondition(clazzConditionBo);
            } else if (ConstantsInteger.USER_DATA_AUTHORITY_SCHOOL.equals(userDataAuthority)
                || ConstantsInteger.USER_DATA_AUTHORITY_AREA.equals(userDataAuthority)) {
                // 校级检索全部
                ajaxResult = clazzApiService.getClassesListByCondition(clazzConditionBo);
            }
        }
        if (null == ajaxResult || ajaxResult.isFail()) {
            return map;
        }
        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        List<Object> objects = (List<Object>)dataMap.get("list");
        Integer total = (Integer)dataMap.get("total");

        List<ClazzInfoVo> clazzInfoVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(objects)) {
            List<ClazzVo> clazzVos = JSONObject.parseArray(JSONObject.toJSONString(objects), ClazzVo.class);
            for (ClazzVo clazzVo : clazzVos) {
                ClazzInfoVo clazzInfoVo = new ClazzInfoVo();
                BeanUtils.copyProperties(clazzVo, clazzInfoVo);
                clazzInfoVo.setGradeName(SchoolYearUtil.gradeMap.get(clazzVo.getGrade()));
                clazzInfoVo
                    .setClassesNameShow(clazzInfoVo.getGradeName().concat(clazzInfoVo.getClassesName()).concat("班"));
                clazzInfoVoList.add(clazzInfoVo);
            }
        }

        map.put("list", clazzInfoVoList);
        map.put("total", total);
        return map;
    }

    @Deprecated
    @Override
    public AjaxResult getClassesListByCondition(ClazzConditionBoExt clazzConditionBo) {
        // 剔除毕业班级、添加排序
        clazzConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        clazzConditionBo.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> map = new HashMap<>(4);
        map.put("list", null);
        map.put("total", 0);
        Integer userDataAuthority = userRoleService.getUserMaxDataAuthority(null);
        // 班级则获取任教班级及任教班主任的合集
        if (ConstantsInteger.USER_DATA_AUTHORITY_CLASS.equals(userDataAuthority)) {
            // 任教科目及班主任
            List<Long> classIds = new ArrayList<>();
            String currentOid = currentUserService.getCurrentOid();
            if (null == clazzConditionBo.getTeachingScope()
                || TeachingScopeEnum.TEACHING_All.getValue() == clazzConditionBo.getTeachingScope()) {
                classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
                classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
            } else if (TeachingScopeEnum.TEACHING_CLASS.getValue() == clazzConditionBo.getTeachingScope()) {
                classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
            } else if (TeachingScopeEnum.TEACHING_SUBJECT.getValue() == clazzConditionBo.getTeachingScope()) {
                classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
            }
            if (CollectionUtils.isEmpty(classIds)) {
                return AjaxResult.success(map);
            }
            clazzConditionBo.setIds(classIds);
            return clazzApiService.getClassesListByCondition(clazzConditionBo);
        } else if (ConstantsInteger.USER_DATA_AUTHORITY_SCHOOL.equals(userDataAuthority)
            || ConstantsInteger.USER_DATA_AUTHORITY_AREA.equals(userDataAuthority)) {
            return clazzApiService.getClassesListByCondition(clazzConditionBo);
        }
        return AjaxResult.success(map);
    }

    @Override
    public AjaxResult getClassesListByClassesIds(ClazzConditionBoExt clazzConditionBo) {
        // 剔除毕业班级、添加排序
        clazzConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        clazzConditionBo.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> map = new HashMap<>(4);
        map.put("list", null);
        map.put("total", 0);
        return clazzApiService.getClassesListByCondition(clazzConditionBo);
    }

    @Override
    public AjaxResult<List<Long>> getClassesIdsListByDataAuthority(ClazzConditionBoExt clazzConditionBo) {
        // 剔除毕业班级、添加排序
        clazzConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        clazzConditionBo.setPageNo(SystemConstants.NO_PAGE);
        clazzConditionBo.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Integer userDataAuthority = userRoleService.getUserMaxDataAuthority(null);
        // 班级则获取任教班级及任教班主任的合集
        if (ConstantsInteger.USER_DATA_AUTHORITY_CLASS.equals(userDataAuthority)) {
            // 任教科目及班主任
            List<Long> classIds = new ArrayList<>();
            String currentOid = currentUserService.getCurrentOid();
            if (null == clazzConditionBo.getTeachingScope()
                || TeachingScopeEnum.TEACHING_All.getValue() == clazzConditionBo.getTeachingScope()) {
                classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
                classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
            } else if (TeachingScopeEnum.TEACHING_CLASS.getValue() == clazzConditionBo.getTeachingScope()) {
                classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
            } else if (TeachingScopeEnum.TEACHING_SUBJECT.getValue() == clazzConditionBo.getTeachingScope()) {
                classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
            }
            return AjaxResult.success(classIds);
        } else if (ConstantsInteger.USER_DATA_AUTHORITY_SCHOOL.equals(userDataAuthority)
            || ConstantsInteger.USER_DATA_AUTHORITY_AREA.equals(userDataAuthority)) {
            AjaxResult ajaxResult = clazzApiService.getClassesListByCondition(clazzConditionBo);
            if (ajaxResult.isFail() || ajaxResult.getData() == null) {
                return AjaxResult.success(Lists.newArrayList());
            }
            Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
            List<ClazzVo> clazzVos = JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), ClazzVo.class);
            List<Long> tempIds = clazzVos.stream().map(ClazzVo::getId).collect(Collectors.toList());
            return AjaxResult.success(tempIds);
        }
        return AjaxResult.success(Lists.newArrayList());
    }

    @Override
    public ClazzVo getByClazzId(Long clazzId) {
        final AjaxResult detail = this.clazzApiService.getDetail(clazzId);
        final Object data = detail.getData();
        if (detail.isFail() || data == null) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)data;
        final ClazzVo clazzVo = JSONUtil.toBean(JSONUtil.toJsonStr(map.get("classesVo")), ClazzVo.class);
        return clazzVo;
    }

    @Override
    public List<StudentVo> getStudentVoList(StudentConditionBo studentConditionBo) {
        // 如果查询学生没有带班级，则默认不查询已毕业的班级的学生
        if (studentConditionBo.getClassesId() == null && CollectionUtils.isEmpty(studentConditionBo.getClassesIds())) {
            studentConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        }
        final AjaxResult result = this.studentApiService.getStudentListByCondition(studentConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)data;
        List<StudentVo> studentVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(map.get("list"))), StudentVo.class);
        return studentVos;
    }

    @Override
    public List<StudentVo> getStudentVoListSlow(StudentConditionBo studentConditionBo) {
        // 如果查询学生没有带班级，则默认不查询已毕业的班级的学生
        if (studentConditionBo.getClassesId() == null && CollectionUtils.isEmpty(studentConditionBo.getClassesIds())) {
            studentConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        }
        if (studentConditionBo.getPageNo() == null || studentConditionBo.getPageNo().equals(SystemConstants.NO_PAGE)) {
            AjaxResult<List<StudentVo>> result = this.openSlowApi.queryStuList(studentConditionBo);
            if (result.isFail() || result == null) {
                return null;
            }
            return result.getData();
        } else {
            AjaxResult<PageInfo<StudentVo>> ajaxResult = this.openSlowApi.queryStuPageList(studentConditionBo);
            if (ajaxResult.isFail() || ajaxResult.getData() == null) {
                return null;
            }
            ajaxResult.getData().getList();
        }
        return null;
    }

    @Override
    public List<TeacherVo> getTeacherVoList(TeacherConditionBo teacherConditionBo) {
        final AjaxResult result = this.teacherApiService.getTeacherListByCondition(teacherConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return null;
        }
        Map<String, Object> map = (Map<String, Object>)data;
        List<TeacherVo> studentVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(map.get("list"))), TeacherVo.class);
        return studentVos;
    }

    @Override
    public Map<String, Object> getStudentListByCondition(StudentConditionBo studentConditionBo) {
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        final AjaxResult result = this.studentApiService.getStudentListByCondition(studentConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return Maps.newHashMap();
        }
        Map<String, Object> map = (Map<String, Object>)data;

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", map.get("total"));
        resultMap.put("list", JSONObject.parseArray(JSONObject.toJSONString(map.get("list")), StudentVo.class));
        return resultMap;
    }

    @Override
    public Map<String, Object> getStudentListByConditionSlow(StudentConditionBo studentConditionBo) {
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        Map<String, Object> resultMap = new HashMap<>();

        if (studentConditionBo.getPageNo() == null || studentConditionBo.getPageNo().equals(SystemConstants.NO_PAGE)) {
            AjaxResult<List<StudentVo>> result = this.openSlowApi.queryStuList(studentConditionBo);
            if (result.isFail() || result == null) {
                return null;
            }
            List<StudentVo> data = result.getData();
            resultMap.put("total", data.size());
            resultMap.put("list", data);
        } else {
            AjaxResult<PageInfo<StudentVo>> ajaxResult = this.openSlowApi.queryStuPageList(studentConditionBo);
            if (ajaxResult.isFail() || ajaxResult.getData() == null) {
                return null;
            }
            PageInfo<StudentVo> data = ajaxResult.getData();
            resultMap.put("total", data.getTotal());
            resultMap.put("list", data.getList());
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getTeacherListByCondition(TeacherConditionBo teacherConditionBo) {
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        teacherConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        final AjaxResult result = this.teacherApiService.getTeacherListByCondition(teacherConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return Maps.newHashMap();
        }
        Map<String, Object> map = (Map<String, Object>)data;

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", map.get("total"));
        resultMap.put("list", JSONObject.parseArray(JSONObject.toJSONString(map.get("list")), TeacherVo.class));
        return resultMap;
    }

    @Override
    public List<StudentVo> getStudentVoByRealName(String realName, Long classesId) {
        AjaxResult<List<StudentVo>> ajaxResult = this.studentApiService.getByRealName(realName, classesId);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return Lists.newArrayList();
        }
        List<StudentVo> studentVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(ajaxResult.getData())), StudentVo.class);
        return studentVos;
    }

    @Override
    public List<TeacherVo> getTeacherVoByPhone(String phone) {
        AjaxResult<List<TeacherVo>> ajaxResult = this.teacherApiService.getByPhone(phone);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return Lists.newArrayList();
        }
        List<TeacherVo> teacherVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(ajaxResult.getData())), TeacherVo.class);
        return teacherVos;
    }

    @Override
    public List<TeacherVo> getTeacherVoByRealName(String realName, Long orgId) {
        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        teacherConditionBo.setRealName(realName);
        teacherConditionBo.setOrganizationId(orgId);
        AjaxResult ajaxResult = this.teacherApiService.getTeacherListByCondition(teacherConditionBo);
        final Object data = ajaxResult.getData();
        if (ajaxResult.isFail() || data == null) {
            return BaseDataService.super.getTeacherVoByRealName(realName, orgId);
        }

        Map<String, Object> map = (Map<String, Object>)data;
        final Object list = map.get("list");
        if (list == null) {
            return BaseDataService.super.getTeacherVoByRealName(realName, orgId);
        }

        List<TeacherVo> teacherVos = JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(list)), TeacherVo.class);
        return teacherVos;
    }

    @Override
    public List<StudentVo> getStudentVoListByClassesId(Long classesId) {
        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setClassesId(classesId);
        studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        final AjaxResult result = this.studentApiService.getStudentListByCondition(studentConditionBo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return BaseDataService.super.getStudentVoListByClassesId(classesId);
        }
        Map<String, Object> map = (Map<String, Object>)data;
        List<StudentVo> studentVos =
            JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(map.get("list"))), StudentVo.class);
        return studentVos;
    }

    @Override
    public List<CampusVo> getCampusVoByCondition(CampusListConditionBo condition) {
        CampusConditionBo bo = new CampusConditionBo();
        BeanUtil.copyProperties(condition, bo);
        bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        final AjaxResult result = this.campusApiService.getCampusListByCondition(bo);
        final Object data = result.getData();
        if (result.isFail() || data == null) {
            return Lists.newArrayList();
        }
        Map<String, Object> map = (Map<String, Object>)data;
        final Object list = map.get("list");
        if (list == null) {
            return Lists.newArrayList();
        }
        List<CampusVo> campusVos = JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(list)), CampusVo.class);
        return campusVos;
    }

    @Override
    public List<StudentVo> getStudentVoAllListByClassesId(Long classesId) {

        final AjaxResult<List<StudentVo>> result = this.studentApiService.getStudentAllListByClassesId(classesId);
        final List<StudentVo> data = result.getData();
        if (result.isFail() || data == null) {
            return BaseDataService.super.getStudentVoAllListByClassesId(classesId);
        }
        return data;
    }

    @Override
    public StudentVo getStudentVoByUserOid(String userOid) {
        final AjaxResult rest = this.studentApiService.getByUserOid(userOid);
        if (rest.isFail() || rest.getData() == null) {
            return null;
        }
        final Map<String, Object> studentMap = (Map<String, Object>)rest.getData();
        StudentVo studentVo = BeanUtil.mapToBean(studentMap, StudentVo.class, true);

        AjaxResult detail = studentApiService.getDetail(studentVo.getId());
        if (detail.isFail() || null == detail.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)detail.getData();
        return BeanUtil.mapToBean(map, StudentVo.class, true);
    }

    @Override
    public List<TeacherVo> getTeacherVoByOrgId(Long orgId) {

        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(SystemConstants.NO_PAGE);
        teacherConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        teacherConditionBo.setOrganizationId(orgId);
        teacherConditionBo.setOrderBy("CONVERT(real_name USING GBK)");
        AjaxResult ajaxResult = this.teacherApiService.getTeacherListByCondition(teacherConditionBo);
        final Object data = ajaxResult.getData();
        if (ajaxResult.isFail() || data == null) {
            return BaseDataService.super.getTeacherVoByOrgId(orgId);
        }

        Map<String, Object> map = (Map<String, Object>)data;
        final Object list = map.get("list");
        if (list == null) {
            return BaseDataService.super.getTeacherVoByOrgId(orgId);
        }
        List<TeacherVo> teacherVos = JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(list)), TeacherVo.class);
        return teacherVos;
    }

    @Override
    public List<ClazzHeadmasterVo> listClazzHeadmasterVoByClassesId(Long classesId) {
        if (classesId == null) {
            return BaseDataService.super.listClazzHeadmasterVoByClassesId(classesId);
        }

        ClazzHeadmasterConditionBo clazzHeadmasterConditionBo = new ClazzHeadmasterConditionBo();
        clazzHeadmasterConditionBo.setClassesId(classesId);
        clazzHeadmasterConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        clazzHeadmasterConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult tcslResult = clazzHeadmasterApi.getClassesHeadmasterListByCondition(clazzHeadmasterConditionBo);
        Object dataTcsl = tcslResult.getData();
        if (tcslResult.isFail() || dataTcsl == null) {
            return BaseDataService.super.listClazzHeadmasterVoByClassesId(classesId);
        }
        Map<String, Object> map = (Map<String, Object>)dataTcsl;
        List<Map> chtList = (List)map.get("list");
        List<ClazzHeadmasterVo> clazzHeadmasterVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(chtList)) {
            clazzHeadmasterVos = JSONObject.parseArray(JSONObject.toJSONString(chtList), ClazzHeadmasterVo.class);
        }
        return clazzHeadmasterVos;
    }

    @Override
    public List<TeacherVo> getTeacherVoByTeacherIds(List<Long> teacherIds) {
        if (CollectionUtils.isEmpty(teacherIds)) {
            BaseDataService.super.getTeacherVoByTeacherIds(teacherIds);
        }

        List<TeacherVo> teacherVos = Lists.newArrayList();
        for (Long teacherId : teacherIds) {
            AjaxResult ajaxResult = teacherApiService.getDetail(teacherId);
            if (ajaxResult.isFail() || ajaxResult.getData() == null) {
                continue;
            }
            TeacherVo teacherVo =
                JSONObject.parseObject(JSONObject.toJSONString(ajaxResult.getData()), TeacherVo.class);
            if (teacherVo == null) {
                continue;
            }
            teacherVos.add(teacherVo);
        }
        return teacherVos;
    }

    public AttachmentVo upload(MultipartFormData multipartFormData) {
        AjaxResult result;
        try {
            result = attachmentApi.upload(multipartFormData);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        if (null == result || result.isFail() || null == result.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)result.getData();
        return BeanUtil.mapToBean(map, AttachmentVo.class, true);
    }

    public AttachmentVo upload(MultipartFile file) {
        AjaxResult result;
        try {
            result = attachmentApi.upload(file);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        if (null == result || result.isFail() || null == result.getData()) {
            return null;
        }
        final Map<String, Object> map = (Map<String, Object>)result.getData();
        return BeanUtil.mapToBean(map, AttachmentVo.class, true);
    }

    public String getCurrentUserOid() {
        return currentUserService.getCurrentOid();
    }

    public LoginAccountVo getCurrentUser() {
        return currentUserService.getCurrentUser();
    }

    @Override
    public AjaxResult<List<OrganizationTermVo>> getByOrganizationId(Long organizationId) {
        return organizationTermApi.getByOrganizationId(organizationId);
    }

    @Override
    public AjaxResult<List<OrganizationTermVo>> getByOrganizationIdAndStudyYear(Long organizationId, String studyYear) {
        return organizationTermApi.getByOrgIdAndStudyYear(organizationId, studyYear);
    }

    @Override
    public AjaxResult delAndSaveByOrgIdAndStudyYear(OrganizationTermDelSaveBo organizationTermDelSaveBo) {
        return organizationTermApi.delAndSaveByOrgIdAndStudyYear(organizationTermDelSaveBo);
    }

    @Override
    public AjaxResult listSubject(SubjectBo subjectBo) {
        // 1 先查询学段对应的id
        List<CategoryVo> sectionVos = getSectionByCode(subjectBo.getSection());
        if (CollectionUtil.isEmpty(sectionVos)) {
            return AjaxResult.fail("没有找到该学段，查询失败");
        }
        // 2 直接查组织表，自己和上级组织id
        AjaxResult orgResult = organizationApiService.getDetail(subjectBo.getOrganizationId());
        if (orgResult.isFail()) {
            return orgResult;
        }
        Map<String, Object> map = (Map<String, Object>)orgResult.getData();
        OrganizationVo organizationVo =
            JSONObject.parseObject(JSONObject.toJSONString(map.get("organizationVo")), OrganizationVo.class);

        // 3 查找科目
        subjectBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        subjectBo.setOrderBy("CONVERT(name USING GBK)");
        subjectBo.setModuleCode(DictType.SUBJECT_SECTION.getValue());
        List<Long> orgIds = new ArrayList<>();
        Long typeId = subjectBo.getTypeId();
        SubjectType subjectType = SubjectType.getSubjectType(typeId);
        switch (subjectType) {
            case TYPE_COUNTRY:
                orgIds.add(0L);
                break;
            case TYPE_ORGANIZATION:
                // orgIds.addAll(Arrays.asList((Long
                // [])ConvertUtils.convert(organizationVo.getSuperiorsIds().split(","), Long.class)));
                if (organizationVo.getType().equals(OrgType.ORG.getValue())) {
                    orgIds.add(subjectBo.getOrganizationId());
                } else {
                    orgIds.add(organizationVo.getParentId());
                }
                break;
            case TYPE_SCHOOL:
                orgIds.add(subjectBo.getOrganizationId());
                break;
            case TYPE_ALL:
                orgIds.add(0L);
                orgIds.add(organizationVo.getParentId());
                orgIds.add(subjectBo.getOrganizationId());
                subjectBo.setState(1L);
                subjectBo.setTypeId(null);
                break;
            case TYPE_ALL_PAGE:
                orgIds.add(0L);
                if (organizationVo.getType().equals(OrgType.SCHOOL.getValue())) {
                    orgIds.add(organizationVo.getParentId());
                }
                orgIds.add(subjectBo.getOrganizationId());
                subjectBo.setTypeId(null);
                subjectBo.setOrderBy("type_id,CONVERT(name USING GBK)");
                break;
            default:
                break;
        }
        subjectBo.setOrganizationIds(orgIds);
        subjectBo.setOrganizationId(null);
        // 学段
        subjectBo.setParentId(sectionVos.get(0).getId());
        subjectBo.setCode(null);
        if (StringUtils.isNotBlank(subjectBo.getName())) {
            subjectBo.setName(FuzzyQueryUtil.transferMean(subjectBo.getName()));
        }
        AjaxResult ajaxResult = categoryApi.getCategoryListByCondition(subjectBo);
        if (typeId.equals(SubjectType.TYPE_ALL.getCode())) {
            if (ajaxResult.isFail()) {
                return ajaxResult;
            }
            Map<String, Object> resultData = (Map<String, Object>)ajaxResult.getData();
            List<Map> mapList = (List)resultData.get("list");
            List<CategoryVo> categoryVos = null;
            if (CollectionUtil.isNotEmpty(mapList)) {
                List<CategoryVo> categoryVoList = new ArrayList<>();
                categoryVos = JSONObject.parseArray(JSONObject.toJSONString(mapList), CategoryVo.class);
                List<CategoryVo> countyVos = new ArrayList<>();
                List<CategoryVo> organizationVos = new ArrayList<>();
                List<CategoryVo> schoolVos = new ArrayList<>();
                for (CategoryVo categoryVo : categoryVos) {
                    if (categoryVo.getTypeId().equals(SubjectType.TYPE_COUNTRY.getCode())) {
                        countyVos.add(categoryVo);
                    } else if (categoryVo.getTypeId().equals(SubjectType.TYPE_ORGANIZATION.getCode())) {
                        organizationVos.add(categoryVo);
                    } else if (categoryVo.getTypeId().equals(SubjectType.TYPE_SCHOOL.getCode())) {
                        schoolVos.add(categoryVo);
                    }
                }
                categoryVoList.addAll(countyVos);
                categoryVoList.addAll(organizationVos);
                categoryVoList.addAll(schoolVos);
                resultData.put("list", categoryVoList);
            }
        }
        return ajaxResult;
    }

    /**
     * 根据code 查询学段
     *
     * @param code
     * @return java.util.List<com.light.base.category.entity.vo.CategoryVo>
     * <AUTHOR>
     * @date 2022/5/5 15:39
     */
    private List<CategoryVo> getSectionByCode(String code) {
        CategoryConditionBo sectionCondition = new CategoryConditionBo();
        sectionCondition.setPageNo(SystemConstants.NO_PAGE);
        sectionCondition.setParentId(0L);
        sectionCondition.setCode(code);
        sectionCondition.setModuleCode(DictType.SUBJECT_SECTION.getValue());
        AjaxResult sectionResult = categoryApi.getCategoryListByCondition(sectionCondition);
        if (sectionResult.isFail()) {
            return null;
        }
        Map<String, Object> sectionMap = (Map<String, Object>)sectionResult.getData();
        List<Map> sectionList = (List)sectionMap.get("list");
        List<CategoryVo> sectionVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(sectionList)) {
            sectionVos = JSONObject.parseArray(JSONObject.toJSONString(sectionList), CategoryVo.class);
        }
        return sectionVos;
    }

    /**
     * 根据真是姓名列表获取 组织机构下的学生列表
     *
     * @param orgId org id
     * @param realNames 真实姓名
     * @return
     */
    @Override
    public List<StudentVo> getOrgStudentListByRealNames(Long orgId, List<String> realNames) {
        AjaxResult<List<StudentVo>> ajaxResult = studentApiService.getOrgStudentListByRealNames(orgId, realNames);
        final Object data = ajaxResult.getData();
        if (ajaxResult.isFail() || data == null) {
            return BaseDataService.super.getOrgStudentListByRealNames(orgId, realNames);
        }
        List<StudentVo> studentVos = JSONUtil.toList(JSONUtil.parseArray(data), StudentVo.class);

        // 封装年级
        List<Long> classIds = studentVos.stream().map(StudentVo::getClassesId).collect(Collectors.toList());
        ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
        clazzConditionBo.setIds(classIds);
        clazzConditionBo.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult result = getClassesListByClassesIds(clazzConditionBo);
        if (null == result.getData() || result.isFail()) {
            return studentVos;
        }
        Map<String, Object> dataMap = (Map<String, Object>)result.getData();
        List<Object> objects = (List<Object>)dataMap.get("list");
        List<ClazzVo> clazzVos = JSONObject.parseArray(JSONObject.toJSONString(objects), ClazzVo.class);
        Map<Long, List<ClazzVo>> listMap = clazzVos.stream().collect(Collectors.groupingBy(ClazzVo::getId));

        for (StudentVo studentVo : studentVos) {
            List<ClazzVo> clazzVoList = listMap.get(studentVo.getClassesId());
            if (CollectionUtils.isNotEmpty(clazzVoList)) {
                studentVo.setGrade(clazzVoList.get(0).getGrade());
            }
        }
        return studentVos;
    }

    @Override
    public List<TeacherVo> getOrgTeacherListByRealNames(Long orgId, List<String> realNames) {
        AjaxResult<List<TeacherVo>> ajaxResult = teacherApiService.getOrgStudentListByRealNames(orgId, realNames);
        if (ajaxResult.isFail() || ajaxResult.getData() == null) {
            return Lists.newArrayList();
        }
        return JSONUtil.toList(JSONUtil.parseArray(ajaxResult.getData()), TeacherVo.class);
    }

    @Override
    public Map<String, Object> getClazzInfoVoListSlow(ClazzConditionBoExt clazzConditionBo) {
        clazzConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        clazzConditionBo.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(ConstantsInteger.NUM_2);
        map.put("list", null);
        map.put("total", ConstantsInteger.NUM_0);

        List<ClazzVo> clazzVos = null;
        Integer total = ConstantsInteger.NUM_0;
        if (ConstantsInteger.APP_QUERY.equals(clazzConditionBo.getQueryType())) {
            AjaxResult<List<ClazzVo>> ajaxResult = openSlowApi.queryClassList(clazzConditionBo);
            if (ajaxResult != null && ajaxResult.isSuccess()) {
                clazzVos = ajaxResult.getData();
                total = CollectionUtils.isEmpty(clazzVos) ? ConstantsInteger.NUM_0 : clazzVos.size();
            }
        } else {
            Integer userDataAuthority = userRoleService.getUserMaxDataAuthority(clazzConditionBo.getAppId());
            if (ConstantsInteger.USER_DATA_AUTHORITY_CLASS.equals(userDataAuthority)) {
                List<Long> classIds = new ArrayList<>();
                String currentOid = currentUserService.getCurrentOid();
                if (null == clazzConditionBo.getTeachingScope()
                    || TeachingScopeEnum.TEACHING_All.getValue() == clazzConditionBo.getTeachingScope()) {
                    classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
                    classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
                } else if (TeachingScopeEnum.TEACHING_CLASS.getValue() == clazzConditionBo.getTeachingScope()) {
                    classIds.addAll(userRoleService.getTeacherClassIds(currentOid));
                } else if (TeachingScopeEnum.TEACHING_SUBJECT.getValue() == clazzConditionBo.getTeachingScope()) {
                    classIds.addAll(userRoleService.getTeacherSubjectClassIds(currentOid));
                }
                if (CollectionUtils.isEmpty(classIds)) {
                    return map;
                }
                clazzConditionBo.setIds(classIds);
                AjaxResult<PageInfo<ClazzVo>> ajaxResult = openSlowApi.queryClassPageList(clazzConditionBo);
                if (ajaxResult != null && ajaxResult.isSuccess()) {
                    clazzVos = ajaxResult.getData().getList();
                    total = new Long(ajaxResult.getData().getTotal()).intValue();
                }
            } else if (ConstantsInteger.USER_DATA_AUTHORITY_SCHOOL.equals(userDataAuthority)
                || ConstantsInteger.USER_DATA_AUTHORITY_AREA.equals(userDataAuthority)) {
                AjaxResult<List<ClazzVo>> ajaxResult = openSlowApi.queryClassList(clazzConditionBo);
                if (ajaxResult != null && ajaxResult.isSuccess()) {
                    clazzVos = ajaxResult.getData();
                    total = CollectionUtils.isEmpty(clazzVos) ? ConstantsInteger.NUM_0 : clazzVos.size();
                }
            }
        }
        if (CollectionUtils.isEmpty(clazzVos)) {
            return map;
        }

        List<ClazzInfoVo> clazzInfoVoList = new ArrayList<>();
        for (ClazzVo clazzVo : clazzVos) {
            ClazzInfoVo clazzInfoVo = new ClazzInfoVo();
            BeanUtils.copyProperties(clazzVo, clazzInfoVo);
            clazzInfoVo.setGradeName(SchoolYearUtil.gradeMap.get(clazzVo.getGrade()));
            clazzInfoVo.setClassesNameShow(clazzInfoVo.getGradeName().concat(clazzInfoVo.getClassesName()).concat("班"));
            clazzInfoVoList.add(clazzInfoVo);
        }
        map.put("list", clazzInfoVoList);
        map.put("total", total);
        return map;
    }

    /**
     * 获取易盾检测开关
     *
     * @return boolean 开true,关false
     * <AUTHOR>
     * @date 2023/5/17 9:55
     */
    public boolean getCheckSwitch() {
        return inspectProperties.isOpen();
    }

    /**
     * 检查文本是否合规
     *
     * @param text 文本
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 9:55
     */
    public boolean checkSingleText(String text) {
        return inspectService.checkSingleText(text);
    }

    /**
     * 富文本检查是否合规
     * 
     * @param richText富文本
     * @return boolean 通过true,不通过false
     */
    @Override
    public boolean checkRichText(String richText) {
        if (StringUtils.isBlank(richText)) {
            return true;
        }
        String textFromHtml = HtmlUtil.getTextFromHtml(richText);
        List<String> imagesFromHtml = HtmlUtil.getImagesFromHtml(richText);
        boolean checkSingleText = inspectService.checkSingleText(textFromHtml);
        if (CollectionUtils.isEmpty(imagesFromHtml)) {
            return checkSingleText;
        }

        boolean result = checkSingleText;
        for (String imageFromHtml : imagesFromHtml) {
            result = checkSingleText && inspectService.checkSingleImage(imageFromHtml);
        }
        return result;
    }

    /**
     * 检查图片是否合规
     *
     * @param imageUrl 图片路径
     * @return boolean 通过true,不通过false
     * <AUTHOR>
     * @date 2023/5/17 9:56
     */
    public boolean checkSingleImage(String imageUrl) {
        return inspectService.checkSingleImage(imageUrl);
    }

    /**
     * 获取班级列表
     *
     * @param clazzConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/25 15:47
     **/
    public AjaxResult<List<ClazzInfoVo>> getClazzListWithoutDataAuthority(ClazzConditionBoExt clazzConditionBo) {
        // 剔除毕业班级、添加排序
        clazzConditionBo.setPageNo(SystemConstants.NO_PAGE);
        clazzConditionBo.setIsGraduate(StatusEnum.NO.getCode());
        clazzConditionBo.setOrderBy(
            " CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        AjaxResult ajaxResult = null;
        ajaxResult = clazzApiService.getClassesListByCondition(clazzConditionBo);

        if (null == ajaxResult || ajaxResult.isFail()) {
            return AjaxResult.fail();
        }
        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        List<Object> objects = (List<Object>)dataMap.get("list");
        Integer total = (Integer)dataMap.get("total");

        List<ClazzInfoVo> clazzInfoVoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(objects)) {
            List<ClazzVo> clazzVos = JSONObject.parseArray(JSONObject.toJSONString(objects), ClazzVo.class);
            for (ClazzVo clazzVo : clazzVos) {
                ClazzInfoVo clazzInfoVo = new ClazzInfoVo();
                BeanUtils.copyProperties(clazzVo, clazzInfoVo);
                clazzInfoVo.setGradeName(SchoolYearUtil.gradeMap.get(clazzVo.getGrade()));
                clazzInfoVo
                    .setClassesNameShow(clazzInfoVo.getGradeName().concat(clazzInfoVo.getClassesName()).concat("班"));
                clazzInfoVoList.add(clazzInfoVo);
            }
        }

        return AjaxResult.success(clazzInfoVoList);
    }

    /**
     * 获取学校/组织和套餐关系
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 10:56
     **/
    public List<OrganizationPackageRelVo>
        getOrganizationPackageRelListByCondition(OrganizationPackageRelConditionBo condition) {
        AjaxResult ajaxResult = organizationPackageRelApi.getOrganizationPackageRelListByCondition(condition);
        if (ajaxResult.isSuccess() && ajaxResult.getData() != null) {
            return JSONArray.parseArray(JSONArray.toJSONString(ajaxResult.getData()), OrganizationPackageRelVo.class);
        }
        return null;
    }

    /**
     * 获取班主任信息
     *
     * @param condition
     * @return
     */
    public AjaxResult getClassesHeadmasterListByCondition(@RequestBody ClazzHeadmasterConditionBo condition) {
        return clazzHeadmasterApi.getClassesHeadmasterListByCondition(condition);
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictTypes the dict types
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    public List<DictionaryDataVo> listValueByTypes(List<String> dictTypes) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dictTypes)) {
            return new ArrayList<>();
        }

        // 去重
        dictTypes = dictTypes.stream().distinct().collect(Collectors.toList());

        List<DictionaryDataVo> resultList = new ArrayList<>();
        for (String dictType : dictTypes) {
            List<DictionaryDataVo> temp = listValueByTypeAndLabel(dictType);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(temp)) {
                continue;
            }
            resultList.addAll(temp);
        }
        return resultList;
    }

    /**
     * 获取被监管的学校id列表
     *
     * @param parentOrganizationId
     * @return
     */
    public List<Long> getSuperviseOrganizationIds(Long parentOrganizationId) {
        OrganizationSuperviseConditionBo condition = new OrganizationSuperviseConditionBo();
        condition.setParentOrganizationId(parentOrganizationId);
        condition.setSuperviseState(OrganizationSuperviseState.SUPERVISE_ON.getValue());
        AjaxResult<List<OrganizationSuperviseVo>> ajaxResult = organizationSuperviseApi.getOrganizationSuperviseListByCondition(condition);
        if (ajaxResult.isSuccess()) {
            List<OrganizationSuperviseVo> list = ajaxResult.getData();
            if (CollectionUtil.isNotEmpty(list)) {
                List<Long> organizationIds = list.stream().map(OrganizationSuperviseVo::getOrganizationId)
                        .collect(Collectors.toList());
                // 查询学校是否存在
                List<OrganizationVo> organizationVos = getOrganizationVoList(organizationIds);
                if (CollectionUtils.isNotEmpty(organizationVos)) {
                    return organizationVos.stream().map(OrganizationVo::getId).collect(Collectors.toList());
                }
            }
        }
        return null;
    }

    /**
     * 查询监管教育局信息
     *
     * @param organizationId
     * @return
     */
    public OrganizationVoExt getSuperviseParentOrganization(Long organizationId) {
        OrganizationSuperviseConditionBo condition = new OrganizationSuperviseConditionBo();
        condition.setOrganizationId(organizationId);
        condition.setSuperviseState(OrganizationSuperviseState.SUPERVISE_ON.getValue());
        AjaxResult<List<OrganizationSuperviseVo>> ajaxResult = organizationSuperviseApi.getOrganizationSuperviseListByCondition(condition);
        if (ajaxResult.isSuccess()) {
            List<OrganizationSuperviseVo> list = ajaxResult.getData();
            if (CollectionUtil.isNotEmpty(list)) {
                OrganizationSuperviseVo vo = list.get(ConstantsInteger.NUM_0);
                Long parentId = vo.getParentOrganizationId();
                OrganizationVoExt organizationVo = getOrganizationVoByOrgId(parentId);
                return organizationVo;
            }
        }
        return null;
    }

    /**
     * 获取监管学校列表
     *
     * @param parentOrganizationId
     * @return java.util.List<com.light.user.organization.entity.vo.OrganizationVo>
     * <AUTHOR>
     * @date 2024/8/9 15:41
     **/
    public List<OrganizationVo> getSuperviseOrganizationList(Long parentOrganizationId) {
        OrganizationSuperviseConditionBo condition = new OrganizationSuperviseConditionBo();
        condition.setParentOrganizationId(parentOrganizationId);
        condition.setSuperviseState(OrganizationSuperviseState.SUPERVISE_ON.getValue());
        AjaxResult<List<OrganizationSuperviseVo>> ajaxResult = organizationSuperviseApi.getOrganizationSuperviseListByCondition(condition);
        if (ajaxResult.isSuccess()) {
            List<OrganizationSuperviseVo> list = ajaxResult.getData();
            if (CollectionUtil.isNotEmpty(list)) {
                List<Long> organizationIds = list.stream().map(OrganizationSuperviseVo::getOrganizationId)
                        .collect(Collectors.toList());
                // 查询学校是否存在
                List<OrganizationVo> organizationVos = getOrganizationVoList(organizationIds);
                if (CollectionUtils.isNotEmpty(organizationVos)) {
                    return organizationVos;
                }
            }
        }
        return null;
    }

    @Override
    public List<KeeperRelationVo> getKeeperRelationListByUserOidList(List<String> userOidList) {
        if (CollectionUtils.isEmpty(userOidList)) {
            return null;
        }
        AjaxResult<List<KeeperRelationVo>> ajaxResult = keeperRelationApi.getListByUserOidList(userOidList);
        if (ajaxResult.isSuccess()) {
            return ajaxResult.getData();
        }
        return null;
    }

    @Override
    public AjaxResult<List<OrganizationVo>> getOrganizationListByAppId(Long appId) {
        OrganizationAppRelConditionBo conditionBo = new OrganizationAppRelConditionBo();
        conditionBo.setAppId(appId);
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        AjaxResult<List<OrganizationAppRelVo>> orgAppRelResult = organizationAppRelApi.getOrganizationAppRelListByCondition(conditionBo);
        if (orgAppRelResult.isSuccess() && CollectionUtils.isNotEmpty(orgAppRelResult.getData())) {
            List<OrganizationAppRelVo> organizationAppRelVos = orgAppRelResult.getData();
            List<Long> organizationIds = organizationAppRelVos.stream().map(OrganizationAppRelVo::getOrganizationId)
                    .collect(Collectors.toList());
            return AjaxResult.success(getOrganizationVoList(organizationIds));
        }
        return BaseDataService.super.getOrganizationListByAppId(appId);
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictType the dict type
     * @param dictLabel the dict label
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    public List<DictionaryDataVo> listValueByTypeAndLabel(String dictType) {
        DictionaryDataListConditionBo dictionaryDataListConditionBo = new DictionaryDataListConditionBo();
        dictionaryDataListConditionBo.setDictType(dictType);
        dictionaryDataListConditionBo.setPageNo(ConstantsInteger.NUM_1);
        dictionaryDataListConditionBo.setPageSize(ConstantsInteger.NUM_1000);
        AjaxResult<Object> ajaxResult =
            dictionaryDataApiService.getAllDictionaryDataList(dictionaryDataListConditionBo);

        if (ajaxResult.isFail()) {
            return new ArrayList<>();
        }
        Object data = ajaxResult.getData();

        List<DictionaryDataVo> dictionaryDataVos =
            JSONObject.parseArray(JSONObject.toJSONString(data), DictionaryDataVo.class);
        return dictionaryDataVos;
    }

}
