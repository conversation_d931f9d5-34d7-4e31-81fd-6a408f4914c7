package com.fh.cloud.screen.service.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceLog;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo;

import java.util.List;

/**
 * 考勤流水表，不用于业务查询Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface AttendanceLogMapper extends BaseMapper<AttendanceLog> {

    List<AttendanceLogVo> getAttendanceLogListByCondition(AttendanceLogListConditionBo condition);

}
