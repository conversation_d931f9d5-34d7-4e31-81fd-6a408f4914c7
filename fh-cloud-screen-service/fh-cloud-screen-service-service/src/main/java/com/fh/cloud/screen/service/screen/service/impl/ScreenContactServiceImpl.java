package com.fh.cloud.screen.service.screen.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContactDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContactVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenContactMapper;
import com.fh.cloud.screen.service.screen.service.IScreenContactService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
/**
 * 云屏产品咨询收集联系表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
@Service
public class ScreenContactServiceImpl extends ServiceImpl<ScreenContactMapper, ScreenContactDto> implements IScreenContactService {

	@Resource
	private ScreenContactMapper screenContactMapper;
	
    @Override
	public List<ScreenContactVo> getScreenContactListByCondition(ScreenContactConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return screenContactMapper.getScreenContactListByCondition(condition);
	}

	@Override
	public AjaxResult addScreenContact(ScreenContactBo screenContactBo) {
		ScreenContactDto screenContact = new ScreenContactDto();
		BeanUtils.copyProperties(screenContactBo, screenContact);
		screenContact.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(screenContact)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateScreenContact(ScreenContactBo screenContactBo) {
		ScreenContactDto screenContact = new ScreenContactDto();
		BeanUtils.copyProperties(screenContactBo, screenContact);
		if(updateById(screenContact)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public ScreenContactVo getScreenContactByCondition(ScreenContactConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		ScreenContactVo vo = screenContactMapper.getScreenContactByCondition(condition);
		return vo;
	}

}