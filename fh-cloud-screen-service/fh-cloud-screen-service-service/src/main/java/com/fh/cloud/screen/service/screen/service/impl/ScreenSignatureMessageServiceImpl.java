package com.fh.cloud.screen.service.screen.service.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureMessageDto;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenSignatureMessageMapper;
import com.fh.cloud.screen.service.screen.service.IScreenSignatureMessageService;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;

/**
 * 电子签名寄语资源表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
@Service
public class ScreenSignatureMessageServiceImpl extends
    ServiceImpl<ScreenSignatureMessageMapper, ScreenSignatureMessageDto> implements IScreenSignatureMessageService {

    @Resource
    private ScreenSignatureMessageMapper screenSignatureMessageMapper;

    @Override
    public List<ScreenSignatureMessageVo>
        getScreenSignatureMessageListByCondition(ScreenSignatureMessageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return screenSignatureMessageMapper.getScreenSignatureMessageListByCondition(condition);
    }

    @Override
    public AjaxResult addScreenSignatureMessage(ScreenSignatureMessageBo screenSignatureMessageBo) {
        ScreenSignatureMessageDto screenSignatureMessage = new ScreenSignatureMessageDto();
        BeanUtils.copyProperties(screenSignatureMessageBo, screenSignatureMessage);
        screenSignatureMessage.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(screenSignatureMessage)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateScreenSignatureMessage(ScreenSignatureMessageBo screenSignatureMessageBo) {
        ScreenSignatureMessageDto screenSignatureMessage = new ScreenSignatureMessageDto();
        BeanUtils.copyProperties(screenSignatureMessageBo, screenSignatureMessage);
        if (updateById(screenSignatureMessage)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ScreenSignatureMessageVo getScreenSignatureMessageByCondition(ScreenSignatureMessageConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        ScreenSignatureMessageVo vo = screenSignatureMessageMapper.getScreenSignatureMessageByCondition(condition);
        return vo;
    }

}