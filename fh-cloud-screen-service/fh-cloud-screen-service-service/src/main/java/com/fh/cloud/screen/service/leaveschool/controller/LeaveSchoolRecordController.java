package com.fh.cloud.screen.service.leaveschool.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.config.WxMpProperties;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.LeaveSchoolType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.OrganizationWxMsgTemplateType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolRecordApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordDto;
import com.fh.cloud.screen.service.leaveschool.entity.vo.*;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolBroadcastInfoService;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDeviceService;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceGroup;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.wx.entity.bo.OrganizationWxMsgTemplateConfigConditionBo;
import com.fh.cloud.screen.service.wx.entity.vo.OrganizationWxMsgTemplateConfigVo;
import com.fh.cloud.screen.service.wx.service.IOrganizationWxMsgTemplateConfigService;
import com.fh.cloud.screen.service.wx.service.WxMpMsgService;
import com.fh.sso.service.organization.api.OrganizationWxConfigApi;
import com.fh.sso.service.organization.entity.bo.OrganizationWxConfigConditionBo;
import com.fh.sso.service.organization.entity.vo.OrganizationWxConfigVo;
import com.fh.sso.service.user.api.UserAuthorizationApi;
import com.fh.sso.service.user.entity.bo.UserAuthorizationConditionBo;
import com.fh.sso.service.user.entity.vo.UserAuthorizationVo;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.user.entity.vo.KeeperRelationVo;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordBo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolRecordService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 放学记录表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
@RestController
@Validated
public class LeaveSchoolRecordController implements LeaveSchoolRecordApi {

    @Autowired
    private ILeaveSchoolRecordService leaveSchoolRecordService;


    /**
     * 查询放学记录表分页列表
     *
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<PageInfo<LeaveSchoolRecordVo>>
        getLeaveSchoolRecordPageListByCondition(@RequestBody LeaveSchoolRecordConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<LeaveSchoolRecordVo> pageInfo =
            new PageInfo<>(leaveSchoolRecordService.getLeaveSchoolRecordListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询放学记录表列表
     * 
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<List<LeaveSchoolRecordVo>>
        getLeaveSchoolRecordListByCondition(@RequestBody LeaveSchoolRecordConditionBo condition) {
        List<LeaveSchoolRecordVo> list = leaveSchoolRecordService.getLeaveSchoolRecordListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增放学记录表
     *
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult addLeaveSchoolRecord(@Validated @RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo) {
        return leaveSchoolRecordService.addLeaveSchoolRecord(leaveSchoolRecordBo);
    }

    /**
     * 修改放学记录表
     * 
     * @param leaveSchoolRecordBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult updateLeaveSchoolRecord(@Validated @RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo) {
        return leaveSchoolRecordService.updateLeaveSchoolRecordAndSendMsg(leaveSchoolRecordBo);
    }

    /**
     * 查询放学记录表详情
     *
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult<LeaveSchoolRecordVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("放学记录表id不能为空");
        }
        LeaveSchoolRecordConditionBo condition = new LeaveSchoolRecordConditionBo();
        condition.setLeaveSchoolRecordId(id);
        LeaveSchoolRecordVo vo = leaveSchoolRecordService.getLeaveSchoolRecordByCondition(condition);
        return AjaxResult.success(vo);
    }

    /**
     * 删除放学记录表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:31
     */
    @Override
    @FeignValidatorAnnotation
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        LeaveSchoolRecordDto leaveSchoolRecordDto = new LeaveSchoolRecordDto();
        leaveSchoolRecordDto.setLeaveSchoolRecordId(id);
        leaveSchoolRecordDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (leaveSchoolRecordService.updateById(leaveSchoolRecordDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 获取放学记录
     *
     * @param leaveSchoolRecordBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 19:45
     **/
    @Override
    public AjaxResult getLeaveSchoolRecord(@RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo) {
        return AjaxResult.success(leaveSchoolRecordService.getLeaveSchoolRecord(leaveSchoolRecordBo));
    }

}
