package com.fh.cloud.screen.service.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenQrcodeContentVo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 展示设备表，例如云屏接口
 *
 * <AUTHOR>
 * @email sunqb @ppm.com
 * @date 2022 -04-26 17:17:10
 */
public interface IShowDeviceService extends IService<ShowDevice> {

    /**
     * 只查询showDevice表的数据，不关联任何数据查询
     *
     * @param condition the condition
     * @return the show device list by condition
     */
    List<ShowDeviceVo> getShowDeviceListByConditionSingle(ShowDeviceListConditionBo condition);

    /**
     * 查询showDevice，同时会查询状态等数据（必传organizationid）
     *
     * @param condition the condition
     * @return the show device list by condition
     */
    List<ShowDeviceVo> getShowDeviceListByCondition(ShowDeviceListConditionBo condition);

    /**
     * Add show device boolean.
     *
     * @param showDeviceBo the show device bo
     * @return the boolean
     */
    boolean addShowDevice(ShowDeviceBo showDeviceBo);

    /**
     * Add show device batch.
     *
     * @param showDeviceBos the show device bo
     * @return the boolean
     */
    boolean addShowDeviceBatch(List<ShowDeviceBo> showDeviceBos);

    /**
     * Update show device boolean.
     *
     * @param showDeviceBo the show device bo
     * @return the boolean
     */
    boolean updateShowDevice(ShowDeviceBo showDeviceBo);

    /**
     * Gets detail.
     *
     * @param showDeviceId the show device id
     * @return the detail
     */
    ShowDeviceVo getDetail(Long showDeviceId);

    /**
     * 设备激活
     *
     * @param bo the bo
     * @return ajax result
     */
    AjaxResult<ShowDeviceVo> activate(ShowDeviceBo bo);

    /**
     * Gets by device num.
     *
     * @param deviceNum the device num
     * @return the by device num
     */
    ShowDevice getByDeviceNum(String deviceNum);

    /**
     * 根据班级获取设备列表
     *
     * @param classesId the classes id
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-09 17:38:02
     */
    List<ShowDeviceVo> listShowDeviceVoByClassesId(Long classesId);

    /**
     * 根据内容获取设备列表
     *
     * @param contentId the content id
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-09 17:38:02
     */
    List<ShowDeviceVo> listShowDeviceVoByContentId(Long contentId);

    /**
     * 根据内容获取设备列表
     *
     * @param contentSpecialId the content special id
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-09 17:38:02
     */
    List<ShowDeviceVo> listShowDeviceVoByContentSpecialId(Long contentSpecialId);

    /**
     * 根据内容获取设备列表
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param classesId the classes id
     * @param moduleId the module id
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-09 17:38:10
     */
    List<ShowDeviceVo> listShowDeviceVoByModuleId(Long organizationId, Long campusId, Long classesId, Long moduleId);

    /**
     * 根据场景获取设备列表
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param classesId the classes id
     * @param sceneIds the scene ids
     * @param isDelete the is delete
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-09 17:38:06
     */
    List<ShowDeviceVo> listShowDeviceVoBySceneIds(Long organizationId, Long campusId, Long classesId,
        List<Long> sceneIds, Integer isDelete);

    /**
     * 根据地点组获取设备列表（地点组-地点-设备）,行政教室
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param classesIds the classes ids
     * @param spaceGroupIds 地点组，必填
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:31:47
     */
    List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfXz(Long organizationId, Long campusId, List<Long> classesIds,
        List<Long> spaceGroupIds);

    /**
     * 根据地点组获取设备列表（地点组-地点-设备），非行政教室
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param spaceInfoIds the space info ids
     * @param spaceGroupIds 地点组，必填
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:31:47
     */
    List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfNotXz(Long organizationId, Long campusId,
        List<Long> spaceInfoIds, List<Long> spaceGroupIds);

    /**
     * 查询绑定了地点的设备列表-根据地点组查询
     *
     * @param condition the condition
     * @return show device list by condition
     * <AUTHOR>
     * @date 2022 -05-11 15:41:11
     */
    List<ShowDeviceVo> listShowDeviceDataByCondition(ShowDeviceListConditionBo condition);

    /**
     * 根据 设备号进行 更新设备状态
     *
     * @param deviceNumber the device number
     * @param status the device status
     * @return ajax result
     */
    AjaxResult updateStatusByDeviceNum(String deviceNumber, Integer status);

    /**
     * 更新设备状态
     *
     * @param deviceStatusType the device status type
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -05-12 15:54:15
     */
    boolean updateShowDeviceByDeviceNumbers(Integer deviceStatusType, Long organizationId, List<String> deviceNumbers);

    /**
     * 更新设备状态
     *
     * @param faceModType the face mod type
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -05-12 15:54:15
     */
    boolean updateShowDeviceFaceModByDeviceNumbers(Integer faceModType, Long organizationId, List<String> deviceNumbers);

    /**
     * 更新设备监管状态
     *
     * @param superviseState the supervise state
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -05-12 15:54:15
     */
    boolean updateShowDeviceSuperviseStateByDeviceNumbers(Integer superviseState, Long organizationId, List<String> deviceNumbers);

    /**
     * 更新 设备模式
     *
     * @param deviceNumber the device number
     * @param pattern the pattern
     * @return boolean boolean
     */
    boolean updatePatternByDeviceNumber(String deviceNumber, Integer pattern);

    /**
     * 更新 设备海报时间间隔
     *
     * @param deviceNumber the device number
     * @param devicePosterDuration the devicePosterDuration
     * @return boolean boolean
     */
    boolean updatePosterDurationByDeviceNumber(String deviceNumber, Integer devicePosterDuration);

    /**
     * 更新 虹软激活码
     *
     * @param deviceNumber the device number
     * @param arcsoftFaceCode 虹软激活码
     * @return boolean boolean
     */
    boolean updateArcsoftFaceCodeByDeviceNumber(String deviceNumber, String arcsoftFaceCode);

    /**
     * 更新 设备名称
     * @return
     */
    boolean updateDeviceNameByDeviceNumber(String deviceNumber,String deviceName);

    /**
     * 查询该学校绑定了地点的所有设备
     *
     * @param organizationId the organization id
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-24 16:56:38
     */
    List<ShowDeviceVo> listShowDeviceBindByOrganizationId(Long organizationId);

    /**
     * 修改全屏版式
     *
     * @param bo the bo
     * @return show device vo
     */
    ShowDeviceVo full(ShowDeviceBo bo);

    /**
     * 修改虹软人脸识别激活码
     *
     * @param bo the bo
     * @return show device vo
     */
    boolean arcCode(ShowDeviceBo bo);

    /**
     * 根据地点组获取设备列表（地点组-地点-设备），非行政教室
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param spaceInfoIds the space info ids
     * @param spaceGroupIds 地点组，非必填
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 10:31:47
     */
    List<ShowDeviceVo> listShowDeviceVoBySpaceGroupIdsOfAll(Long organizationId, Long campusId, List<Long> spaceInfoIds,
        List<Long> spaceGroupIds);

    /**
     * 根据非专用教室地点ids，查找其绑定的设备列表
     *
     * @param spaceIfoIds 非专用教室地点ids
     * @return java.util.List<com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo> list
     * <AUTHOR>
     * @date 2022 /8/26 9:26
     */
    List<ShowDeviceVo> listBySpaceInfoIdsOfNotXz(List<Long> spaceIfoIds);

    /**
     * 根据序列号，获取设备名称（班牌名称），出货号。
     *
     * @param deviceNumber the device number
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2022 /11/4 11:51
     */
    AjaxResult aboutByDeviceNum(String deviceNumber);

    /**
     * 根据设备号查询二维码内容（加密后的内容）:根据设备号封装内容并插入，插入缓存，返回加密内容
     *
     * @param screenQrcodeContentVo the screen qrcode content vo
     * @return qrcode content encode
     */
    void cacheQrcodeContent(ScreenQrcodeContentVo screenQrcodeContentVo);

    /**
     * 根据设备号获取二维码信息
     *
     * @param deviceNumber the device number
     * @return qrcode content decode
     */
    ScreenQrcodeContentVo getQrcodeContent(String deviceNumber);

    /**
     * 根据设备id 设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    AjaxResult setPosterRule(ShowDeviceBo showDeviceBo);

    /**
     * 根据设备空间列表 批量设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    AjaxResult setPosterRuleBatch(ShowDeviceBo showDeviceBo);

    /**
     * 根据设备id 获取设备主动推送及设备标签列表
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    AjaxResult getPosterRule(Long showDeviceId);

    /**
     * 根据行政教室地点ids，查找其绑定的设备列表
     *
     * @param spaceIfoIds 非专用教室地点ids
     * @return java.util.List<com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo> list
     * <AUTHOR>
     * @date 2022 /8/26 9:26
     */
    List<ShowDeviceVo> listBySpaceInfoIdsOfXz(List<Long> spaceIfoIds);

    /**
     * 查询该学校绑定了地点的所有设备
     *
     * @param organizationId the organization id
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-24 16:56:38
     */
    List<ShowDeviceVo> listShowDeviceBindByCondition(ShowDeviceListConditionBo condition);

    /**
     * 查询绑定了地点的设备列表-仅仅根据设备ids查询
     *
     * @param deviceIds the device ids
     * @return list
     * <AUTHOR>
     * @date 2024 -04-08 14:11:36
     */
    List<ShowDeviceVo> listShowDeviceByDeviceIdsOfBind(List<Long> deviceIds);
}
