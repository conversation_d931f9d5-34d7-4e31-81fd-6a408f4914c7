package com.fh.cloud.screen.service.rest.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestGradeActivityListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestGradeActivity;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeActivityVo;

import java.util.List;
import java.util.Map;

/**
 * 作息时间年级活动课设置表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IWorkRestGradeActivityService extends IService<WorkRestGradeActivity> {

    List<WorkRestGradeActivityVo>
        getWorkRestGradeActivityListByCondition(WorkRestGradeActivityListConditionBo condition);

    boolean addWorkRestGradeActivity(WorkRestGradeActivityBo workRestGradeActivityBo);

    boolean updateWorkRestGradeActivity(WorkRestGradeActivityBo workRestGradeActivityBo);

    Map<String, Object> getDetail(Long workRestGradeActivityId);

}
