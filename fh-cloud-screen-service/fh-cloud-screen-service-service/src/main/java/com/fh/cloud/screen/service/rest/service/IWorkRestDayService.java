package com.fh.cloud.screen.service.rest.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestDayListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRestDay;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo;

/**
 * 作息时间天设置表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IWorkRestDayService extends IService<WorkRestDay> {

    List<WorkRestDayVo> getWorkRestDayListByCondition(WorkRestDayListConditionBo condition);

    boolean addWorkRestDay(WorkRestDayBo workRestDayBo);

    boolean updateWorkRestDay(WorkRestDayBo workRestDayBo);

    WorkRestDayVo getDetail(Long workRestDayId);

    /**
     * 批量添加，同时将主键id添加到workRestGradeBoList并返回
     *
     * @param workRestId the work rest id
     * @param workRestDayBoList the work rest day bo list
     * @return
     */
    void deleteAndSaveBatch(Long workRestId, List<WorkRestDayBo> workRestDayBoList);

    /**
     * 按照workRestId删除
     * 
     * @param workRestId
     */
    void deleteByWorkRestId(Long workRestId);

    /**
     * 根据grade获取作息时间列表（为空则表示该学校各个年级作息时间一样）
     *
     * @param organizationId the organization id
     * @param grade the grade
     * @return work rest day list by grade
     * <AUTHOR>
     * @date 2023 -09-19 15:17:43
     */
    List<WorkRestDayVo> getWorkRestDayListByGrade(Long organizationId, String grade);

}
