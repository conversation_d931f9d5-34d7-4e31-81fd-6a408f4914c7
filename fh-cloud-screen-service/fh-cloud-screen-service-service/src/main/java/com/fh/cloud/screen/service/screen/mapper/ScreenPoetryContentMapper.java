package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryContentDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo;

/**
 * 共话诗词表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
public interface ScreenPoetryContentMapper extends BaseMapper<ScreenPoetryContentDto> {

	List<ScreenPoetryContentVo> getScreenPoetryContentListByCondition(ScreenPoetryContentConditionBo condition);

	ScreenPoetryContentVo getScreenPoetryContentByCondition(ScreenPoetryContentConditionBo condition);

}
