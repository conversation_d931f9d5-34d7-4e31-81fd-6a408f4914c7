package com.fh.cloud.screen.service.device.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fh.cloud.screen.service.card.entity.dto.UserCard;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.delayqueue.DelayService;
import com.fh.cloud.screen.service.delayqueue.entity.TaskDelayed;
import com.fh.cloud.screen.service.device.api.ShowDeviceCaptureApi;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceCaptureDto;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.CaptureMethodType;
import com.fh.cloud.screen.service.enums.DeviceCaptureStatusType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceCaptureService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import sun.swing.StringUIClientPropertyKey;

import java.util.List;

/**
 * 设备抓图表
 *
 * <AUTHOR>
 * @email sunqb @ppm.cn
 * @date 2022 -12-20 15:05:05
 */
@RestController
@Validated
public class ShowDeviceCaptureController implements ShowDeviceCaptureApi {

    @Autowired
    private IShowDeviceCaptureService showDeviceCaptureService;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Qualifier("DelayServiceCaptureImpl")
    @Autowired
    private DelayService<ShowDeviceCaptureVo> delayService;
    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 查询设备抓图表分页列表
     * 
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @Override
    public AjaxResult<PageInfo<ShowDeviceCaptureVo>>
        getShowDeviceCapturePageListByCondition(@RequestBody ShowDeviceCaptureConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ShowDeviceCaptureVo> pageInfo =
            new PageInfo<>(showDeviceCaptureService.getShowDeviceCaptureListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询设备抓图表列表
     * 
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @Override
    public AjaxResult<List<ShowDeviceCaptureVo>>
        getShowDeviceCaptureListByCondition(@RequestBody ShowDeviceCaptureConditionBo condition) {
        List<ShowDeviceCaptureVo> list = showDeviceCaptureService.getShowDeviceCaptureListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增设备抓图表
     * 
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @Override
    public AjaxResult addShowDeviceCapture(@Validated @RequestBody ShowDeviceCaptureBo showDeviceCaptureBo) {
        String deviceNumber = showDeviceCaptureBo.getDeviceNumber();
        if (StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("deviceNumber不可为空");
        }
        if (showDeviceCaptureBo.getShowDeviceId() == null) {
            Long showDeviceId = showDeviceService.getByDeviceNum(deviceNumber).getShowDeviceId();
            showDeviceCaptureBo.setShowDeviceId(showDeviceId);
        }
        showDeviceCaptureService.addShowDeviceCapture(showDeviceCaptureBo);
        return AjaxResult.success();
    }

    /**
     * 修改设备抓图表
     * 
     * @param showDeviceCaptureBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @Override
    public AjaxResult updateShowDeviceCapture(@Validated @RequestBody ShowDeviceCaptureBo showDeviceCaptureBo) {
        if (null == showDeviceCaptureBo.getId()) {
            return AjaxResult.fail("设备抓图表id不能为空");
        }
        return showDeviceCaptureService.updateShowDeviceCapture(showDeviceCaptureBo);
    }

    /**
     * 查询设备抓图表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @Override
    public AjaxResult<ShowDeviceCaptureVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("设备抓图表id不能为空");
        }
        ShowDeviceCaptureVo vo = showDeviceCaptureService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除设备抓图表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-20 15:05:05
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ShowDeviceCaptureDto showDeviceCaptureDto = new ShowDeviceCaptureDto();
        showDeviceCaptureDto.setId(id);
        showDeviceCaptureDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (showDeviceCaptureService.updateById(showDeviceCaptureDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 根据设备号查询最后一个截图信息
     *
     * @param deviceNumber the device number
     * @return detail by number
     * <AUTHOR>
     * @date 2022 -12-21 10:17:59
     */
    @Override
    public AjaxResult<ShowDeviceCaptureVo> getDetailByNumber(String deviceNumber) {
        if (StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("deviceNumber不可为空");
        }
        return AjaxResult.success(showDeviceCaptureService.getDetailByNumber(deviceNumber));
    }

    /**
     * 处理云屏提交截图
     *
     * @param showDeviceCaptureBo the show device capture bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -12-21 10:20:34
     */
    @Override
    public AjaxResult uploadShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo) {
        // 主动提交一次截图
        if (StringUtils.isNotBlank(showDeviceCaptureBo.getCaptureMethod())
            && showDeviceCaptureBo.getCaptureMethod().equals(CaptureMethodType.CREATE.getValue())) {
            showDeviceCaptureBo.setDeviceCaptureStatus(DeviceCaptureStatusType.SUCCESS.getValue());
            if (showDeviceCaptureBo.getShowDeviceId() == null) {
                Long showDeviceId =
                    showDeviceService.getByDeviceNum(showDeviceCaptureBo.getDeviceNumber()).getShowDeviceId();
                showDeviceCaptureBo.setShowDeviceId(showDeviceId);
                return AjaxResult.success(showDeviceCaptureService.addShowDeviceCapture(showDeviceCaptureBo));
            }
        }

        // 更新一次截图
        ShowDeviceCaptureVo showDeviceCaptureVo =
            showDeviceCaptureService.getDetailByNumber(showDeviceCaptureBo.getDeviceNumber());
        if (showDeviceCaptureVo == null) {
            return AjaxResult.fail("未查询到获取截图的请求");
        }
        if (showDeviceCaptureVo.getDeviceCaptureStatus().equals(DeviceCaptureStatusType.SUCCESS.getValue())) {
            return AjaxResult.fail("截图已经存在，需要重新发起截图请求");
        }
        // if (showDeviceCaptureVo.getDeviceCaptureStatus().equals(DeviceCaptureStatusType.FAIL.getValue())) {
        // return AjaxResult.fail("截图处理已经超时，需要重新发起截图请求");
        // }
        showDeviceCaptureBo.setId(showDeviceCaptureVo.getId());
        showDeviceCaptureBo.setDeviceNumber(showDeviceCaptureVo.getDeviceNumber());
        showDeviceCaptureBo.setIsDelete(showDeviceCaptureVo.getIsDelete());
        showDeviceCaptureBo.setOrganizationId(showDeviceCaptureVo.getOrganizationId());
        showDeviceCaptureBo.setShowDeviceId(showDeviceCaptureVo.getShowDeviceId());
        showDeviceCaptureBo.setDeviceCaptureStatus(DeviceCaptureStatusType.SUCCESS.getValue());
        return showDeviceCaptureService.updateShowDeviceCapture(showDeviceCaptureBo);
    }

    @Override
    public AjaxResult launchShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo) {
        String deviceNumber = showDeviceCaptureBo.getDeviceNumber();
        if (StringUtils.isBlank(deviceNumber)) {
            return AjaxResult.fail("deviceNumber不可为空");
        }
        if (showDeviceCaptureBo.getShowDeviceId() == null) {
            Long showDeviceId = showDeviceService.getByDeviceNum(deviceNumber).getShowDeviceId();
            showDeviceCaptureBo.setShowDeviceId(showDeviceId);
        }

        // 添加截图数据
        Long id = showDeviceCaptureService.addShowDeviceCapture(showDeviceCaptureBo);
        if (id == null) {
            return AjaxResult.fail("添加截图数据失败");
        }

        // 添加任务到延迟队列
        TaskDelayed<ShowDeviceCaptureVo> taskDelayed =
            new TaskDelayed<>(String.valueOf(id), null, ConstantsLong.DELAY_SERVICE_CAPTURE_EXPIRE_IN, null);
        delayService.addToDelayQueue(taskDelayed);

        // 通知云屏端ws消息
        applicationContext.publishEvent(PublishEvent.produceDevicePublishEvent(MessageWsType.SCREEN_CAPTURE.getValue(),
            showDeviceCaptureBo.getOrganizationId(), Lists.newArrayList(deviceNumber), null));
        return AjaxResult.success();
    }
}
