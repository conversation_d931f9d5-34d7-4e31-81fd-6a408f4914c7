package com.fh.cloud.screen.service.space.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.ClassesInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.mapper.ClassesInfoMapper;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.clazz.entity.bo.ClazzConditionBo;
import com.light.user.clazz.entity.vo.ClazzVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 行政区域内容扩展信息表（班级扩展信息）接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 16:11:44
 */
@Service
public class ClassesInfoServiceImpl extends ServiceImpl<ClassesInfoMapper, ClassesInfo> implements IClassesInfoService {

    @Resource
    private ClassesInfoMapper classesInfoMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Override
    public Map<String, Object> getClassesInfoListByCondition(ClassesInfoListConditionBo condition) {
        List<SpaceInfoVo> spaceInfoVoList = new ArrayList<>();

        // 如果 meetingUse不为空则需要提前查询 classinfo，然后提取出 ids，作为getClazzInfoVoList查询时候的条件。
        List<Long> meetingClassesIds = null;
        if (condition.getMeetingUse() != null) {
            ClassesInfoListConditionBo meetingCondition = new ClassesInfoListConditionBo();
            meetingCondition.setOrganizationId(condition.getOrganizationId());
            meetingCondition.setCampusId(condition.getCampusId());
            meetingCondition.setMeetingUse(condition.getMeetingUse());
            meetingCondition.setIsDelete(StatusEnum.NOTDELETE.getCode());
            
            List<ClassesInfoVo> meetingClassesInfoList = classesInfoMapper.getClassesInfoListByCondition(meetingCondition);
            if (CollectionUtils.isEmpty(meetingClassesInfoList)) {
                // 如果没有符合meetingUse条件的班级，返回空结果
                Map<String, Object> emptyResult = new java.util.HashMap<>();
                emptyResult.put("list", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("pageNo", condition.getPageNo());
                emptyResult.put("pageSize", condition.getPageSize());
                return emptyResult;
            }
            
            meetingClassesIds = meetingClassesInfoList.stream()
                .map(ClassesInfoVo::getClassesId)
                .collect(Collectors.toList());
        }

        ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
        clazzConditionBo.setOrganizationId(condition.getOrganizationId());
        clazzConditionBo.setGrade(condition.getGrade());
        if (StringUtils.isNotBlank(clazzConditionBo.getGrade())) {
            clazzConditionBo.setEnrollmentYear(SchoolYearUtil
                .getYearByGradeAndSchoolYear(clazzConditionBo.getGrade(), SchoolYearUtil.getCurrentSchoolYear())
                .longValue());
        }
        clazzConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        clazzConditionBo.setPageNo(condition.getPageNo());
        clazzConditionBo.setPageSize(condition.getPageSize());
        clazzConditionBo.setCampusId(condition.getCampusId());
        clazzConditionBo.setQueryType(condition.getQueryType());
        clazzConditionBo.setTeachingScope(condition.getTeachingScope());
        // 如果有meetingUse条件，设置班级ID过滤条件
        if (CollectionUtils.isNotEmpty(meetingClassesIds)) {
            clazzConditionBo.setIds(meetingClassesIds);
        }
        clazzConditionBo.setOrderBy(
            " grade Asc, CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> clazzInfoVoMap = baseDataService.getClazzInfoVoList(clazzConditionBo);
        List<ClazzInfoVo> clazzInfoVoList = (List<ClazzInfoVo>)clazzInfoVoMap.get("list");
        if (CollectionUtil.isEmpty(clazzInfoVoList)) {
            return clazzInfoVoMap;
        }

        List<Long> classesIds = clazzInfoVoList.stream().map(ClazzInfoVo::getId).collect(Collectors.toList());
        condition.setClassesIds(classesIds);
        List<ClassesInfoVo> classInfoList = classesInfoMapper.getClassesInfoListByCondition(condition);
        Map<Long, ClassesInfoVo> classInfoMap =
            classInfoList.stream().collect(Collectors.toMap(ClassesInfoVo::getClassesId, a -> a, (k1, k2) -> k1));

        for (ClazzInfoVo clazzInfoVo : clazzInfoVoList) {
            SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
            String grade = SchoolYearUtil.gradeMap.get(clazzInfoVo.getGrade());
            String spaceName = grade.concat(clazzInfoVo.getClassesName()).concat("班");
            spaceInfoVo.setSpaceInfoId(clazzInfoVo.getId());
            spaceInfoVo.setSpaceInfoName(spaceName);
            spaceInfoVo.setClassesName(clazzInfoVo.getClassesName());
            spaceInfoVo.setRemark(spaceName.concat("教室"));
            spaceInfoVo.setOrganizationId(clazzInfoVo.getOrganizationId());
            spaceInfoVo.setClassesId(clazzInfoVo.getId());
            spaceInfoVo.setCampusId(clazzInfoVo.getCampusId());
            spaceInfoVo.setCampusName(clazzInfoVo.getCampusName());
            spaceInfoVo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
            spaceInfoVo.setGrade(clazzInfoVo.getGrade());
            spaceInfoVo.setGradeName(clazzInfoVo.getGradeName());
            spaceInfoVo.setEnrollmentYear(clazzInfoVo.getEnrollmentYear());
            // 复制业务字段
            if (classInfoMap.containsKey(spaceInfoVo.getClassesId())
                && classInfoMap.get(spaceInfoVo.getClassesId()) != null) {
                BeanUtils.copyProperties(classInfoMap.get(spaceInfoVo.getClassesId()), spaceInfoVo);
            }
            spaceInfoVo.setSpaceGroupId(ConstantsConfig.SPACE_GROUP_ID_PTJS);
            spaceInfoVoList.add(spaceInfoVo);
        }
        clazzInfoVoMap.put("list", spaceInfoVoList);
        return clazzInfoVoMap;
    }

    @Override
    public boolean saveOrUpdateClassesInfo(ClassesInfoBo classesInfoBo) {
        ClassesInfo classesInfo = new ClassesInfo();
        BeanUtils.copyProperties(classesInfoBo, classesInfo);
        classesInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return saveOrUpdate(classesInfo);
    }

    @Override
    public boolean addClassesInfo(ClassesInfoBo classesInfoBo) {
        ClassesInfo classesInfo = new ClassesInfo();
        BeanUtils.copyProperties(classesInfoBo, classesInfo);
        classesInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(classesInfo);
    }

    @Override
    public boolean updateClassesInfo(ClassesInfoBo classesInfoBo) {
        ClassesInfo classesInfo = new ClassesInfo();
        BeanUtils.copyProperties(classesInfoBo, classesInfo);
        return updateById(classesInfo);
    }

    @Override
    public ClassesInfoVo getDetail(Long classesInfoId) {
        LambdaQueryWrapper<ClassesInfo> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ClassesInfo::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ClassesInfo::getClassesInfoId, classesInfoId);
        ClassesInfo classesInfo = getOne(lqw);
        ClassesInfoVo classesInfoVo = new ClassesInfoVo();
        BeanUtils.copyProperties(classesInfo, classesInfoVo);
        return classesInfoVo;
    }

    @Override
    public List<ClazzInfoVo> listClazzInfoVosByClassIds(List<Long> classesIds) {
        if(CollectionUtils.isEmpty(classesIds)){
            return Lists.newArrayList();
        }
        ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
        clazzConditionBo.setPageNo(SystemConstants.NO_PAGE);
        clazzConditionBo.setIds(classesIds);
        AjaxResult ajaxResult = baseDataService.getClassesListByClassesIds(clazzConditionBo);
        if (ajaxResult.isFail()) {
            return Lists.newArrayList();
        }

        Map<String, Object> dataMap = (Map<String, Object>)ajaxResult.getData();
        List<ClazzVo> clazzVos = JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), ClazzVo.class);
        if (CollectionUtil.isEmpty(clazzVos)) {
            return Lists.newArrayList();
        }
        List<ClazzInfoVo> clazzInfoVoList = new ArrayList<>();
        for (ClazzVo clazzVo : clazzVos) {
            ClazzInfoVo clazzInfoVo = new ClazzInfoVo();
            BeanUtils.copyProperties(clazzVo, clazzInfoVo);
            clazzInfoVo.setGradeName(SchoolYearUtil.gradeMap.get(clazzVo.getGrade()));
            clazzInfoVo.setClassesNameShow(clazzInfoVo.getGradeName().concat(clazzInfoVo.getClassesName()).concat("班"));
            clazzInfoVoList.add(clazzInfoVo);
        }
        return clazzInfoVoList;
    }

    @Override
    public ClassesInfoVo selectById(Long classInfoId) {
        QueryWrapper<ClassesInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ClassesInfo::getClassesInfoId, classInfoId).eq(ClassesInfo::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        final ClassesInfo classesInfo = this.classesInfoMapper.selectOne(queryWrapper);
        if (classesInfo == null) {
            return null;
        }
        return BeanUtil.toBean(classesInfo, ClassesInfoVo.class);
    }

    @Override
    public ClassesInfoVo getByClassesId(Long classesId) {
        QueryWrapper<ClassesInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ClassesInfo::getClassesId, classesId).eq(ClassesInfo::getIsDelete,
            StatusEnum.NOTDELETE.getCode());
        final ClassesInfo classesInfo = this.classesInfoMapper.selectOne(queryWrapper);
        if (classesInfo == null) {
            return null;
        }
        return BeanUtil.toBean(classesInfo, ClassesInfoVo.class);
    }

    @Override
    public Map<String, Object> getClassesInfoListByConditionSlow(ClassesInfoListConditionBo condition) {
        List<SpaceInfoVo> spaceInfoVoList = new ArrayList<>();

        ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
        clazzConditionBo.setOrganizationId(condition.getOrganizationId());
        clazzConditionBo.setGrade(condition.getGrade());
        if (StringUtils.isNotBlank(clazzConditionBo.getGrade())) {
            clazzConditionBo.setEnrollmentYear(SchoolYearUtil
                .getYearByGradeAndSchoolYear(clazzConditionBo.getGrade(), SchoolYearUtil.getCurrentSchoolYear())
                .longValue());
        }
        clazzConditionBo.setPageNo(condition.getPageNo());
        clazzConditionBo.setPageSize(condition.getPageSize());
        clazzConditionBo.setCampusId(condition.getCampusId());
        clazzConditionBo.setQueryType(condition.getQueryType());
        clazzConditionBo.setTeachingScope(condition.getTeachingScope());
        clazzConditionBo.setOrderBy(
            " grade Asc, CASE WHEN `classes_name` NOT REGEXP '^[0-9]+$' THEN CONVERT(`classes_name` USING gbk) END ASC ,CASE WHEN `classes_name` REGEXP '^[0-9]+$' THEN CAST(`classes_name` AS UNSIGNED) END ASC");
        Map<String, Object> clazzInfoVoMap = baseDataService.getClazzInfoVoListSlow(clazzConditionBo);
        List<ClazzInfoVo> clazzInfoVoList = (List<ClazzInfoVo>)clazzInfoVoMap.get("list");
        if (CollectionUtil.isEmpty(clazzInfoVoList)) {
            return clazzInfoVoMap;
        }

        List<Long> classesIds = clazzInfoVoList.stream().map(ClazzInfoVo::getId).collect(Collectors.toList());
        condition.setClassesIds(classesIds);
        List<ClassesInfoVo> classInfoList = classesInfoMapper.getClassesInfoListByCondition(condition);
        Map<Long, ClassesInfoVo> classInfoMap =
            classInfoList.stream().collect(Collectors.toMap(ClassesInfoVo::getClassesId, a -> a, (k1, k2) -> k1));

        for (ClazzInfoVo clazzInfoVo : clazzInfoVoList) {
            SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
            String grade = SchoolYearUtil.gradeMap.get(clazzInfoVo.getGrade());
            String spaceName = grade.concat(clazzInfoVo.getClassesName()).concat("班");
            spaceInfoVo.setSpaceInfoId(clazzInfoVo.getId());
            spaceInfoVo.setSpaceInfoName(spaceName);
            spaceInfoVo.setClassesName(clazzInfoVo.getClassesName());
            spaceInfoVo.setRemark(spaceName.concat("教室"));
            spaceInfoVo.setClassesId(clazzInfoVo.getId());
            spaceInfoVo.setCampusId(clazzInfoVo.getCampusId());
            spaceInfoVo.setCampusName(clazzInfoVo.getCampusName());
            spaceInfoVo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
            spaceInfoVo.setGrade(clazzInfoVo.getGrade());
            spaceInfoVo.setGradeName(clazzInfoVo.getGradeName());
            spaceInfoVo.setEnrollmentYear(clazzInfoVo.getEnrollmentYear());
            // 复制业务字段
            if (classInfoMap.containsKey(spaceInfoVo.getClassesId())
                && classInfoMap.get(spaceInfoVo.getClassesId()) != null) {
                BeanUtils.copyProperties(classInfoMap.get(spaceInfoVo.getClassesId()), spaceInfoVo);
            }
            spaceInfoVo.setSpaceGroupId(ConstantsConfig.SPACE_GROUP_ID_PTJS);
            spaceInfoVoList.add(spaceInfoVo);
        }
        clazzInfoVoMap.put("list", spaceInfoVoList);
        return clazzInfoVoMap;

    }
}