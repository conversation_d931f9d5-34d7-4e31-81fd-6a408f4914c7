package com.fh.cloud.screen.service.device.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceCaptureDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;

/**
 * 设备抓图表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
public interface ShowDeviceCaptureMapper extends BaseMapper<ShowDeviceCaptureDto> {

    List<ShowDeviceCaptureVo> getShowDeviceCaptureListByCondition(ShowDeviceCaptureConditionBo condition);

}
