package com.fh.cloud.screen.service.screenConfig.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screenConfig.entity.dto.ScreenConfigDto;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;

/**
 * 云屏配置表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2024-07-29 09:10:13
 */
public interface ScreenConfigMapper extends BaseMapper<ScreenConfigDto> {

	List<ScreenConfigVo> getScreenConfigListByCondition(ScreenConfigConditionBo condition);

	ScreenConfigVo getScreenConfigByCondition(ScreenConfigConditionBo condition);

}
