package com.fh.cloud.screen.service.space.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.ClassesInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;

import java.util.List;
import java.util.Map;

/**
 * 行政区域内容扩展信息表（班级扩展信息）接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 16:11:44
 */
public interface IClassesInfoService extends IService<ClassesInfo> {

    Map<String, Object> getClassesInfoListByCondition(ClassesInfoListConditionBo condition);

    boolean saveOrUpdateClassesInfo(ClassesInfoBo classesInfoBo);

    boolean addClassesInfo(ClassesInfoBo classesInfoBo);

    boolean updateClassesInfo(ClassesInfoBo classesInfoBo);

    ClassesInfoVo getDetail(Long classesInfoId);

    /**
     * 根据classInfoIs查询班级数据
     *
     * @param classesIds the classes ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-11 18:22:06
     */
    List<ClazzInfoVo> listClazzInfoVosByClassIds(List<Long> classesIds);

    /**
     * 根据Id获取信息
     *
     * @param classInfoId
     * @return
     */
    ClassesInfoVo selectById(Long classInfoId);

    /**
     * 根据班级ID 获取信息
     *
     * @param classesId
     * @return
     */
    ClassesInfoVo getByClassesId(Long classesId);

    /**
     * 查询班级信息-slow，不带is_delete条件
     *
     * @param condition the condition
     * @return classes info list by condition slow
     * <AUTHOR>
     * @date 2023 -03-30 13:42:26
     */
    Map<String, Object> getClassesInfoListByConditionSlow(ClassesInfoListConditionBo condition);
}
