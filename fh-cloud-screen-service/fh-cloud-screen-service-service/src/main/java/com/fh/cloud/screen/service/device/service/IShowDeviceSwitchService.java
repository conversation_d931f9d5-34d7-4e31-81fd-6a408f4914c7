package com.fh.cloud.screen.service.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceSwitch;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceSwitchVo;

import java.util.List;

/**
 * 开关机设置接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IShowDeviceSwitchService extends IService<ShowDeviceSwitch> {

    List<ShowDeviceSwitchVo> getShowDeviceSwitchListByCondition(ShowDeviceSwitchListConditionBo condition);

    boolean addShowDeviceSwitch(ShowDeviceSwitchBo showDeviceSwitchBo);

    boolean updateShowDeviceSwitch(ShowDeviceSwitchBo showDeviceSwitchBo);

    ShowDeviceSwitchVo getDetail(Long showDeviceSwitchId);

    /**
     * 先删除学校校区的开关机设置再批量新增
     *
     * @param showDeviceSwitchBos the show device switch bos
     * <AUTHOR>
     * @date 2022 -06-15 15:09:08
     */
    void deleteAndAddScreenSceneModuleRelBatch(Long organizationId, Long campusId,
        List<ShowDeviceSwitchBo> showDeviceSwitchBos);

}
