package com.fh.cloud.screen.service.leaveschool.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 学生进出记录表
 * 
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:32:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("leave_school_student_sign_record")
public class LeaveSchoolStudentSignRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "leave_school_student_sign_record_id", type = IdType.AUTO)
	private Long leaveSchoolStudentSignRecordId;

	/**
	 * 学生id
	 */
	@TableField("student_id")
	private Long studentId;

	/**
	 * 学生姓名
	 */
	@TableField("student_name")
	private String studentName;

	/**
	 * 证件号
	 */
	@TableField("identity_card_number")
	private String identityCardNumber;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 组织名称
	 */
	@TableField("organization_name")
	private String organizationName;

	/**
	 * 班级id
	 */
	@TableField("classes_id")
	private Long classesId;

	/**
	 * 签到时间
	 */
	@TableField("sign_time")
	private Date signTime;

	/**
	 * 签到类型 0-进校 1-离校
	 */
	@TableField("sign_type")
	private Integer signType;

	/**
	 * 签到类型名称
	 */
	@TableField("sign_type_name")
	private String signTypeName;

	/**
	 * 签到方式 0-刷脸 1-刷卡
	 */
	@TableField("sign_way")
	private Integer signWay;

	/**
	 * 签到方式名称
	 */
	@TableField("sign_way_name")
	private String signWayName;

	/**
	 * 第三方学校id
	 */
	@TableField("third_school_id")
	private String thirdSchoolId;

	/**
	 * 第三方学校名称
	 */
	@TableField("third_school_name")
	private String thirdSchoolName;

	/**
	 * 第三方学生id
	 */
	@TableField("third_student_user_id")
	private String thirdStudentUserId;

	/**
	 * 第三方学生姓名
	 */
	@TableField("third_student_name")
	private String thirdStudentName;

	/**
	 * 第三方卡号
	 */
	@TableField("third_card_number")
	private String thirdCardNumber;

	/**
	 * 第三方记录id
	 */
	@TableField("third_sign_record_id")
	private String thirdSignRecordId;

	/**
	 * 应用code
	 */
	@TableField("app_code")
	private String appCode;

	/**
	 * 数据来源 0-本系统 1-第三方推送
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
