package com.fh.cloud.screen.service.rest.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间年级表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("work_rest_grade")
public class WorkRestGrade implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "work_rest_grade_id", type = IdType.AUTO)
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    @TableField("work_rest_id")
    private Long workRestId;

    /**
     * grade 的code值，年级一致的情况这个值为默认值
     */
    @TableField("grade")
    private String grade;

    /**
     * 一周作息时间是否一致：1一致，2不一致
     */
    @TableField("week_same_type")
    private Integer weekSameType;

    /**
     * 上午课节数：0，1，2，3...
     */
    @TableField("course_num_am")
    private Integer courseNumAm;

    /**
     * 下午课节数：0，1，2，3...
     */
    @TableField("course_num_pm")
    private Integer courseNumPm;

    /**
     * 晚上课节数：0，1，2，3...
     */
    @TableField("course_num_nt")
    private Integer courseNumNt;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
