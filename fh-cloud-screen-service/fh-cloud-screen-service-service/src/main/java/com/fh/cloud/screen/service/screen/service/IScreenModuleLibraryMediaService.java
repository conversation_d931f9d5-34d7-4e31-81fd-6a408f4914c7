package com.fh.cloud.screen.service.screen.service;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * <p>
 * 云屏模块库媒体资源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
public interface IScreenModuleLibraryMediaService extends IService<ScreenModuleLibraryMedia> {

    List<ScreenModuleLibraryMediaVo> getScreenModuleLibraryMediaListByCondition(ScreenModuleLibraryMediaBo condition);

    /**
     * 全部海报列表
     *
     * @param organizationId 组织id
     * @param selectType 1 校本海报包含热门，2 校本海报库无热门，3校本海报
     * @param pattern 1横屏 2竖屏
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/24 11:00
     */
    AjaxResult getThemePosterList(Long organizationId, Integer selectType, Integer pattern);

    /**
     * 海报前20个分组和每个分组的前10主题列表（移动端）
     *
     * @param organizationId 组织id
     * @param selectType 1 校本海报包含热门，2 校本海报库无热门，3校本海报
     * @param pattern 1横屏 2竖屏
     * @param moduleGroupTopLimit 取模块分组前多少个，null全部
     * @param libraryTopLimit 取模块前多少个，null全部
     * @param moduleDefaultH5LabelGroup h5端首页查询的默认标签组下面的标签和海报（默认没有，通过nacos指定）
     * @return com.light.core.entity.AjaxResult top poster list
     * <AUTHOR>
     * @date 2023 /3/24 11:00
     */
    AjaxResult getTopPosterList(Long organizationId, Integer selectType, Integer pattern, Integer moduleGroupTopLimit,
        Integer libraryTopLimit, Integer moduleDefaultH5LabelGroup, Long classesId);

    /**
     * 我收藏的所有海报列表
     *
     * @param organizationId 组织id
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo>
     * <AUTHOR>
     * @date 2023/4/6 9:40
     */
    public List<ScreenModuleLibraryCollectVo> getMyCollectLibraryVos(Long organizationId);

    /**
     * 我收藏的海报
     *
     * @param organization, pattern
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/31 17:39
     */
    AjaxResult getMyCollectPosters(Long organization, Integer pattern);

    /**
     * 分页获取我收藏的海报列表
     *
     * @param libraryCollectConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/20 15:31
     */
    AjaxResult getMyCollectPostersPage(ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo);

    AjaxResult coverPoster();

    /**
     * 海报导入优化 全匹配文件与库中海报更新db，以文件海报的分组、模块、图片为准
     *
     * @param organizationId 组织id
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/24 14:24
     */
    AjaxResult posterOptimization(Long organizationId);

    /**
     * 海报导入 （只加不删，重复图片跳过）
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/12 17:19
     */
    public AjaxResult importPoster(Long organizationId);

    AjaxResult addScreenModuleLibraryMedia(ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo);

    AjaxResult addScreenModuleLibraryMediaBatch(List<ScreenModuleLibraryMediaBo> screenModuleLibraryMediaBos);

    AjaxResult updateScreenModuleLibraryMedia(ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo);

    /**
     * 查询当天节日或者节点的海报列表
     *
     * @param condition the condition，可以初始值为空
     * @return current day festival module library media list
     * <AUTHOR>
     * @date 2023 -03-23 14:53:47
     */
    List<ScreenModuleLibraryMediaVo> getCurrentDayFestivalModuleLibraryMediaList(ScreenModuleLibraryMediaBo condition);

    /**
     * 查询某个设备订阅的所有标签的海报集合
     *
     * @param showDeviceId the show device id
     * @return label module library media list
     * <AUTHOR>
     * @date 2023 -03-23 16:14:29
     */
    List<ScreenModuleLibraryMediaVo> getLabelModuleLibraryMediaListByShowDeviceId(Long showDeviceId);

    /**
     * 查询默认的分类下面的所有的海报数据（目前产品定义为：公益分类）{@link com.fh.cloud.screen.service.consts.ConstantsConfig.DEFAULT_MODULE_GROUP_TYPE}
     *
     * @return default module library media list
     * <AUTHOR>
     * @date 2023 -03-23 16:33:09
     */
    List<ScreenModuleLibraryMediaVo> getDefaultModuleLibraryMediaList();
}
