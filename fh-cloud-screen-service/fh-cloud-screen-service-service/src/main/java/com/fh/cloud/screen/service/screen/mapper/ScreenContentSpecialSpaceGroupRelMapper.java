package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialSpaceGroupRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecialSpaceGroupRel;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialSpaceGroupRelVo;

import java.util.List;

/**
 * 云屏紧急发布内容-地点组关系表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface ScreenContentSpecialSpaceGroupRelMapper extends BaseMapper<ScreenContentSpecialSpaceGroupRel> {

    List<ScreenContentSpecialSpaceGroupRelVo>
        getScreenContentSpecialSpaceGroupRelListByCondition(ScreenContentSpecialSpaceGroupRelListConditionBo condition);

}
