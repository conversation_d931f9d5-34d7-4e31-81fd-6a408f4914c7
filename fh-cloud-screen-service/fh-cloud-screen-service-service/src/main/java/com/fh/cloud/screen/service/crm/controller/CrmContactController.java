package com.fh.cloud.screen.service.crm.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.crm.api.CrmContactApi;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactConditionBo;
import com.fh.cloud.screen.service.crm.entity.dto.CrmContactDto;
import com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo;
import com.fh.cloud.screen.service.crm.service.ICrmContactService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
/**
 * CRM商讯联系人表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
@RestController
@Validated
public class CrmContactController implements CrmContactApi{
	
    @Autowired
    private ICrmContactService crmContactService;

    /**
     * 查询CRM商讯联系人表分页列表
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
    @Override
    public AjaxResult<PageInfo<CrmContactVo>> getCrmContactPageListByCondition(@RequestBody CrmContactConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<CrmContactVo> pageInfo = new PageInfo<>(crmContactService.getCrmContactListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询CRM商讯联系人表列表
	 * <AUTHOR>
	 * @date 2023-12-27 18:20:08
	 */
	@Override
	public AjaxResult<List<CrmContactVo>> getCrmContactListByCondition(@RequestBody CrmContactConditionBo condition){
		List<CrmContactVo> list = crmContactService.getCrmContactListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增CRM商讯联系人表
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
     */
	@Override
    public AjaxResult addCrmContact(@Validated @RequestBody CrmContactBo crmContactBo){
		return crmContactService.addCrmContact(crmContactBo);
    }

    /**
	 * 修改CRM商讯联系人表
	 * @param crmContactBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
	 */
	@Override
	public AjaxResult updateCrmContact(@Validated @RequestBody CrmContactBo crmContactBo) {
		if(null == crmContactBo.getCrmContactId()) {
			return AjaxResult.fail("CRM商讯联系人表id不能为空");
		}
		return crmContactService.updateCrmContact(crmContactBo);
	}

	/**
	 * 查询CRM商讯联系人表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
	 */
	@Override
	public AjaxResult<CrmContactVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("CRM商讯联系人表id不能为空");
		}
		CrmContactConditionBo condition = new CrmContactConditionBo();
		condition.setCrmContactId(id);
		CrmContactVo vo = crmContactService.getCrmContactByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除CRM商讯联系人表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-27 18:20:08
	 */
	@Override
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		CrmContactDto crmContactDto = new CrmContactDto();
		crmContactDto.setCrmContactId(id);
		crmContactDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(crmContactService.updateById(crmContactDto)) {
			return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
