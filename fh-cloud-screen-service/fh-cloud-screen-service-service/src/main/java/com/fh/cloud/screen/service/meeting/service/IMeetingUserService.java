package com.fh.cloud.screen.service.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingUserDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingUserVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 会议人员表接口
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
public interface IMeetingUserService extends IService<MeetingUserDto> {

    List<MeetingUserVo> getMeetingUserListByCondition(MeetingUserConditionBo condition);

    AjaxResult addMeetingUser(MeetingUserBo meetingUserBo);

    AjaxResult updateMeetingUser(MeetingUserBo meetingUserBo);

    MeetingUserVo getDetail(Long meetingUserId);

}
