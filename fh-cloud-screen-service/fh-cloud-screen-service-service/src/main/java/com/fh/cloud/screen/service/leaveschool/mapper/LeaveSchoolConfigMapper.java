package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo;
import org.apache.ibatis.annotations.Param;

/**
 * 放学配置表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
public interface LeaveSchoolConfigMapper extends BaseMapper<LeaveSchoolConfigDto> {

	List<LeaveSchoolConfigVo> getLeaveSchoolConfigListByCondition(LeaveSchoolConfigConditionBo condition);

	LeaveSchoolConfigVo getLeaveSchoolConfigByCondition(LeaveSchoolConfigConditionBo condition);

	LeaveSchoolConfigVo getLeaveSchoolConfigBySpaceInfo(@Param("organizationId") Long organizationId,
														@Param("spaceInfoId") Long spaceInfoId,
														@Param("spaceGroupUseType") Integer spaceGroupUseType,
														@Param("showDeviceId") Long showDeviceId);
}
