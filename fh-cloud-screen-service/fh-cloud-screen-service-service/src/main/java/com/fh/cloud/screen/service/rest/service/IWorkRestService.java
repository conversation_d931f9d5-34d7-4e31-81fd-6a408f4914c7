package com.fh.cloud.screen.service.rest.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestListConditionBo;
import com.fh.cloud.screen.service.rest.entity.dto.WorkRest;
import com.fh.cloud.screen.service.rest.entity.vo.WorkRestVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;

/**
 * 作息时间主表接口
 *
 * <AUTHOR>
 * @email sunqb @ppm.com
 * @date 2022 -04-26 17:17:10
 */
public interface IWorkRestService extends IService<WorkRest> {

    /**
     * Gets work rest list by condition.
     *
     * @param condition the condition
     * @return the work rest list by condition
     */
    List<WorkRestVo> getWorkRestListByCondition(WorkRestListConditionBo condition);

    /**
     * Add work rest long.
     *
     * @param workRestBo the work rest bo
     * @return the long
     */
    Long addWorkRest(WorkRestBo workRestBo);

    /**
     * Update work rest boolean.
     *
     * @param workRestBo the work rest bo
     * @return the boolean
     */
    boolean updateWorkRest(WorkRestBo workRestBo);

    /**
     * Gets detail.
     *
     * @param workRestId the work rest id
     * @return the detail
     */
    WorkRestVo getDetail(Long workRestId);

    /**
     * 新增或者更新作息时间同时更新详情信息，返回作息时间id
     *
     * @param workRestBo the work rest bo
     * @return long long
     * <AUTHOR>
     * @date 2022 -08-22 17:12:15
     */
    Long saveOrUpdateWorkRestWithDetail(WorkRestBo workRestBo);

    /**
     * 启用禁用作息时间业务
     *
     * @param workRestBo the work rest bo
     */
    void changeStatus(WorkRestBo workRestBo);
}
