package com.fh.cloud.screen.service.config;

import com.aliyun.facebody20191230.Client;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云faceBody人脸识别配置类
 * 
 * <AUTHOR>
 * @date 2023/5/16 15:56
 */
@Configuration
public class FaceBodyConfig {

    @Value("${aliyun.facebody.access.key.id:}")
    private String accessKeyId;
    @Value("${aliyun.facebody.access.key.secret:}")
    private String accessKeySecret;
    @Value("${aliyun.facebody.endpoint:}")
    private String endpoint;

    /**
     * 人脸搜索1:N产品的client
     *
     * @return com . aliyun . facebody 20191230 . client
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2023 -05-16 16:03:17
     */
    @Bean
    public com.aliyun.facebody20191230.Client createClient() {
        com.aliyun.teaopenapi.models.Config config =
            new com.aliyun.teaopenapi.models.Config().setAccessKeyId(accessKeyId).setAccessKeySecret(accessKeySecret);
        config.endpoint = endpoint;
        Client client = null;
        try {
            client = new Client(config);
        } catch (Exception e) {
            // 忽略异常
        }
        return client;
    }
}
