package com.fh.cloud.screen.service.leaveschool.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 放学播报信息表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("leave_school_broadcast_info")
public class LeaveSchoolBroadcastInfoDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学播报信息表id
	 */
	@TableId(value = "broadcast_info_id", type = IdType.AUTO)
	private Long broadcastInfoId;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@TableField("campus_id")
	private Long campusId;

	/**
	 * 播报文字内容
	 */
	@TableField("broadcast_content")
	private String broadcastContent;

	/**
	 * 播报文件oid
	 */
	@TableField("broadcast_id")
	private String broadcastId;

	/**
	 * 播报文件url
	 */
	@TableField("broadcast_url")
	private String broadcastUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 播放次数
	 */
	@TableField("play_times")
	private Integer playTimes;
}
