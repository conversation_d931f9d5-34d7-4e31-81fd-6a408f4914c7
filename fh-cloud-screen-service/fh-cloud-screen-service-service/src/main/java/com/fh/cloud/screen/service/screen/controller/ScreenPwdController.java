package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenPwdApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo;
import com.fh.cloud.screen.service.screen.service.IScreenPwdService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 云屏密码
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@RequestMapping("/screen/pwd")
@Validated
public class ScreenPwdController implements ScreenPwdApi {

    @Autowired
    private IScreenPwdService screenPwdService;

    /**
     * 查询云屏密码列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询云屏密码列表", httpMethod = "POST")
    public AjaxResult getScreenPwdListByCondition(@RequestBody ScreenPwdListConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);

        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ScreenPwdVo> pageInfo = new PageInfo<>(screenPwdService.getScreenPwdListByCondition(condition));
            map.put("count", pageInfo.getTotal());
            map.put("list", pageInfo.getList());
        } else {
            List<ScreenPwdVo> screenPwdList = screenPwdService.getScreenPwdListByCondition(condition);
            map.put("list", screenPwdList);
        }

        return AjaxResult.success(map);
    }

    /**
     * 根据组织机构获取 云屏密码信息
     * 
     * @param orgId
     * @return
     */
    @GetMapping("getByOrgId")
    public AjaxResult<ScreenPwdVo> getByOrgId(@RequestParam("orgId") Long orgId) {
        return AjaxResult.success(this.screenPwdService.getByOrgId(orgId));
    }

    /**
     * 新增云屏密码
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增云屏密码", httpMethod = "POST")
    public AjaxResult addScreenPwd(@RequestBody ScreenPwdBo screenPwdBo) {
        boolean save = screenPwdService.addScreenPwd(screenPwdBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏密码
     * 
     * @param screenPwdBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改云屏密码", httpMethod = "POST")
    public AjaxResult updateScreenPwd(@RequestBody ScreenPwdBo screenPwdBo) {
        if (null == screenPwdBo.getScreenPwdId()) {
            return AjaxResult.fail("云屏密码id不能为空");
        }
        boolean update = screenPwdService.updateScreenPwd(screenPwdBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏密码详情
     * 
     * @param screenPwdId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询云屏密码详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenPwdId") Long screenPwdId) {
        Map<String, Object> map = screenPwdService.getDetail(screenPwdId);
        return AjaxResult.success(map);
    }

    /**
     * 删除云屏密码
     * 
     * @param screenPwdId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除云屏密码", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenPwdId") Long screenPwdId) {
        ScreenPwdBo screenPwdBo = new ScreenPwdBo();
        screenPwdBo.setScreenPwdId(screenPwdId);
        screenPwdBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenPwdService.updateScreenPwd(screenPwdBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
