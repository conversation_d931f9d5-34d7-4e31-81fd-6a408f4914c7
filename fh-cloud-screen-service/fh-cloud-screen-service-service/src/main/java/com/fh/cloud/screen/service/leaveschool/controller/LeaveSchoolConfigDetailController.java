package com.fh.cloud.screen.service.leaveschool.controller;

import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigDetailApi;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDetailDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDetailService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 放学配置详情表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
@RestController
@Validated
public class LeaveSchoolConfigDetailController implements LeaveSchoolConfigDetailApi{
	
    @Autowired
    private ILeaveSchoolConfigDetailService leaveSchoolConfigDetailService;

    /**
     * 查询放学配置详情表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<LeaveSchoolConfigDetailVo>> getLeaveSchoolConfigDetailPageListByCondition(@RequestBody LeaveSchoolConfigDetailConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<LeaveSchoolConfigDetailVo> pageInfo = new PageInfo<>(leaveSchoolConfigDetailService.getLeaveSchoolConfigDetailListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询放学配置详情表列表
	 * <AUTHOR>
	 * @date 2023-08-23 10:23:14
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<LeaveSchoolConfigDetailVo>> getLeaveSchoolConfigDetailListByCondition(@RequestBody LeaveSchoolConfigDetailConditionBo condition){
		List<LeaveSchoolConfigDetailVo> list = leaveSchoolConfigDetailService.getLeaveSchoolConfigDetailListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增放学配置详情表
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addLeaveSchoolConfigDetail(@Validated @RequestBody LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo){
		return leaveSchoolConfigDetailService.addLeaveSchoolConfigDetail(leaveSchoolConfigDetailBo);
    }

    /**
	 * 修改放学配置详情表
	 * @param leaveSchoolConfigDetailBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateLeaveSchoolConfigDetail(@Validated @RequestBody LeaveSchoolConfigDetailBo leaveSchoolConfigDetailBo) {
		if(null == leaveSchoolConfigDetailBo.getLeaveSchoolConfigDetailId()) {
			return AjaxResult.fail("放学配置详情表id不能为空");
		}
		return leaveSchoolConfigDetailService.updateLeaveSchoolConfigDetail(leaveSchoolConfigDetailBo);
	}

	/**
	 * 查询放学配置详情表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<LeaveSchoolConfigDetailVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("放学配置详情表id不能为空");
		}
		LeaveSchoolConfigDetailConditionBo condition = new LeaveSchoolConfigDetailConditionBo();
		condition.setLeaveSchoolConfigDetailId(id);
		LeaveSchoolConfigDetailVo vo = leaveSchoolConfigDetailService.getLeaveSchoolConfigDetailByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除放学配置详情表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:14
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		LeaveSchoolConfigDetailDto leaveSchoolConfigDetailDto = new LeaveSchoolConfigDetailDto();
		leaveSchoolConfigDetailDto.setLeaveSchoolConfigDetailId(id);
		leaveSchoolConfigDetailDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(leaveSchoolConfigDetailService.updateById(leaveSchoolConfigDetailDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
