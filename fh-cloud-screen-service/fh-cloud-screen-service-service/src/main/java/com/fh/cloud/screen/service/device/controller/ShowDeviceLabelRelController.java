package com.fh.cloud.screen.service.device.controller;

import com.fh.cloud.screen.service.device.api.ShowDeviceLabelRelApi;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceLabelRelService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 设备订阅标签表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
@RestController
@Validated
public class ShowDeviceLabelRelController implements ShowDeviceLabelRelApi {

    @Autowired
    private IShowDeviceLabelRelService showDeviceLabelRelService;

    /**
     * 查询设备订阅标签表分页列表
     * 
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @Override
    public AjaxResult<PageInfo<ShowDeviceLabelRelVo>>
        getShowDeviceLabelRelPageListByCondition(@RequestBody ShowDeviceLabelRelConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ShowDeviceLabelRelVo> pageInfo =
            new PageInfo<>(showDeviceLabelRelService.getShowDeviceLabelRelListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询设备订阅标签表列表
     * 
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @Override
    public AjaxResult<List<ShowDeviceLabelRelVo>>
        getShowDeviceLabelRelListByCondition(@RequestBody ShowDeviceLabelRelConditionBo condition) {
        List<ShowDeviceLabelRelVo> list = showDeviceLabelRelService.getShowDeviceLabelRelListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增设备订阅标签表
     * 
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @Override
    public AjaxResult addShowDeviceLabelRel(@Validated @RequestBody ShowDeviceLabelRelBo showDeviceLabelRelBo) {
        return showDeviceLabelRelService.addShowDeviceLabelRel(showDeviceLabelRelBo);
    }

    /**
     * 修改设备订阅标签表
     * 
     * @param showDeviceLabelRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @Override
    public AjaxResult updateShowDeviceLabelRel(@Validated @RequestBody ShowDeviceLabelRelBo showDeviceLabelRelBo) {
        if (null == showDeviceLabelRelBo.getId()) {
            return AjaxResult.fail("设备订阅标签表id不能为空");
        }
        return showDeviceLabelRelService.updateShowDeviceLabelRel(showDeviceLabelRelBo);
    }

    /**
     * 查询设备订阅标签表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @Override
    public AjaxResult<ShowDeviceLabelRelVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("设备订阅标签表id不能为空");
        }
        ShowDeviceLabelRelVo vo = showDeviceLabelRelService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除设备订阅标签表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ShowDeviceLabelRelDto showDeviceLabelRelDto = new ShowDeviceLabelRelDto();
        showDeviceLabelRelDto.setId(id);
        showDeviceLabelRelDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (showDeviceLabelRelService.updateById(showDeviceLabelRelDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
