package com.fh.cloud.screen.service.card.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.dto.UserCard;
import com.fh.cloud.screen.service.card.entity.vo.UserCardVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 用户卡表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface IUserCardService extends IService<UserCard> {

    List<UserCardVo> getUserCardListByCondition(UserCardListConditionBo condition);

    boolean addUserCard(UserCardBo userCardBo);

    boolean updateUserCard(UserCardBo userCardBo);

    UserCardVo getDetail(Long userCardId);

    AjaxResult updateCard(UserCardBo userCardBo);

    AjaxResult tiedCard(UserCardBo userCardBo);

    AjaxResult unbindCard(Long userCardId);

    AjaxResult batchUnbindCard(String userCardIds);

    UserCard getDetailByUserOid(String userOid);

    List<UserCard> getUserCardList(UserCardBo userCardBo);

    boolean userCardImport(UserCardBo userCardBo);
}
