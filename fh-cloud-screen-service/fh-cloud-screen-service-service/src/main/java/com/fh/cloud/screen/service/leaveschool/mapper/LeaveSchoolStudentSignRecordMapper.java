package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolStudentSignRecordDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolStudentSignRecordConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo;

/**
 * 学生进出记录表Mapper
 *
 * <AUTHOR>
 * @email 
 * @date 2025-04-10 10:32:34
 */
public interface LeaveSchoolStudentSignRecordMapper extends BaseMapper<LeaveSchoolStudentSignRecordDto> {

	List<LeaveSchoolStudentSignRecordVo> getLeaveSchoolStudentSignRecordListByCondition(LeaveSchoolStudentSignRecordConditionBo condition);

	LeaveSchoolStudentSignRecordVo getLeaveSchoolStudentSignRecordByCondition(LeaveSchoolStudentSignRecordConditionBo condition);

}
