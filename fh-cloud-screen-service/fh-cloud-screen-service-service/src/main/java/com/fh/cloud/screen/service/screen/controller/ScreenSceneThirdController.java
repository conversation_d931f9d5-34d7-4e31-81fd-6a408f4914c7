package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenSceneThirdApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneThirdDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneThirdBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo;
import com.fh.cloud.screen.service.screen.service.IScreenSceneThirdService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 第三方对接云屏场景信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
@RestController
public class ScreenSceneThirdController implements ScreenSceneThirdApi {

    @Autowired
    private IScreenSceneThirdService screenSceneThirdService;

    /**
     * 查询第三方对接云屏场景信息表分页列表
     * 
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @Override
    public AjaxResult<PageInfo<ScreenSceneThirdVo>>
        getScreenSceneThirdPageListByCondition(@RequestBody ScreenSceneThirdConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenSceneThirdVo> pageInfo =
            new PageInfo<>(screenSceneThirdService.getScreenSceneThirdListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询第三方对接云屏场景信息表列表
     * 
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @Override
    public AjaxResult<List<ScreenSceneThirdVo>>
        getScreenSceneThirdListByCondition(@RequestBody ScreenSceneThirdConditionBo condition) {
        List<ScreenSceneThirdVo> list = screenSceneThirdService.getScreenSceneThirdListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增第三方对接云屏场景信息表
     * 
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @Override
    public AjaxResult addScreenSceneThird(@Validated @RequestBody ScreenSceneThirdBo screenSceneThirdBo) {
        return screenSceneThirdService.addScreenSceneThird(screenSceneThirdBo);
    }

    /**
     * 修改第三方对接云屏场景信息表
     * 
     * @param screenSceneThirdBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @Override
    public AjaxResult updateScreenSceneThird(@Validated @RequestBody ScreenSceneThirdBo screenSceneThirdBo) {
        if (null == screenSceneThirdBo.getScreenSceneThirdId()) {
            return AjaxResult.fail("第三方对接云屏场景信息表id不能为空");
        }
        return screenSceneThirdService.updateScreenSceneThird(screenSceneThirdBo);
    }

    /**
     * 查询第三方对接云屏场景信息表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @Override
    public AjaxResult<ScreenSceneThirdVo> getDetail(@RequestParam("screenSceneThirdId") Long screenSceneThirdId) {
        if (null == screenSceneThirdId) {
            return AjaxResult.fail("第三方对接云屏场景信息表id不能为空");
        }
        ScreenSceneThirdVo vo = screenSceneThirdService.getDetail(screenSceneThirdId);
        return AjaxResult.success(vo);
    }

    /**
     * 删除第三方对接云屏场景信息表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-04-06 17:50:34
     */
    @Override
    public AjaxResult delete(@RequestParam("screenSceneThirdId") Long screenSceneThirdId) {
        if (null == screenSceneThirdId) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenSceneThirdDto screenSceneThirdDto = new ScreenSceneThirdDto();
        screenSceneThirdDto.setScreenSceneThirdId(screenSceneThirdId);
        screenSceneThirdDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenSceneThirdService.updateById(screenSceneThirdDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 删除第三方对接云屏场景信息表
     *
     * @param screenSceneThirdId the screen scene third id
     * @param appCode the app code
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -04-06 17:50:34
     * @returnType AjaxResult
     */
    @Override
    public AjaxResult deleteByAppCode(@RequestParam("screenSceneThirdId") Long screenSceneThirdId,
        @RequestParam("appCode") String appCode) {
        if (null == screenSceneThirdId || appCode == null) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        if (screenSceneThirdService.deleteByIdAndAppCode(screenSceneThirdId, appCode)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

}
