package com.fh.cloud.screen.service.er.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceSwitch;
import com.fh.cloud.screen.service.er.service.IExamInfoStudentService;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.er.entity.dto.ExamInfoStudentDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.mapper.ExamInfoStudentMapper;
import com.light.core.entity.AjaxResult;

/**
 * 考场_考试计划里面一次考试的学生接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@Service
public class ExamInfoStudentServiceImpl extends ServiceImpl<ExamInfoStudentMapper, ExamInfoStudentDto>
    implements IExamInfoStudentService {

    @Resource
    private ExamInfoStudentMapper examInfoStudentMapper;
    @Autowired
    private IExamInfoStudentService examInfoStudentService;

    @Override
    public List<ExamInfoStudentVo> getExamInfoStudentListByCondition(ExamInfoStudentConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return examInfoStudentMapper.getExamInfoStudentListByCondition(condition);
    }

    @Override
    public AjaxResult addExamInfoStudent(ExamInfoStudentBo examInfoStudentBo) {
        ExamInfoStudentDto examInfoStudent = new ExamInfoStudentDto();
        BeanUtils.copyProperties(examInfoStudentBo, examInfoStudent);
        examInfoStudent.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(examInfoStudent)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateExamInfoStudent(ExamInfoStudentBo examInfoStudentBo) {
        ExamInfoStudentDto examInfoStudent = new ExamInfoStudentDto();
        BeanUtils.copyProperties(examInfoStudentBo, examInfoStudent);
        if (updateById(examInfoStudent)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ExamInfoStudentVo getDetail(Long id) {
        ExamInfoStudentConditionBo condition = new ExamInfoStudentConditionBo();
        condition.setExamInfoStudentId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ExamInfoStudentVo> list = examInfoStudentMapper.getExamInfoStudentListByCondition(condition);
        ExamInfoStudentVo vo = new ExamInfoStudentVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public AjaxResult addExamInfoStudentBatch(List<ExamInfoStudentBo> examInfoStudentBos) {
        if (CollectionUtils.isEmpty(examInfoStudentBos)) {
            return AjaxResult.success();
        }
        List<ExamInfoStudentDto> examInfoStudentDtos = examInfoStudentBos.stream().map(examInfoStudentBo -> {
            ExamInfoStudentDto examInfoStudent = new ExamInfoStudentDto();
            BeanUtils.copyProperties(examInfoStudentBo, examInfoStudent);
            examInfoStudent.setExamInfoStudentId(null);
            examInfoStudent.setIsDelete(StatusEnum.NOTDELETE.getCode());
            return examInfoStudent;
        }).collect(Collectors.toList());

        boolean saveBatch = saveBatch(examInfoStudentDtos);
        if (saveBatch) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 批量添加通过xml
     *
     * @param examInfoStudentBos
     * @return
     */
    @Override
    public AjaxResult addExamInfoStudentBatchByXML(List<ExamInfoStudentBo> examInfoStudentBos) {
        if (CollectionUtils.isEmpty(examInfoStudentBos)) {
            return AjaxResult.success();
        }
        examInfoStudentMapper.addExamInfoStudentBatchByXML(examInfoStudentBos);
        return AjaxResult.success("保存成功");
    }

    @Override
    public AjaxResult updateExamInfoStudentBatch(List<ExamInfoStudentBo> examInfoStudentBos) {
        // 删除
        if (CollectionUtils.isEmpty(examInfoStudentBos)) {
            return AjaxResult.success();
        }
        Long examInfoId = examInfoStudentBos.get(0).getExamInfoId();
        LambdaUpdateWrapper<ExamInfoStudentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExamInfoStudentDto::getExamInfoId, examInfoId);
        updateWrapper.eq(ExamInfoStudentDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ExamInfoStudentDto examInfoStudentDto = new ExamInfoStudentDto();
        examInfoStudentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        examInfoStudentMapper.update(examInfoStudentDto, updateWrapper);

        // 新增
        AjaxResult ajaxResult = examInfoStudentService.addExamInfoStudentBatch(examInfoStudentBos);
        return ajaxResult;
    }

    @Override
    public AjaxResult deleteExamInfoStudentBatch(Long examInfoId) {
        if (examInfoId == null) {
            return AjaxResult.success();
        }

        LambdaUpdateWrapper<ExamInfoStudentDto> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ExamInfoStudentDto::getExamInfoId, examInfoId);
        updateWrapper.eq(ExamInfoStudentDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        ExamInfoStudentDto examInfoStudentDto = new ExamInfoStudentDto();
        examInfoStudentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        examInfoStudentMapper.update(examInfoStudentDto, updateWrapper);
        return AjaxResult.success();
    }
}