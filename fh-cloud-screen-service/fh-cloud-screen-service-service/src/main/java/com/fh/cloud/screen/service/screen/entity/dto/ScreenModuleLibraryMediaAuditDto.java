package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 云屏模块库媒体资源审核表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_module_library_media_audit")
public class ScreenModuleLibraryMediaAuditDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_module_library_media_audit_id", type = IdType.AUTO)
	private Long screenModuleLibraryMediaAuditId;

	/**
	 * FK模块库表
	 */
	@TableField("screen_module_library_audit_id")
	private Long screenModuleLibraryAuditId;

	/**
	 * 云屏图片或者视频媒体地址
	 */
	@TableField("screen_module_library_media_url")
	private String screenModuleLibraryMediaUrl;

	/**
	 * 云屏图片或者视频媒体地址-压缩后
	 */
	@TableField("screen_module_library_media_url_compress")
	private String screenModuleLibraryMediaUrlCompress;

	/**
	 * 云屏图片或者视频媒体地址-封面
	 */
	@TableField("screen_module_library_media_url_cover")
	private String screenModuleLibraryMediaUrlCover;

	/**
	 * 云屏图片或者视频媒体名称（不包含后缀）
	 */
	@TableField("screen_module_library_media_name")
	private String screenModuleLibraryMediaName;

	/**
	 * 云屏图片或者视频原始媒体名称（包含后缀）
	 */
	@TableField("screen_module_library_media_name_ori")
	private String screenModuleLibraryMediaNameOri;

	/**
	 * 云屏图片或者视频媒体fileoid
	 */
	@TableField("screen_content_media_id")
	private String screenContentMediaId;

	/**
	 * 云屏图片或者视频媒体fileoid-压缩后
	 */
	@TableField("screen_content_media_id_compress")
	private String screenContentMediaIdCompress;

	/**
	 * 更新时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 创建时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 图片版式：1横屏，2竖屏。
	 */
	@TableField("device_pattern")
	private Integer devicePattern;

	/**
	 * 文件md5
	 */
	@TableField("screen_content_media_md5")
	private String screenContentMediaMd5;

	/**
	 * 海报图片排序
	 */
	@TableField("media_sort")
	private Long mediaSort;

}
