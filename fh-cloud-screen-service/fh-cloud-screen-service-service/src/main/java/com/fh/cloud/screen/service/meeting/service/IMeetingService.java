package com.fh.cloud.screen.service.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingGroupBySpaceVo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 会议表接口
 *
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
public interface IMeetingService extends IService<MeetingDto> {

    List<MeetingVo> getMeetingListByCondition(MeetingConditionBo condition);

    Map<String, Object> getMyMeetingListByCondition(MeetingConditionBo condition);

    List<MeetingGroupBySpaceVo> getMeetingListByDate(MeetingConditionBo condition);

    /**
     * web 新增或修改会议
     *
     * @param meetingBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/11 10:07
     */
    AjaxResult addMeeting(MeetingBo meetingBo);

    /**
     * web 新增-批量
     *
     * @param meetingBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/11 10:07
     */
    AjaxResult addMeetingBatch(MeetingBo meetingBo);

    AjaxResult updateMeeting(MeetingBo meetingBo);

    /**
     * 获取会议详情，包含与会人员
     *
     * @param meetingId
     * @return com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo
     * <AUTHOR>
     * @date 2023/4/10 16:46
     */
    MeetingVo getDetail(Long meetingId);

    AjaxResult delete(MeetingBo meetingBo);

    /**
     * 获取当前及下一个会议（当天)
     *
     * @param conditionBo screenNowDate云屏时间
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/11 10:08
     */
    AjaxResult getNowAndNextMeeting(MeetingConditionBo conditionBo);
}
