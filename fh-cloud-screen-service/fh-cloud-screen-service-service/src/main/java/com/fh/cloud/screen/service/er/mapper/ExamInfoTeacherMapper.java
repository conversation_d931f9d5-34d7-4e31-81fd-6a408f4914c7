package com.fh.cloud.screen.service.er.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherBo;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoTeacherDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoTeacherConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo;
import org.apache.ibatis.annotations.Param;

/**
 * 考场_考试计划里面一次考试的老师Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamInfoTeacherMapper extends BaseMapper<ExamInfoTeacherDto> {

    List<ExamInfoTeacherVo> getExamInfoTeacherListByCondition(ExamInfoTeacherConditionBo condition);

    void addExamInfoTeacherBatchByXML(@Param("examInfoTeacherBos") List<ExamInfoTeacherBo> examInfoTeacherBos);

}
