package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecial;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo;

import java.util.Date;
import java.util.List;

/**
 * 云屏紧急发布内容表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface IScreenContentSpecialService extends IService<ScreenContentSpecial> {

    List<ScreenContentSpecialVo> getScreenContentSpecialListByCondition(ScreenContentSpecialListConditionBo condition);

    Long addScreenContentSpecial(ScreenContentSpecialBo screenContentSpecialBo);

    boolean updateScreenContentSpecial(ScreenContentSpecialBo screenContentSpecialBo);

    ScreenContentSpecialVo getDetail(Long screenContentSpecialId);

    /**
     * 添加特殊场景内容并且保存发布对象组、发布状态等-有多表事务
     *
     * @param screenContentSpecialBo the screen content special bo
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -06-06 10:41:52
     */
    Long addScreenContentSpecialPublish(ScreenContentSpecialBo screenContentSpecialBo);

    /**
     * 新增或者更新特殊场景内容并且保存发布对象组、发布状态等-有多表事务
     *
     * @param screenContentSpecialBo the screen content special bo
     * @return boolean boolean
     * <AUTHOR>
     * @date 2022 -06-06 10:41:52
     */
    Long saveOrUpdateScreenContentSpecialPublish(ScreenContentSpecialBo screenContentSpecialBo);

    /**
     * 修改特殊场景内容并且保存发布对象组、发布状态等-有多表事务
     *
     * @param screenContentSpecialBo the screen content special bo
     * @return boolean
     * <AUTHOR>
     * @date 2022 -06-06 10:41:55
     */
    boolean updateScreenContentSpecialPublish(ScreenContentSpecialBo screenContentSpecialBo);

    /**
     * 根据地点组获取紧急发布内容
     *
     * @param organizationId 组织结构id，必须
     * @param campusId 校区id
     * @param spaceGroupId 地点组id，必须
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-17 10:02:15
     */
    List<ScreenContentSpecialVo> listScreenContentSpecialVosBySpaceGroupId(Long organizationId, Long campusId,
        Long spaceGroupId, Date nowDate);

    /**
     * 取消某个组织/校区的所有紧急发布
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * <AUTHOR>
     * @date 2022 -06-22 14:45:40
     */
    void cancelPublish(Long organizationId, Long campusId);

    /**
     * 根据组织和校区查询当前正在发布的特殊内容
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @return now publish
     */
    ScreenContentSpecialVo getNowPublish(Long organizationId, Long campusId);

    /**
     * 根据内容id更新第一条详情数据ImgUrl
     *
     * @param screenContentSpecialId the screen content special id
     * @param screenContentMediaUrl the screen content media url
     * @param screenContentMediaId the screen content media id
     * <AUTHOR>
     * @date 2022 -12-29 16:15:56
     */
    void updateImgUrlByScreenContentSpecialId(Long screenContentSpecialId, String screenContentMediaUrl,
        String screenContentMediaId);
}
