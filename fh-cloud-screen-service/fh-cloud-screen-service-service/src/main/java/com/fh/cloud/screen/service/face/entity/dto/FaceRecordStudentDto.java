package com.fh.cloud.screen.service.face.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 学生人脸库
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("face_record_student")
public class FaceRecordStudentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "face_record_student_id", type = IdType.AUTO)
	private Long faceRecordStudentId;

	/**
	 * 学生的user_oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 人脸来源:1导入，2单个添加
	 */
	@TableField("source_type")
	private Integer sourceType;

	/**
	 * 人脸导入或者添加的提示信息，多个使用英文逗号分割
	 */
	@TableField("tip_message")
	private String tipMessage;

	/**
	 * 状态：1未上传，2识别中，3成功，4失败
	 */
	@TableField("face_status")
	private Integer faceStatus;

	/**
	 * 人脸图片
	 */
	@TableField("face_media_url")
	private String faceMediaUrl;

	/**
	 * 人脸图片-压缩后
	 */
	@TableField("face_media_url_compress")
	private String faceMediaUrlCompress;

	/**
	 * 人脸图片名称（不包含后缀）
	 */
	@TableField("face_media_name")
	private String faceMediaName;

	/**
	 * 人脸图片名称（包含后缀）
	 */
	@TableField("face_media_name_ori")
	private String faceMediaNameOri;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 学校id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 人脸图片fileOid
	 */
	@TableField("face_media_id")
	private String faceMediaId;

	/**
	 * 人脸图片fileOid-压缩
	 */
	@TableField("face_media_id_compress")
	private String faceMediaIdCompress;

	/**
	 * 真实姓名
	 */
	@TableField("real_name")
	private String realName;
}
