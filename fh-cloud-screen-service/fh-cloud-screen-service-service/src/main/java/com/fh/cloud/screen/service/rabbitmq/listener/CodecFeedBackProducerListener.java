package com.fh.cloud.screen.service.rabbitmq.listener;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailBo;
import com.fh.cloud.screen.service.screen.service.IScreenContentDetailService;
import com.fh.cp.codec.enums.CodecBizType;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.fh.cp.codec.entity.dto.CodecProcessDto;
import com.fh.cp.codec.rabbitmq.constant.CodecRabbitConstant;
import com.rabbitmq.client.Channel;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 转码回调消息listener
 */
@Slf4j
@Component
public class CodecFeedBackProducerListener {
    /**
     * 业务消息的uuid,重投递的次数。如果是集群要改成redis缓存
     */
    private Map<String, Integer> uuidMap = new ConcurrentHashMap<>();
    @Resource
    private IScreenContentDetailService screenContentDetailService;

    /**
     * 转码添加消息接收
     *
     * @param codecProcessDto the codec process dto
     * @param message the message
     * @param channel the channel
     * <AUTHOR>
     * @date 2023 -06-07 16:47:46
     */
    @RabbitHandler
    @RabbitListener(bindings = @QueueBinding(value = @Queue(CodecRabbitConstant.CODEC_FEEDBACK_ADD_QUEUE_SCREEN), // queue定义
        key = CodecRabbitConstant.CODEC_FEEDBACK_ADD_QUEUE_SCREEN, // 发送的key即routing
        exchange = @Exchange(name = CodecRabbitConstant.CODEC_FEEDBACK_EXCHANGE, declare = "false")),
        ackMode = "MANUAL")
    // exchange binding
    public void codecProducerAddListener(CodecProcessDto codecProcessDto, Message message, Channel channel) {
        try {
            log.info("MQ收到消息：CodecFeedBackProducerListener:" + codecProcessDto);
            // 业务处理
            if (codecProcessDto.getBizType() != null
                && codecProcessDto.getBizType().equals(CodecBizType.SCREEN_CONTENT_DETAIL.getValue())) {
                ScreenContentDetailBo screenContentDetailBo = new ScreenContentDetailBo();
                screenContentDetailBo.setScreenContentDetailId(codecProcessDto.getId());
                screenContentDetailBo.setScreenContentMediaUrlCover(codecProcessDto.getFilePathCover());
                screenContentDetailService.updateScreenContentDetail(screenContentDetailBo);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("MQ消息处理成功：CodecFeedBackProducerListener:" + codecProcessDto);
        } catch (Exception e) {
            log.error("CodecFeedBackProducerListener error:", e);
            try {
                String uuid = codecProcessDto.getUuid();
                if (!uuidMap.containsKey(uuid)) {
                    uuidMap.put(uuid, ConstantsInteger.NUM_0);
                }
                Integer retryCount = uuidMap.get(uuid);
                // 投递次数过多则丢弃消息，否则重新投递消息。这里仅仅做1次重试。
                if (retryCount != null && retryCount >= ConstantsInteger.NUM_1) {
                    channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
                    uuidMap.remove(uuid);
                    log.info("MQ消息处理失败重新投递达到最大次数，消息已丢弃：CodecFeedBackProducerListener:" + codecProcessDto);
                } else {
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
                    retryCount++;
                    uuidMap.put(uuid, retryCount);
                    log.info("MQ消息处理失败已重新投递到队列：CodecFeedBackProducerListener:" + codecProcessDto);
                }
            } catch (Exception ei) {
                log.error("CodecFeedBackProducerListener basicNack error:", e);
            }
        }
    }

}