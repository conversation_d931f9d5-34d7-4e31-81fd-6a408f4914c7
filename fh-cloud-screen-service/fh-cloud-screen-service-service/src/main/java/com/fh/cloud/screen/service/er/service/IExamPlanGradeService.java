package com.fh.cloud.screen.service.er.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.er.entity.dto.ExamPlanGradeDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanGradeBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 考场_考试计划涉及的年级接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface IExamPlanGradeService extends IService<ExamPlanGradeDto> {

    List<ExamPlanGradeVo> getExamPlanGradeListByCondition(ExamPlanGradeConditionBo condition);

    AjaxResult addExamPlanGrade(ExamPlanGradeBo examPlanGradeBo);

    AjaxResult updateExamPlanGrade(ExamPlanGradeBo examPlanGradeBo);

    ExamPlanGradeVo getDetail(Long id);

    /**
     * 批量先删除再保存考试计划年级
     * 
     * @param examPlanId
     * @param grades
     */
    void batchDelAddExamPlanGrade(Long examPlanId, List<String> grades);
}
