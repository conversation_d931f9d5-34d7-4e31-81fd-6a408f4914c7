package com.fh.cloud.screen.service.device.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceFullCustomDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceFullCustomBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 云屏全屏非全屏设置自定义接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-06-12 16:12:06
 */
public interface IShowDeviceFullCustomService extends IService<ShowDeviceFullCustomDto> {

    List<ShowDeviceFullCustomVo> getShowDeviceFullCustomListByCondition(ShowDeviceFullCustomConditionBo condition);

    AjaxResult addShowDeviceFullCustom(ShowDeviceFullCustomBo showDeviceFullCustomBo);

    AjaxResult updateShowDeviceFullCustom(ShowDeviceFullCustomBo showDeviceFullCustomBo);

    ShowDeviceFullCustomVo getShowDeviceFullCustomByCondition(ShowDeviceFullCustomConditionBo condition);

    /**
     * 查询一个学校所有的自定义的全屏非全屏的设置
     *
     * @param organizationId the organization id
     * @return show device full custom list by organization id with cache
     * <AUTHOR>
     * @date 2023 -06-12 16:58:12
     */
    List<ShowDeviceFullCustomVo> getShowDeviceFullCustomListByOrganizationIdWithCache(Long organizationId);

    /**
     * 获取所有已有数据的组织
     *
     * @return exist organization ids
     */
    List<Long> getExistOrganizationIds();

}
