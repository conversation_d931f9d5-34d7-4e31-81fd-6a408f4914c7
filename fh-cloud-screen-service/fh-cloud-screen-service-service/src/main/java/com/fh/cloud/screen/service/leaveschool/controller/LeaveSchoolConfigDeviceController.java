package com.fh.cloud.screen.service.leaveschool.controller;

import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigDeviceApi;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDeviceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDeviceService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 放学配置设备表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:23
 */
@RestController
@Validated
public class LeaveSchoolConfigDeviceController implements LeaveSchoolConfigDeviceApi{
	
    @Autowired
    private ILeaveSchoolConfigDeviceService leaveSchoolConfigDeviceService;

    /**
     * 查询放学配置设备表分页列表
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<LeaveSchoolConfigDeviceVo>> getLeaveSchoolConfigDevicePageListByCondition(@RequestBody LeaveSchoolConfigDeviceConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<LeaveSchoolConfigDeviceVo> pageInfo = new PageInfo<>(leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询放学配置设备表列表
	 * <AUTHOR>
	 * @date 2023-08-23 10:23:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<LeaveSchoolConfigDeviceVo>> getLeaveSchoolConfigDeviceListByCondition(@RequestBody LeaveSchoolConfigDeviceConditionBo condition){
		List<LeaveSchoolConfigDeviceVo> list = leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增放学配置设备表
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addLeaveSchoolConfigDevice(@Validated @RequestBody LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo){
		return leaveSchoolConfigDeviceService.addLeaveSchoolConfigDevice(leaveSchoolConfigDeviceBo);
    }

    /**
	 * 修改放学配置设备表
	 * @param leaveSchoolConfigDeviceBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateLeaveSchoolConfigDevice(@Validated @RequestBody LeaveSchoolConfigDeviceBo leaveSchoolConfigDeviceBo) {
		if(null == leaveSchoolConfigDeviceBo.getLeaveSchoolConfigDeviceId()) {
			return AjaxResult.fail("放学配置设备表id不能为空");
		}
		return leaveSchoolConfigDeviceService.updateLeaveSchoolConfigDevice(leaveSchoolConfigDeviceBo);
	}

	/**
	 * 查询放学配置设备表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<LeaveSchoolConfigDeviceVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("放学配置设备表id不能为空");
		}
		LeaveSchoolConfigDeviceConditionBo condition = new LeaveSchoolConfigDeviceConditionBo();
		condition.setLeaveSchoolConfigDeviceId(id);
		LeaveSchoolConfigDeviceVo vo = leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除放学配置设备表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-08-23 10:23:23
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		LeaveSchoolConfigDeviceDto leaveSchoolConfigDeviceDto = new LeaveSchoolConfigDeviceDto();
		leaveSchoolConfigDeviceDto.setLeaveSchoolConfigDeviceId(id);
		leaveSchoolConfigDeviceDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(leaveSchoolConfigDeviceService.updateById(leaveSchoolConfigDeviceDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
