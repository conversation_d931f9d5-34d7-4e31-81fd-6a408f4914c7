package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryUserRelApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryUserRelDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryUserRelVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryUserRelService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 模块用户关系表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
@RestController
@Validated
public class ScreenModuleLibraryUserRelController implements ScreenModuleLibraryUserRelApi {

    @Autowired
    private IScreenModuleLibraryUserRelService screenModuleLibraryUserRelService;

    /**
     * 查询模块用户关系表分页列表
     * 
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @Override
    public AjaxResult<PageInfo<ScreenModuleLibraryUserRelVo>>
        getScreenModuleLibraryUserRelPageListByCondition(@RequestBody ScreenModuleLibraryUserRelConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenModuleLibraryUserRelVo> pageInfo =
            new PageInfo<>(screenModuleLibraryUserRelService.getScreenModuleLibraryUserRelListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询模块用户关系表列表
     * 
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @Override
    public AjaxResult<List<ScreenModuleLibraryUserRelVo>>
        getScreenModuleLibraryUserRelListByCondition(@RequestBody ScreenModuleLibraryUserRelConditionBo condition) {
        List<ScreenModuleLibraryUserRelVo> list =
            screenModuleLibraryUserRelService.getScreenModuleLibraryUserRelListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增模块用户关系表
     * 
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @Override
    public AjaxResult addScreenModuleLibraryUserRel(
        @Validated @RequestBody ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo) {
        return screenModuleLibraryUserRelService.addScreenModuleLibraryUserRel(screenModuleLibraryUserRelBo);
    }

    /**
     * 修改模块用户关系表
     * 
     * @param screenModuleLibraryUserRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @Override
    public AjaxResult updateScreenModuleLibraryUserRel(
        @Validated @RequestBody ScreenModuleLibraryUserRelBo screenModuleLibraryUserRelBo) {
        if (null == screenModuleLibraryUserRelBo.getId()) {
            return AjaxResult.fail("模块用户关系表id不能为空");
        }
        return screenModuleLibraryUserRelService.updateScreenModuleLibraryUserRel(screenModuleLibraryUserRelBo);
    }

    /**
     * 查询模块用户关系表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @Override
    public AjaxResult<ScreenModuleLibraryUserRelVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("模块用户关系表id不能为空");
        }
        ScreenModuleLibraryUserRelVo vo = screenModuleLibraryUserRelService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除模块用户关系表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-29 15:06:48
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenModuleLibraryUserRelDto screenModuleLibraryUserRelDto = new ScreenModuleLibraryUserRelDto();
        screenModuleLibraryUserRelDto.setId(id);
        screenModuleLibraryUserRelDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenModuleLibraryUserRelService.updateById(screenModuleLibraryUserRelDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
