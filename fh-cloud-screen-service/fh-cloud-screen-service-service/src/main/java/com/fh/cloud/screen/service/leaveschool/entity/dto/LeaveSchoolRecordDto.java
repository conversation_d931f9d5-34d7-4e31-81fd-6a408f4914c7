package com.fh.cloud.screen.service.leaveschool.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 放学记录表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("leave_school_record")
public class LeaveSchoolRecordDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 放学记录表id
	 */
	@TableId(value = "leave_school_record_id", type = IdType.AUTO)
	private Long leaveSchoolRecordId;

	/**
	 * 组织id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * 校区id
	 */
	@TableField("campus_id")
	private Long campusId;

	/**
	 * 区域id或者classesId
	 */
	@TableField("space_info_id")
	private Long spaceInfoId;

	/**
	 * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
	 */
	@TableField("space_group_use_type")
	private Integer spaceGroupUseType;

	/**
	 * 放学状态 1-未放学 2-放学中 3-已放学
	 */
	@TableField("leave_school_type")
	private Integer leaveSchoolType;

	/**
	 * 放学日期
	 */
	@TableField("leave_school_day")
	private Date leaveSchoolDay;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
