package com.fh.cloud.screen.service.rest.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 作息时间天设置表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("work_rest_day")
public class WorkRestDay implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "work_rest_day_id", type = IdType.AUTO)
    private Long workRestDayId;

    /**
     * FK作息时间年级表主键
     */
    @TableField("work_rest_grade_id")
    private Long workRestGradeId;

    /**
     * FK作息时间主表主键id
     */
    @TableField("work_rest_id")
    private Long workRestId;

    /**
     * 课程节次：1，2，3...
     */
    @TableField("course_classes_position")
    private Integer courseClassesPosition;

    /**
     * 课程节次名称，例如第1节
     */
    @TableField("course_classes_name")
    private String courseClassesName;

    /**
     * 课程节次类型：1普通课，2活动课
     */
    @TableField("course_classes_type")
    private Integer courseClassesType;

    /**
     * 课程节次顺序（含活动课）：1，2，3...
     */
    @TableField("course_classes_index")
    private Integer courseClassesIndex;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    @TableField("week")
    private Integer week;

    /**
     * 上午1、下午2、晚上3
     */
    @TableField("day_type")
    private Integer dayType;

    /**
     * 起始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 截止时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
