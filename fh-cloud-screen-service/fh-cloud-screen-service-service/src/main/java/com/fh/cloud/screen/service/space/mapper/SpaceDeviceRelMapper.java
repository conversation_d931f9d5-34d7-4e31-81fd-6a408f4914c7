package com.fh.cloud.screen.service.space.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;

import java.util.List;

/**
 * 地点和设备关系表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface SpaceDeviceRelMapper extends BaseMapper<SpaceDeviceRel> {

    List<SpaceDeviceRelVo> getSpaceDeviceRelListByCondition(SpaceDeviceRelListConditionBo condition);

}
