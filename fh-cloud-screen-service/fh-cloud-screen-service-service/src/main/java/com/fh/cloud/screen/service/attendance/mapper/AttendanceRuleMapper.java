package com.fh.cloud.screen.service.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRule;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo;

import java.util.List;

/**
 * 考勤规则表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface AttendanceRuleMapper extends BaseMapper<AttendanceRule> {

    List<AttendanceRuleVo> getAttendanceRuleListByCondition(AttendanceRuleListConditionBo condition);

}
