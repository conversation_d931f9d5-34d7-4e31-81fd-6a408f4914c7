package com.fh.cloud.screen.service.meeting.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会议人员表
 * 
 * <AUTHOR>
 * @date 2022-08-16 17:51:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("meeting_user")
public class MeetingUserDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "meeting_user_id", type = IdType.AUTO)
    private Long meetingUserId;

    /**
     * FK会议ID
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 参与人user_oid
     */
    @TableField("user_oid")
    private String userOid;

    /**
     * 签到时间
     */
    @TableField("sign_time")
    private Date signTime;

    /**
     * 与会状态，1：未签到，2：已签到[提前]，3：已签到[正常]，4已签到[迟到]
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * grade 的code值
     */
    @TableField("grade")
    private String grade;

    /**
     * 班级ID
     */
    @TableField("classes_id")
    private Long classesId;

    /**
     * 班级名称
     */
    @TableField("classes_name")
    private String classesName;
}
