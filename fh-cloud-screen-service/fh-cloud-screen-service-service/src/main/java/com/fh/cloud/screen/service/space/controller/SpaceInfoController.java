package com.fh.cloud.screen.service.space.controller;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.baseinfo.entity.vo.OrganizationVoExt;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceListConditionBo;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceListVo;
import com.fh.cloud.screen.service.enums.DeviceStatusType;
import com.fh.cloud.screen.service.enums.SuperviseStateType;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.message.service.MessageService;
import com.fh.cloud.screen.service.space.entity.dto.SpaceGroup;
import com.fh.cloud.screen.service.space.entity.dto.SpaceInfo;
import com.fh.cloud.screen.service.space.entity.vo.ClassesInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoDeviceCountVo;
import com.fh.cloud.screen.service.space.mapper.SpaceGroupMapper;
import com.fh.cloud.screen.service.space.service.ISpaceGroupService;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceConditionBo;
import com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubDeviceVo;
import com.fh.cloud.screen.service.wx.service.IWxMsgSubDeviceService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.user.organization.entity.vo.OrganizationVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.ptg.AttrPtg;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.autoconfigure.health.HealthProperties;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.adapter.SpaceInfoEventAdapter;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.fh.cloud.screen.service.space.api.SpaceInfoApi;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.ClassesInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.IClassesInfoService;
import com.fh.cloud.screen.service.space.service.ISpaceInfoService;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.campus.entity.vo.CampusVo;
import com.light.user.clazz.entity.vo.ClazzVo;

import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

/**
 * 区域信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Validated
public class SpaceInfoController implements SpaceInfoApi {

    @Autowired
    private ISpaceInfoService spaceInfoService;
    @Autowired
    private IClassesInfoService classesInfoService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private SpaceInfoEventAdapter spaceInfoEventAdapter;
    @Autowired
    private IShowDeviceService showDeviceService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private ISpaceGroupService spaceGroupService;
    @Resource
    private IWxMsgSubDeviceService wxMsgSubDeviceService;

    /**
     * 查询区域信息表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询区域信息表列表", httpMethod = "POST")
    public AjaxResult getSpaceInfoListByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", null);
        map.put("spaceInfoList", null);

        if (SpaceGroupUseType.XZ.getValue() == condition.getSpaceGroupUseType()) {
            Map<String, Object> classesInfoMap = new HashMap<>();
            if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
                ClassesInfoListConditionBo classesInfoListConditionBo = new ClassesInfoListConditionBo();
                BeanUtils.copyProperties(condition, classesInfoListConditionBo);
                classesInfoMap = classesInfoService.getClassesInfoListByCondition(classesInfoListConditionBo);
            } else {
                PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
                ClassesInfoListConditionBo classesInfoListConditionBo = new ClassesInfoListConditionBo();
                BeanUtils.copyProperties(condition, classesInfoListConditionBo);
                classesInfoMap = classesInfoService.getClassesInfoListByCondition(classesInfoListConditionBo);
            }
            map.put("count", classesInfoMap.get("total"));
            map.put("spaceInfoList", classesInfoMap.get("list"));
        } else if (SpaceGroupUseType.NOT_XZ.getValue() == condition.getSpaceGroupUseType()) {
            PageInfo<SpaceInfoVo> pageInfo = new PageInfo<>();
            List<SpaceInfoVo> list = new ArrayList<>();
            if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
                PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            }
            pageInfo = new PageInfo<>(spaceInfoService.getSpaceInfoListByCondition(condition));
            list = JSONObject.parseArray(JSONObject.toJSONString(pageInfo.getList()), SpaceInfoVo.class);
            if (CollectionUtils.isNotEmpty(list)) {
                AjaxResult campusResult =
                    baseDataService.getCampusListByOrganizationId(list.get(0).getOrganizationId());
                if (!campusResult.isFail()) {
                    Map<String, Object> resultMap = (Map<String, Object>)campusResult.getData();
                    List<CampusVo> campusVos =
                        JSONObject.parseArray(JSONObject.toJSONString(resultMap.get("list")), CampusVo.class);
                    for (CampusVo campusVo : campusVos) {
                        for (SpaceInfoVo spaceInfoVo : list) {
                            if (campusVo.getId().equals(spaceInfoVo.getCampusId())) {
                                spaceInfoVo.setCampusName(campusVo.getName());
                            }
                        }
                    }
                }
            }
            map.put("count", pageInfo.getTotal());
            map.put("spaceInfoList", list);
        }
        if (!condition.isQuerySpaceWithDevice()) {
            return AjaxResult.success(map);
        }

        // 查询设备
        List<SpaceInfoVo> list = (List<SpaceInfoVo>)map.get("spaceInfoList");
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(map);
        }
        List<ShowDeviceVo> showDeviceVos =
            showDeviceService.listShowDeviceBindByOrganizationId(condition.getOrganizationId());
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(map);
        }
        // 设备关注状态
        WxMsgSubDeviceConditionBo wxMsgSubDeviceConditionBo = new WxMsgSubDeviceConditionBo();
        wxMsgSubDeviceConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        wxMsgSubDeviceConditionBo.setUserOid(baseDataService.getCurrentUserOid());
        List<WxMsgSubDeviceVo> wxMsgSubDeviceVos =
                wxMsgSubDeviceService.getWxMsgSubDeviceListByCondition(wxMsgSubDeviceConditionBo);
        List<String> subDeviceNumbers = CollectionUtils.isEmpty(wxMsgSubDeviceVos) ? Lists.newArrayList()
                : wxMsgSubDeviceVos.stream().map(WxMsgSubDeviceVo::getDeviceNumber).collect(Collectors.toList());
        // 设备异常状态检测
        List<String> showDeviceNumbers = messageService.listOnLineDeviceNumber(condition.getOrganizationId());
        showDeviceVos.stream().forEach(showDeviceVo -> {
            if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            }
            // 设备关注状态
            showDeviceVo.setWxMsgSub(subDeviceNumbers.contains(showDeviceVo.getDeviceNumber()));
        });
        Map<String, List<ShowDeviceVo>> spaceDeviceMap = showDeviceVos.stream().collect(Collectors.groupingBy(
            showDeviceVo -> showDeviceVo.getSpaceInfoId() + ConstString.jh + showDeviceVo.getSpaceGroupUseType()));
        for (SpaceInfoVo spaceInfoVo : list) {
            if (spaceInfoVo.getSpaceInfoId() == null) {
                continue;
            }
            String key = spaceInfoVo.getSpaceInfoId() + ConstString.jh + spaceInfoVo.getSpaceGroupUseType();
            List<ShowDeviceVo> showDeviceVoList = spaceDeviceMap.get(key);
            if (CollectionUtils.isNotEmpty(showDeviceVoList)) {
                // 排序
                showDeviceVoList = showDeviceVoList.stream().sorted(Comparator.comparing(x -> {
                    if (StringUtils.isBlank(x.getDeviceName())) {
                        return x.getDeviceNumber().toUpperCase();
                    }
                    // 防止重名设备，最后增加设备号
                    return PinyinUtil.getPinyin(x.getDeviceName()).replaceAll(" ", "").toUpperCase()
                            + x.getDeviceNumber().toUpperCase();
                })).collect(Collectors.toList());
            }
            spaceInfoVo.setShowDeviceVoList(showDeviceVoList);
        }
        return AjaxResult.success(map);
    }

    @ApiOperation(value = "查询区域信息表列表", httpMethod = "POST")
    public AjaxResult queryList(@RequestBody SpaceInfoListConditionBo condition) {
        if (SpaceGroupUseType.XZ.getValue() == condition.getSpaceGroupUseType()) {
            ClassesInfoListConditionBo classesInfoListConditionBo = new ClassesInfoListConditionBo();
            BeanUtils.copyProperties(condition, classesInfoListConditionBo);
            return AjaxResult
                .success(classesInfoService.getClassesInfoListByCondition(classesInfoListConditionBo).get("list"));
        } else if (SpaceGroupUseType.NOT_XZ.getValue() == condition.getSpaceGroupUseType()) {
            return AjaxResult.success(spaceInfoService.getSpaceInfoListByCondition(condition));
        }

        return AjaxResult.success();
    }

    /**
     * 新增区域信息表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "新增区域信息表", httpMethod = "POST")
    public AjaxResult addSpaceInfo(@RequestBody SpaceInfoBo spaceInfoBo) {
        return spaceInfoService.addSpaceInfo(spaceInfoBo);
    }

    /**
     * 修改区域信息表
     *
     * @param spaceInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "修改区域信息表", httpMethod = "POST")
    public AjaxResult updateSpaceInfo(@RequestBody SpaceInfoBo spaceInfoBo) {
        if (null == spaceInfoBo.getSpaceGroupUseType()) {
            return AjaxResult.fail("区域分组使用类型不能为空");
        }
        boolean update = false;
        if (null == spaceInfoBo.getUserCapacity()) {
            spaceInfoBo.setUserCapacity(0);
        }
        if (SpaceGroupUseType.XZ.getValue() == spaceInfoBo.getSpaceGroupUseType()) {
            ClassesInfoBo classesInfoBo = new ClassesInfoBo();
            BeanUtils.copyProperties(spaceInfoBo, classesInfoBo);
            // saveOrUpdate
            update = classesInfoService.saveOrUpdateClassesInfo(classesInfoBo);
        } else if (SpaceGroupUseType.NOT_XZ.getValue() == spaceInfoBo.getSpaceGroupUseType()) {
            update = spaceInfoService.updateSpaceInfo(spaceInfoBo);
        }
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult<SpaceInfoVo> getById(@PathVariable("spaceInfoId") Long spaceInfoId) {
        return AjaxResult.success(this.spaceInfoService.getById(spaceInfoId));
    }

    /**
     * 查询区域信息表详情
     *
     * @param spaceInfoId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询区域信息表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("spaceInfoId") Long spaceInfoId) {
        SpaceInfoVo spaceInfoVo = spaceInfoService.getDetail(spaceInfoId);
        return AjaxResult.success(spaceInfoVo);
    }

    /**
     * 删除区域信息表
     *
     * @param spaceInfoId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "删除区域信息表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("spaceInfoId") Long spaceInfoId) {
        SpaceInfoBo spaceInfoBo = new SpaceInfoBo();
        spaceInfoBo.setSpaceInfoId(spaceInfoId);
        spaceInfoBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = spaceInfoService.updateSpaceInfo(spaceInfoBo);
        if (delete) {
            this.spaceInfoEventAdapter.spaceInfoDelEvent(spaceInfoId);
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询校区表列表
     *
     * @param organizationId
     */
    @ApiOperation(value = "查询校区表列表", httpMethod = "GET")
    public AjaxResult getCampusListByCondition(@RequestParam("organizationId") Long organizationId) {
        return baseDataService.getCampusListByOrganizationId(organizationId);
    }

    /**
     * 获取地点信息（可以获取行政班级或者非行政班级的地点信息）
     * 
     * @param screenBusinessBo the screen business bo
     * @return
     */
    @ApiOperation(value = "获取地点信息（可以获取行政班级或者非行政班级的地点信息）", httpMethod = "POST")
    public AjaxResult getSpaceInfo(ScreenBusinessBo screenBusinessBo) {
        SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
        if (screenBusinessBo.getSpaceGroupUseType().equals(SpaceGroupUseType.XZ.getValue())) {
            ClazzVo clazzVo = baseDataService.getByClazzId(screenBusinessBo.getSpaceInfoId());
            String grade = SchoolYearUtil.gradeMap.get(clazzVo.getGrade());
            String spaceName = grade.concat(clazzVo.getClassesName()).concat("班");
            spaceInfoVo.setSpaceInfoName(spaceName);
            spaceInfoVo.setClassesName(clazzVo.getClassesName());
            spaceInfoVo.setRemark(spaceName.concat("教室"));
            spaceInfoVo.setClassesId(clazzVo.getId());
            spaceInfoVo.setCampusId(clazzVo.getCampusId());
            spaceInfoVo.setCampusName(clazzVo.getCampusName());
            spaceInfoVo.setOrganizationId(clazzVo.getOrganizationId());
            spaceInfoVo.setGrade(clazzVo.getGrade());
        } else if (screenBusinessBo.getSpaceGroupUseType().equals(SpaceGroupUseType.NOT_XZ.getValue())) {
            spaceInfoVo = spaceInfoService.getDetail(screenBusinessBo.getSpaceInfoId());
        }
        return AjaxResult.success(spaceInfoVo);
    }

    /**
     * 查询区域设备数
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/8 9:34
     **/
    @Override
    public AjaxResult getSpaceInfoDeviceCountByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        List<SpaceGroup> spaceGroups = spaceGroupService.list(new LambdaQueryWrapper<SpaceGroup>()
                .eq(SpaceGroup::getIsDelete, StatusEnum.NOTDELETE.getCode()));
        if (CollectionUtils.isEmpty(spaceGroups)) {
            return AjaxResult.fail("获取区域组信息失败");
        }
        ShowDeviceListConditionBo conditionBo = new ShowDeviceListConditionBo();
        conditionBo.setOrganizationId(condition.getOrganizationId());
        conditionBo.setCampusId(condition.getCampusId());
        List<ShowDeviceVo> showDeviceVos =
                showDeviceService.listShowDeviceBindByCondition(conditionBo);
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(getSpaceInfoDeviceCountList(spaceGroups, new ArrayList<>()));
        }
        return AjaxResult.success(getSpaceInfoDeviceCountList(spaceGroups, showDeviceVos));
    }

    /**
     * 查询空间设备列表（不包含没有设备的地点）  根据监管教育局id批量查询多校地点设备
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/7/31 16:20
     **/
    @Override
    public AjaxResult getSpaceDeviceListByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        // 1、获取被监管的学校
        ShowDeviceListVo listVo = new ShowDeviceListVo();
        List<OrganizationVo> organizationVos = baseDataService.getSuperviseOrganizationList(condition.getParentOrganizationId());
        if (CollectionUtils.isEmpty(organizationVos)) {
            return AjaxResult.success(listVo);
        }
        Map<Long, OrganizationVo> organizationVoMap = organizationVos.stream()
                .collect(Collectors.toMap(OrganizationVo::getId, x -> x, (v1, v2) -> v1));
        List<Long> organizationIds = organizationVos.stream().map(OrganizationVo::getId).collect(Collectors.toList());

        // 2、查询符合条件的设备（目的：反向查询出地点）-->进而设置符合条件的地点
        ShowDeviceListConditionBo conditionBo = new ShowDeviceListConditionBo();
        conditionBo.setOrganizationIds(organizationIds);
        conditionBo.setOrganizationId(condition.getOrganizationId());
        conditionBo.setSpaceInfoId(condition.getSpaceInfoId());
        conditionBo.setSpaceGroupId(condition.getSpaceGroupId());
        conditionBo.setSpaceGroupUseType(condition.getSpaceGroupUseType());
        // 只查询监管的设备
        conditionBo.setSuperviseState(SuperviseStateType.MONITOR_YES.getValue());
        List<ShowDeviceVo> showDeviceVos = showDeviceService.listShowDeviceBindByCondition(conditionBo);
        // 设备列表为空
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            listVo.setTotal(ConstantsLong.NUM_0);
            listVo.setErrorCount(ConstantsInteger.NUM_0);
            listVo.setShowDeviceList(new ArrayList<>());
            return AjaxResult.success(listVo);
        }
        // 反向查询地点，符合条件的所有设备对应的地点（目的：过滤掉被删除的地点）
        List<SpaceInfoVo> spaceInfoVoList = getAllSpaceInfoListByDeviceList(showDeviceVos);
        Map<String, SpaceInfoVo> spaceInfoVoMap = spaceInfoVoList.stream()
                .collect(Collectors.toMap(x -> x.getSpaceInfoId()+"-"+x.getSpaceGroupId()+"-"+x.getSpaceGroupUseType(),
                        x -> x, (v1, v2) -> v1));
        // 判断地点列表是否为空
        if (CollectionUtils.isNotEmpty(spaceInfoVoList)) {
            List<SpaceInfoBo> spaceInfoBos = spaceInfoVoList.stream().map(x -> {
                SpaceInfoBo bo = new SpaceInfoBo();
                BeanUtils.copyProperties(x, bo);
                return bo;
            }).collect(Collectors.toList());
            // 仅查询地点还未被删除的地点下的设备
            conditionBo.setSpaceInfoBos(spaceInfoBos);
        } else {
            listVo.setTotal(ConstantsLong.NUM_0);
            listVo.setErrorCount(ConstantsInteger.NUM_0);
            listVo.setShowDeviceList(new ArrayList<>());
            return AjaxResult.success(listVo);
        }

        // 3、查询分页的符合条件的设备列表
        PageInfo<ShowDeviceVo> pageInfo = new PageInfo<>();
//        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
//            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
//        }
        pageInfo = new PageInfo<>(showDeviceService.listShowDeviceBindByCondition(conditionBo));
        showDeviceVos = JSONObject.parseArray(JSONObject.toJSONString(pageInfo.getList()), ShowDeviceVo.class);
        // 总数
        listVo.setTotal(pageInfo.getTotal());

        // 4、在线的设备序列号列表
        List<String> showDeviceNumbers = Lists.newArrayList();
        if (condition.getOrganizationId() != null && condition.getOrganizationId() != ConstantsLong.NUM_0) {
            List<String> showDeviceNumberList = messageService.listOnLineDeviceNumber(condition.getOrganizationId());
            if (CollectionUtils.isNotEmpty(showDeviceNumberList)) {
                showDeviceNumbers.addAll(showDeviceNumberList);
            }
        } else {
            List<String> showDeviceNumberList = messageService.listOnLineDeviceNumberBatch(organizationIds);
            if (CollectionUtils.isNotEmpty(showDeviceNumberList)) {
                showDeviceNumbers.addAll(showDeviceNumberList);
            }
        }
        // 异常设备数
        if (CollectionUtils.isNotEmpty(showDeviceNumbers)) {
            conditionBo.setShowDeviceNumbers(showDeviceNumbers);
            List<ShowDeviceVo> onlineDevices = showDeviceService.listShowDeviceBindByCondition(conditionBo);
            listVo.setErrorCount(listVo.getTotal().intValue() - onlineDevices.size());
        } else {
            listVo.setErrorCount(listVo.getTotal().intValue());
        }

        // 5、返回结果处理，循环分页的数据列表设置额外值（状态、地点名称、组织名称）
        showDeviceVos.stream().forEach(showDeviceVo -> {
            if (CollectionUtils.isEmpty(showDeviceNumbers)) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            } else if (!showDeviceNumbers.contains(showDeviceVo.getDeviceNumber())) {
                showDeviceVo.setDeviceStatus(DeviceStatusType.ERROR.getValue());
            }
            String key = showDeviceVo.getSpaceInfoId()+"-"+showDeviceVo.getSpaceGroupId()+"-"+showDeviceVo.getSpaceGroupUseType();
            if (spaceInfoVoMap.containsKey(key)) {
                showDeviceVo.setSpaceInfoName(spaceInfoVoMap.get(key).getSpaceInfoName());
            }
            if (organizationVoMap.containsKey(showDeviceVo.getOrganizationId())) {
                showDeviceVo.setOrganizationName(organizationVoMap.get(showDeviceVo.getOrganizationId()).getName());
            }
        });

        // 排序、分页
        if (CollectionUtils.isNotEmpty(showDeviceVos)) {
            // 排序
            showDeviceVos = showDeviceVos.stream().sorted(Comparator.comparing(x -> {
                StringBuilder builder = new StringBuilder();
                // 根据组织id、是否行政教室、空间id、设备名转拼音、设备号排序
                builder.append(x.getOrganizationId()).append("-")
                        .append(x.getSpaceGroupUseType()).append("-")
                        .append(x.getSpaceInfoId()).append("-");
                if (StringUtils.isNotBlank(x.getDeviceName())) {
                    builder.append(PinyinUtil.getPinyin(x.getDeviceName()).replaceAll(" ", "").toUpperCase());
                }
                builder.append(x.getDeviceNumber().toUpperCase());
                return builder.toString();
            })).collect(Collectors.toList());
        }
        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            int startIndex = (condition.getPageNo() - 1) * condition.getPageSize();
            int endIndex = condition.getPageNo() * condition.getPageSize();
            if (endIndex > showDeviceVos.size()) {
                endIndex = showDeviceVos.size();
            }
            showDeviceVos = showDeviceVos.subList(startIndex, endIndex);
        }

        listVo.setShowDeviceList(showDeviceVos);
        return AjaxResult.success(listVo);
    }

    /**
     * 查询教育局监管学校设备地点
     *
     * @param parentOrganizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/2 9:52
     **/
    @Override
    public AjaxResult getSuperviseSpaceByParentId(@RequestParam Long parentOrganizationId) {
        // 获取监管全部组织信息
        List<OrganizationVo> organizationVos = baseDataService.getSuperviseOrganizationList(parentOrganizationId);
        if (CollectionUtils.isEmpty(organizationVos)) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<Long> organizationIds = organizationVos.stream().map(OrganizationVo::getId).collect(Collectors.toList());
        // 查询全部设备。增加监管状态条件
        ShowDeviceListConditionBo conditionBo = new ShowDeviceListConditionBo();
        conditionBo.setOrganizationIds(organizationIds);
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setSuperviseState(SuperviseStateType.MONITOR_YES.getValue());
        List<ShowDeviceVo> showDeviceVos =
                showDeviceService.listShowDeviceBindByCondition(conditionBo);
        // 设备列表判空
        if (CollectionUtils.isEmpty(showDeviceVos)) {
            return AjaxResult.success(new ArrayList<>());
        }
        // 反向查询地点
        // 全部地点
        List<SpaceInfoVo> spaceInfoVoList = getAllSpaceInfoListByDeviceList(showDeviceVos);
        // 组装数据
        Map<Long, List<SpaceInfoVo>> spaceInfoVoMap = spaceInfoVoList.stream()
                .collect(Collectors.groupingBy(SpaceInfoVo::getOrganizationId));
        List<OrganizationVoExt> organizationVosExt = new ArrayList<>();
        for (OrganizationVo organizationVo : organizationVos) {
            OrganizationVoExt voExt = new OrganizationVoExt();
            BeanUtils.copyProperties(organizationVo, voExt);
            if (spaceInfoVoMap.containsKey(voExt.getId())) {
                voExt.setSpaceInfoList(spaceInfoVoMap.get(voExt.getId()));
            }
            organizationVosExt.add(voExt);
        }
        return AjaxResult.success(organizationVosExt);
    }

    /**
     * 根据设备查询地点
     *
     * @param showDeviceVos
     * @return java.util.List<com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo>
     * <AUTHOR>
     * @date 2024/8/2 15:44
     **/
    private List<SpaceInfoVo> getAllSpaceInfoListByDeviceList(List<ShowDeviceVo> showDeviceVos) {
        List<SpaceInfoVo> spaceInfoVoList = new ArrayList<>();
        // 行政教室
        List<Long> classesIds = new ArrayList<>();
        // 非行政教室
        List<Long> spaceInfoIds = new ArrayList<>();
        List<ShowDeviceVo> showDeviceVosXz = showDeviceVos.stream()
                .filter(s -> SpaceGroupUseType.XZ.getValue() == s.getSpaceGroupUseType())
                .collect(Collectors.toList());
        List<ShowDeviceVo> showDeviceVosNotXz = showDeviceVos.stream()
                .filter(s -> SpaceGroupUseType.NOT_XZ.getValue() == s.getSpaceGroupUseType())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(showDeviceVosXz)) {
            List<Long> xzSpaceInfoIds = showDeviceVosXz.stream().map(ShowDeviceVo::getSpaceInfoId)
                    .collect(Collectors.toList());
            classesIds.addAll(xzSpaceInfoIds);
        }
        if (CollectionUtils.isNotEmpty(showDeviceVosNotXz)) {
            List<Long> notXzSpaceInfoIds = showDeviceVosNotXz.stream().map(ShowDeviceVo::getSpaceInfoId)
                    .collect(Collectors.toList());
            spaceInfoIds.addAll(notXzSpaceInfoIds);
        }
        if (CollectionUtils.isNotEmpty(classesIds)) {
            ClazzConditionBoExt clazzConditionBo = new ClazzConditionBoExt();
            clazzConditionBo.setIds(classesIds);
            AjaxResult classesResult = baseDataService.getClassesListByClassesIds(clazzConditionBo);
            if (classesResult.isSuccess()) {
                Map classesResultMap = JSON.parseObject(JSON.toJSONString(classesResult.getData()), Map.class);
                List<ClazzInfoVo> clazzInfoVos
                        = JSONArray.parseArray(JSONArray.toJSONString(classesResultMap.get("list")), ClazzInfoVo.class);
                for (ClazzInfoVo clazzInfoVo : clazzInfoVos) {
                    SpaceInfoVo spaceInfoVo = new SpaceInfoVo();
                    String grade = SchoolYearUtil.gradeMap.get(clazzInfoVo.getGrade());
                    String spaceName = grade.concat(clazzInfoVo.getClassesName()).concat("班");
                    spaceInfoVo.setSpaceInfoId(clazzInfoVo.getId());
                    spaceInfoVo.setSpaceInfoName(spaceName);
                    spaceInfoVo.setClassesName(clazzInfoVo.getClassesName());
                    spaceInfoVo.setRemark(spaceName.concat("教室"));
                    spaceInfoVo.setClassesId(clazzInfoVo.getId());
                    spaceInfoVo.setCampusId(clazzInfoVo.getCampusId());
                    spaceInfoVo.setCampusName(clazzInfoVo.getCampusName());
                    spaceInfoVo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
                    spaceInfoVo.setGrade(clazzInfoVo.getGrade());
                    spaceInfoVo.setGradeName(clazzInfoVo.getGradeName());
                    spaceInfoVo.setEnrollmentYear(clazzInfoVo.getEnrollmentYear());
                    spaceInfoVo.setSpaceGroupId(ConstantsConfig.SPACE_GROUP_ID_PTJS);
                    spaceInfoVo.setOrganizationId(clazzInfoVo.getOrganizationId());
                    spaceInfoVoList.add(spaceInfoVo);
                }
            }

        }
        if (CollectionUtils.isNotEmpty(spaceInfoIds)) {
            SpaceInfoListConditionBo spaceInfoListConditionBo = new SpaceInfoListConditionBo();
            spaceInfoListConditionBo.setSpaceInfoIds(spaceInfoIds);
            List<SpaceInfoVo> spaceInfoVos = spaceInfoService.getSpaceInfoListByCondition(spaceInfoListConditionBo);
            spaceInfoVoList.addAll(spaceInfoVos);
        }
        // 去重
        spaceInfoVoList = spaceInfoVoList.stream().distinct().collect(Collectors.toList());
        return spaceInfoVoList;
    }


    /**
     * 封装区域设备数返回list
     *
     * @param spaceGroups
     * @param showDeviceVos
     * @return
     */
    private List<SpaceInfoDeviceCountVo> getSpaceInfoDeviceCountList(List<SpaceGroup> spaceGroups,
                                                                       List<ShowDeviceVo> showDeviceVos) {
        List<SpaceInfoDeviceCountVo> list = new ArrayList<>();
        for (SpaceGroup spaceGroup : spaceGroups) {
            SpaceInfoDeviceCountVo countVo = new SpaceInfoDeviceCountVo();
            countVo.setSpaceGroupId(spaceGroup.getSpaceGroupId());
            countVo.setSpaceGroupUseType(spaceGroup.getSpaceGroupUseType());
            if (spaceGroup.getSpaceGroupUseType() == SpaceGroupUseType.XZ.getValue()) {
                List<ShowDeviceVo> showDeviceVosXZ = showDeviceVos.stream()
                        .filter(s -> SpaceGroupUseType.XZ.getValue() == s.getSpaceGroupUseType())
                        .collect(Collectors.toList());
                countVo.setDeviceCount(CollectionUtils.isEmpty(showDeviceVosXZ) ? 0 : showDeviceVosXZ.size());
            } else {
                List<ShowDeviceVo> showDeviceVosNotXZBySpaceGroupId = showDeviceVos.stream()
                        .filter(s -> SpaceGroupUseType.NOT_XZ.getValue() == s.getSpaceGroupUseType()
                                && spaceGroup.getSpaceGroupId().equals(s.getSpaceGroupId()))
                        .collect(Collectors.toList());
                countVo.setDeviceCount(CollectionUtils.isEmpty(showDeviceVosNotXZBySpaceGroupId) ? 0 : showDeviceVosNotXZBySpaceGroupId.size());
            }
            list.add(countVo);
        }
        return list;
    }

}
