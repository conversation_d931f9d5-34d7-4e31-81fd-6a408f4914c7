package com.fh.cloud.screen.service.screen.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 第三方对接云屏场景信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-06 17:50:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_scene_third")
public class ScreenSceneThirdDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "screen_scene_third_id", type = IdType.AUTO)
	private Long screenSceneThirdId;

	/**
	 * 名称
	 */
	@TableField("screen_scene_third_name")
	private String screenSceneThirdName;

	/**
	 * 第三方场景类型：1课后服务
	 */
	@TableField("screen_scene_third_type")
	private Integer screenSceneThirdType;

	/**
	 * 第三方场景开始时间
	 */
	@TableField("screen_scene_third_start_time")
	private Date screenSceneThirdStartTime;

	/**
	 * 第三方场景结束时间
	 */
	@TableField("screen_scene_third_end_time")
	private Date screenSceneThirdEndTime;

	/**
	 * 学校id
	 */
	@TableField("organization_id")
	private Long organizationId;

	/**
	 * FK所属校区ID
	 */
	@TableField("campus_id")
	private Long campusId;

	/**
	 * FK空间ID或者班级id
	 */
	@TableField("space_info_id")
	private Long spaceInfoId;

	/**
	 * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
	 */
	@TableField("space_group_use_type")
	private Integer spaceGroupUseType;

	/**
	 * FK设备id
	 */
	@TableField("show_device_id")
	private Long showDeviceId;

	/**
	 * 第三方场景展示内容页面url
	 */
	@TableField("screen_scene_third_show_url")
	private String screenSceneThirdShowUrl;

	/**
	 * 第三方场景提交数据url
	 */
	@TableField("screen_scene_third_post_url")
	private String screenSceneThirdPostUrl;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 创建人
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 更新人
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

	/**
	 * 应用code
	 */
	@TableField("app_code")
	private String appCode;

	/**
	 * 场景提前N分钟在云屏上面展示，默认提前30分钟
	 */
	@TableField("screen_scene_third_delay_minute")
	private Integer screenSceneThirdDelayMinute;

	/**
	 * 冗余的关联该场景的第三方业务id
	 */@TableField("third_biz_id")
	private String thirdBizId;

}
