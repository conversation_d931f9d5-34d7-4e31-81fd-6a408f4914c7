package com.fh.cloud.screen.service.card.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.card.api.UserCardApi;
import com.fh.cloud.screen.service.card.entity.bo.StudentCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.TeacherCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.dto.UserCard;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardExportVo;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardVo;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardExportVo;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardVo;
import com.fh.cloud.screen.service.card.entity.vo.UserCardVo;
import com.fh.cloud.screen.service.card.service.IUserCardService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.teacher.entity.bo.TeacherConditionBo;
import com.light.user.teacher.entity.vo.TeacherVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户卡表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Validated
@Api(value = "", tags = "卡管理")
public class UserCardController implements UserCardApi {

    @Autowired
    private IUserCardService userCardService;
    @Autowired
    private BaseDataService baseDataService;

    /**
     * 学生卡列表
     *
     * @param conditionBo
     * @return
     */
    @ApiOperation(value = "查询学生卡列表", httpMethod = "POST")
    public AjaxResult getStudentCardList(@RequestBody UserCardListConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<StudentCardVo> list = new ArrayList<>();
        map.put("list", list);
        map.put("total", 0);
        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setPageNo(conditionBo.getPageNo());
        studentConditionBo.setPageSize(conditionBo.getPageSize());
        studentConditionBo.setOrganizationId(conditionBo.getOrganizationId());
        if (StringUtils.isNotBlank(conditionBo.getRealName())) {
            studentConditionBo.setRealName(conditionBo.getRealName());
        }
        if (null != conditionBo.getCampusId()) {
            studentConditionBo.setCampusId(conditionBo.getCampusId());
        }
        if (StringUtils.isNotBlank(conditionBo.getSection())) {
            studentConditionBo.setSection(conditionBo.getSection());
        }
        if (StringUtils.isNotBlank(conditionBo.getGrade())) {
            studentConditionBo.setGrade(conditionBo.getGrade());
        }
        if (null != conditionBo.getClassesId()) {
            studentConditionBo.setClassesId(conditionBo.getClassesId());
        }
        Map resMap = baseDataService.getStudentListByConditionSlow(studentConditionBo);
        List<StudentVo> studentList = (List<StudentVo>)resMap.get("list");
        if (CollectionUtil.isNotEmpty(studentList)) {
            for (StudentVo item : studentList) {
                StudentCardVo studentCardVo = new StudentCardVo();
                studentCardVo.setUserOid(item.getUserOid());
                studentCardVo.setUserName(item.getUserVo().getRealName());
                studentCardVo.setCampusName(item.getClazzVo().getCampusName());
                studentCardVo.setGrade(item.getClazzVo().getGrade());
                studentCardVo.setClassesName(item.getClazzVo().getClassesName());
                studentCardVo.setClassesId(item.getClassesId());
                UserCard detail = userCardService.getDetailByUserOid(item.getUserOid());
                if (null != detail) {
                    studentCardVo.setUserCardId(detail.getUserCardId());
                    studentCardVo.setCardNumber(detail.getCardNumber());
                    studentCardVo.setCardType(detail.getCardType());
                }
                list.add(studentCardVo);
            }
            map.put("list", list);
            map.put("total", resMap.get("total"));
        }
        return AjaxResult.success(map);
    }

    /**
     * 教师卡列表
     *
     * @param conditionBo
     * @return
     */
    @ApiOperation(value = "查询教师卡列表", httpMethod = "POST")
    public AjaxResult getTeacherCardList(@RequestBody UserCardListConditionBo conditionBo) {
        Map<String, Object> map = new HashMap<>(4);
        List<TeacherCardVo> list = new ArrayList<>();
        map.put("list", list);
        map.put("total", 0);
        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(conditionBo.getPageNo());
        teacherConditionBo.setPageSize(conditionBo.getPageSize());
        teacherConditionBo.setOrganizationId(conditionBo.getOrganizationId());
        if (StringUtils.isNotBlank(conditionBo.getRealName())) {
            teacherConditionBo.setRealName(conditionBo.getRealName());
        }
        Map resMap = baseDataService.getTeacherListByCondition(teacherConditionBo);
        List<TeacherVo> teacherList = (List<TeacherVo>)resMap.get("list");
        if (CollectionUtil.isNotEmpty(teacherList)) {
            for (TeacherVo item : teacherList) {
                TeacherCardVo teacherCardVo = new TeacherCardVo();
                teacherCardVo.setUserOid(item.getUserOid());
                teacherCardVo.setUserName(item.getUserVo().getRealName());
                teacherCardVo.setPhone(item.getUserVo().getPhone());
                UserCard detail = userCardService.getDetailByUserOid(item.getUserOid());
                if (null != detail) {
                    teacherCardVo.setUserCardId(detail.getUserCardId());
                    teacherCardVo.setCardNumber(detail.getCardNumber());
                    teacherCardVo.setCardType(detail.getCardType());
                }
                list.add(teacherCardVo);
            }
            map.put("list", list);
            map.put("total", resMap.get("total"));
        }
        return AjaxResult.success(map);
    }

    /**
     * 学生卡统计
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "学生卡统计", httpMethod = "POST")
    public AjaxResult getStudentCardCount(@RequestBody UserCardBo userCardBo) {
        Map<String, Object> map = new HashMap<>();
        map.put("allCount", 0);
        map.put("tiedCardCount", 0);
        map.put("unboundCardCount", 0);
        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setPageNo(-1);
        studentConditionBo.setPageSize(1);
        studentConditionBo.setOrganizationId(userCardBo.getOrganizationId());
        if (StringUtils.isNotBlank(userCardBo.getRealName())) {
            studentConditionBo.setRealName(userCardBo.getRealName());
        }
        if (null != userCardBo.getCampusId()) {
            studentConditionBo.setCampusId(userCardBo.getCampusId());
        }
        if (StringUtils.isNotBlank(userCardBo.getSection())) {
            studentConditionBo.setSection(userCardBo.getSection());
        }
        if (StringUtils.isNotBlank(userCardBo.getGrade())) {
            studentConditionBo.setGrade(userCardBo.getGrade());
        }
        if (null != userCardBo.getClassesId()) {
            studentConditionBo.setClassesId(userCardBo.getClassesId());
        }
        Map resMap = baseDataService.getStudentListByCondition(studentConditionBo);
        List<StudentVo> studentList = (List<StudentVo>)resMap.get("list");
        if (CollectionUtil.isNotEmpty(studentList)) {
            List<String> allUserOids =
                studentList.stream().map(studentVo -> studentVo.getUserOid()).collect(Collectors.toList());
            map.put("allCount", allUserOids.size());
            UserCardBo userCardAllBo = new UserCardBo();
            userCardAllBo.setCardType(1);
            List<UserCard> userCardAllList = userCardService.getUserCardList(userCardAllBo);
            if (CollectionUtil.isNotEmpty(userCardAllList)) {
                List<String> userOids =
                    userCardAllList.stream().map(userCard -> userCard.getUserOid()).collect(Collectors.toList());
                List<String> tiedCardUserOids =
                    allUserOids.stream().filter(item -> userOids.contains(item)).collect(Collectors.toList());
                map.put("tiedCardCount", tiedCardUserOids.size());
                map.put("unboundCardCount", allUserOids.size() - tiedCardUserOids.size());
            }
        }
        return AjaxResult.success(map);
    }

    /**
     * 教师卡统计
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "教师卡统计", httpMethod = "POST")
    public AjaxResult getTeacherCardCount(@RequestBody UserCardBo userCardBo) {
        Map<String, Object> map = new HashMap<>();
        map.put("allCount", 0);
        map.put("tiedCardCount", 0);
        map.put("unboundCardCount", 0);
        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(-1);
        teacherConditionBo.setPageSize(1);
        teacherConditionBo.setOrganizationId(userCardBo.getOrganizationId());
        if (StringUtils.isNotBlank(userCardBo.getRealName())) {
            teacherConditionBo.setRealName(userCardBo.getRealName());
        }
        Map resMap = baseDataService.getTeacherListByCondition(teacherConditionBo);
        List<TeacherVo> teacherList = (List<TeacherVo>)resMap.get("list");
        if (CollectionUtil.isNotEmpty(teacherList)) {
            List<String> allUserOids =
                teacherList.stream().map(teacherVo -> teacherVo.getUserOid()).collect(Collectors.toList());
            map.put("allCount", allUserOids.size());
            UserCardBo userCardAllBo = new UserCardBo();
            userCardAllBo.setCardType(2);
            List<UserCard> userCardAllList = userCardService.getUserCardList(userCardAllBo);
            if (CollectionUtil.isNotEmpty(userCardAllList)) {
                List<String> userOids =
                    userCardAllList.stream().map(userCard -> userCard.getUserOid()).collect(Collectors.toList());
                List<String> tiedCardUserOids =
                    allUserOids.stream().filter(item -> userOids.contains(item)).collect(Collectors.toList());
                map.put("tiedCardCount", tiedCardUserOids.size());
                map.put("unboundCardCount", allUserOids.size() - tiedCardUserOids.size());
            }
        }
        return AjaxResult.success(map);
    }

    /**
     * 查询用户卡表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询用户卡表列表", httpMethod = "POST")
    public AjaxResult getUserCardListByCondition(@RequestBody UserCardListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<UserCardVo> pageInfo = new PageInfo<>(userCardService.getUserCardListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("userCardList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增用户卡表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "新增用户卡表", httpMethod = "POST")
    public AjaxResult addUserCard(@RequestBody UserCardBo userCardBo) {
        boolean save = userCardService.addUserCard(userCardBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改用户卡表
     *
     * @param userCardBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "修改用户卡表", httpMethod = "POST")
    public AjaxResult updateUserCard(@RequestBody UserCardBo userCardBo) {
        if (null == userCardBo.getUserCardId()) {
            return AjaxResult.fail("用户卡表id不能为空");
        }
        boolean update = userCardService.updateUserCard(userCardBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询用户卡详情
     *
     * @param userCardId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询用户卡详情", httpMethod = "GET")
    public AjaxResult getDetail(@NotNull(message = "请选择用户") Long userCardId) {
        return AjaxResult.success(userCardService.getDetail(userCardId));
    }

    /**
     * 删除用户卡表
     *
     * @param userCardId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "删除用户卡表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("userCardId") Long userCardId) {
        UserCardBo userCardBo = new UserCardBo();
        userCardBo.setUserCardId(userCardId);
        userCardBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = userCardService.updateUserCard(userCardBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 编辑卡
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "编辑卡", notes = "编辑卡")
    public AjaxResult updateCard(@RequestBody UserCardBo userCardBo) {
        if (null == userCardBo.getUserCardId()) {
            return AjaxResult.fail("请选择需要编辑卡的用户");
        }
        if (StringUtils.isBlank(userCardBo.getCardNumber())) {
            return AjaxResult.fail("卡号不能为空");
        }
        return userCardService.updateCard(userCardBo);
    }

    /**
     * 绑卡
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "绑卡", notes = "绑卡")
    public AjaxResult tiedCard(@RequestBody UserCardBo userCardBo) {
        if (StringUtils.isBlank(userCardBo.getUserOid())) {
            return AjaxResult.fail("请选择用户");
        }
        if (StringUtils.isBlank(userCardBo.getCardNumber())) {
            return AjaxResult.fail("卡号不能为空");
        }
        return userCardService.tiedCard(userCardBo);
    }

    /**
     * 解绑卡
     *
     * @param userCardId
     * @return
     */
    @ApiOperation(value = "解绑卡", notes = "解绑卡")
    public AjaxResult unbindCard(@NotNull(message = "请选择用户") Long userCardId) {
        return userCardService.unbindCard(userCardId);
    }

    /**
     * 批量解绑卡
     *
     * @param userCardIds
     * @return
     */
    @ApiOperation(value = "批量解绑卡（逗号分割字符串）", notes = "批量解绑卡（逗号分割字符串）")
    public AjaxResult batchUnbindCard(@NotBlank(message = "请选择用户") String userCardIds) {
        return userCardService.batchUnbindCard(userCardIds);
    }

    /**
     * 学生卡导出
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "学生卡导出", httpMethod = "POST")
    public List<StudentCardExportVo> studentCardExport(@RequestBody UserCardBo userCardBo) {
        List<StudentCardExportVo> exportList = new ArrayList<>();
        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setPageNo(-1);
        studentConditionBo.setPageSize(1);
        studentConditionBo.setClassesId(userCardBo.getClassesId());
        // studentConditionBo.setClassesIds(userCardBo.getClassesIds());
        Map resMap = baseDataService.getStudentListByCondition(studentConditionBo);
        List<StudentVo> studentList = (List<StudentVo>)resMap.get("list");
        if (CollectionUtil.isNotEmpty(studentList)) {
            for (StudentVo item : studentList) {
                StudentCardExportVo studentCardExportVo = new StudentCardExportVo();
                studentCardExportVo.setUserName(item.getUserVo().getRealName());
                exportList.add(studentCardExportVo);
            }
        }
        return exportList;
    }

    /**
     * 学生卡导入
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "学生卡导入", httpMethod = "POST")
    public AjaxResult studentCardImport(@RequestBody UserCardBo userCardBo) {
        List<StudentCardImportBo> list = userCardBo.getStudentCardImportList();
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.fail("excel文件不能为空");
        }
        // 用户不存在
        List<String> nothingnessList = new ArrayList<>();
        // 用户重名
        List<String> duplicateNameList = new ArrayList<>();
        // 卡号已存在
        List<String> cardNumberExistList = new ArrayList<>();
        // 卡号为空
        List<String> cardIsNullList = new ArrayList<>();
        // 卡号超过10位数字
        List<String> overLengthList = new ArrayList<>();
        // 成功数
        Integer successNum = 0;
        for (StudentCardImportBo item : list) {
            if (StringUtils.isBlank(item.getCardNumber())) {
                cardIsNullList.add(item.getUserName());
                continue;
            }
            if (10 < item.getCardNumber().trim().length()) {
                overLengthList.add(item.getUserName());
                continue;
            }
            List<StudentVo> studentList =
                baseDataService.getStudentVoByRealName(item.getUserName(), userCardBo.getClassesId());
            if (CollectionUtil.isEmpty(studentList)) {
                nothingnessList.add(item.getUserName());
            } else if (1 < studentList.size()) {
                duplicateNameList.add(item.getUserName());
            } else {
                UserCardBo bo = new UserCardBo();
                bo.setUserOid(studentList.get(0).getUserOid());
                bo.setCardNumber(item.getCardNumber().trim());
                bo.setCardType(1);
                if (userCardService.userCardImport(bo)) {
                    successNum = successNum + 1;
                } else {
                    cardNumberExistList.add(item.getUserName());
                }
            }
        }
        // if (list.size() == cardIsNullList.size()) {
        // return AjaxResult.fail("卡号不能为空");
        // }
        String msg = "成功导入" + successNum + "条学生卡数据。";
        if (CollectionUtil.isNotEmpty(cardIsNullList)) {
            String.join(",", cardIsNullList);
            msg = msg + "有" + cardIsNullList.size() + "条数据因卡号为空而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(overLengthList)) {
            String.join(",", overLengthList);
            msg = msg + "有" + overLengthList.size() + "条数据因卡号超过10位数字而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(nothingnessList)) {
            String.join(",", nothingnessList);
            msg = msg + "有" + nothingnessList.size() + "条数据因学生不存在而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(duplicateNameList)) {
            String.join(",", duplicateNameList);
            msg = msg + "有" + duplicateNameList.size() + "条数据因学生重名而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(cardNumberExistList)) {
            String.join(",", cardNumberExistList);
            msg = msg + "有" + cardNumberExistList.size() + "条数据因卡号已存在而导入失败。";
        }
        return AjaxResult.success(msg);
    }

    /**
     * 教师卡导出
     *
     * @param userCardBo
     * @return
     */
    @ApiOperation(value = "教师卡导出", httpMethod = "POST")
    public List<TeacherCardExportVo> teacherCardExport(@RequestBody UserCardBo userCardBo) {
        List<TeacherCardExportVo> exportList = new ArrayList<>();
        TeacherConditionBo teacherConditionBo = new TeacherConditionBo();
        teacherConditionBo.setPageNo(-1);
        teacherConditionBo.setPageSize(1);
        teacherConditionBo.setOrganizationId(userCardBo.getOrganizationId());
        Map resMap = baseDataService.getTeacherListByCondition(teacherConditionBo);
        List<TeacherVo> teacherList = (List<TeacherVo>)resMap.get("list");
        if (CollectionUtil.isNotEmpty(teacherList)) {
            for (TeacherVo item : teacherList) {
                TeacherCardExportVo teacherCardExportVo = new TeacherCardExportVo();
                teacherCardExportVo.setUserName(item.getUserVo().getRealName());
                teacherCardExportVo.setPhone(item.getUserVo().getPhone());
                exportList.add(teacherCardExportVo);
            }
        }
        return exportList;
    }

    /**
     * 教师卡导入
     *
     * @param list
     * @return
     */
    @ApiOperation(value = "教师卡导入", httpMethod = "POST")
    public AjaxResult teacherCardImport(@RequestBody List<TeacherCardImportBo> list) {
        if (CollectionUtil.isEmpty(list)) {
            return AjaxResult.fail("excel文件不能为空");
        }
        // 手机号不存在
        List<String> nothingnessList = new ArrayList<>();
        // 手机号重复
        List<String> phoneRepeatList = new ArrayList<>();
        // 卡号已存在
        List<String> cardNumberExistList = new ArrayList<>();
        // 卡号为空
        List<String> cardIsNullList = new ArrayList<>();
        // 卡号超过10位数字
        List<String> overLengthList = new ArrayList<>();
        // 成功数
        Integer successNum = 0;
        for (TeacherCardImportBo item : list) {
            if (StringUtils.isBlank(item.getCardNumber())) {
                cardIsNullList.add(item.getUserName());
                continue;
            }
            if (10 < item.getCardNumber().trim().length()) {
                overLengthList.add(item.getUserName());
                continue;
            }
            List<TeacherVo> teacherList = baseDataService.getTeacherVoByPhone(item.getPhone());
            if (CollectionUtil.isEmpty(teacherList)) {
                nothingnessList.add(item.getUserName());
            } else if (1 < teacherList.size()) {
                phoneRepeatList.add(item.getUserName());
            } else {
                UserCardBo userCardBo = new UserCardBo();
                userCardBo.setUserOid(teacherList.get(0).getUserOid());
                userCardBo.setCardNumber(item.getCardNumber().trim());
                userCardBo.setCardType(2);
                if (userCardService.userCardImport(userCardBo)) {
                    successNum = successNum + 1;
                } else {
                    cardNumberExistList.add(item.getUserName());
                }
            }
        }
        // if (list.size() == cardIsNullList.size()) {
        // return AjaxResult.fail("卡号不能为空");
        // }
        String msg = "成功导入" + successNum + "条教师卡数据。";
        if (CollectionUtil.isNotEmpty(cardIsNullList)) {
            String.join(",", cardIsNullList);
            msg = msg + "有" + cardIsNullList.size() + "条数据因卡号为空而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(overLengthList)) {
            String.join(",", overLengthList);
            msg = msg + "有" + overLengthList.size() + "条数据因卡号超过10位数字而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(nothingnessList)) {
            String.join(",", nothingnessList);
            msg = msg + "有" + nothingnessList.size() + "条数据因手机号不存在而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(phoneRepeatList)) {
            String.join(",", phoneRepeatList);
            msg = msg + "有" + phoneRepeatList.size() + "条数据因手机号重复而导入失败。";
        }
        if (CollectionUtil.isNotEmpty(cardNumberExistList)) {
            String.join(",", cardNumberExistList);
            msg = msg + "有" + cardNumberExistList.size() + "条数据因卡号已存在而导入失败。";
        }
        return AjaxResult.success(msg);
    }
}
