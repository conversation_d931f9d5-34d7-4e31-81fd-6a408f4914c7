package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenContactDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContactVo;

/**
 * 云屏产品咨询收集联系表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-07-22 10:52:57
 */
public interface ScreenContactMapper extends BaseMapper<ScreenContactDto> {

	List<ScreenContactVo> getScreenContactListByCondition(ScreenContactConditionBo condition);

	ScreenContactVo getScreenContactByCondition(ScreenContactConditionBo condition);

}
