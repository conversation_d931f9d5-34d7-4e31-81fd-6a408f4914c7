package com.fh.cloud.screen.service.screen.controller;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.enums.ScreenModuleLibrarySource;
import org.apache.commons.collections.CollectionUtils;
import org.bouncycastle.oer.its.IValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.consts.ConstantsConfig;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.device.entity.dto.ShowDevice;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.DevicePatternType;
import com.fh.cloud.screen.service.enums.ScreenDevicePushType;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryMediaApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaService;
import com.fh.cloud.screen.service.utils.StringKit;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 云屏模块库媒体资源表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */

@Api(value = "", tags = "云屏模块库媒体资源管理")
@Slf4j
@RestController
@RequestMapping("/screen-module-library-media")
public class ScreenModuleLibraryMediaController implements ScreenModuleLibraryMediaApi {

    @Resource
    private IScreenModuleLibraryMediaService screenModuleLibraryMediaService;
    @Resource
    private IShowDeviceService showDeviceService;

    @Value("${module.group.type.limit:20}")
    private Integer MODULE_LIMIT;
    @Value("${library.limit:10}")
    private Integer LIBRARY_LIMIT;
    @Value("${module.default.h5.label.group:0}")
    private Integer MODULE_DEFAULT_H5_LABEL_GROUP;

    /**
     * 查询云屏模块库媒体资源列表
     *
     * <AUTHOR>
     * @date 2022/6/29 15:25
     */
    @ApiOperation(value = "查询云屏模块库媒体资源列表", httpMethod = "POST")
    @PostMapping("/list")
    public AjaxResult getScreenModuleLibraryListByCondition(@RequestBody ScreenModuleLibraryMediaBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        Map<String, Object> map = new HashMap<String, Object>();
        condition
            .setScreenModuleLibraryIds(StringKit.splitString2ListLong(condition.getScreenModuleLibrarySelIds(), null));
        // 线上问题，空的传参，查询了全部。
        if (CollectionUtils.isEmpty(condition.getScreenModuleLibraryIds())
            && null == condition.getScreenModuleLibraryId()) {
            map.put("list", null);
            return AjaxResult.success(map);
        }
        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaListByCondition =
                screenModuleLibraryMediaService.getScreenModuleLibraryMediaListByCondition(condition);
            // 多选海报时候，按照主题参数对主题排序
            if (StringUtils.isNotBlank(condition.getScreenModuleLibrarySelIds())
                && condition.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                screenModuleLibraryMediaListByCondition = sortResultWithScreenModuleLibrarySelIds(
                    condition.getScreenModuleLibraryIds(), screenModuleLibraryMediaListByCondition);
            }
            // 有推送逻辑，只有云屏设备有接收海报推送逻辑
            if (condition.isWithPushLibraryProcess()) {
                screenModuleLibraryMediaListByCondition =
                    addPushLibraryProcess(screenModuleLibraryMediaListByCondition, condition.getDeviceNumber());
            }
            map.put("list", screenModuleLibraryMediaListByCondition);
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaListByCondition =
                screenModuleLibraryMediaService.getScreenModuleLibraryMediaListByCondition(condition);
            // 多选海报时候，按照主题参数对主题排序
            if (StringUtils.isNotBlank(condition.getScreenModuleLibrarySelIds())
                && condition.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                screenModuleLibraryMediaListByCondition = sortResultWithScreenModuleLibrarySelIds(
                    condition.getScreenModuleLibraryIds(), screenModuleLibraryMediaListByCondition);
            }
            // 分页暂时不支持添加推送订阅的海报
            PageInfo<ScreenModuleLibraryMediaVo> pageInfo = new PageInfo<>(screenModuleLibraryMediaListByCondition);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }

    }

    /**
     * 全部海报列表
     *
     * @param selectType 1 校本海报包含热门，2 校本海报库无热门，3校本海报
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/16 15:21
     */
    @ApiOperation(value = "全部海报列表", httpMethod = "GET")
    @GetMapping("/posters")
    public AjaxResult getThemePosterList(@RequestParam("organizationId") Long organizationId,
        @RequestParam("selectType") Integer selectType,
        @RequestParam(name = "pattern", required = false) Integer pattern) {
        return screenModuleLibraryMediaService.getThemePosterList(organizationId, selectType, pattern);
    }

    /**
     * 我收藏的海报模块列表
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/16 17:07
     */
    @ApiOperation(value = "我收藏的海报模块列表", httpMethod = "GET")
    @GetMapping("/my-posters")
    public AjaxResult getMyCollectPosters(@RequestParam("organizationId") Long organizationId,
        @RequestParam(name = "pattern", required = false) Integer pattern) {
        return screenModuleLibraryMediaService.getMyCollectPosters(organizationId, pattern);
    }

    /**
     * 覆盖海报
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/30 15:08
     */
    @GetMapping("/coverPoster")
    public AjaxResult coverPoster() {
        return screenModuleLibraryMediaService.coverPoster();
    }

    /**
     * 海报导入优化
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/20 11:25
     */
    @GetMapping("/posterOptimization")
    public AjaxResult posterOptimization(@RequestParam("filterOther") Long filterOther) {
        return screenModuleLibraryMediaService.posterOptimization(filterOther);
    }

    /**
     * 新增云屏模块库媒体资源表
     *
     * <AUTHOR>
     * @date 2023-01-03 14:57:48
     */
    @Override
    @PostMapping("/add")
    public AjaxResult addScreenModuleLibraryMedia(@RequestBody ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo) {
        return screenModuleLibraryMediaService.addScreenModuleLibraryMedia(screenModuleLibraryMediaBo);
    }

    /**
     * 批量新增海报图片
     *
     * @param screenModuleLibraryMediaBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/10 16:38
     */
    @Override
    @PostMapping("/add-batch")
    public AjaxResult
        addScreenModuleLibraryMediaBatch(@RequestBody List<ScreenModuleLibraryMediaBo> screenModuleLibraryMediaBos) {
        return screenModuleLibraryMediaService.addScreenModuleLibraryMediaBatch(screenModuleLibraryMediaBos);
    }

    /**
     * 修改云屏模块库媒体资源表
     *
     * @param screenModuleLibraryMediaBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-01-03 14:57:48
     */
    @Override
    @PostMapping("/update")
    public AjaxResult
        updateScreenModuleLibraryMedia(@RequestBody ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo) {
        if (null == screenModuleLibraryMediaBo.getScreenModuleLibraryMediaId()) {
            return AjaxResult.fail("云屏模块库媒体资源表id不能为空");
        }
        return screenModuleLibraryMediaService.updateScreenModuleLibraryMedia(screenModuleLibraryMediaBo);
    }

    /**
     * 删除云屏模块库媒体资源表
     *
     * @param screenModuleLibraryMediaId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-01-03 14:57:48
     */
    @Override
    @GetMapping("/delete")
    public AjaxResult delete(@RequestParam("screenModuleLibraryMediaId") Long screenModuleLibraryMediaId) {
        if (null == screenModuleLibraryMediaId) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ScreenModuleLibraryMedia screenModuleLibraryMediaDto = new ScreenModuleLibraryMedia();
        screenModuleLibraryMediaDto.setScreenModuleLibraryMediaId(screenModuleLibraryMediaId);
        screenModuleLibraryMediaDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (screenModuleLibraryMediaService.updateById(screenModuleLibraryMediaDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    /**
     * 增加推送海报的处理逻辑：节日节点主动推送+标签订阅推送+公益主题海报
     *
     * @param screenModuleLibraryMediaListByCondition the screen module library media list by condition
     * @return the list
     * <AUTHOR>
     * @date 2023 -03-23 16:01:09
     */
    private List<ScreenModuleLibraryMediaVo> addPushLibraryProcess(
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaListByCondition, String showDeviceNumber) {
        List<ScreenModuleLibraryMediaVo> resultList = screenModuleLibraryMediaListByCondition;
        if (resultList == null) {
            resultList = Lists.newArrayList();
        }
        ShowDevice showDevice = showDeviceService.getByDeviceNum(showDeviceNumber);
        Integer pushType = showDevice.getPushType();

        // 推送节日的海报：节日->标签->海报
        if (pushType != null && pushType.equals(ScreenDevicePushType.ACCEPT.getValue())) {
            ScreenModuleLibraryMediaBo condition = new ScreenModuleLibraryMediaBo();
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVosOfFestivals =
                screenModuleLibraryMediaService.getCurrentDayFestivalModuleLibraryMediaList(condition);
            if (CollectionUtils.isNotEmpty(screenModuleLibraryMediaVosOfFestivals)) {
                resultList = Stream.of(resultList, screenModuleLibraryMediaVosOfFestivals).flatMap(Collection::stream)
                    .distinct().collect(Collectors.toList());
            }
        }
        //
        // // 标签订阅：设备->标签->海报
        // Long showDeviceId = null;
        // if (showDevice != null) {
        // showDeviceId = showDevice.getShowDeviceId();
        // }
        // List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVosOfLabels =
        // screenModuleLibraryMediaService.getLabelModuleLibraryMediaListByShowDeviceId(showDeviceId);
        // if (CollectionUtils.isNotEmpty(screenModuleLibraryMediaVosOfLabels)) {
        // resultList = Stream.of(resultList, screenModuleLibraryMediaVosOfLabels).flatMap(Collection::stream)
        // .distinct().collect(Collectors.toList());
        // }

        // 公益海报
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVosOfDefault =
            screenModuleLibraryMediaService.getDefaultModuleLibraryMediaList();
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = Stream.of(resultList, screenModuleLibraryMediaVosOfDefault).flatMap(Collection::stream)
                .distinct().collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * 查询云屏模块库媒体资源列表-默认
     *
     * @param condition the condition
     * @return the screen module library list by condition default
     * <AUTHOR>
     * @date 2023 -03-27 18:57:35
     */
    @ApiOperation(value = "查询云屏模块库媒体资源列表-默认", httpMethod = "POST")
    @PostMapping("/list-default")
    @Override
    public AjaxResult getScreenModuleLibraryListByConditionDefault(ScreenModuleLibraryMediaBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (condition.getDevicePattern().equals(DevicePatternType.HORIZONTAL.getValue())) {
            condition.setModuleGroupType(ConstantsConfig.DEFAULT_MODULE_GROUP_TYPE_HORIZONTAL);
        }
        if (condition.getDevicePattern().equals(DevicePatternType.VERTICAL.getValue())) {
            condition.setModuleGroupType(ConstantsConfig.DEFAULT_MODULE_GROUP_TYPE_VERTICAL);
        }

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaListByCondition =
                screenModuleLibraryMediaService.getScreenModuleLibraryMediaListByCondition(condition);
            map.put("list", screenModuleLibraryMediaListByCondition);
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaListByCondition =
                screenModuleLibraryMediaService.getScreenModuleLibraryMediaListByCondition(condition);
            PageInfo<ScreenModuleLibraryMediaVo> pageInfo = new PageInfo<>(screenModuleLibraryMediaListByCondition);
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }

    }

    /**
     * 海报前20个分组和前10主题列表（移动端）
     *
     * @param organizationId
     * @param selectType
     * @param pattern
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/4 9:34
     */
    @Override
    @GetMapping("/poster-page")
    public AjaxResult getPosterPageList(@RequestParam("organizationId") Long organizationId,
        @RequestParam("selectType") Integer selectType,
        @RequestParam(name = "pattern", required = false) Integer pattern,
        @RequestParam(value = "classesId", required = false) Long classesId) {
        return screenModuleLibraryMediaService.getTopPosterList(organizationId, selectType, pattern, MODULE_LIMIT,
            LIBRARY_LIMIT, MODULE_DEFAULT_H5_LABEL_GROUP, classesId);
    }

    /**
     * 分页获取我收藏的海报列表
     *
     * @param libraryCollectConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/20 15:31
     */
    @ApiOperation(value = "分页获取我收藏的海报列表", httpMethod = "POST")
    @PostMapping("/my-posters-page")
    public AjaxResult
        getMyCollectPostersPage(@RequestBody ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo) {
        return screenModuleLibraryMediaService.getMyCollectPostersPage(libraryCollectConditionBo);
    }

    /**
     * 对screenModuleLibraryMediaVos进行排序，按照screenModuleLibrarySelIds值顺序进行排序
     *
     * @param screenModuleLibraryIds 主题id的集合
     * @param screenModuleLibraryMediaVos screenModuleLibraryMediaVos的查询结果集。可以为空
     * @return list list
     * <AUTHOR>
     * @date 2023 -04-20 13:47:22
     */
    private List<ScreenModuleLibraryMediaVo> sortResultWithScreenModuleLibrarySelIds(List<Long> screenModuleLibraryIds,
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVos) {
        if (CollectionUtils.isEmpty(screenModuleLibraryIds) || screenModuleLibraryIds.size() <= ConstantsInteger.NUM_1
            || CollectionUtils.isEmpty(screenModuleLibraryMediaVos)) {
            return screenModuleLibraryMediaVos;
        }
        List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVosResult = Lists.newArrayList();

        Map<Long, List<ScreenModuleLibraryMediaVo>> ScreenModuleLibraryMediaVosMap = screenModuleLibraryMediaVos
            .stream().collect(Collectors.groupingBy(ScreenModuleLibraryMediaVo::getScreenModuleLibraryId));
        screenModuleLibraryIds.forEach(screenModuleLibraryId -> {
            if (screenModuleLibraryId != null) {
                List<ScreenModuleLibraryMediaVo> screenModuleLibraryMediaVoListTemp =
                    ScreenModuleLibraryMediaVosMap.get(screenModuleLibraryId);
                if (CollectionUtils.isNotEmpty(screenModuleLibraryMediaVoListTemp)) {
                    screenModuleLibraryMediaVosResult.addAll(screenModuleLibraryMediaVoListTemp);
                }
            }
        });
        return screenModuleLibraryMediaVosResult;
    }
}
