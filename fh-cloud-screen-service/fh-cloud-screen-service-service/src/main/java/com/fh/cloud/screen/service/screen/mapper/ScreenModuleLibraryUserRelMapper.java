package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryUserRelDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryUserRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryUserRelVo;

/**
 * 模块用户关系表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-03-29 15:06:48
 */
public interface ScreenModuleLibraryUserRelMapper extends BaseMapper<ScreenModuleLibraryUserRelDto> {

    List<ScreenModuleLibraryUserRelVo>
        getScreenModuleLibraryUserRelListByCondition(ScreenModuleLibraryUserRelConditionBo condition);

}
