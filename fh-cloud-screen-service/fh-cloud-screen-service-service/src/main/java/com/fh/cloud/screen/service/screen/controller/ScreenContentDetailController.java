package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentDetailListConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenContentDetailVo;
import com.fh.cloud.screen.service.screen.service.IScreenContentDetailService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 云屏内容详情表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@RestController
@RequestMapping("/screen/content-detail")
@Validated
public class ScreenContentDetailController {

    @Autowired
    private IScreenContentDetailService screenContentDetailService;

    /**
     * 查询云屏内容详情表列表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询云屏内容详情表列表", httpMethod = "POST")
    public AjaxResult getScreenContentDetailListByCondition(@RequestBody ScreenContentDetailListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ScreenContentDetailVo> pageInfo =
            new PageInfo<>(screenContentDetailService.getScreenContentDetailListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("screenContentDetailList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增云屏内容详情表
     * 
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增云屏内容详情表", httpMethod = "POST")
    public AjaxResult addScreenContentDetail(@RequestBody ScreenContentDetailBo screenContentDetailBo) {
        boolean save = screenContentDetailService.addScreenContentDetail(screenContentDetailBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改云屏内容详情表
     * 
     * @param screenContentDetailBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改云屏内容详情表", httpMethod = "POST")
    public AjaxResult updateScreenContentDetail(@RequestBody ScreenContentDetailBo screenContentDetailBo) {
        if (null == screenContentDetailBo.getScreenContentDetailId()) {
            return AjaxResult.fail("云屏内容详情表id不能为空");
        }
        boolean update = screenContentDetailService.updateScreenContentDetail(screenContentDetailBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询云屏内容详情表详情
     * 
     * @param screenContentDetailId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询云屏内容详情表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenContentDetailId") Long screenContentDetailId) {
        ScreenContentDetailVo screenContentDetailVo = screenContentDetailService.getDetail(screenContentDetailId);
        return AjaxResult.success(screenContentDetailVo);
    }

    /**
     * 删除云屏内容详情表
     * 
     * @param screenContentDetailId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:09
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除云屏内容详情表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("screenContentDetailId") Long screenContentDetailId) {
        ScreenContentDetailBo screenContentDetailBo = new ScreenContentDetailBo();
        screenContentDetailBo.setScreenContentDetailId(screenContentDetailId);
        screenContentDetailBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = screenContentDetailService.updateScreenContentDetail(screenContentDetailBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
