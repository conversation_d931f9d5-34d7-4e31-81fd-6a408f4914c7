package com.fh.cloud.screen.service.screen.controller;

import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryMediaAuditApi;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMediaAuditDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaAuditBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaAuditService;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.feign.annotation.FeignValidatorAnnotation;

import java.util.List;
/**
 * 云屏模块库媒体资源审核表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:25:55
 */
@RestController
@Validated
public class ScreenModuleLibraryMediaAuditController implements ScreenModuleLibraryMediaAuditApi{
	
    @Autowired
    private IScreenModuleLibraryMediaAuditService screenModuleLibraryMediaAuditService;

    /**
     * 查询云屏模块库媒体资源审核表分页列表
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
    @Override
	@FeignValidatorAnnotation
    public AjaxResult<PageInfo<ScreenModuleLibraryMediaAuditVo>> getScreenModuleLibraryMediaAuditPageListByCondition(@RequestBody ScreenModuleLibraryMediaAuditConditionBo condition){
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
    	PageInfo<ScreenModuleLibraryMediaAuditVo> pageInfo = new PageInfo<>(screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

	/**
	 * 查询云屏模块库媒体资源审核表列表
	 * <AUTHOR>
	 * @date 2023-12-06 10:25:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<List<ScreenModuleLibraryMediaAuditVo>> getScreenModuleLibraryMediaAuditListByCondition(@RequestBody ScreenModuleLibraryMediaAuditConditionBo condition){
		List<ScreenModuleLibraryMediaAuditVo> list = screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditListByCondition(condition);
		return AjaxResult.success(list);
	}


    /**
     * 新增云屏模块库媒体资源审核表
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
     */
	@Override
	@FeignValidatorAnnotation
    public AjaxResult addScreenModuleLibraryMediaAudit(@Validated @RequestBody ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo){
		return screenModuleLibraryMediaAuditService.addScreenModuleLibraryMediaAudit(screenModuleLibraryMediaAuditBo);
    }

    /**
	 * 修改云屏模块库媒体资源审核表
	 * @param screenModuleLibraryMediaAuditBo
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult updateScreenModuleLibraryMediaAudit(@Validated @RequestBody ScreenModuleLibraryMediaAuditBo screenModuleLibraryMediaAuditBo) {
		if(null == screenModuleLibraryMediaAuditBo.getScreenModuleLibraryMediaAuditId()) {
			return AjaxResult.fail("云屏模块库媒体资源审核表id不能为空");
		}
		return screenModuleLibraryMediaAuditService.updateScreenModuleLibraryMediaAudit(screenModuleLibraryMediaAuditBo);
	}

	/**
	 * 查询云屏模块库媒体资源审核表详情
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult<ScreenModuleLibraryMediaAuditVo> getDetail(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("云屏模块库媒体资源审核表id不能为空");
		}
		ScreenModuleLibraryMediaAuditConditionBo condition = new ScreenModuleLibraryMediaAuditConditionBo();
		condition.setScreenModuleLibraryMediaAuditId(id);
		ScreenModuleLibraryMediaAuditVo vo = screenModuleLibraryMediaAuditService.getScreenModuleLibraryMediaAuditByCondition(condition);
		return AjaxResult.success(vo);
	}

    
    /**
	 * 删除云屏模块库媒体资源审核表
	 * @param id
	 * @return
	 * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-12-06 10:25:55
	 */
	@Override
	@FeignValidatorAnnotation
	public AjaxResult delete(@RequestParam("id") Long id) {
		if(null == id) {
			return AjaxResult.fail("请选择需要删除的数据");
		}
		ScreenModuleLibraryMediaAuditDto screenModuleLibraryMediaAuditDto = new ScreenModuleLibraryMediaAuditDto();
		screenModuleLibraryMediaAuditDto.setScreenModuleLibraryMediaAuditId(id);
		screenModuleLibraryMediaAuditDto.setIsDelete(StatusEnum.ISDELETE.getCode());
		if(screenModuleLibraryMediaAuditService.updateById(screenModuleLibraryMediaAuditDto)) {
						return AjaxResult.success("删除成功");
		}
		return AjaxResult.fail("删除失败");
	}

}
