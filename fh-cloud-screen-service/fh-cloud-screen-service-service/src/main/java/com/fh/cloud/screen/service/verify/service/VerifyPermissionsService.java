package com.fh.cloud.screen.service.verify.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.user.entity.vo.LoginUserVo;
import com.light.user.user.entity.vo.UserOrgVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 校验权限
 *
 * <AUTHOR>
 * @date 2023/4/20
 */
@Service
public class VerifyPermissionsService {

    @Resource
    private BaseDataService baseDataService;

    /**
     * 校验当前登陆用户的组织id是否与参数组织id相等
     *
     * @param organizationId 组织id
     * @return boolean true:组织一致,false:未登录或组织不一致
     * <AUTHOR>
     * @date 2023/4/20 10:50
     */
    public boolean verifyOrganization(Long organizationId) {
        if (null == organizationId) {
            return false;
        }
        LoginAccountVo loginAccountVo = baseDataService.getCurrentUser();
        if (null == loginAccountVo) {
            return false;
        }
        LoginUserVo currentUser = loginAccountVo.getCurrentUser();
        if (null == currentUser) {
            return false;
        }
        List<UserOrgVo> loginUserOrgs = currentUser.getLoginUserOrgs();
        if (CollectionUtils.isEmpty(loginUserOrgs)) {
            return false;
        }
        for (UserOrgVo loginUserOrg : loginUserOrgs) {
            if (organizationId.equals(loginUserOrg.getOrganizationId())) {
                return true;
            }
        }
        return false;
    }
}
