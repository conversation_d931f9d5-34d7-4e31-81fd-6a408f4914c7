package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏紧急发布内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_content_special")
public class ScreenContentSpecial implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "screen_content_special_id", type = IdType.AUTO)
    private Long screenContentSpecialId;

    /**
     * FK所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @TableField("campus_id")
    private Long campusId;

    /**
     * 使用模板类型：1通知公告、2图片、3标语、4倒计时、5视频、6网页URL、7富文本
     */
    @TableField("screen_template_type")
    private Integer screenTemplateType;

    /**
     * 云屏内容-标题
     */
    @TableField("screen_content_title")
    private String screenContentTitle;

    /**
     * 云屏内容-文本
     */
    @TableField("screen_content_txt")
    private String screenContentTxt;

    /**
     * 云屏内容-url
     */
    @TableField("screen_content_url")
    private String screenContentUrl;

    /**
     * 云屏图片或者视频媒体地址
     */
    @TableField("screen_content_media_url")
    private String screenContentMediaUrl;

    /**
     * 云屏图片或者视频媒体地址-压缩后
     */
    @TableField("screen_content_media_url_compress")
    private String screenContentMediaUrlCompress;

    /**
     * 云屏图片或者视频媒体地址-封面
     */
    @TableField("screen_content_media_url_cover")
    private String screenContentMediaUrlCover;

    /**
     * 云屏图片或者视频媒体名称（不包含后缀）
     */
    @TableField("screen_content_media_name")
    private String screenContentMediaName;

    /**
     * 云屏图片或者视频原始媒体名称（包含后缀）
     */
    @TableField("screen_content_media_name_ori")
    private String screenContentMediaNameOri;

    /**
     * 有效时间-开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 有效时间-结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 发布状态：1未发布，2已发布
     */
    @TableField("screen_content_status")
    private Integer screenContentStatus;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 云屏图片或者视频媒体fileoid
     */
    @TableField("screen_content_media_id")
    private String screenContentMediaId;

    /**
     * 云屏图片或者视频媒体fileoid-压缩后
     */
    @TableField("screen_content_media_id_compress")
    private String screenContentMediaIdCompress;

    /**
     * 称呼内容
     */
    @TableField("call_content")
    private String callContent;

    /**
     * 落款内容
     */
    @TableField("sign_content")
    private String signContent;

    /**
     * 内容数据来源
     */
    @TableField("screen_content_source")
    private Integer screenContentSource;
}
