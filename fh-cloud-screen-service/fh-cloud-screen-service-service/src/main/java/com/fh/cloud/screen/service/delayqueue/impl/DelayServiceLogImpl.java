package com.fh.cloud.screen.service.delayqueue.impl;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fh.cloud.screen.service.delayqueue.DelayService;
import com.fh.cloud.screen.service.delayqueue.entity.TaskDelayed;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceLogService;
import com.fh.cloud.screen.service.enums.DeviceLogStatusType;

import lombok.extern.slf4j.Slf4j;

/**
 * 日志延迟队列服务的实现,taskId存放show_device_log表主键
 * 
 * <AUTHOR>
 * @date 2022/12/21 15:45
 */
@Slf4j
@Service("DelayServiceLogImpl")
public class DelayServiceLogImpl implements DelayService<ShowDeviceLogVo> {

    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    private IShowDeviceLogService showDeviceLogService;

    /**
     * 专属日志延迟队列服务的延迟队列,是线程安全的
     */
    private final DelayQueue<TaskDelayed<ShowDeviceLogVo>> delayQueue = new DelayQueue<>();

    @Override
    public void init() {
        threadPoolExecutor.execute(() -> {
            log.info("启动设备日志处理线程:" + Thread.currentThread().getName());
            TaskDelayed<ShowDeviceLogVo> taskDelayed;
            while (true) {
                try {
                    taskDelayed = delayQueue.take();
                    ShowDeviceLogVo detail = showDeviceLogService.getDetail(Long.valueOf(taskDelayed.getTaskId()));
                    if (detail != null && !detail.getDeviceLogStatus().equals(DeviceLogStatusType.SUCCESS.getValue())) {
                        detail.setDeviceLogStatus(DeviceLogStatusType.FAIL.getValue());
                        ShowDeviceLogBo showDeviceLogBo = new ShowDeviceLogBo();
                        BeanUtils.copyProperties(detail, showDeviceLogBo);
                        showDeviceLogService.updateShowDeviceLog(showDeviceLogBo);
                    }
                } catch (Exception e) {
                    log.error("执行设备日志超时处理异常:", e);
                }
            }
        });
    }

    @Override
    public boolean addToDelayQueueBatch(List<TaskDelayed<ShowDeviceLogVo>> dataList) {
        this.delayQueue.addAll(dataList);
        return true;
    }

    @Override
    public boolean addToDelayQueue(TaskDelayed<ShowDeviceLogVo> data) {
        boolean result = this.delayQueue.add(data);
        return result;
    }

    @Override
    public void removeFromDelayQueue(String taskId) {
        for (Iterator<TaskDelayed<ShowDeviceLogVo>> iterator = this.delayQueue.iterator(); iterator.hasNext();) {
            TaskDelayed<ShowDeviceLogVo> queue = iterator.next();
            if (queue.getTaskId().equals(taskId)) {
                this.delayQueue.remove(queue);
            }
        }
    }
}
