package com.fh.cloud.screen.service.leaveschool.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.config.WxMpProperties;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.device.service.IShowDeviceService;
import com.fh.cloud.screen.service.enums.LeaveSchoolType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.OrganizationWxMsgTemplateType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.entity.bo.*;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordOperateDto;
import com.fh.cloud.screen.service.leaveschool.entity.vo.*;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolRecordOperateMapper;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolBroadcastInfoService;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDeviceService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.fh.cloud.screen.service.utils.SchoolYearUtil;
import com.fh.cloud.screen.service.wx.entity.bo.OrganizationWxMsgTemplateConfigConditionBo;
import com.fh.cloud.screen.service.wx.entity.vo.OrganizationWxMsgTemplateConfigVo;
import com.fh.cloud.screen.service.wx.service.IOrganizationWxMsgTemplateConfigService;
import com.fh.cloud.screen.service.wx.service.WxMpMsgService;
import com.fh.sso.service.organization.api.OrganizationWxConfigApi;
import com.fh.sso.service.organization.entity.bo.OrganizationWxConfigConditionBo;
import com.fh.sso.service.organization.entity.vo.OrganizationWxConfigVo;
import com.fh.sso.service.user.api.UserAuthorizationApi;
import com.fh.sso.service.user.entity.bo.UserAuthorizationConditionBo;
import com.fh.sso.service.user.entity.vo.UserAuthorizationVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.user.entity.vo.KeeperRelationVo;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordDto;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolRecordService;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolRecordMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 放学记录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:31
 */
@Service
public class LeaveSchoolRecordServiceImpl extends ServiceImpl<LeaveSchoolRecordMapper, LeaveSchoolRecordDto> implements ILeaveSchoolRecordService {

	@Resource
	private LeaveSchoolRecordMapper leaveSchoolRecordMapper;
	@Resource
	private LeaveSchoolRecordOperateMapper leaveSchoolRecordOperateMapper;

	@Autowired
	private BaseDataService baseDataService;
	@Autowired
	private ApplicationContext applicationContext;
	@Autowired
	private ILeaveSchoolBroadcastInfoService leaveSchoolBroadcastInfoService;
	@Lazy
	@Autowired
	private IShowDeviceService showDeviceService;
	@Resource
	private WxMpProperties wxMpProperties;
	@Resource
	private UserAuthorizationApi userAuthorizationApi;
	@Resource
	private WxMpMsgService wxMpMsgService;

	@Autowired
	private ILeaveSchoolConfigDeviceService leaveSchoolConfigDeviceService;

	// 放学提醒
	@Value("${wxMpSubMsgLeaveSchoolStartPage:}")
	private String wxMpSubMsgLeaveSchoolStartPage;
	@Value("${wxMpSubMsgLeaveSchoolStartTemplateId:}")
	private String wxMpSubMsgLeaveSchoolStartTemplateId;

	@Value("${wxMpSubMsgLeaveSchoolEndPage:}")
	private String wxMpSubMsgLeaveSchoolEndPage;
	@Value("${wxMpSubMsgLeaveSchoolEndTemplateId:}")
	private String wxMpSubMsgLeaveSchoolEndTemplateId;

	@Resource
	private OrganizationWxConfigApi organizationWxConfigApi;

	@Resource
	private IOrganizationWxMsgTemplateConfigService organizationWxMsgTemplateConfigService;

	@Resource
	private ThreadPoolExecutor threadPoolExecutor;
	
    @Override
	public List<LeaveSchoolRecordVo> getLeaveSchoolRecordListByCondition(LeaveSchoolRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolRecordMapper.getLeaveSchoolRecordListByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo) {
		LeaveSchoolRecordDto leaveSchoolRecord = new LeaveSchoolRecordDto();
		BeanUtils.copyProperties(leaveSchoolRecordBo, leaveSchoolRecord);
		leaveSchoolRecord.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(leaveSchoolRecord)){
			// 插入操作记录
			LeaveSchoolRecordOperateDto operateDto = new LeaveSchoolRecordOperateDto();
			operateDto.setLeaveSchoolRecordId(leaveSchoolRecord.getLeaveSchoolRecordId());
			operateDto.setLeaveSchoolType(leaveSchoolRecord.getLeaveSchoolType());
			leaveSchoolRecordOperateMapper.insert(operateDto);
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public AjaxResult updateLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo) {
		LeaveSchoolRecordDto leaveSchoolRecord = new LeaveSchoolRecordDto();
		BeanUtils.copyProperties(leaveSchoolRecordBo, leaveSchoolRecord);
		if(updateById(leaveSchoolRecord)){
			// 插入操作记录
			LeaveSchoolRecordOperateDto operateDto = new LeaveSchoolRecordOperateDto();
			operateDto.setLeaveSchoolRecordId(leaveSchoolRecord.getLeaveSchoolRecordId());
			operateDto.setLeaveSchoolType(leaveSchoolRecord.getLeaveSchoolType());
			leaveSchoolRecordOperateMapper.insert(operateDto);
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public LeaveSchoolRecordVo getLeaveSchoolRecordByCondition(LeaveSchoolRecordConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return leaveSchoolRecordMapper.getLeaveSchoolRecordByCondition(condition);
	}

	@Override
	public LeaveSchoolRecordVo getLeaveSchoolRecord(LeaveSchoolRecordBo leaveSchoolRecordBo) {
		LambdaQueryWrapper<LeaveSchoolRecordDto> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(LeaveSchoolRecordDto::getOrganizationId, leaveSchoolRecordBo.getOrganizationId());
		if (leaveSchoolRecordBo.getCampusId() != null) {
			queryWrapper.eq(LeaveSchoolRecordDto::getCampusId, leaveSchoolRecordBo.getCampusId());
		}
		queryWrapper.eq(LeaveSchoolRecordDto::getSpaceInfoId, leaveSchoolRecordBo.getSpaceInfoId());
		queryWrapper.eq(LeaveSchoolRecordDto::getSpaceGroupUseType, leaveSchoolRecordBo.getSpaceGroupUseType());
		queryWrapper.eq(LeaveSchoolRecordDto::getLeaveSchoolDay, leaveSchoolRecordBo.getLeaveSchoolDay());
		queryWrapper.orderByDesc(LeaveSchoolRecordDto::getLeaveSchoolRecordId);
		queryWrapper.last("limit 1");
		LeaveSchoolRecordDto dto = baseMapper.selectOne(queryWrapper);
		if (dto == null) {
			dto = new LeaveSchoolRecordDto();
			dto.setOrganizationId(leaveSchoolRecordBo.getOrganizationId());
			dto.setCampusId(leaveSchoolRecordBo.getCampusId());
			dto.setSpaceInfoId(leaveSchoolRecordBo.getSpaceInfoId());
			dto.setSpaceGroupUseType(leaveSchoolRecordBo.getSpaceGroupUseType());
			dto.setLeaveSchoolDay(leaveSchoolRecordBo.getLeaveSchoolDay());
			dto.setLeaveSchoolType(LeaveSchoolType.NOT_LEAVE_SCHOOL.getCode());
			save(dto);
			LeaveSchoolRecordOperateDto operateDto = new LeaveSchoolRecordOperateDto();
			operateDto.setLeaveSchoolRecordId(dto.getLeaveSchoolRecordId());
			operateDto.setLeaveSchoolType(dto.getLeaveSchoolType());
			leaveSchoolRecordOperateMapper.insert(operateDto);
		}
		LeaveSchoolRecordVo vo = new LeaveSchoolRecordVo();
		BeanUtils.copyProperties(dto, vo);

		// 操作记录
		LeaveSchoolRecordOperateConditionBo conditionBo = new LeaveSchoolRecordOperateConditionBo();
		conditionBo.setLeaveSchoolRecordId(vo.getLeaveSchoolRecordId());
		conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		conditionBo.setNeLeaveSchoolType(LeaveSchoolType.NOT_LEAVE_SCHOOL.getCode());
		List<LeaveSchoolRecordOperateVo> operateVos = leaveSchoolRecordOperateMapper.getLeaveSchoolRecordOperateListByCondition(conditionBo);
		vo.setOperateList(operateVos);

		return vo;
	}

	@Override
	public AjaxResult updateLeaveSchoolRecordAndSendMsg(LeaveSchoolRecordBo leaveSchoolRecordBo) {
		if (null == leaveSchoolRecordBo.getLeaveSchoolRecordId()) {
			return AjaxResult.fail("放学记录表id不能为空");
		}
		LeaveSchoolRecordConditionBo condition = new LeaveSchoolRecordConditionBo();
		condition.setLeaveSchoolRecordId(leaveSchoolRecordBo.getLeaveSchoolRecordId());
		LeaveSchoolRecordVo vo = getLeaveSchoolRecordByCondition(condition);

		AjaxResult ajaxResult = updateLeaveSchoolRecord(leaveSchoolRecordBo);

		String spaceName = "";
		Long classesId = 0L;
		String grade = "";
		// 本期只考虑行政班级
		if (SpaceGroupUseType.XZ.getValue() == vo.getSpaceGroupUseType()) {
			ClazzVo clazzVo = baseDataService.getByClazzId(vo.getSpaceInfoId());
			if (clazzVo != null) {
				spaceName = SchoolYearUtil.gradeMap.get(clazzVo.getGrade()).concat(clazzVo.getClassesName()).concat("班");
				classesId = clazzVo.getId();
				grade = clazzVo.getGrade();
			}
		}

		if (ajaxResult.isSuccess()) {
			// 设置messageBody数据
			LeaveSchoolPublishVo leaveSchoolPublishVo = new LeaveSchoolPublishVo();
			leaveSchoolPublishVo.setLeaveSchoolType(leaveSchoolRecordBo.getLeaveSchoolType());
			leaveSchoolPublishVo.setSpaceInfoId(vo.getSpaceInfoId());
			leaveSchoolPublishVo.setSpaceGroupUseType(vo.getSpaceGroupUseType());
			leaveSchoolPublishVo.setOrganizationId(vo.getOrganizationId());
			leaveSchoolPublishVo.setCampusId(vo.getCampusId());
			leaveSchoolPublishVo.setSpaceName(spaceName);

			// 查询放学配置设备
			List<String> deviceNumbers = new ArrayList<>();
			LeaveSchoolConfigDeviceConditionBo deviceConditionBo = new LeaveSchoolConfigDeviceConditionBo();
			deviceConditionBo.setOrganizationId(vo.getOrganizationId());
			deviceConditionBo.setCampusId(vo.getCampusId());
			List<LeaveSchoolConfigDeviceVo> deviceVos =
					leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceList(deviceConditionBo);
			if (CollectionUtil.isNotEmpty(deviceVos)) {
				deviceNumbers =
						deviceVos.stream().map(LeaveSchoolConfigDeviceVo::getDeviceNumber).collect(Collectors.toList());
			}

			if (leaveSchoolRecordBo.getLeaveSchoolType() > vo.getLeaveSchoolType()) {
				if (LeaveSchoolType.LEAVE_SCHOOL_IN_PROGRESS.getCode()
						.equals(leaveSchoolRecordBo.getLeaveSchoolType())) {
					// 查询播报信息
					LeaveSchoolBroadcastInfoConditionBo conditionBo = new LeaveSchoolBroadcastInfoConditionBo();
					conditionBo.setOrganizationId(vo.getOrganizationId());
					conditionBo.setCampusId(vo.getCampusId());
					conditionBo.setBroadcastContent(spaceName);
					List<LeaveSchoolBroadcastInfoVo> broadcastInfoVos =
							leaveSchoolBroadcastInfoService.getLeaveSchoolBroadcastInfoListByCondition(conditionBo);
					if (CollectionUtil.isNotEmpty(broadcastInfoVos)) {
						LeaveSchoolBroadcastInfoVo broadcastInfoVo = broadcastInfoVos.get(0);
						leaveSchoolPublishVo.setBroadcastId(broadcastInfoVo.getBroadcastId());
						leaveSchoolPublishVo.setBroadcastUrl(broadcastInfoVo.getBroadcastUrl());
					}
					applicationContext.publishEvent(PublishEvent.produceLeaveSchoolPublishEvent(
							MessageWsType.LEAVE_SCHOOL_START.getValue(), vo.getOrganizationId(), vo.getSpaceInfoId(),
							vo.getSpaceGroupUseType(), spaceName, deviceNumbers, leaveSchoolPublishVo));
					// 推送开始放学消息
					String finalGrade = grade;
					Long finalClassesId = classesId;
					String finalSpaceName = spaceName;
					threadPoolExecutor.execute(new Runnable() {
						@Override
						public void run() {
							leaveSchoolWxMpRemind(vo.getOrganizationId(), finalGrade, finalClassesId, finalSpaceName,
									LeaveSchoolType.LEAVE_SCHOOL_IN_PROGRESS.getCode());
						}
					});
				} else {
					applicationContext.publishEvent(PublishEvent.produceLeaveSchoolPublishEvent(
							MessageWsType.LEAVE_SCHOOL_END.getValue(), vo.getOrganizationId(), vo.getSpaceInfoId(),
							vo.getSpaceGroupUseType(), spaceName, deviceNumbers, leaveSchoolPublishVo));
					// 推送放学结束消息
					String finalGrade1 = grade;
					Long finalClassesId1 = classesId;
					String finalSpaceName1 = spaceName;
					threadPoolExecutor.execute(new Runnable() {
						@Override
						public void run() {
							leaveSchoolWxMpRemind(vo.getOrganizationId(), finalGrade1, finalClassesId1, finalSpaceName1,
									LeaveSchoolType.ALREADY_LEAVE_SCHOOL.getCode());
						}
					});
				}
			} else {
				applicationContext.publishEvent(PublishEvent.produceLeaveSchoolPublishEvent(
						MessageWsType.LEAVE_SCHOOL_STATE_CHANGE.getValue(), vo.getOrganizationId(), vo.getSpaceInfoId(),
						vo.getSpaceGroupUseType(), spaceName, deviceNumbers, leaveSchoolPublishVo));
			}
		}
		return ajaxResult;
	}

	/**
	 * 放学推送微信消息
	 *
	 * @param
	 * @return java.util.List<java.lang.String>
	 * <AUTHOR>
	 * @date 2024/9/10 15:43
	 **/
	private void leaveSchoolWxMpRemind(Long organizationId, String grade, Long classesId, String spaceName,
									   Integer leaveSchoolType) {
		// 查询学生列表
		StudentConditionBo studentConditionBo = new StudentConditionBo();
		studentConditionBo.setOrganizationId(organizationId);
		studentConditionBo.setGrade(grade);
		studentConditionBo.setClassesId(classesId);
		studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		// 不分页
		studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
		Map<String, Object> studentResultMap = baseDataService.getStudentListByCondition(studentConditionBo);
		List<String> studentOids = org.apache.commons.compress.utils.Lists.newArrayList();
		List<StudentVo> studentVos = org.apache.commons.compress.utils.Lists.newArrayList();
		if (studentResultMap.containsKey("list") && studentResultMap.get("list") != null) {
			studentVos = JSONArray.parseArray(JSONArray.toJSONString(studentResultMap.get("list")), StudentVo.class);
			studentOids = studentVos.stream().map(StudentVo::getUserOid).collect(Collectors.toList());
		}
		if (CollectionUtil.isEmpty(studentOids)) {
			return;
		}

		// 查询家长列表
		List<String> mainUserOids = org.apache.commons.compress.utils.Lists.newArrayList();
		Map<String, List<StudentVo>> keeperRelationMap = new HashMap<>();
		List<KeeperRelationVo> keeperRelationVos = baseDataService.getKeeperRelationListByUserOidList(studentOids);
		if (CollectionUtil.isEmpty(keeperRelationVos)) {
			return;
		}
		mainUserOids =
				keeperRelationVos.stream().map(KeeperRelationVo::getMainUserOid).distinct().collect(Collectors.toList());
		Map<String, List<KeeperRelationVo>> relationMap =
				keeperRelationVos.stream().collect(Collectors.groupingBy(KeeperRelationVo::getMainUserOid));
		for (String mainUserOid : mainUserOids) {
			List<String> mainUserStudentOids =
					relationMap.get(mainUserOid).stream().map(KeeperRelationVo::getUserOid).collect(Collectors.toList());
			List<StudentVo> mainUserStudentVos = studentVos.stream()
					.filter(s -> mainUserStudentOids.contains(s.getUserOid())).collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(mainUserStudentVos)) {
				keeperRelationMap.put(mainUserOid, mainUserStudentVos);
			}
		}

		// 查询绑定微信的家长openId
		Map<String, String> toUserOpenIdMap = Maps.newHashMap();
		Map<String, String> toUserOpenIdMapWithOrg = Maps.newHashMap();
		// 学校是否配置了appId标识，用于后续推送消息校验是否推送到云屏服务号
		boolean isOrganizationAppId = false;
		String appId = wxMpProperties.getConfigs().get(0).getAppId();
		String orgAppId = null;
		// 查询组织是否配置了appId
		OrganizationWxConfigConditionBo orgWxConfigCondition = new OrganizationWxConfigConditionBo();
		orgWxConfigCondition.setOrganizationId(organizationId);
		AjaxResult<List<OrganizationWxConfigVo>> orgWxConfigResult =
				organizationWxConfigApi.getOrganizationWxConfigListByCondition(orgWxConfigCondition);
		if (orgWxConfigResult.isSuccess() && CollectionUtils.isNotEmpty(orgWxConfigResult.getData())) {
			orgAppId = orgWxConfigResult.getData().get(ConstantsInteger.NUM_0).getAppId();
			isOrganizationAppId = true;

			// 查询绑定了学校公众号的用户
			UserAuthorizationConditionBo userAuthorizationConditionBo = new UserAuthorizationConditionBo();
			userAuthorizationConditionBo.setAppId(orgAppId);
			userAuthorizationConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
			userAuthorizationConditionBo.setUserOids(mainUserOids);
			AjaxResult<List<UserAuthorizationVo>> userAuthorizationVosAjaxResult =
					userAuthorizationApi.getUserAuthorizationListByCondition(userAuthorizationConditionBo);
			if (userAuthorizationVosAjaxResult.isSuccess()
					&& CollectionUtils.isNotEmpty(userAuthorizationVosAjaxResult.getData())) {
				toUserOpenIdMapWithOrg = userAuthorizationVosAjaxResult.getData().stream().collect(
						Collectors.toMap(UserAuthorizationVo::getUserOid, UserAuthorizationVo::getOpenId, (k1, k2) -> k1));
			}
		}

		// 查询绑定了云屏公众号的用户（过滤掉已绑定了学校公众号的用户）
		UserAuthorizationConditionBo userAuthorizationConditionBo = new UserAuthorizationConditionBo();
		userAuthorizationConditionBo.setAppId(appId);
		userAuthorizationConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		userAuthorizationConditionBo.setUserOids(mainUserOids);
		AjaxResult<List<UserAuthorizationVo>> userAuthorizationVosAjaxResult =
				userAuthorizationApi.getUserAuthorizationListByCondition(userAuthorizationConditionBo);
		if (userAuthorizationVosAjaxResult.isSuccess()
				&& CollectionUtils.isNotEmpty(userAuthorizationVosAjaxResult.getData())) {
			Map<String, String> finalToUserOpenIdMapWithOrg = toUserOpenIdMapWithOrg;
			toUserOpenIdMap = userAuthorizationVosAjaxResult.getData().stream()
					.filter(u -> !finalToUserOpenIdMapWithOrg.containsKey(u.getUserOid())).collect(
							Collectors.toMap(UserAuthorizationVo::getUserOid, UserAuthorizationVo::getOpenId, (k1, k2) -> k1));
		}

		// 推送学校服务号
		if (CollectionUtil.isNotEmpty(toUserOpenIdMapWithOrg)) {
			sendWxMsg(orgAppId, leaveSchoolType, keeperRelationMap, toUserOpenIdMapWithOrg, spaceName, organizationId,
					isOrganizationAppId);
		}

		// 推送云屏服务号
		if (CollectionUtil.isNotEmpty(toUserOpenIdMap)) {
			sendWxMsg(appId, leaveSchoolType, keeperRelationMap, toUserOpenIdMap, spaceName, null, false);
		}

	}

	/**
	 * 推送放学消息
	 *
	 * @param appId
	 * @param leaveSchoolType
	 * @param keeperRelationMap
	 * @param toUserOpenIdMap
	 * @param spaceName
	 * @param organizationId
	 * @param isOrganizationAppId
	 * @return void
	 * <AUTHOR>
	 * @date 2024/9/26 14:49
	 **/
	private void sendWxMsg(String appId, Integer leaveSchoolType, Map<String, List<StudentVo>> keeperRelationMap,
						   Map<String, String> toUserOpenIdMap, String spaceName, Long organizationId, boolean isOrganizationAppId) {
		// 推送消息
		String leaveSchoolTime = DateKit.date2String(new Date(), "yyyy年MM月dd日 HH:mm");
		String wxMpSubMsgPage = "";
		String wxMpSubMsgTemplateId = "";
		// 获取配置的推送模板信息
		if (isOrganizationAppId) {
			if (LeaveSchoolType.LEAVE_SCHOOL_IN_PROGRESS.getCode().equals(leaveSchoolType)) {
				OrganizationWxMsgTemplateConfigConditionBo conditionBo =
						new OrganizationWxMsgTemplateConfigConditionBo();
				conditionBo.setOrganizationId(organizationId);
				conditionBo.setType(OrganizationWxMsgTemplateType.LEAVE_SCHOOL_IN_PROGRESS.getValue());
				List<OrganizationWxMsgTemplateConfigVo> list = organizationWxMsgTemplateConfigService
						.getOrganizationWxMsgTemplateConfigListByCondition(conditionBo);
				if (CollectionUtil.isNotEmpty(list)) {
					// 放学中消息推送
					OrganizationWxMsgTemplateConfigVo templateConfigVo = list.get(ConstantsInteger.NUM_0);
					wxMpSubMsgPage = templateConfigVo.getUrl();
					wxMpSubMsgTemplateId = templateConfigVo.getTemplateId();
				}
			} else if (LeaveSchoolType.ALREADY_LEAVE_SCHOOL.getCode().equals(leaveSchoolType)) {
				OrganizationWxMsgTemplateConfigConditionBo conditionBo =
						new OrganizationWxMsgTemplateConfigConditionBo();
				conditionBo.setOrganizationId(organizationId);
				conditionBo.setType(OrganizationWxMsgTemplateType.ALREADY_LEAVE_SCHOOL.getValue());
				List<OrganizationWxMsgTemplateConfigVo> list = organizationWxMsgTemplateConfigService
						.getOrganizationWxMsgTemplateConfigListByCondition(conditionBo);
				if (CollectionUtil.isNotEmpty(list)) {
					// 已放学消息推送
					OrganizationWxMsgTemplateConfigVo templateConfigVo = list.get(ConstantsInteger.NUM_0);
					wxMpSubMsgPage = templateConfigVo.getUrl();
					wxMpSubMsgTemplateId = templateConfigVo.getTemplateId();
				}
			}
		} else {
			if (LeaveSchoolType.LEAVE_SCHOOL_IN_PROGRESS.getCode().equals(leaveSchoolType)) {
				// 放学中消息推送
				wxMpSubMsgPage = wxMpSubMsgLeaveSchoolStartPage;
				wxMpSubMsgTemplateId = wxMpSubMsgLeaveSchoolStartTemplateId;
			} else if (LeaveSchoolType.ALREADY_LEAVE_SCHOOL.getCode().equals(leaveSchoolType)) {
				// 已放学消息推送
				wxMpSubMsgPage = wxMpSubMsgLeaveSchoolEndPage;
				wxMpSubMsgTemplateId = wxMpSubMsgLeaveSchoolEndTemplateId;
			}
		}
		// 循环获取家长，推送消息
		for (String mainUserOid : toUserOpenIdMap.keySet()) {
			if (!keeperRelationMap.containsKey(mainUserOid)) {
				continue;
			}
			List<StudentVo> mainUserStudentVos = keeperRelationMap.get(mainUserOid);
			String toUser = toUserOpenIdMap.get(mainUserOid);
			for (StudentVo studentVo : mainUserStudentVos) {
				List<WxMpTemplateData> data = new ArrayList<>();
				// 组装消息推送
				if (LeaveSchoolType.LEAVE_SCHOOL_IN_PROGRESS.getCode().equals(leaveSchoolType)) {
					// 放学中消息推送
					WxMpTemplateData wxMpTemplateData =
							new WxMpTemplateData("thing2", studentVo.getUserVo().getRealName(), "#FF0000");
					WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("thing8", spaceName, "#FF0000");
					WxMpTemplateData wxMpTemplateData3 = new WxMpTemplateData("time3", leaveSchoolTime, "#FF0000");
					data = com.google.common.collect.Lists.newArrayList(wxMpTemplateData, wxMpTemplateData2,
							wxMpTemplateData3);
				} else if (LeaveSchoolType.ALREADY_LEAVE_SCHOOL.getCode().equals(leaveSchoolType)) {
					// 已放学消息推送
					WxMpTemplateData wxMpTemplateData =
							new WxMpTemplateData("thing2", studentVo.getUserVo().getRealName(), "#FF0000");
					WxMpTemplateData wxMpTemplateData2 = new WxMpTemplateData("time3", leaveSchoolTime, "#FF0000");
					WxMpTemplateData wxMpTemplateData3 = new WxMpTemplateData("thing4", spaceName, "#FF0000");
					data = com.google.common.collect.Lists.newArrayList(wxMpTemplateData, wxMpTemplateData2,
							wxMpTemplateData3);
				}
				wxMpMsgService.sendTemplateMsg(appId, wxMpSubMsgPage, toUser, data, null, wxMpSubMsgTemplateId);
			}
		}
	}
}