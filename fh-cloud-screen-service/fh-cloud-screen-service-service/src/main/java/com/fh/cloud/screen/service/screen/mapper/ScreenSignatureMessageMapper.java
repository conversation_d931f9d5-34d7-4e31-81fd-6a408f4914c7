package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureMessageDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo;

/**
 * 电子签名寄语资源表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
public interface ScreenSignatureMessageMapper extends BaseMapper<ScreenSignatureMessageDto> {

	List<ScreenSignatureMessageVo> getScreenSignatureMessageListByCondition(ScreenSignatureMessageConditionBo condition);

	ScreenSignatureMessageVo getScreenSignatureMessageByCondition(ScreenSignatureMessageConditionBo condition);

}
