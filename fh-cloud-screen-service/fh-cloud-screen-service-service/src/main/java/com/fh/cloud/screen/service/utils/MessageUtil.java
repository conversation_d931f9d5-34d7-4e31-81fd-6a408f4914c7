package com.fh.cloud.screen.service.utils;

import com.fh.cloud.screen.websocekt.api.message.entity.bo.WsSendBo;

import java.util.List;

/**
 * 消息工具类，用于封装消息调用MessageApi
 *
 * <AUTHOR>
 * @date 2022/5/9 16:23
 */
public class MessageUtil {

    /**
     * 生产WsSendBo对象
     *
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @param msg the msg
     * @return ws send bo
     * <AUTHOR>
     * @date 2022 -05-09 16:28:25
     */
    public static WsSendBo produceWsSendBo(Long organizationId, List<String> deviceNumbers, Object msg) {
        WsSendBo wsSendBo = new WsSendBo();
        wsSendBo.setRoomId(String.valueOf(organizationId));
        wsSendBo.setClientIds(deviceNumbers);
        wsSendBo.setValue(msg);
        return wsSendBo;
    }
}
