package com.fh.cloud.screen.service.leaveschool.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.lang.UUID;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceMapper;
import com.fh.cloud.screen.service.enums.LeaveSchoolGradeType;
import com.fh.cloud.screen.service.enums.LeaveSchoolWeekType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.enums.SpaceGroupUseType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDeviceConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolBroadcastInfoDto;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDetailDto;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDeviceDto;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDto;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolConfigMapper;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolBroadcastInfoService;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDetailService;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigDeviceService;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolConfigService;
import com.fh.cloud.screen.service.space.entity.vo.ClazzInfoVo;
import com.fh.cloud.screen.service.tts.service.TtsService;
import com.google.common.collect.Maps;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.user.clazz.entity.vo.ClazzVo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;

/**
 * 放学配置表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:11:40
 */
@Service
public class LeaveSchoolConfigServiceImpl extends ServiceImpl<LeaveSchoolConfigMapper, LeaveSchoolConfigDto>
    implements ILeaveSchoolConfigService {

    @Resource
    private LeaveSchoolConfigMapper leaveSchoolConfigMapper;
    @Lazy
    @Resource
    private ILeaveSchoolConfigDetailService leaveSchoolConfigDetailService;
    @Lazy
    @Resource
    private ILeaveSchoolConfigDeviceService leaveSchoolConfigDeviceService;
    @Resource
    private ILeaveSchoolBroadcastInfoService leaveSchoolBroadcastInfoService;
    @Resource
    private BaseDataService baseDataService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ShowDeviceMapper showDeviceMapper;
    @Resource
    private TtsService ttsService;

    @Value("${leave.school.broadcast.prefix:正在放学}")
    private String leaveSchoolBroadcastSuffix;
    @Value("${leave.school.broadcast.play.times:3}")
    private String leaveSchoolBroadcastPlayTimes;

    @Override
    public List<LeaveSchoolConfigVo> getLeaveSchoolConfigListByCondition(LeaveSchoolConfigConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolConfigMapper.getLeaveSchoolConfigListByCondition(condition);
    }

    @Override
    public AjaxResult addLeaveSchoolConfig(LeaveSchoolConfigBo leaveSchoolConfigBo) {
        LeaveSchoolConfigDto leaveSchoolConfig = new LeaveSchoolConfigDto();
        BeanUtils.copyProperties(leaveSchoolConfigBo, leaveSchoolConfig);
        leaveSchoolConfig.setIsDelete(StatusEnum.NOTDELETE.getCode());
        boolean saveSuccess = save(leaveSchoolConfig);

        if (saveSuccess) {
            // 年级星期配置
            if (CollectionUtil.isNotEmpty(leaveSchoolConfigBo.getConfigDetailList())) {
                List<LeaveSchoolConfigDetailDto> configDetailDtos =
                    leaveSchoolConfigBo.getConfigDetailList().stream().map(c -> {
                        LeaveSchoolConfigDetailDto configDetailDto = new LeaveSchoolConfigDetailDto();
                        BeanUtils.copyProperties(c, configDetailDto);
                        configDetailDto.setLeaveSchoolConfigId(leaveSchoolConfig.getLeaveSchoolConfigId());
                        configDetailDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
                        return configDetailDto;
                    }).collect(Collectors.toList());
                leaveSchoolConfigDetailService.saveBatch(configDetailDtos);
            }
            // 设备配置
            if (CollectionUtil.isNotEmpty(leaveSchoolConfigBo.getDeviceList())) {
                // 设备信息插入
                List<LeaveSchoolConfigDeviceDto> deviceDtos = leaveSchoolConfigBo.getDeviceList().stream().map(d -> {
                    LeaveSchoolConfigDeviceDto deviceDto = new LeaveSchoolConfigDeviceDto();
                    BeanUtils.copyProperties(d, deviceDto);
                    deviceDto.setLeaveSchoolConfigId(leaveSchoolConfig.getLeaveSchoolConfigId());
                    deviceDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
                    return deviceDto;
                }).collect(Collectors.toList());
                leaveSchoolConfigDeviceService.saveBatch(deviceDtos);
                // 播报信息插入
                saveOrUpdateLeaveSchoolBroadcast(leaveSchoolConfigBo);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public AjaxResult updateLeaveSchoolConfig(LeaveSchoolConfigBo leaveSchoolConfigBo) {
        LeaveSchoolConfigConditionBo conditionBo = new LeaveSchoolConfigConditionBo();
        conditionBo.setLeaveSchoolConfigId(leaveSchoolConfigBo.getLeaveSchoolConfigId());
        LeaveSchoolConfigVo configVo = getLeaveSchoolConfigByCondition(conditionBo);

        LeaveSchoolConfigDto leaveSchoolConfig = new LeaveSchoolConfigDto();
        BeanUtils.copyProperties(leaveSchoolConfigBo, leaveSchoolConfig);
        if (updateById(leaveSchoolConfig)) {
            leaveSchoolConfigDetailService.deleteAndAddLeaveSchoolConfigDetailList(
                leaveSchoolConfig.getLeaveSchoolConfigId(), leaveSchoolConfigBo.getConfigDetailList());
            leaveSchoolConfigDeviceService.deleteAndAddLeaveSchoolConfigDeviceList(
                leaveSchoolConfig.getLeaveSchoolConfigId(), leaveSchoolConfigBo.getDeviceList());
            // 播报信息处理
            saveOrUpdateLeaveSchoolBroadcast(leaveSchoolConfigBo);

            Set<String> grades = new HashSet<>();
            Set<Long> showDeviceIds = getChangeShowDeviceIds(leaveSchoolConfigBo, configVo);
            boolean gradeCheck = checkLeaveSchoolConfigDetail(leaveSchoolConfigBo, configVo, grades);
            // 推送给设备告知变更信息
            if (gradeCheck || CollectionUtil.isNotEmpty(showDeviceIds)) {
                publishEventByLeaveSchoolModify(leaveSchoolConfigBo.getOrganizationId(),
                    leaveSchoolConfigBo.getLeaveSchoolConfigId(), leaveSchoolConfigBo.getCampusId(), grades, gradeCheck,
                    showDeviceIds);
            }
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    /**
     * 播报信息处理-临时处理，后期再重构到对应的service里面
     *
     * @param leaveSchoolConfigBo the leave school config bo
     * <AUTHOR>
     * @date 2023 -11-02 18:04:47
     */
    private void saveOrUpdateLeaveSchoolBroadcast(LeaveSchoolConfigBo leaveSchoolConfigBo) {
        // 查询数据库已有的播报信息
        LeaveSchoolBroadcastInfoConditionBo leaveSchoolBroadcastInfoConditionBo =
            new LeaveSchoolBroadcastInfoConditionBo();
        leaveSchoolBroadcastInfoConditionBo.setOrganizationId(leaveSchoolConfigBo.getOrganizationId());
        leaveSchoolBroadcastInfoConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<LeaveSchoolBroadcastInfoVo> leaveSchoolBroadcastInfoListVos = leaveSchoolBroadcastInfoService
            .getLeaveSchoolBroadcastInfoListByCondition(leaveSchoolBroadcastInfoConditionBo);
        Map<String, LeaveSchoolBroadcastInfoVo> leaveSchoolBroadcastInfoVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(leaveSchoolBroadcastInfoListVos)) {
            leaveSchoolBroadcastInfoVoMap = leaveSchoolBroadcastInfoListVos.stream().filter(
                leaveSchoolBroadcastInfoVo -> StringUtils.isNotBlank(leaveSchoolBroadcastInfoVo.getBroadcastContent()))
                .collect(Collectors.toMap(LeaveSchoolBroadcastInfoVo::getBroadcastContent, a -> a, (k1, k2) -> k1));
        }
        Map<String, LeaveSchoolBroadcastInfoVo> leaveSchoolBroadcastInfoVoMapFinal = leaveSchoolBroadcastInfoVoMap;

        // 查询该校所有班级
        ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
        clazzConditionBoExt.setOrganizationId(leaveSchoolConfigBo.getOrganizationId());
        clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
        clazzConditionBoExt.setIsDelete(StatusEnum.NOTDELETE.getCode());
        AjaxResult<List<ClazzInfoVo>> ajaxResult = baseDataService.getClazzListWithoutDataAuthority(clazzConditionBoExt);
        List<ClazzInfoVo> clazzInfoVoList = ajaxResult.getData();
        if (ajaxResult.isFail() || CollectionUtil.isEmpty(clazzInfoVoList)) {
            return;
        }

        List<LeaveSchoolBroadcastInfoDto> broadcastInfoDtos = new ArrayList<>();
        for (ClazzInfoVo clazzInfoVo : clazzInfoVoList) {
            LeaveSchoolBroadcastInfoDto broadcastInfoDto = new LeaveSchoolBroadcastInfoDto();
            String contentKey = clazzInfoVo.getClassesNameShow().concat(leaveSchoolBroadcastSuffix);
            broadcastInfoDto.setOrganizationId(leaveSchoolConfigBo.getOrganizationId());
            broadcastInfoDto.setBroadcastContent(contentKey);
            broadcastInfoDto.setPlayTimes(Integer.parseInt(leaveSchoolBroadcastPlayTimes));
            broadcastInfoDto.setIsDelete(StatusEnum.NOTDELETE.getCode());
            if (leaveSchoolBroadcastInfoVoMapFinal.containsKey(contentKey)) {
                if (StringUtils.isBlank(leaveSchoolBroadcastInfoVoMapFinal.get(contentKey).getBroadcastUrl())) {
                    broadcastInfoDto
                        .setBroadcastInfoId(leaveSchoolBroadcastInfoVoMapFinal.get(contentKey).getBroadcastInfoId());
                    String audioUrl = ttsService.buguTextToVoiceUrl(broadcastInfoDto.getBroadcastContent());
                    String fileName = UUID.fastUUID().toString(true) + ".mp3";
                    AttachmentVo attachmentVo = ttsService.saveOnlineFile(audioUrl, fileName);
                    if (attachmentVo != null) {
                        broadcastInfoDto.setBroadcastUrl(attachmentVo.getPreviewAllPath());
                        broadcastInfoDto.setBroadcastId(attachmentVo.getFileOid());
                    }
                    broadcastInfoDtos.add(broadcastInfoDto);
                }
            } else {
                String audioUrl = ttsService.buguTextToVoiceUrl(broadcastInfoDto.getBroadcastContent());
                String fileName = UUID.fastUUID().toString(true) + ".mp3";
                AttachmentVo attachmentVo = ttsService.saveOnlineFile(audioUrl, fileName);
                if (attachmentVo != null) {
                    broadcastInfoDto.setBroadcastUrl(attachmentVo.getPreviewAllPath());
                    broadcastInfoDto.setBroadcastId(attachmentVo.getFileOid());
                }
                broadcastInfoDtos.add(broadcastInfoDto);
            }
        }
        if (CollectionUtils.isNotEmpty(broadcastInfoDtos)) {
            leaveSchoolBroadcastInfoService.saveOrUpdateBatch(broadcastInfoDtos);
        }
    }

    /**
     * 获取改动的设备id
     *
     * @param bo
     * @param vo
     * @return java.util.Set<java.lang.Long>
     * <AUTHOR>
     * @date 2023/8/30 17:44
     **/
    private Set<Long> getChangeShowDeviceIds(LeaveSchoolConfigBo bo, LeaveSchoolConfigVo vo) {
        Set<Long> showDeviceIds = new HashSet<>();
        List<Long> lastShowDeviceIds =
            vo.getDeviceList().stream().map(LeaveSchoolConfigDeviceVo::getShowDeviceId).collect(Collectors.toList());
        List<Long> newShowDeviceIds =
            bo.getDeviceList().stream().map(LeaveSchoolConfigDeviceBo::getShowDeviceId).collect(Collectors.toList());
        showDeviceIds
            .addAll(lastShowDeviceIds.stream().filter(d -> !newShowDeviceIds.contains(d)).collect(Collectors.toList()));
        showDeviceIds
            .addAll(newShowDeviceIds.stream().filter(d -> !lastShowDeviceIds.contains(d)).collect(Collectors.toList()));
        return showDeviceIds;
    }

    /**
     * 校验放学配置修改 true：修改 false：未修改
     *
     * @param bo
     * @param vo
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @date 2023/8/30 10:26
     **/
    private boolean checkLeaveSchoolConfigDetail(LeaveSchoolConfigBo bo, LeaveSchoolConfigVo vo, Set<String> grades) {
        for (LeaveSchoolConfigDetailVo detailVo : vo.getConfigDetailList()) {
            for (LeaveSchoolConfigDetailBo detailBo : bo.getConfigDetailList()) {
                // 年级为空的情况
                if (StringUtils.isBlank(detailBo.getGrade()) && StringUtils.isNotBlank(detailVo.getGrade())) {
                    if (!detailBo.getLeaveSchoolStartTime().equals(detailVo.getLeaveSchoolStartTime())
                        || !detailBo.getLeaveSchoolEndTime().equals(detailVo.getLeaveSchoolEndTime())) {
                        grades.add(detailBo.getGrade());
                    }
                }
                if (StringUtils.isBlank(detailVo.getGrade()) && StringUtils.isNotBlank(detailBo.getGrade())) {
                    if (!detailBo.getLeaveSchoolStartTime().equals(detailVo.getLeaveSchoolStartTime())
                        || !detailBo.getLeaveSchoolEndTime().equals(detailVo.getLeaveSchoolEndTime())) {
                        grades.add(detailVo.getGrade());
                    }
                }
                if (StringUtils.isBlank(detailBo.getGrade()) && StringUtils.isBlank(detailVo.getGrade())) {
                    if (!detailBo.getLeaveSchoolStartTime().equals(detailVo.getLeaveSchoolStartTime())
                        || !detailBo.getLeaveSchoolEndTime().equals(detailVo.getLeaveSchoolEndTime())) {
                        grades.clear();
                        return true;
                    }
                }
            }
        }
        if (CollectionUtil.isNotEmpty(grades)) {
            return true;
        }
        return false;
    }

    @Override
    public LeaveSchoolConfigVo getLeaveSchoolConfigByCondition(LeaveSchoolConfigConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        LeaveSchoolConfigVo vo = leaveSchoolConfigMapper.getLeaveSchoolConfigByCondition(condition);
        if (vo == null) {
            return null;
        }

        // 查询配置详情
        LeaveSchoolConfigDetailConditionBo detailConditionBo = new LeaveSchoolConfigDetailConditionBo();
        detailConditionBo.setLeaveSchoolConfigId(vo.getLeaveSchoolConfigId());
        List<LeaveSchoolConfigDetailVo> configDetailVos =
            leaveSchoolConfigDetailService.getLeaveSchoolConfigDetailListByCondition(detailConditionBo);
        vo.setConfigDetailList(configDetailVos);

        // 查询设备列表
        LeaveSchoolConfigDeviceConditionBo deviceConditionBo = new LeaveSchoolConfigDeviceConditionBo();
        deviceConditionBo.setLeaveSchoolConfigId(vo.getLeaveSchoolConfigId());
        List<LeaveSchoolConfigDeviceVo> deviceVos =
            leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceList(deviceConditionBo);
        vo.setDeviceList(deviceVos);

        return vo;
    }

    @Override
    public LeaveSchoolConfigVo getLeaveSchoolConfigBySpaceInfo(Long organizationId, Long spaceInfoId,
        Integer spaceGroupUseType, Date nowDay, Long showDeviceId) {
        LeaveSchoolConfigVo vo =
            baseMapper.getLeaveSchoolConfigBySpaceInfo(organizationId, spaceInfoId, spaceGroupUseType, showDeviceId);
        if (vo == null) {
            return null;
        }

        LeaveSchoolConfigDetailConditionBo conditionBo = new LeaveSchoolConfigDetailConditionBo();
        conditionBo.setLeaveSchoolConfigId(vo.getLeaveSchoolConfigId());
        if (LeaveSchoolWeekType.DIFFERENT.getCode().equals(vo.getLeaveSchoolWeekType())) {
            conditionBo.setWeekDay(Long.parseLong(DateUtil.dayOfWeek(nowDay) + ""));
        }
        if (LeaveSchoolGradeType.DIFFERENT.getCode().equals(vo.getLeaveSchoolGradeType())) {
            // 根据spaceInfo和spaceUseType获取区域信息
            // 本期只考虑行政班级的情况
            if (SpaceGroupUseType.XZ.getValue() == spaceGroupUseType) {
                ClazzVo clazzVo = baseDataService.getByClazzId(spaceInfoId);
                conditionBo.setGrade(clazzVo.getGrade());
            } else { // todo 暂不考虑非行政班级的情况

            }
        }
        vo.setConfigDetailList(leaveSchoolConfigDetailService.getLeaveSchoolConfigDetailListByCondition(conditionBo));

        return vo;

    }

    /**
     * 放学修改事件推送云屏app 注：推送类型为会议修改，当前放学场景变更推送和会议变更云屏操作一致，复用消息类型，后期再考虑调整
     *
     * @param organizationId, spaceInfoId, grades
     * @return void
     * <AUTHOR>
     * @date 2022/8/26 9:49
     */
    private void publishEventByLeaveSchoolModify(Long organizationId, Long leaveSchoolConfigId, Long campusId,
        Set<String> grades, boolean gradeCheck, Set<Long> showDeviceIds) {
        // 查询学校绑定设备
        List<LeaveSchoolConfigDeviceVo> deviceVos = new ArrayList<>();
        if (gradeCheck) {// 年级数据做了调整，获取设备
            if (CollectionUtil.isEmpty(grades)) {
                LeaveSchoolConfigDeviceConditionBo conditionBo = new LeaveSchoolConfigDeviceConditionBo();
                conditionBo.setLeaveSchoolConfigId(leaveSchoolConfigId);
                deviceVos = leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceList(conditionBo);
            } else { // 根据年级查询绑定设备
                ClazzConditionBoExt clazzConditionBoExt = new ClazzConditionBoExt();
                clazzConditionBoExt.setOrganizationId(organizationId);
                clazzConditionBoExt.setCampusId(campusId);
                clazzConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
                AjaxResult<List<ClazzInfoVo>> ajaxResult = baseDataService.getClazzListWithoutDataAuthority(clazzConditionBoExt);
                List<ClazzInfoVo> list = ajaxResult.getData();
                if (ajaxResult.isFail() || CollectionUtil.isEmpty(list)) {
                    deviceVos = new ArrayList<>();
                } else {
                    LeaveSchoolConfigDeviceConditionBo conditionBo = new LeaveSchoolConfigDeviceConditionBo();
                    conditionBo.setLeaveSchoolConfigId(leaveSchoolConfigId);
                    conditionBo.setSpaceInfoIds(list.stream().map(ClazzInfoVo::getId).collect(Collectors.toList()));
                    conditionBo.setSpaceGroupUseType(SpaceGroupUseType.XZ.getValue());
                    deviceVos = leaveSchoolConfigDeviceService.getLeaveSchoolConfigDeviceList(conditionBo);
                }
            }
        }
        List<ShowDeviceVo> showDeviceVos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(showDeviceIds)) {
            showDeviceVos = showDeviceMapper.listShowDeviceDataByIds(new ArrayList<>(showDeviceIds));
        }
        // 地点绑定的设备不为空
        List<String> deviceNumbers = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(deviceVos)) {
            deviceNumbers.addAll(
                deviceVos.stream().map(LeaveSchoolConfigDeviceVo::getDeviceNumber).collect(Collectors.toList()));
        }
        if (CollectionUtil.isNotEmpty(showDeviceVos)) {
            deviceNumbers
                .addAll(showDeviceVos.stream().map(ShowDeviceVo::getDeviceNumber).collect(Collectors.toList()));
        }
        // 去重
        deviceNumbers = new ArrayList<>(new HashSet<>(deviceNumbers));
        if (CollectionUtils.isNotEmpty(deviceNumbers)) {
            applicationContext.publishEvent(PublishEvent
                .produceMeetingPublishEvent(MessageWsType.MODIFY_MEETING.getValue(), organizationId, deviceNumbers));
        }
    }

}