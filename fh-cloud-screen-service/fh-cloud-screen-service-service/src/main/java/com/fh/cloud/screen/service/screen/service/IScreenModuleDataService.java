package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleData;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo;

import java.util.List;

/**
 * 云屏模块表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface IScreenModuleDataService extends IService<ScreenModuleData> {

    /**
     * 查询模块列表（如果根据场景查询请勿使用这个查询方式）
     * 
     * @param condition
     * @return
     */
    List<ScreenModuleDataVo> getScreenModuleDataListByCondition(ScreenModuleDataListConditionBo condition);

    boolean addScreenModuleData(ScreenModuleDataBo screenModuleDataBo);

    boolean updateScreenModuleData(ScreenModuleDataBo screenModuleDataBo);

    ScreenModuleDataVo getDetail(Long screenModuleDataId);

    /**
     * 初始化学校的模块数据（从模块库获取预置模块插入）
     *
     * @param organizationId
     */
    void initScreenModuleData(Long organizationId);

    /**
     * 学校模块新增或移除预置模块
     *
     * @param screenModuleDataBo
     */
    void updatePresetModule(ScreenModuleDataBo screenModuleDataBo);

    /**
     * 修改数据的删除属性
     *
     * @param screenModuleDataIds
     * @param isDelete
     */
    void updateDelete(List<Long> screenModuleDataIds, Integer isDelete);

    /**
     * 根据场景id集合查询模块信息。
     *
     * @param screenSceneIds the screen scene ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-14 11:23:07
     */
    List<ScreenModuleDataVo> listByScreenSceneIds(List<Long> screenSceneIds);

    /**
     * 检测自定义模块名称在当前学校的当前模块组下面是否存在:存在返回true
     *
     * @return boolean
     * <AUTHOR>
     * @date 2022 -07-04 14:28:28
     */
    boolean checkCustomModuleNameExist(ScreenModuleDataBo screenModuleDataBo);

    /**
     * 根据screenModuleLibraryId获取screenModuleDataId
     *
     * @param screenModuleLibraryId the screen module library id
     * @param organizationId the organization id
     * @return screen module data id by screen module library id
     * <AUTHOR>
     * @date 2023 -01-16 10:31:49
     */
    Long getScreenModuleDataIdByScreenModuleLibraryId(Long screenModuleLibraryId, Long organizationId);
}
