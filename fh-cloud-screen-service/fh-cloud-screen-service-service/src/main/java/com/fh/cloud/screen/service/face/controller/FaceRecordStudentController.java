package com.fh.cloud.screen.service.face.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fh.cloud.screen.service.enums.FaceBrandType;
import com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;
import com.fh.cloud.screen.service.facebody.FaceBodyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsLong;
import com.fh.cloud.screen.service.enums.FaceStatusType;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.event.PublishEvent;
import com.fh.cloud.screen.service.face.api.FaceRecordStudentApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceCountQueryBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.dto.FaceRecordStudentDto;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.fh.cloud.screen.service.face.service.IFaceRecordStudentService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.student.entity.bo.StudentConditionBo;
import com.light.user.student.entity.vo.StudentVo;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 学生人脸库
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-18 14:16:36
 */
@RestController
@Validated
public class FaceRecordStudentController implements FaceRecordStudentApi {

    @Autowired
    private IFaceRecordStudentService faceRecordStudentService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private IFaceConfigService faceConfigService;
    @Autowired
    private FaceBodyService faceBodyService;

    /**
     * 查询学生人脸库分页列表
     * 
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult<PageInfo<FaceRecordStudentVo>>
        getFaceRecordStudentPageListByCondition(@RequestBody FaceRecordStudentConditionBo condition) {
        if (condition.getClassesId() != null) {
            StudentConditionBo studentConditionBo = new StudentConditionBo();
            studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
            studentConditionBo.setClassesId(condition.getClassesId());
            List<StudentVo> studentVoList = baseDataService.getStudentVoList(studentConditionBo);
            if (CollectionUtils.isNotEmpty(studentVoList)) {
                List<String> userOidsOfClazz =
                    studentVoList.stream().map(StudentVo::getUserOid).collect(Collectors.toList());
                condition.setUserOids(userOidsOfClazz);
            }
        }
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        List<FaceRecordStudentVo> faceRecordStudentVos =
            faceRecordStudentService.getFaceRecordStudentListByCondition(condition);
        convertResult(faceRecordStudentVos);
        PageInfo<FaceRecordStudentVo> pageInfo = new PageInfo<>(faceRecordStudentVos);
        return AjaxResult.success(pageInfo);
    }

    /**
     * 返回值转换
     * 
     * @param faceRecordStudentVos
     */
    private void convertResult(List<FaceRecordStudentVo> faceRecordStudentVos) {
        if (CollectionUtils.isEmpty(faceRecordStudentVos)) {
            return;
        }

        List<String> studentOIds =
            faceRecordStudentVos.stream().map(FaceRecordStudentVo::getUserOid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(studentOIds)) {
            return;
        }

        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        studentConditionBo.setUserOids(studentOIds);
        List<StudentVo> studentVoList = baseDataService.getStudentVoList(studentConditionBo);
        if (studentVoList == null) {
            faceRecordStudentVos = Lists.newArrayList();
            return;
        }
        if (CollectionUtils.isEmpty(studentVoList)) {
            return;
        }
        List<String> existUserOids =
            studentVoList.stream().map(studentVo -> studentVo.getUserOid()).collect(Collectors.toList());
        for (int i = 0; i < faceRecordStudentVos.size(); i++) {
            if (!existUserOids.contains(faceRecordStudentVos.get(i).getUserOid())) {
                faceRecordStudentVos.remove(i);
                i--;
            }
        }
    }

    /**
     * 查询学生人脸库列表
     * 
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult<List<FaceRecordStudentVo>>
        getFaceRecordStudentListByCondition(@RequestBody FaceRecordStudentConditionBo condition) {
        if (condition.getClassesId() != null) {
            StudentConditionBo studentConditionBo = new StudentConditionBo();
            studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
            studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
            studentConditionBo.setClassesId(condition.getClassesId());
            List<StudentVo> studentVoList = baseDataService.getStudentVoList(studentConditionBo);
            if (CollectionUtils.isNotEmpty(studentVoList)) {
                List<String> userOidsOfClazz =
                    studentVoList.stream().map(StudentVo::getUserOid).collect(Collectors.toList());
                condition.setUserOids(userOidsOfClazz);
            }
        }

        List<FaceRecordStudentVo> faceRecordStudentVos =
            faceRecordStudentService.getFaceRecordStudentListByCondition(condition);
        convertResult(faceRecordStudentVos);
        return AjaxResult.success(faceRecordStudentVos);
    }

    /**
     * 新增学生人脸库
     * 
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult addFaceRecordStudent(@Validated @RequestBody FaceRecordStudentBo faceRecordStudentBo) {
        return faceRecordStudentService.addFaceRecordStudent(faceRecordStudentBo);
    }

    /**
     * 修改学生人脸库
     * 
     * @param faceRecordStudentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult updateFaceRecordStudent(@Validated @RequestBody FaceRecordStudentBo faceRecordStudentBo) {
        if (null == faceRecordStudentBo.getFaceRecordStudentId()) {
            return AjaxResult.fail("学生人脸库id不能为空");
        }
        return faceRecordStudentService.updateFaceRecordStudent(faceRecordStudentBo);
    }

    /**
     * 批量修改学生人脸信息
     *
     * @param faceRecordStudentBoList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/1 14:19
     */
    @Override
    public AjaxResult updateFaceRecordStudentBatch(@RequestBody List<FaceRecordStudentBo> faceRecordStudentBoList) {
        if (CollectionUtil.isEmpty(faceRecordStudentBoList)) {
            return AjaxResult.fail("学生人脸库信息不能为空");
        }
        return faceRecordStudentService.updateFaceRecordStudentBatch(faceRecordStudentBoList);
    }

    /**
     * 查询学生人脸库详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult<FaceRecordStudentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("学生人脸库id不能为空");
        }
        FaceRecordStudentVo vo = faceRecordStudentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除学生人脸库
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-11-18 14:16:36
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        FaceRecordStudentDto faceRecordStudentDto = new FaceRecordStudentDto();
        faceRecordStudentDto.setFaceRecordStudentId(id);
        faceRecordStudentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean result = faceRecordStudentService.updateById(faceRecordStudentDto);
        if (result) {
            // 如果使用的是阿里云则删除阿里云人脸库
            FaceRecordStudentVo detail = faceRecordStudentService.getDetail(id);
            FaceConfigVo faceConfigVo = faceConfigService.getDetailByOrganizationId(detail.getOrganizationId());
            if (faceConfigVo != null && faceConfigVo.getFaceBrandType().equals(FaceBrandType.ALI.getValue())) {
                faceBodyService.delFaceTransaction(detail.getUserOid());
            }
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult<PageInfo<FaceRecordStudentVo>>
        getFaceRecordStudentPageListByConditionWithUser(FaceRecordStudentConditionBo condition) {
        List<FaceRecordStudentVo> faceRecordStudentListByCondition = null;
        List<String> userOids = null;
        if (condition.getFaceStatus() != null) {
            // 查询并设置人脸数据封装返回
            FaceRecordStudentConditionBo faceRecordStudentConditionBo = new FaceRecordStudentConditionBo();
            faceRecordStudentConditionBo.setPageNo(SystemConstants.NO_PAGE);
            faceRecordStudentConditionBo.setOrganizationId(condition.getOrganizationId());
            if (!condition.getFaceStatus().equals(FaceStatusType.NOT_UPLOAD.getValue())) {
                faceRecordStudentConditionBo.setFaceStatus(condition.getFaceStatus());
            }
            faceRecordStudentConditionBo.setUserOids(userOids);
            faceRecordStudentListByCondition =
                faceRecordStudentService.getFaceRecordStudentListByCondition(faceRecordStudentConditionBo);
            userOids = faceRecordStudentListByCondition.stream()
                .filter(faceRecordStudentVo -> StringUtils.isNotBlank(faceRecordStudentVo.getUserOid()))
                .map(FaceRecordStudentVo::getUserOid).collect(Collectors.toList());

            if (!condition.getFaceStatus().equals(FaceStatusType.NOT_UPLOAD.getValue())
                && CollectionUtils.isEmpty(userOids)) {
                PageInfo<FaceRecordStudentVo> pageInfo = new PageInfo<>();
                pageInfo.setList(Lists.newArrayList());
                pageInfo.setTotal(ConstantsLong.NUM_0);
                return AjaxResult.success(pageInfo);
            }
        }

        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setPageNo(condition.getPageNo());
        studentConditionBo.setPageSize(condition.getPageSize());
        studentConditionBo.setRealName(condition.getRealName());
        studentConditionBo.setOrganizationId(condition.getOrganizationId());
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setClassesId(condition.getClassesId());
        if (CollectionUtils.isNotEmpty(userOids) && condition.getFaceStatus() != null) {
            if (condition.getFaceStatus().equals(FaceStatusType.NOT_UPLOAD.getValue())) {
                studentConditionBo.setNotUserOids(userOids);
            } else {
                studentConditionBo.setUserOids(userOids);
            }
        }
        Map<String, Object> studentListByCondition = baseDataService.getStudentListByCondition(studentConditionBo);
        if (studentListByCondition == null || studentListByCondition.get("list") == null) {
            return AjaxResult.success();
        }
        List<StudentVo> studentVoList = (List<StudentVo>)studentListByCondition.get("list");
        Integer total = (Integer)studentListByCondition.get("total");
        if (CollectionUtils.isEmpty(studentVoList)) {
            return AjaxResult.success();
        }
        // 设置重名手机号
        List<String> realNames = studentVoList.stream().filter(studentVo -> studentVo.getUserVo() != null)
            .collect(Collectors.toMap(studentVo -> studentVo.getUserVo().getRealName(), e -> 1, Integer::sum))
            .entrySet().stream().filter(e -> e.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        studentVoList.forEach(studentVo -> {
            if (studentVo.getUserVo() != null && realNames.contains(studentVo.getUserVo().getRealName())) {
                if (StringUtils.isNotBlank(studentVo.getUserVo().getIdentityCardNumber())) {
                    studentVo.setIdentityCardNumber(
                        StringUtils.right(studentVo.getUserVo().getIdentityCardNumber(), ConstantsInteger.NUM_4));
                }
            } else {
                studentVo.setIdentityCardNumber(null);
            }
        });
        List<String> userOidsOfAll =
            studentVoList.stream().filter(studentVo -> StringUtils.isNotBlank(studentVo.getUserOid()))
                .map(StudentVo::getUserOid).collect(Collectors.toList());

        // 查询并设置人脸数据封装返回
        if (CollectionUtils.isEmpty(faceRecordStudentListByCondition)) {
            FaceRecordStudentConditionBo faceRecordStudentConditionBo = new FaceRecordStudentConditionBo();
            faceRecordStudentConditionBo.setPageNo(SystemConstants.NO_PAGE);
            faceRecordStudentConditionBo.setOrganizationId(condition.getOrganizationId());
            faceRecordStudentConditionBo.setFaceStatus(condition.getFaceStatus());
            faceRecordStudentConditionBo.setUserOids(userOidsOfAll);
            faceRecordStudentListByCondition =
                faceRecordStudentService.getFaceRecordStudentListByCondition(faceRecordStudentConditionBo);
        }
        // userOid,FaceRecordStudentVo
        Map<String, FaceRecordStudentVo> faceRecordStudentVoMap = faceRecordStudentListByCondition.stream()
            .collect(Collectors.toMap(FaceRecordStudentVo::getUserOid, a -> a, (k1, k2) -> k1));

        List<FaceRecordStudentVo> faceRecordStudentVos = studentVoList.stream().map(studentVo -> {
            FaceRecordStudentVo faceRecordStudentVo = new FaceRecordStudentVo();
            faceRecordStudentVo.setRealName(studentVo.getUserVo().getRealName());
            faceRecordStudentVo.setIdentityCardNumber(studentVo.getIdentityCardNumber());
            faceRecordStudentVo.setUserOid(studentVo.getUserOid());
            if (faceRecordStudentVoMap.containsKey(studentVo.getUserOid())) {
                FaceRecordStudentVo faceRecordStudentVoTemp = faceRecordStudentVoMap.get(studentVo.getUserOid());
                faceRecordStudentVo.setFaceStatus(faceRecordStudentVoTemp.getFaceStatus());
                faceRecordStudentVo.setTipMessage(faceRecordStudentVoTemp.getTipMessage());
                faceRecordStudentVo.setFaceMediaName(faceRecordStudentVoTemp.getFaceMediaName());
                faceRecordStudentVo.setFaceMediaUrl(faceRecordStudentVoTemp.getFaceMediaUrl());
                faceRecordStudentVo.setFaceMediaNameOri(faceRecordStudentVoTemp.getFaceMediaNameOri());
                faceRecordStudentVo.setFaceMediaUrlCompress(faceRecordStudentVoTemp.getFaceMediaUrlCompress());
                faceRecordStudentVo.setFaceRecordStudentId(faceRecordStudentVoTemp.getFaceRecordStudentId());
            }
            return faceRecordStudentVo;
        }).collect(Collectors.toList());
        PageInfo<FaceRecordStudentVo> pageInfo = new PageInfo<>();
        pageInfo.setList(faceRecordStudentVos);
        pageInfo.setTotal(total);
        return AjaxResult.success(pageInfo);
    }

    @Override
    public AjaxResult upload(List<FaceRecordStudentConditionBo> faceRecordStudentConditionBos) {
        if (CollectionUtils.isEmpty(faceRecordStudentConditionBos)) {
            return AjaxResult.success();
        }

        // 导入
        AjaxResult ajaxResult = faceRecordStudentService.upload(faceRecordStudentConditionBos);
        if (ajaxResult.isSuccess()) {
            Long organizationId = faceRecordStudentConditionBos.get(0).getOrganizationId();
            Long classesId = faceRecordStudentConditionBos.get(0).getClassesId();
            // 虹软人脸识别学生不按照班级建模
            Integer faceBrandType = null;
            FaceConfigVo detailByOrganizationId = faceConfigService.getDetailByOrganizationId(organizationId);
            if (detailByOrganizationId != null) {
                faceBrandType = detailByOrganizationId.getFaceBrandType();
            }
            if (faceBrandType != null && faceBrandType.equals(FaceBrandType.ARCSOFT.getValue())) {
                classesId = null;
            }
            // 推送给云屏
            applicationContext.publishEvent(PublishEvent.produceStudentPublishEvent(
                MessageWsType.MODIFY_FACE_STUDENT.getValue(), organizationId, classesId, null));
            return ajaxResult;
        }
        return AjaxResult.fail();
    }

    @Override
    public AjaxResult<Map<Integer, Integer>> count(FaceCountQueryBo faceCountQueryBo) {
        // 班级学生总数
        List<StudentVo> studentVos = baseDataService.getStudentVoListByClassesId(faceCountQueryBo.getClassesId());
        Integer totalCount = studentVos.size();
        List<String> userOids = studentVos.stream().filter(studentVo -> StringUtils.isNotBlank(studentVo.getUserOid()))
            .distinct().map(StudentVo::getUserOid).collect(Collectors.toList());

        FaceRecordStudentConditionBo faceRecordStudentConditionBo = new FaceRecordStudentConditionBo();
        faceRecordStudentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        faceRecordStudentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        faceRecordStudentConditionBo.setOrganizationId(faceCountQueryBo.getOrganizationId());
        faceRecordStudentConditionBo.setUserOids(userOids);
        faceRecordStudentConditionBo.setGroupByUserOid("groupByUserOid");
        List<FaceRecordStudentVo> faceRecordStudentVos =
            faceRecordStudentService.getFaceRecordStudentListByCondition(faceRecordStudentConditionBo);
        // faceStatus,数量。未上传人数需要前端减法
        Map<Integer, Integer> faceStudentCountMap = faceRecordStudentVos.stream().collect(
            Collectors.toMap(faceRecordStudentVo -> faceRecordStudentVo.getFaceStatus(), e -> 1, Integer::sum));
        Integer uploadCount = faceStudentCountMap.values().stream().reduce(0, Integer::sum);

        faceStudentCountMap.put(FaceStatusType.NOT_UPLOAD.getValue(), totalCount - uploadCount.intValue());
        faceStudentCountMap.put(FaceStatusType.TOTAL.getValue(), totalCount);
        return AjaxResult.success(faceStudentCountMap);
    }

    @Override
    public AjaxResult<Map<Integer, Integer>> overview(FaceCountQueryBo faceCountQueryBo) {
        // 数据查询
        StudentConditionBo studentConditionBo = new StudentConditionBo();
        studentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        studentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        studentConditionBo.setGrade(faceCountQueryBo.getGrade());
        if (StringUtils.isNotBlank(studentConditionBo.getGrade())) {
            studentConditionBo.setGrade(studentConditionBo.getGrade());
        }
        studentConditionBo.setOrganizationId(faceCountQueryBo.getOrganizationId());
        List<StudentVo> studentVos = baseDataService.getStudentVoList(studentConditionBo);
        Integer totalCount = studentVos.size();
        List<String> userOids = studentVos.stream().filter(studentVo -> StringUtils.isNotBlank(studentVo.getUserOid()))
            .distinct().map(StudentVo::getUserOid).collect(Collectors.toList());

        FaceRecordStudentConditionBo faceRecordStudentConditionBo = new FaceRecordStudentConditionBo();
        faceRecordStudentConditionBo.setPageNo(SystemConstants.NO_PAGE);
        faceRecordStudentConditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        faceRecordStudentConditionBo.setOrganizationId(faceCountQueryBo.getOrganizationId());
        faceRecordStudentConditionBo.setUserOids(userOids);
        faceRecordStudentConditionBo.setGroupByUserOid("groupByUserOid");
        List<FaceRecordStudentVo> faceRecordStudentVos =
            faceRecordStudentService.getFaceRecordStudentListByCondition(faceRecordStudentConditionBo);
        // faceStatus,数量。未上传人数需要前端减法
        Map<Integer, Integer> faceStudentCountMap = faceRecordStudentVos.stream().collect(
            Collectors.toMap(faceRecordStudentVo -> faceRecordStudentVo.getFaceStatus(), e -> 1, Integer::sum));
        Integer uploadCount = faceStudentCountMap.values().stream().reduce(0, Integer::sum);

        faceStudentCountMap.put(FaceStatusType.NOT_UPLOAD.getValue(), totalCount - uploadCount.intValue());
        faceStudentCountMap.put(FaceStatusType.TOTAL.getValue(), totalCount);
        return AjaxResult.success(faceStudentCountMap);
    }
}
