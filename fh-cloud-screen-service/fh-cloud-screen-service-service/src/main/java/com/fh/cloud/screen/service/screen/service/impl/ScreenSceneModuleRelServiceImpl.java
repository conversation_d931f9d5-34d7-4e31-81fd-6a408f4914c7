package com.fh.cloud.screen.service.screen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneModuleRel;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo;
import com.fh.cloud.screen.service.screen.mapper.ScreenSceneModuleRelMapper;
import com.fh.cloud.screen.service.screen.service.IScreenSceneModuleRelService;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 云屏场景模块关系表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
@Service
public class ScreenSceneModuleRelServiceImpl extends ServiceImpl<ScreenSceneModuleRelMapper, ScreenSceneModuleRel>
    implements IScreenSceneModuleRelService {
    @Resource
    private ScreenSceneModuleRelMapper screenSceneModuleRelMapper;

    @Lazy
    @Autowired
    private IScreenSceneModuleRelService screenSceneModuleRelService;

    @Override
    public List<ScreenSceneModuleRelVo>
        getScreenSceneModuleRelListByCondition(ScreenSceneModuleRelListConditionBo condition) {
        return screenSceneModuleRelMapper.getScreenSceneModuleRelListByCondition(condition);
    }

    @Override
    public boolean addScreenSceneModuleRel(ScreenSceneModuleRelBo screenSceneModuleRelBo) {
        ScreenSceneModuleRel screenSceneModuleRel = new ScreenSceneModuleRel();
        BeanUtils.copyProperties(screenSceneModuleRelBo, screenSceneModuleRel);
        screenSceneModuleRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return save(screenSceneModuleRel);
    }

    @Override
    public boolean updateScreenSceneModuleRel(ScreenSceneModuleRelBo screenSceneModuleRelBo) {
        ScreenSceneModuleRel screenSceneModuleRel = new ScreenSceneModuleRel();
        BeanUtils.copyProperties(screenSceneModuleRelBo, screenSceneModuleRel);
        return updateById(screenSceneModuleRel);
    }

    @Override
    public ScreenSceneModuleRelVo getDetail(Long screenSceneModuleRelId) {
        LambdaQueryWrapper<ScreenSceneModuleRel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenSceneModuleRel::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(ScreenSceneModuleRel::getId, screenSceneModuleRelId);
        ScreenSceneModuleRel screenSceneModuleRel = getOne(lqw);
        ScreenSceneModuleRelVo screenSceneModuleRelVo = new ScreenSceneModuleRelVo();
        BeanUtils.copyProperties(screenSceneModuleRel, screenSceneModuleRelVo);
        return screenSceneModuleRelVo;
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void deleteAndAddScreenSceneModuleRelBatch(Long screenSceneId, List<ScreenModuleDataBo> screenModuleDatas) {
        if (screenSceneId == null) {
            return;
        }
        // 删除
        screenSceneModuleRelService.deleteByScreenSceneId(screenSceneId);

        // 插入
        if (CollectionUtils.isEmpty(screenModuleDatas)) {
            return;
        }
        List<ScreenSceneModuleRel> screenSceneModuleRels = screenModuleDatas.stream().map(screenModuleDataBo -> {
            ScreenSceneModuleRel screenSceneModuleRel = new ScreenSceneModuleRel();
            screenSceneModuleRel.setIsDelete(StatusEnum.NOTDELETE.getCode());
            screenSceneModuleRel.setScreenSceneId(screenSceneId);
            screenSceneModuleRel.setScreenModuleDataId(screenModuleDataBo.getScreenModuleDataId());
            screenSceneModuleRel.setScreenModuleLibraryId(screenModuleDataBo.getScreenModuleLibraryId());
            screenSceneModuleRel.setScreenModuleLibrarySelIds(screenModuleDataBo.getScreenModuleLibrarySelIds());
            // 兼容海报单选多选字段为空
            if (screenSceneModuleRel.getScreenModuleLibraryId() == null) {
                if (StringUtils.isNotBlank(screenModuleDataBo.getScreenModuleLibrarySelIds())
                    && !screenModuleDataBo.getScreenModuleLibrarySelIds().contains(ConstString.ywdh)) {
                    screenSceneModuleRel
                        .setScreenModuleLibraryId(Long.valueOf(screenModuleDataBo.getScreenModuleLibrarySelIds()));
                }
            }
            if (StringUtils.isBlank(screenSceneModuleRel.getScreenModuleLibrarySelIds())) {
                if (screenSceneModuleRel.getScreenModuleLibraryId() != null) {
                    screenSceneModuleRel
                        .setScreenModuleLibrarySelIds(String.valueOf(screenSceneModuleRel.getScreenModuleLibraryId()));
                }
            }
            return screenSceneModuleRel;
        }).collect(Collectors.toList());
        screenSceneModuleRelService.saveBatch(screenSceneModuleRels);
    }

    @Override
    public void deleteByScreenSceneId(Long screenSceneId) {
        if (screenSceneId == null) {
            return;
        }
        UpdateWrapper<ScreenSceneModuleRel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("screen_scene_id", screenSceneId);
        ScreenSceneModuleRel screenSceneModuleRel = new ScreenSceneModuleRel();
        screenSceneModuleRel.setIsDelete(StatusEnum.ISDELETE.getCode());
        screenSceneModuleRelMapper.update(screenSceneModuleRel, updateWrapper);
    }

    @Override
    public List<ScreenSceneModuleRelVo> listByScreenSceneIds(List<Long> screenSceneIds) {
        if (CollectionUtils.isEmpty(screenSceneIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<ScreenSceneModuleRel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ScreenSceneModuleRel::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.in(ScreenSceneModuleRel::getScreenSceneId, screenSceneIds);
        List<ScreenSceneModuleRel> screenSceneModuleRels = list(lqw);
        if (CollectionUtils.isEmpty(screenSceneModuleRels)) {
            return Lists.newArrayList();
        }
        List<ScreenSceneModuleRelVo> screenSceneModuleRelVos =
            screenSceneModuleRels.stream().map(screenSceneModuleRel -> {
                ScreenSceneModuleRelVo screenSceneModuleRelVo = new ScreenSceneModuleRelVo();
                BeanUtils.copyProperties(screenSceneModuleRel, screenSceneModuleRelVo);
                return screenSceneModuleRelVo;
            }).collect(Collectors.toList());
        return screenSceneModuleRelVos;
    }
}