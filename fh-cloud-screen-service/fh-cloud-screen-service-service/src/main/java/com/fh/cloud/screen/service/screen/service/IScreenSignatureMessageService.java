package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureMessageDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 电子签名寄语资源表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-14 15:17:15
 */
public interface IScreenSignatureMessageService extends IService<ScreenSignatureMessageDto> {

    List<ScreenSignatureMessageVo> getScreenSignatureMessageListByCondition(ScreenSignatureMessageConditionBo condition);

	AjaxResult addScreenSignatureMessage(ScreenSignatureMessageBo screenSignatureMessageBo);

	AjaxResult updateScreenSignatureMessage(ScreenSignatureMessageBo screenSignatureMessageBo);

	ScreenSignatureMessageVo getScreenSignatureMessageByCondition(ScreenSignatureMessageConditionBo condition);

}

