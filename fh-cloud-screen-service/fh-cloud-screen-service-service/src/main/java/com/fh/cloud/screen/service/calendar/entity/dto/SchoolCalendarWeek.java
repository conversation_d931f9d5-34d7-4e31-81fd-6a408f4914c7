package com.fh.cloud.screen.service.calendar.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 校历上课日星期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("school_calendar_week")
public class SchoolCalendarWeek implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "school_calendar_week_id", type = IdType.AUTO)
    private Long schoolCalendarWeekId;

    /**
     * FK校历主表主键id
     */
    @TableField("school_calendar_id")
    private Long schoolCalendarId;

    /**
     * 上课类型：1上课、2不上课
     */
    @TableField("type")
    private Integer type;

    /**
     * 星期几：1-7，分别为星期一到星期日
     */
    @TableField("week")
    private Integer week;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
