package com.fh.cloud.screen.service.task.handler;

import com.fh.cloud.screen.service.consts.ConstString;
import com.fh.cloud.screen.service.enums.MessageWsType;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import com.fh.cloud.screen.service.utils.StringKit;
import com.fh.cloud.screen.service.wx.service.IWxMsgSubConfigService;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3 14:18
 */
@Component
@Slf4j
public class WxMsgSubTask {
    @Resource
    private IWxMsgSubConfigService wxMsgSubConfigService;

    /**
     * 定时推送微信消息订阅
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -06-24 17:03:10
     */
    @XxlJob("WxMsgSubTask-push")
    public void pushWxMsgSub() throws Exception {
        log.info("WxMsgSubTask-push------>start");
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        List<Long> organizationIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(param)) {
            String[] methodParams = param.split(",");
            String param0 = "";
            if (methodParams.length > 0) {
                param0 = methodParams[0];
            }
            organizationIds = StringKit.splitString2ListLong(param0, ConstString.jh);
        }

        try {
            boolean pushResult = wxMsgSubConfigService.pushWxMsgSub(organizationIds);
            XxlJobHelper.handleSuccess(String.valueOf(pushResult));
        } catch (Exception e) {
            XxlJobHelper.handleFail("WxMsgSubTask-push------>error:" + e.getLocalizedMessage());
        }
        log.info("WxMsgSubTask-push------>end");
    }
}
