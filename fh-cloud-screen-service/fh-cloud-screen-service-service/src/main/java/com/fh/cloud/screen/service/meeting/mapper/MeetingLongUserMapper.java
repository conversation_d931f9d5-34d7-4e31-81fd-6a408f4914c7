package com.fh.cloud.screen.service.meeting.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongUserDto;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongUserConditionBo;
import com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongUserVo;

/**
 * 长期预约表人员表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-11 14:26:44
 */
public interface MeetingLongUserMapper extends BaseMapper<MeetingLongUserDto> {

	List<MeetingLongUserVo> getMeetingLongUserListByCondition(MeetingLongUserConditionBo condition);

	MeetingLongUserVo getMeetingLongUserByCondition(MeetingLongUserConditionBo condition);

}
