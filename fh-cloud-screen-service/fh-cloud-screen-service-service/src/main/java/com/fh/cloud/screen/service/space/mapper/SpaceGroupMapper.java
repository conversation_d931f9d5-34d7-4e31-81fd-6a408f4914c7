package com.fh.cloud.screen.service.space.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.space.entity.bo.SpaceGroupListConditionBo;
import com.fh.cloud.screen.service.space.entity.dto.SpaceGroup;
import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区域组表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface SpaceGroupMapper extends BaseMapper<SpaceGroup> {

    List<SpaceGroupVo> getSpaceGroupListByCondition(SpaceGroupListConditionBo condition);

    /**
     * 根据地点组id查询地点信息
     *
     * @param spaceGroupIds the space group ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -05-10 11:55:13
     */
    List<SpaceGroupVo> listSpaceGroupVoBySpaceGroupIds(@Param("spaceGroupIds") List<Long> spaceGroupIds);
}
