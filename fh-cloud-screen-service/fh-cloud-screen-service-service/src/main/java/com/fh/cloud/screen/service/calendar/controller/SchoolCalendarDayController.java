package com.fh.cloud.screen.service.calendar.controller;

import com.fh.cloud.screen.service.calendar.api.SchoolCalendarDayApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayVo;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarDayService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校历上课日日期表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@RestController
@RequestMapping("/calendar/day")
@Validated
@Api(tags = "校历上课日日期表")
public class SchoolCalendarDayController implements SchoolCalendarDayApi {

    @Autowired
    private ISchoolCalendarDayService schoolCalendarDayService;

    /**
     * 查询校历上课日日期表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询校历上课日日期表列表", httpMethod = "POST")
    public AjaxResult getSchoolCalendarDayListByCondition(@RequestBody SchoolCalendarDayListConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);
        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<SchoolCalendarDayVo> pageInfo =
                new PageInfo<>(schoolCalendarDayService.getSchoolCalendarDayListByCondition(condition));
            map.put("count", pageInfo.getTotal());
            map.put("schoolCalendarDayList", pageInfo.getList());
        } else {
            List<SchoolCalendarDayVo> schoolCalendarDayListByCondition =
                schoolCalendarDayService.getSchoolCalendarDayListByCondition(condition);
            map.put("schoolCalendarDayList", schoolCalendarDayListByCondition);
        }

        return AjaxResult.success(map);
    }

    /**
     * 保存或修改校历上课日日期表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/save-or-update")
    @ApiOperation(value = "保存或修改校历上课日日期表", httpMethod = "POST")
    public AjaxResult saveOrUpdateSchoolCalendarDay(@Validated @RequestBody SchoolCalendarDayBo schoolCalendarDayBo) {
        boolean save = schoolCalendarDayService.saveOrUpdateSchoolCalendarDay(schoolCalendarDayBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改校历上课日日期表
     *
     * @param schoolCalendarDayBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改校历上课日日期表", httpMethod = "POST")
    public AjaxResult updateSchoolCalendarDay(@Validated @RequestBody SchoolCalendarDayBo schoolCalendarDayBo) {
        if (null == schoolCalendarDayBo.getSchoolCalendarDayId()) {
            return AjaxResult.fail("校历上课日日期表id不能为空");
        }
        boolean update = schoolCalendarDayService.updateSchoolCalendarDay(schoolCalendarDayBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询校历上课日日期表详情
     *
     * @param schoolCalendarDayId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询校历上课日日期表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("schoolCalendarDayId") Long schoolCalendarDayId) {
        Map<String, Object> map = schoolCalendarDayService.getDetail(schoolCalendarDayId);
        return AjaxResult.success(map);
    }

    /**
     * 删除校历上课日日期表
     *
     * @param schoolCalendarDayId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除校历上课日日期表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("schoolCalendarDayId") Long schoolCalendarDayId) {
        SchoolCalendarDayBo schoolCalendarDayBo = new SchoolCalendarDayBo();
        schoolCalendarDayBo.setSchoolCalendarDayId(schoolCalendarDayId);
        schoolCalendarDayBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = schoolCalendarDayService.updateSchoolCalendarDay(schoolCalendarDayBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
