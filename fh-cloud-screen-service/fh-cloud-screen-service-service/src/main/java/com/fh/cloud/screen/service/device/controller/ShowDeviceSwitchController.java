package com.fh.cloud.screen.service.device.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.device.api.ShowDeviceSwitchApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBatchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceSwitchVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceSwitchService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 开关机设置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Api(value = "", tags = "设备开关机设置管理")
public class ShowDeviceSwitchController implements ShowDeviceSwitchApi {

    @Autowired
    private IShowDeviceSwitchService showDeviceSwitchService;

    /**
     * 查询开关机设置列表
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询开关机设置列表", httpMethod = "POST")
    public AjaxResult getShowDeviceSwitchListByCondition(@RequestBody ShowDeviceSwitchListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        condition.setOrderBy("week asc");

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", showDeviceSwitchService.getShowDeviceSwitchListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<ShowDeviceSwitchVo> pageInfo =
                new PageInfo<>(showDeviceSwitchService.getShowDeviceSwitchListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    /**
     * 新增开关机设置
     *
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "新增开关机设置", httpMethod = "POST")
    public AjaxResult addShowDeviceSwitch(@RequestBody ShowDeviceSwitchBo showDeviceSwitchBo) {
        boolean save = showDeviceSwitchService.addShowDeviceSwitch(showDeviceSwitchBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改开关机设置
     *
     * @param showDeviceSwitchBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "修改开关机设置", httpMethod = "POST")
    public AjaxResult updateShowDeviceSwitch(@RequestBody ShowDeviceSwitchBo showDeviceSwitchBo) {
        if (null == showDeviceSwitchBo.getShowDeviceSwitchId()) {
            return AjaxResult.fail("开关机设置id不能为空");
        }
        boolean update = showDeviceSwitchService.updateShowDeviceSwitch(showDeviceSwitchBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询开关机设置详情
     *
     * @param showDeviceSwitchId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "查询开关机设置详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("showDeviceSwitchId") Long showDeviceSwitchId) {
        ShowDeviceSwitchVo showDeviceSwitchVo = showDeviceSwitchService.getDetail(showDeviceSwitchId);
        return AjaxResult.success(showDeviceSwitchVo);
    }

    /**
     * 删除开关机设置
     *
     * @param showDeviceSwitchId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 17:17:10
     */
    @ApiOperation(value = "删除开关机设置", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("showDeviceSwitchId") Long showDeviceSwitchId) {
        ShowDeviceSwitchBo showDeviceSwitchBo = new ShowDeviceSwitchBo();
        showDeviceSwitchBo.setShowDeviceSwitchId(showDeviceSwitchId);
        showDeviceSwitchBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = showDeviceSwitchService.updateShowDeviceSwitch(showDeviceSwitchBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 保存开关机设置（新增或者修改）
     *
     * @param organizationId the organization id
     * @param campusId the campus id
     * @param showDeviceSwitchBos the show device switch bos
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -06-15 15:16:27
     */
    @ApiOperation(value = "保存开关机设置（新增或修改）", httpMethod = "POST")
    public AjaxResult saveShowDeviceSwitch(@RequestBody ShowDeviceSwitchBatchBo showDeviceSwitchBatchBo) {
        showDeviceSwitchService.deleteAndAddScreenSceneModuleRelBatch(showDeviceSwitchBatchBo.getOrganizationId(),
            showDeviceSwitchBatchBo.getCampusId(), showDeviceSwitchBatchBo.getShowDeviceSwitchBos());
        return AjaxResult.success();
    }

}
