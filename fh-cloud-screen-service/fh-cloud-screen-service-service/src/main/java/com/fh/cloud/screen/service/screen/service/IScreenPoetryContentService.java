package com.fh.cloud.screen.service.screen.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryContentDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryContentVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 共话诗词表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-06-26 16:32:47
 */
public interface IScreenPoetryContentService extends IService<ScreenPoetryContentDto> {

    List<ScreenPoetryContentVo> getScreenPoetryContentListByCondition(ScreenPoetryContentConditionBo condition);

	AjaxResult addScreenPoetryContent(ScreenPoetryContentBo screenPoetryContentBo);

	AjaxResult updateScreenPoetryContent(ScreenPoetryContentBo screenPoetryContentBo);

	ScreenPoetryContentVo getScreenPoetryContentByCondition(ScreenPoetryContentConditionBo condition);

}

