package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleData;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 云屏模块表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
public interface ScreenModuleDataMapper extends BaseMapper<ScreenModuleData> {

    List<ScreenModuleDataVo> getScreenModuleDataListByCondition(ScreenModuleDataListConditionBo condition);

    /**
     * 根据场景id集合查询模块信息
     *
     * @param screenSceneIds the screen scene ids
     * @return list list
     * <AUTHOR>
     * @date 2022 -06-14 11:23:07
     */
    List<ScreenModuleDataVo> listByScreenSceneIds(@Param("screenSceneIds") List<Long> screenSceneIds);

    /**
     * 根据模块名称查询(organizationId,customModuleGroupType,isDelete,customModuleName,screenModuleDataId)
     *
     * @param screenModuleDataBo the screen module data bo
     * @return list list
     * <AUTHOR>
     * @date 2022 -07-04 14:44:14
     */
    List<ScreenModuleDataVo> listCustomModuleDataVosByName(ScreenModuleDataBo screenModuleDataBo);

}
