package com.fh.cloud.screen.service.attendance.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.attendance.api.AttendanceLogApi;
import com.fh.cloud.screen.service.attendance.api.AttendanceRuleApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo;
import com.fh.cloud.screen.service.attendance.service.IAttendanceRuleService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 考勤规则表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@RestController
@Validated
@Api(value = "", tags = "考勤规则接口")
@RequestMapping("/attendance-rule")
public class AttendanceRuleController implements AttendanceRuleApi {

    @Autowired
    private IAttendanceRuleService attendanceRuleService;

    /**
     * 查询考勤规则表列表
     *
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询考勤规则表列表", httpMethod = "POST")
    public AjaxResult getAttendanceRuleListByCondition(@RequestBody AttendanceRuleListConditionBo condition) {
        Map<String, Object> map = new HashMap<>(4);
        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<AttendanceRuleVo> pageInfo =
                new PageInfo<>(attendanceRuleService.getAttendanceRuleListByCondition(condition));
            map.put("count", pageInfo.getTotal());
            map.put("attendanceRuleList", pageInfo.getList());
        } else {
            List<AttendanceRuleVo> attendanceRuleList =
                attendanceRuleService.getAttendanceRuleListByCondition(condition);
            map.put("count", attendanceRuleList.size());
            map.put("attendanceRuleList", attendanceRuleList);
        }
        return AjaxResult.success(map);
    }

    /**
     * 新增或修改考勤规则表
     *
     * @param attendanceRuleAddBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/6 16:07
     */
    @PostMapping("/save-or-update")
    @ApiOperation(value = "新增或修改考勤规则表", httpMethod = "POST")
    public AjaxResult saveOrUpdateAttendanceRule(@Validated @RequestBody AttendanceRuleAddBo attendanceRuleAddBo) {
        if (CollectionUtils.isEmpty(attendanceRuleAddBo.getAttendanceRuleDays())) {
            return AjaxResult.fail("考勤时间不能为空");
        }
        attendanceRuleAddBo.setCreateTime(new Date());
        boolean save = attendanceRuleService.saveOrUpdateAttendanceRule(attendanceRuleAddBo);
        if (save) {
            return AjaxResult.success();
        }
        return AjaxResult.fail();
    }

    /**
     * 修改考勤规则表
     *
     * @param attendanceRuleBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改考勤规则表", httpMethod = "POST")
    public AjaxResult updateAttendanceRule(@Validated @RequestBody AttendanceRuleBo attendanceRuleBo) {
        if (null == attendanceRuleBo.getAttendanceRuleId()) {
            return AjaxResult.fail("考勤规则表id不能为空");
        }
        boolean update = attendanceRuleService.updateAttendanceRule(attendanceRuleBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 查询考勤规则表详情
     *
     * @param attendanceRuleBo 根据组织id查询，必填
     * @return com.light.core.entity.AjaxResult 包括考勤时间
     * <AUTHOR>
     * @date 2022/6/8 15:04
     */
    @PostMapping("/detail")
    @ApiOperation(value = "查询考勤规则表详情", httpMethod = "POST")
    public AjaxResult getDetail(@RequestBody AttendanceRuleBo attendanceRuleBo) {
        if (null == attendanceRuleBo.getOrganizationId() || null == attendanceRuleBo.getAttendanceType()) {
            return AjaxResult.success();
        }
        return AjaxResult.success(attendanceRuleService.getDetail(attendanceRuleBo));
    }

    /**
     * 删除考勤规则表
     *
     * @param attendanceRuleId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-25 15:33:10
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除考勤规则表", httpMethod = "GET")
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") Long attendanceRuleId) {
        AttendanceRuleBo attendanceRuleBo = new AttendanceRuleBo();
        attendanceRuleBo.setAttendanceRuleId(attendanceRuleId);
        attendanceRuleBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = attendanceRuleService.updateAttendanceRule(attendanceRuleBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 获取靠近当天时间的考勤规则时间
     *
     * @param attendanceRuleBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/16 16:03
     */
    @PostMapping("/getDate")
    @ApiOperation(value = "获取靠近当天时间的考勤规则时间", httpMethod = "POST")
    public AjaxResult getDate(@RequestBody AttendanceRuleBo attendanceRuleBo) {
        if (null == attendanceRuleBo.getOrganizationId() || null == attendanceRuleBo.getAttendanceType()
            || null == attendanceRuleBo.getNowDate()) {
            return AjaxResult.fail("参数错误");
        }
        return AjaxResult.success(attendanceRuleService.getRuleDataTimeByCondition(attendanceRuleBo));
    }

    /**
     * app查询缓存中的考勤规则
     *
     * @param attendanceRuleBo 根据组织id查询，必填
     * @return com.light.core.entity.AjaxResult 包括考勤时间
     * <AUTHOR>
     * @date 2022/6/8 15:04
     */
    @PostMapping("/info")
    @ApiOperation(value = "app查询缓存中的考勤规则", httpMethod = "POST")
    public AjaxResult getInfo(@RequestBody AttendanceRuleBo attendanceRuleBo) {
        if (null == attendanceRuleBo.getOrganizationId() || null == attendanceRuleBo.getAttendanceType()) {
            return AjaxResult.fail("参数异常");
        }
        AttendanceRuleAddBo attendanceRuleByCache = attendanceRuleService.getAttendanceRuleByOrganizationIdAndType(
            attendanceRuleBo.getOrganizationId(), attendanceRuleBo.getAttendanceType());
        return AjaxResult.success(attendanceRuleByCache);
    }
}
