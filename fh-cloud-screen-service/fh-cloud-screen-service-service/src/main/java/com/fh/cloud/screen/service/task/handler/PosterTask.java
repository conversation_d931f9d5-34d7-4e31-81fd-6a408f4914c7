package com.fh.cloud.screen.service.task.handler;

import com.alibaba.nacos.common.utils.StringUtils;
import com.fh.cloud.screen.service.label.service.ILabelService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryMediaService;
import com.fh.cloud.screen.service.screen.service.IScreenModuleLibraryService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/7/11
 */
@Component
@Slf4j
public class PosterTask {

    @Resource
    private IScreenModuleLibraryMediaService screenModuleLibraryMediaService;
    @Resource
    private ILabelService labelService;

    @XxlJob("cover-poster")
    public void coverPoster() {

        try {
            log.info("覆盖海报方法执行");
            screenModuleLibraryMediaService.coverPoster();
        } catch (Exception e) {
            XxlJobHelper.handleFail("cover-poster error:" + e.getLocalizedMessage());
        }

    }

    @XxlJob("poster-optimization")
    public void posterOptimization() {
        try {
            log.info("海报导入优化方法执行");
            String filterOther = XxlJobHelper.getJobParam();
            if (StringUtils.isBlank(filterOther)) {
                XxlJobHelper.handleFail("参数不足");
                return;
            }
            screenModuleLibraryMediaService.posterOptimization(Long.valueOf(filterOther));
        } catch (Exception e) {
            XxlJobHelper.handleFail("poster optimization error:" + e.getLocalizedMessage());
        }

    }

    @XxlJob("poster-import")
    public void importPoster() {
        try {
            log.info("海报导入方法执行");
            String filterOther = XxlJobHelper.getJobParam();
            if (StringUtils.isBlank(filterOther)) {
                XxlJobHelper.handleFail("参数不足");
                return;
            }
            screenModuleLibraryMediaService.importPoster(Long.valueOf(filterOther));
        } catch (Exception e) {
            XxlJobHelper.handleFail("poster import error:" + e.getLocalizedMessage());
        }

    }

    @XxlJob("init-poster-group-to-label")
    public void initPosterGroupToLabel() {
        try {
            log.info("初始化海报分组为标签");
            labelService.initPosterGroupToLabel();
        } catch (Exception e) {
            XxlJobHelper.handleFail("poster import error:" + e.getLocalizedMessage());
        }

    }

}
