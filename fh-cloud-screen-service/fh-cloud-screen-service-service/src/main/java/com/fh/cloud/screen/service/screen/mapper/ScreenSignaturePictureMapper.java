package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSignaturePictureDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo;

/**
 * 电子签名图片资源表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-07-12 09:50:58
 */
public interface ScreenSignaturePictureMapper extends BaseMapper<ScreenSignaturePictureDto> {

	List<ScreenSignaturePictureVo> getScreenSignaturePictureListByCondition(ScreenSignaturePictureConditionBo condition);

	ScreenSignaturePictureVo getScreenSignaturePictureByCondition(ScreenSignaturePictureConditionBo condition);

}
