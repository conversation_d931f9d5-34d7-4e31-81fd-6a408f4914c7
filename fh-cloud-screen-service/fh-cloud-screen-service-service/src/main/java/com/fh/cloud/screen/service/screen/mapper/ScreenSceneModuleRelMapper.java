package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneModuleRelListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneModuleRel;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo;

import java.util.List;

/**
 * 云屏场景模块关系表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-10 09:31:56
 */
public interface ScreenSceneModuleRelMapper extends BaseMapper<ScreenSceneModuleRel> {

    List<ScreenSceneModuleRelVo> getScreenSceneModuleRelListByCondition(ScreenSceneModuleRelListConditionBo condition);

}
