package com.fh.cloud.screen.service.consts;

/**
 * <AUTHOR>
 * @date 2022/11/18 10:19
 */
public interface AttendanceConstants {

    /**
     * 考勤规则时间前后间隔秒数=分钟*60s
     */
    long MIN_SECOND = 30L * 60;
    long MAX_SECOND = 120L * 60;

    /**
     * 考勤规则时间前后间隔分钟
     */
    int MAX_MINUTES = 120;
    int MIN_MINUTES = 30;

    /**
     * 签到截止时间=00+59s
     */
    int FIFTY_NINE = 59;

    /**
     * 会议允许提前签到的时间=30*60秒
     */
    Integer ALLOW_SIGN_IN_SECOND = 30 * 60;
}
