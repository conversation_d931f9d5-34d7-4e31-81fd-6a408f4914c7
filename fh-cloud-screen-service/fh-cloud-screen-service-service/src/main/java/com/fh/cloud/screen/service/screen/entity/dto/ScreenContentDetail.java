package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏内容详情表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_content_detail")
public class ScreenContentDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "screen_content_detail_id", type = IdType.AUTO)
    private Long screenContentDetailId;

    /**
     * FK云屏内容表
     */
    @TableField("screen_content_id")
    private Long screenContentId;

    /**
     * 顺序
     */
    @TableField("screen_content_index")
    private Long screenContentIndex;

    /**
     * 云屏内容-标题
     */
    @TableField("screen_content_title")
    private String screenContentTitle;

    /**
     * 云屏内容-文本
     */
    @TableField("screen_content_txt")
    private String screenContentTxt;

    /**
     * 云屏内容-url
     */
    @TableField("screen_content_url")
    private String screenContentUrl;

    /**
     * 云屏图片或者视频媒体地址
     */
    @TableField("screen_content_media_url")
    private String screenContentMediaUrl;

    /**
     * 云屏图片或者视频媒体地址-压缩后
     */
    @TableField("screen_content_media_url_compress")
    private String screenContentMediaUrlCompress;

    /**
     * 云屏图片或者视频媒体地址-封面
     */
    @TableField("screen_content_media_url_cover")
    private String screenContentMediaUrlCover;

    /**
     * 云屏图片或者视频媒体名称（不包含后缀）
     */
    @TableField("screen_content_media_name")
    private String screenContentMediaName;

    /**
     * 云屏图片或者视频原始媒体名称（包含后缀）
     */
    @TableField("screen_content_media_name_ori")
    private String screenContentMediaNameOri;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 称呼内容
     */
    @TableField("call_content")
    private String callContent;

    /**
     * 落款内容
     */
    @TableField("sign_content")
    private String signContent;

    /**
     * 云屏图片或者视频媒体fileoid
     */
    @TableField("screen_content_media_id")
    private String screenContentMediaId;

    /**
     * 云屏图片或者视频媒体fileoid-压缩后
     */
    @TableField("screen_content_media_id_compress")
    private String screenContentMediaIdCompress;

    /**
     * 适用设备模式：1横屏，2竖屏。
     */
    @TableField("screen_device_pattern")
    private Integer screenDevicePattern;

    /**
     * 云屏图片或者视频媒体md5
     */
    @TableField("screen_content_media_md5")
    private String screenContentMediaMd5;

    /**
     * 云屏图片或者视频媒体md5-压缩后
     */
    @TableField("screen_content_media_md5_compress")
    private String screenContentMediaMd5Compress;
}
