package com.fh.cloud.screen.service.er.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoSubjectDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoSubjectBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.light.core.entity.AjaxResult;

import java.util.Date;
import java.util.List;

/**
 * 考场_考试计划里面一次考试科目信息接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-09 15:27:03
 */
public interface IExamInfoSubjectService extends IService<ExamInfoSubjectDto> {

    List<ExamInfoSubjectVo> getExamInfoSubjectListByCondition(ExamInfoSubjectConditionBo condition);

    AjaxResult addExamInfoSubject(ExamInfoSubjectBo examInfoSubjectBo);

    AjaxResult updateExamInfoSubject(ExamInfoSubjectBo examInfoSubjectBo);

    ExamInfoSubjectVo getDetail(Long id);

    /**
     * 添加考试科目，同时添加详情
     * 
     * @param examInfoSubjectBo
     * @return
     */
    AjaxResult addExamInfoSubjectBatchWithDetail(List<ExamInfoSubjectBo> examInfoSubjectBos);

    /**
     * 更新考试科目，同时更新详情
     * 
     * @param examInfoSubjectBo
     * @return
     */
    AjaxResult updateExamInfoSubjectBatchWithDetail(List<ExamInfoSubjectBo> examInfoSubjectBos);

    /**
     * 通过地点和日期检索考试科目列表
     *
     * @param spaceGroupUseType, spaceInfoId, date
     * @return java.util.List<com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo>
     * <AUTHOR>
     * @date 2022/10/17 14:55
     */
    List<ExamInfoSubjectVo> getExamInfoSubjectListBySpaceAndDate(Integer spaceGroupUseType, Long spaceInfoId,
        Date date);

    /**
     * 获取当前考试详情
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/1/16 10:05
     */
    AjaxResult getNowExamInfoByCondition(ExamInfoSubjectConditionBo conditionBo);

    /**
     * 获取组织下所有考试科目信息
     * @param organizationId
     * @return
     */
    List<ExamInfoSubjectVo> getExamInfoSubjectListByOrganizationId(Long organizationId);
}
