package com.fh.cloud.screen.service.screen.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 云屏内容表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("screen_content")
public class ScreenContent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "screen_content_id", type = IdType.AUTO)
    private Long screenContentId;

    /**
     * FK所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * FK所属校区ID
     */
    @TableField("campus_id")
    private Long campusId;

    /**
     * FK所属班级ID
     */
    @TableField("classes_id")
    private Long classesId;

    /**
     * 内容所属范围：1校级，2班级
     */
    @TableField("scope_type")
    private Integer scopeType;

    /**
     * FK云屏模块表id
     */
    @TableField("screen_module_data_id")
    private Long screenModuleDataId;

    /**
     * 发布状态：1未发布，2已发布
     */
    @TableField("screen_content_status")
    private Integer screenContentStatus;

    /**
     * 模块内容类型：默认0，1网页地址，2富文本，3图片，4视频，5欢迎图
     */
    @TableField("screen_content_type")
    private Integer screenContentType;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 截至时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 内容数据来源
     */
    @TableField("screen_content_source")
    private Integer screenContentSource;

    /**
     * 通知nice值，越小优越在前面（用于实现置顶功能）
     */
    @TableField("content_nice")
    private Integer contentNice;
}
