package com.fh.cloud.screen.service.leaveschool.service.impl;

import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javax.annotation.Resource;

import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolBroadcastInfoDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolBroadcastInfoBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo;
import com.fh.cloud.screen.service.leaveschool.service.ILeaveSchoolBroadcastInfoService;
import com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolBroadcastInfoMapper;
import com.light.core.entity.AjaxResult;
/**
 * 放学播报信息表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:38
 */
@Service
public class LeaveSchoolBroadcastInfoServiceImpl extends ServiceImpl<LeaveSchoolBroadcastInfoMapper, LeaveSchoolBroadcastInfoDto> implements ILeaveSchoolBroadcastInfoService {

	@Resource
	private LeaveSchoolBroadcastInfoMapper leaveSchoolBroadcastInfoMapper;
	
    @Override
	public List<LeaveSchoolBroadcastInfoVo> getLeaveSchoolBroadcastInfoListByCondition(LeaveSchoolBroadcastInfoConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		FuzzyQueryUtil.transferMeanBean(condition);
        return leaveSchoolBroadcastInfoMapper.getLeaveSchoolBroadcastInfoListByCondition(condition);
	}

	@Override
	public AjaxResult addLeaveSchoolBroadcastInfo(LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo) {
		LeaveSchoolBroadcastInfoDto leaveSchoolBroadcastInfo = new LeaveSchoolBroadcastInfoDto();
		BeanUtils.copyProperties(leaveSchoolBroadcastInfoBo, leaveSchoolBroadcastInfo);
		leaveSchoolBroadcastInfo.setIsDelete(StatusEnum.NOTDELETE.getCode());
		if(save(leaveSchoolBroadcastInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateLeaveSchoolBroadcastInfo(LeaveSchoolBroadcastInfoBo leaveSchoolBroadcastInfoBo) {
		LeaveSchoolBroadcastInfoDto leaveSchoolBroadcastInfo = new LeaveSchoolBroadcastInfoDto();
		BeanUtils.copyProperties(leaveSchoolBroadcastInfoBo, leaveSchoolBroadcastInfo);
		if(updateById(leaveSchoolBroadcastInfo)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public LeaveSchoolBroadcastInfoVo getLeaveSchoolBroadcastInfoByCondition(LeaveSchoolBroadcastInfoConditionBo condition) {
		condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
		return leaveSchoolBroadcastInfoMapper.getLeaveSchoolBroadcastInfoByCondition(condition);
	}

}