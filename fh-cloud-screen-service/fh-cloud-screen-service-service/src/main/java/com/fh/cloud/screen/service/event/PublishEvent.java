package com.fh.cloud.screen.service.event;

import com.fh.cloud.screen.service.dto.EventPublishDto;
import com.fh.cloud.screen.service.message.vo.MessageVo;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10 16:42
 */
public class PublishEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    public PublishEvent(EventPublishDto source) {
        super(source);
    }

    /**
     * 构造一个PublishEvent-云屏内容保存事件
     *
     * @param messageWsType
     * @param organizationId
     * @param contentId
     * @param msg
     * @return
     */
    public static PublishEvent produceContentPublishEvent(Integer messageWsType, Long organizationId, Long contentId,
        Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setContentId(contentId);
        eventPublishDto.setMsg(messageVo);
        return new PublishEvent(eventPublishDto);
    }

    // TODO 根据消息类型，各个业务重载实现不同的produce方法

    /**
     * 构造一个PublishEvent-开关机使用、或凡是需要可以指定推送设备的都可以使用
     *
     * @param messageWsType the message ws type
     * @param organizationId the organization id
     * @param deviceNumbers the device numbers
     * @param msg the msg
     * @return publish event
     * <AUTHOR>
     * @date 2022 -05-12 15:39:21
     */
    public static PublishEvent produceDevicePublishEvent(Integer messageWsType, Long organizationId,
        List<String> deviceNumbers, Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setDeviceNumbers(deviceNumbers);
        eventPublishDto.setMsg(messageVo);

        return new PublishEvent(eventPublishDto);
    }

    /**
     * 构造一个PublishEvent-学生信息变更或卡号变更，需要按班级推送的场景。
     *
     * @param messageWsType
     * @param organizationId
     * @param classesId
     * @param messageBody
     * @return
     */
    public static PublishEvent produceStudentPublishEvent(Integer messageWsType, Long organizationId, Long classesId,
        Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setClassesId(classesId);
        eventPublishDto.setMsg(messageVo);
        return new PublishEvent(eventPublishDto);
    }

    /**
     * 构造一个PublishEvent-紧急发布事件
     *
     * @param messageWsType the message ws type
     * @param organizationId the organization id
     * @param contentSpecialId the content special id
     * @param contentSpecialIdNow the content special id now
     * @param messageBody the message body
     * @return publish event
     * <AUTHOR>
     * @date 2022 -06-06 11:43:12
     */
    public static PublishEvent produceContentSpecialPublishEvent(Integer messageWsType, Long organizationId,
        Long contentSpecialId, Long contentSpecialIdNow, Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setContentSpecialId(contentSpecialId);
        eventPublishDto.setContentSpecialIdNow(contentSpecialIdNow);
        eventPublishDto.setMsg(messageVo);
        return new PublishEvent(eventPublishDto);
    }

    /**
     * 构造一个PublishEvent-云屏场景保存或者删除事件
     *
     * @param messageWsType the message ws type
     * @param organizationId the organization id
     * @param sceneId the scene id
     * @param messageBody the message body
     * @return publish event
     * <AUTHOR>
     * @date 2022 -06-14 14:44:21
     */
    public static PublishEvent produceScenePublishEvent(Integer messageWsType, Long organizationId, Long sceneId,
        Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setSceneId(sceneId);
        eventPublishDto.setMsg(messageVo);
        return new PublishEvent(eventPublishDto);
    }

    /**
     * 构造会议发布对象
     * 
     * @param messageWsType
     * @param organizationId
     * @param deviceNumbers
     * @return
     */
    public static PublishEvent produceMeetingPublishEvent(Integer messageWsType, Long organizationId,
        List<String> deviceNumbers) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageBody(null);
        messageVo.setMessageType(messageWsType);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setMsg(messageVo);
        eventPublishDto.setDeviceNumbers(deviceNumbers);
        return new PublishEvent(eventPublishDto);
    }

    /**
     * 构造考场发布对象
     *
     * @param messageWsType the message ws type
     * @param organizationId the organization id
     * @param examPlanId the exam plan id
     * @return publish event
     * <AUTHOR>
     * @date 2022 -05-12 15:39:21
     */
    public static PublishEvent produceErPublishEvent(Integer messageWsType, Long organizationId, Long examPlanId,
        Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setExamPlanId(examPlanId);
        eventPublishDto.setMsg(messageVo);
        return new PublishEvent(eventPublishDto);
    }

    /**
     * 构造放学场景发布对象
     *
     * @param messageWsType
     * @param organizationId
     * @param spaceInfoId
     * @param spaceGroupUseType
     * @param spaceName
     * @param messageBody
     * @return com.fh.cloud.screen.service.event.PublishEvent
     * <AUTHOR>
     * @date 2023/8/25 17:39
     **/
    public static PublishEvent produceLeaveSchoolPublishEvent(Integer messageWsType,
                                                              Long organizationId,
                                                              Long spaceInfoId,
                                                              Integer spaceGroupUseType,
                                                              String spaceName,
                                                              List<String> deviceNumbers,
                                                              Object messageBody) {
        MessageVo messageVo = new MessageVo();
        messageVo.setMessageType(messageWsType);
        messageVo.setMessageBody(messageBody);
        EventPublishDto eventPublishDto = new EventPublishDto();
        eventPublishDto.setMessageWsType(messageWsType);
        eventPublishDto.setOrganizationId(organizationId);
        eventPublishDto.setSpaceInfoId(spaceInfoId);
        eventPublishDto.setSpaceGroupUseType(spaceGroupUseType);
        eventPublishDto.setSpaceName(spaceName);
        eventPublishDto.setDeviceNumbers(deviceNumbers);
        eventPublishDto.setMsg(messageVo);
        return new PublishEvent(eventPublishDto);
    }
}
