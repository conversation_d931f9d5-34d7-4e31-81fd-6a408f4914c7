package com.fh.cloud.screen.service.er.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoDto;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;

/**
 * 考场_考试计划里面一次考试信息Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
public interface ExamInfoMapper extends BaseMapper<ExamInfoDto> {

	List<ExamInfoVo> getExamInfoListByCondition(ExamInfoConditionBo condition);

}
