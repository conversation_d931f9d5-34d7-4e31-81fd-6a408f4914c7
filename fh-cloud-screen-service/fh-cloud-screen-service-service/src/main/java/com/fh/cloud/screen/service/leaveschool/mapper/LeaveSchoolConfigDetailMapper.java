package com.fh.cloud.screen.service.leaveschool.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDetailDto;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigDetailConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo;

/**
 * 放学配置详情表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-08-23 10:23:14
 */
public interface LeaveSchoolConfigDetailMapper extends BaseMapper<LeaveSchoolConfigDetailDto> {

	List<LeaveSchoolConfigDetailVo> getLeaveSchoolConfigDetailListByCondition(LeaveSchoolConfigDetailConditionBo condition);

	LeaveSchoolConfigDetailVo getLeaveSchoolConfigDetailByCondition(LeaveSchoolConfigDetailConditionBo condition);

}
