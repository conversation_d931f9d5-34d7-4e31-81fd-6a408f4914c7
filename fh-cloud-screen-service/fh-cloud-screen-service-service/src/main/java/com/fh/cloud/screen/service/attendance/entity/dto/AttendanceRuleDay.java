package com.fh.cloud.screen.service.attendance.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * 考勤规则天表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("attendance_rule_day")
public class AttendanceRuleDay implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "attendance_rule_day_id", type = IdType.AUTO)
    private Long attendanceRuleDayId;

    /**
     * 考勤规则表ID
     */
    @TableField("attendance_rule_id")
    private Long attendanceRuleId;

    /**
     * grade 的code值，年级一致的情况这个值为默认值
     */
    @TableField("grade")
    private String grade;

    /**
     * 星期几：默认0，1-7，分别为星期一到星期日。一周一致的情况这个值为默认值
     */
    @TableField("week")
    private Integer week;

    /**
     * 签到时间
     */
    @TableField("sign_in_time")
    private Time signInTime;

    /**
     * 签退时间
     */
    @TableField("sign_out_time")
    private Time signOutTime;

    /**
     * 考勤一组顺序：1，2，3...
     */
    @TableField("attendance_rule_day_index")
    private Integer attendanceRuleDayIndex;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 打卡规则左侧扩充时间分钟
     */
    @TableField("sign_left_minute")
    private Integer signLeftMinute;

    /**
     * 打卡规则右侧扩充时间分钟
     */
    @TableField("sign_right_minute")
    private Integer signRightMinute;
}
