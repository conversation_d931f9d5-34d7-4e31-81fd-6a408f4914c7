package com.fh.cloud.screen.service.common.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.common.api.ScreenDictionaryDataApi;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataBo;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.common.entity.dto.DictionaryData;
import com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo;
import com.fh.cloud.screen.service.common.service.IScreenDictionaryDataService;
import com.fh.cloud.screen.service.enums.DictionaryType;
import com.fh.cloud.screen.service.enums.PosterEnums;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/30 16:57
 */
@RestController
@Api(value = "", tags = "字典数据管理")
@Slf4j
public class ScreenDictionaryDataController implements ScreenDictionaryDataApi {

    @Autowired
    private IScreenDictionaryDataService dictionaryDataService;
    @Resource
    private BaseDataService baseDataService;

    @ApiOperation(value = "查询字典数据列表", httpMethod = "POST")
    @Override
    public AjaxResult getDictionaryDataListByCondition(DictionaryDataListConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());

        if (SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", dictionaryDataService.getDictionaryDataListByCondition(condition));
            return AjaxResult.success(map);
        } else {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            PageInfo<DictionaryDataVo> pageInfo =
                new PageInfo<>(dictionaryDataService.getDictionaryDataListByCondition(condition));
            return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(),
                condition.getPageSize());
        }
    }

    @Override
    public AjaxResult getPosterGroupListByCondition(DictionaryDataListConditionBo condition) {
        condition.setType(PosterEnums.TYPE_SHOW.getCode());
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        Map<String, Object> map = new HashMap<>();
        List<DictionaryDataVo> dataVos;
        if (!SystemConstants.NO_PAGE.equals(condition.getPageNo())) {
            PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
            dataVos = dictionaryDataService.getDictionaryDataListByCondition(condition);
            PageInfo<DictionaryDataVo> pageInfo = new PageInfo<>(dataVos);
            map.put("total", pageInfo.getTotal());
        } else {
            dataVos = dictionaryDataService.getDictionaryDataListByCondition(condition);
            map.put("total", dataVos.size());
        }
        map.put("list", dataVos);
        if (CollectionUtils.isNotEmpty(dataVos)) {
            List<Long> orgIds = dataVos.stream().map(DictionaryDataVo::getOrganizationId).collect(Collectors.toList());
            List<OrganizationVo> organizationVoList = baseDataService.getOrganizationVoList(orgIds);
            if (CollectionUtils.isNotEmpty(organizationVoList)) {
                Map<Long, String> orgMap = organizationVoList.stream()
                    .collect(Collectors.toMap(OrganizationVo::getId, OrganizationVo::getName));
                for (DictionaryDataVo dictionaryDataVo : dataVos) {
                    dictionaryDataVo.setOrganizationName(orgMap.get(dictionaryDataVo.getOrganizationId()));
                }
            }
        }
        return AjaxResult.success(map);
    }

    /**
     * 新增海报分组数据
     * 
     * <AUTHOR>
     * @date 2022-12-30 10:07:25
     */
    @Override
    public AjaxResult addDictionaryData(@RequestBody DictionaryDataBo dictionaryDataBo) {
        dictionaryDataBo.setType(PosterEnums.TYPE_SHOW.getCode());
        return dictionaryDataService.addDictionaryData(dictionaryDataBo);
    }

    /**
     * 修改字典数据
     * 
     * @param dictionaryDataBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-12-30 10:07:25
     */
    @Override
    public AjaxResult updateDictionaryData(@RequestBody DictionaryDataBo dictionaryDataBo) {
        if (null == dictionaryDataBo.getId()) {
            return AjaxResult.fail("字典数据id不能为空");
        }
        return dictionaryDataService.updateDictionaryData(dictionaryDataBo);
    }

    /**
     * 交换顺序
     *
     * @param firstId, secondId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/10 10:34
     */
    @Override
    public AjaxResult exchange(Long firstId, Long secondId) {
        DictionaryData firstData = dictionaryDataService.getById(firstId);
        DictionaryData secondData = dictionaryDataService.getById(secondId);
        if (null == firstData || null == secondData) {
            return AjaxResult.fail("参数错误");
        }
        Integer sort = firstData.getDictSort();
        firstData.setDictSort(secondData.getDictSort());
        secondData.setDictSort(sort);
        List<DictionaryData> dictionaryDataList = new ArrayList<>();
        dictionaryDataList.add(firstData);
        dictionaryDataList.add(secondData);
        dictionaryDataService.updateBatchById(dictionaryDataList);
        return AjaxResult.success("修改成功");
    }

    /**
     * 根据id顺序更新对应的顺序
     *
     * @param idList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/10 10:34
     */
    @Override
    public AjaxResult updateGroupSortByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return AjaxResult.fail("参数不能为空");
        }
        List<DictionaryData> dictionaryDataList = new ArrayList<>();
        int i = 1;
        for (Long id : idList) {
            DictionaryData dictionaryData = new DictionaryData();
            dictionaryData.setId(id);
            dictionaryData.setDictSort(i++);
            dictionaryDataList.add(dictionaryData);
        }
        dictionaryDataService.updateBatchById(dictionaryDataList);
        return AjaxResult.success();
    }

}
