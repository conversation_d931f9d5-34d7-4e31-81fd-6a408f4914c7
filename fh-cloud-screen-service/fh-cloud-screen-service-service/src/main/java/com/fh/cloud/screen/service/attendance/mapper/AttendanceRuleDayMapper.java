package com.fh.cloud.screen.service.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleDayListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRuleDay;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleDayVo;

import java.util.List;

/**
 * 考勤规则天表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface AttendanceRuleDayMapper extends BaseMapper<AttendanceRuleDay> {

    List<AttendanceRuleDayVo> getAttendanceRuleDayListByCondition(AttendanceRuleDayListConditionBo condition);

}
