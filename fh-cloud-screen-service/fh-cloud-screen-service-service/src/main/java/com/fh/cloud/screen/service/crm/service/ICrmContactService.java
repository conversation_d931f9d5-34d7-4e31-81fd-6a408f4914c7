package com.fh.cloud.screen.service.crm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.crm.entity.dto.CrmContactDto;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactConditionBo;
import com.fh.cloud.screen.service.crm.entity.bo.CrmContactBo;
import com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * CRM商讯联系人表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-27 18:20:08
 */
public interface ICrmContactService extends IService<CrmContactDto> {

    List<CrmContactVo> getCrmContactListByCondition(CrmContactConditionBo condition);

    AjaxResult addCrmContact(CrmContactBo crmContactBo);

    AjaxResult updateCrmContact(CrmContactBo crmContactBo);

    CrmContactVo getCrmContactByCondition(CrmContactConditionBo condition);

    /**
     * 新增CRM商讯联系人表
     *
     * @param crmInfoId the crm info id
     * @param crmContactBoList the crm contact bo list
     * @return ajax result
     * <AUTHOR>
     * @date 2023 -12-29 11:40:15
     */
    AjaxResult addCrmContactBatch(Long crmInfoId, List<CrmContactBo> crmContactBoList);

}
