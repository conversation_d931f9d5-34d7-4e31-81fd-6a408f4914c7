package com.fh.cloud.screen.service.device.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceCaptureDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 设备抓图表接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
public interface IShowDeviceCaptureService extends IService<ShowDeviceCaptureDto> {

    List<ShowDeviceCaptureVo> getShowDeviceCaptureListByCondition(ShowDeviceCaptureConditionBo condition);

    Long addShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo);

    AjaxResult updateShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo);

    ShowDeviceCaptureVo getDetail(Long id);

    /**
     * 根据设备号查询最后一条截图信息
     * 
     * @param deviceNumber
     * @return
     */
    ShowDeviceCaptureVo getDetailByNumber(String deviceNumber);
}
