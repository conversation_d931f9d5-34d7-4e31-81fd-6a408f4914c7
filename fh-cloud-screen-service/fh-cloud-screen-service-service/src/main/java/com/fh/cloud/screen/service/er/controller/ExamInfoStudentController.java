package com.fh.cloud.screen.service.er.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.service.consts.ConstantsInteger;
import com.fh.cloud.screen.service.consts.ConstantsRedis;
import com.fh.cloud.screen.service.enums.ExamEnums;
import com.fh.cloud.screen.service.er.api.ExamInfoStudentApi;
import com.fh.cloud.screen.service.er.entity.dto.ExamInfoStudentDto;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import com.fh.cloud.screen.service.er.service.IExamInfoStudentService;
import com.fh.cloud.screen.service.er.service.IExamInfoSubjectService;
import com.fh.cloud.screen.service.face.service.IFaceConfigService;
import com.fh.cloud.screen.service.utils.DateKit;
import com.light.redis.component.RedisComponent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentConditionBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 考场_考试计划里面一次考试的学生
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-29 14:35:17
 */
@RestController
@Validated
public class ExamInfoStudentController implements ExamInfoStudentApi {

    @Autowired
    private IExamInfoStudentService examInfoStudentService;
    @Resource
    private IExamInfoSubjectService examInfoSubjectService;
    @Resource
    private RedisComponent redisComponent;

    /**
     * 查询考场_考试计划里面一次考试的学生分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<PageInfo<ExamInfoStudentVo>>
        getExamInfoStudentPageListByCondition(@RequestBody ExamInfoStudentConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<ExamInfoStudentVo> pageInfo =
            new PageInfo<>(examInfoStudentService.getExamInfoStudentListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询考场_考试计划里面一次考试的学生列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<List<ExamInfoStudentVo>>
        getExamInfoStudentListByCondition(@RequestBody ExamInfoStudentConditionBo condition) {
        List<ExamInfoStudentVo> list = examInfoStudentService.getExamInfoStudentListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增考场_考试计划里面一次考试的学生
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult addExamInfoStudent(@Validated @RequestBody ExamInfoStudentBo examInfoStudentBo) {
        return examInfoStudentService.addExamInfoStudent(examInfoStudentBo);
    }

    /**
     * 修改考场_考试计划里面一次考试的学生
     * 
     * @param examInfoStudentBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult updateExamInfoStudent(@Validated @RequestBody ExamInfoStudentBo examInfoStudentBo) {
        if (null == examInfoStudentBo.getExamInfoStudentId()) {
            return AjaxResult.fail("考场_考试计划里面一次考试的学生id不能为空");
        }
        return examInfoStudentService.updateExamInfoStudent(examInfoStudentBo);
    }

    /**
     * 查询考场_考试计划里面一次考试的学生详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult<ExamInfoStudentVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("考场_考试计划里面一次考试的学生id不能为空");
        }
        ExamInfoStudentVo vo = examInfoStudentService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除考场_考试计划里面一次考试的学生
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        ExamInfoStudentDto examInfoStudentDto = new ExamInfoStudentDto();
        examInfoStudentDto.setExamInfoStudentId(id);
        examInfoStudentDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (examInfoStudentService.updateById(examInfoStudentDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }

    @Override
    public AjaxResult examSign(@RequestBody ExamInfoStudentBo examInfoStudentBo) {
        if (examInfoStudentBo.getAttendanceTime() == null || examInfoStudentBo.getExamInfoSubjectId() == null
            || StringUtils.isBlank(examInfoStudentBo.getUserOid())) {
            return AjaxResult.fail("参数错误");

        }
        Date signDateTime = examInfoStudentBo.getAttendanceTime();
        // 查找考生记录
        ExamInfoStudentConditionBo conditionBo = new ExamInfoStudentConditionBo();
        conditionBo.setExamInfoSubjectId(examInfoStudentBo.getExamInfoSubjectId());
        conditionBo.setUserOid(examInfoStudentBo.getUserOid());
        List<ExamInfoStudentVo> examInfoStudentList =
            examInfoStudentService.getExamInfoStudentListByCondition(conditionBo);
        if (CollectionUtil.isEmpty(examInfoStudentList)) {
            return AjaxResult.fail("学生考试科目不存在");
        }
        ExamInfoStudentVo studentVo = examInfoStudentList.get(0);
        // 查找考生考试的科目
        examInfoStudentBo.setExamInfoStudentId(studentVo.getExamInfoStudentId());
        ExamInfoSubjectVo subjectVo = examInfoSubjectService.getDetail(studentVo.getExamInfoSubjectId());
        // 根据时间，判断状态
        Date startDay = subjectVo.getExamStartTime();
        Date endDay = subjectVo.getExamEndTime();
        Date startDate59 = DateKit.getAfterSeconds(59, startDay);
        // Date endDate59 = DateKit.getAfterSeconds(60, endDay);
        // 间隔秒数=分钟*60s
        long beforeTime = 30L * 60;
        // 签到从59秒开始，判断间隔时间，添加59秒
        beforeTime = beforeTime + 59;
        long diffSignInMin = DateKit.getDiffSecDate(signDateTime, startDate59);
        if (diffSignInMin <= beforeTime && diffSignInMin >= 0) {
            examInfoStudentBo.setAttendanceStatus(ExamEnums.SIGN_NORMAL.getValue());
        } else if (signDateTime.before(endDay) && signDateTime.after(startDate59)) {
            examInfoStudentBo.setAttendanceStatus(ExamEnums.SIGN_NORMAL_LATER.getValue());
        } else {
            return AjaxResult.fail(ConstantsInteger.MEETING_FAIL_CODE, "不在签到范围时间内");
        }
        // 更新考试签到信息
        ExamInfoStudentBo updateStudentBo = new ExamInfoStudentBo();
        updateStudentBo.setExamInfoStudentId(studentVo.getExamInfoStudentId());
        updateStudentBo.setAttendanceTime(examInfoStudentBo.getAttendanceTime());
        updateStudentBo.setAttendanceStatus(examInfoStudentBo.getAttendanceStatus());
        examInfoStudentService.updateExamInfoStudent(updateStudentBo);

        // 缓存考试签到信息
        String key = ConstantsRedis.EXAM_SUBJECT_ATTENDANCE_LOG_KEY_PREFIX + subjectVo.getExamInfoSubjectId();
        updateStudentBo.setUserOid(examInfoStudentBo.getUserOid());
        redisComponent.rPush(key, JSONObject.toJSONString(updateStudentBo));
        // 有效期到签到日期的23:59:59
        redisComponent.expire(key, DateKit.getNextDayMidnightSeconds(signDateTime));
        return AjaxResult.success("签到成功");
    }
}
