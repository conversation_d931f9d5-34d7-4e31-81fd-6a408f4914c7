package com.fh.cloud.screen.service.card.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.dto.UserCard;
import com.fh.cloud.screen.service.card.entity.vo.UserCardVo;

import java.util.List;

/**
 * 用户卡表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface UserCardMapper extends BaseMapper<UserCard> {

    List<UserCardVo> getUserCardListByCondition(UserCardListConditionBo condition);

}
