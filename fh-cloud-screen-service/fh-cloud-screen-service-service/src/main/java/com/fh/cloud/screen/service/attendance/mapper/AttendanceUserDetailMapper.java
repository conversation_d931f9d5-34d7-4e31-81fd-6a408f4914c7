package com.fh.cloud.screen.service.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserDetailListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;

import java.util.List;

/**
 * 考勤用户详情表，需要日终计算Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
public interface AttendanceUserDetailMapper extends BaseMapper<AttendanceUserDetail> {

    List<AttendanceUserDetailVo> getAttendanceUserDetailListByCondition(AttendanceUserDetailListConditionBo condition);

}
