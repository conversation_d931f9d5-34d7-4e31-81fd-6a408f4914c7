package com.fh.cloud.screen.service.label.controller;

import com.fh.cloud.screen.service.label.api.LabelLibraryRelApi;
import com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto;
import com.fh.cloud.screen.service.label.service.ILabelLibraryRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelConditionBo;
import com.fh.cloud.screen.service.label.entity.bo.LabelLibraryRelBo;
import com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import java.util.List;

/**
 * 标签海报关联表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-02-27 10:16:32
 */
@RestController
@Validated
public class LabelLibraryRelController implements LabelLibraryRelApi {

    @Autowired
    private ILabelLibraryRelService labelLibraryRelService;

    /**
     * 查询标签海报关联表分页列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult<PageInfo<LabelLibraryRelVo>>
        getLabelLibraryRelPageListByCondition(@RequestBody LabelLibraryRelConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<LabelLibraryRelVo> pageInfo =
            new PageInfo<>(labelLibraryRelService.getLabelLibraryRelListByCondition(condition));
        return AjaxResult.success(pageInfo);
    }

    /**
     * 查询标签海报关联表列表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult<List<LabelLibraryRelVo>>
        getLabelLibraryRelListByCondition(@RequestBody LabelLibraryRelConditionBo condition) {
        List<LabelLibraryRelVo> list = labelLibraryRelService.getLabelLibraryRelListByCondition(condition);
        return AjaxResult.success(list);
    }

    /**
     * 新增标签海报关联表
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult addLabelLibraryRel(@Validated @RequestBody LabelLibraryRelBo labelLibraryRelBo) {
        return labelLibraryRelService.addLabelLibraryRel(labelLibraryRelBo);
    }

    /**
     * 新增或编辑 标签和节日 与海报关联关系
     * 
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @PostMapping("label/library/festival/rel/add")
    public AjaxResult updateLabelOrFestivalLibraryRel(@Validated @RequestBody LabelLibraryRelBo labelLibraryRelBo) {
        return labelLibraryRelService.updateLabelOrFestivalLibraryRel(labelLibraryRelBo);
    }

    /**
     * 修改标签海报关联表
     * 
     * @param labelLibraryRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult updateLabelLibraryRel(@Validated @RequestBody LabelLibraryRelBo labelLibraryRelBo) {
        if (null == labelLibraryRelBo.getId()) {
            return AjaxResult.fail("标签海报关联表id不能为空");
        }
        return labelLibraryRelService.updateLabelLibraryRel(labelLibraryRelBo);
    }

    /**
     * 查询标签海报关联表详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult<LabelLibraryRelVo> getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("标签海报关联表id不能为空");
        }
        LabelLibraryRelVo vo = labelLibraryRelService.getDetail(id);
        return AjaxResult.success(vo);
    }

    /**
     * 删除标签海报关联表
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-02-27 10:16:32
     */
    @Override
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        LabelLibraryRelDto labelLibraryRelDto = new LabelLibraryRelDto();
        labelLibraryRelDto.setId(id);
        labelLibraryRelDto.setIsDelete(StatusEnum.ISDELETE.getCode());
        if (labelLibraryRelService.updateById(labelLibraryRelDto)) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail("删除失败");
    }
}
