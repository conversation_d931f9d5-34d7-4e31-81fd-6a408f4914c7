package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.label.entity.vo.LabelVo;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelBo;
import com.fh.cloud.screen.service.screen.entity.dto.LabelLibraryAuditRelDto;
import com.fh.cloud.screen.service.screen.entity.bo.LabelLibraryAuditRelConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo;

/**
 * 标签海报关联表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2023-12-06 10:26:05
 */
public interface LabelLibraryAuditRelMapper extends BaseMapper<LabelLibraryAuditRelDto> {

    List<LabelLibraryAuditRelVo> getLabelLibraryAuditRelListByCondition(LabelLibraryAuditRelConditionBo condition);

    LabelLibraryAuditRelVo getLabelLibraryAuditRelByCondition(LabelLibraryAuditRelConditionBo condition);

    List<LabelVo> getLabelListByLibraryAuditId(Long screenModuleLibraryAuditId);

    List<LabelLibraryAuditRelVo> getLabelLibraryAuditRelListWithLibraryAudit(LabelLibraryAuditRelConditionBo condition);

}
