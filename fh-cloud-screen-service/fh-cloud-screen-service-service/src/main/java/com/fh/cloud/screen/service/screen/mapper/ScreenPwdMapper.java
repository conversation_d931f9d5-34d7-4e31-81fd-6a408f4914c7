package com.fh.cloud.screen.service.screen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPwdListConditionBo;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenPwd;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo;

import java.util.List;

/**
 * 云屏密码Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
public interface ScreenPwdMapper extends BaseMapper<ScreenPwd> {

    List<ScreenPwdVo> getScreenPwdListByCondition(ScreenPwdListConditionBo condition);

}
