package com.fh.cloud.screen.service.screen.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryCollectDto;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectConditionBo;
import com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo;
import org.apache.ibatis.annotations.Param;

/**
 * 海报收藏表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
public interface ScreenModuleLibraryCollectMapper extends BaseMapper<ScreenModuleLibraryCollectDto> {

    List<ScreenModuleLibraryCollectVo>
        getScreenModuleLibraryCollectListByCondition(ScreenModuleLibraryCollectConditionBo condition);

    /**
     * 获取热门海报排行前多少个的LibraryIds
     *
     * @param limit page size
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2023/3/23 16:40
     */
    List<Long> getHotPosterModuleLibraryIds(@Param("limit") Integer limit);

    /**
     * 分页获取我收藏的海报记录
     *
     * @param libraryCollectConditionBo
     * @return java.util.List<com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo>
     * <AUTHOR>
     * @date 2023/4/20 15:28
     */
    List<ScreenModuleLibraryCollectVo>
        getLibraryIdPageListByUserOidAndPattern(ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo);
}
