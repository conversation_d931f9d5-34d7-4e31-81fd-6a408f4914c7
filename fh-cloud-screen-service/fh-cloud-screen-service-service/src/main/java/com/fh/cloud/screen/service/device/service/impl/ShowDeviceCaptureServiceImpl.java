package com.fh.cloud.screen.service.device.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.core.utils.FuzzyQueryUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.device.entity.dto.ShowDeviceCaptureDto;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceCaptureService;
import com.fh.cloud.screen.service.device.mapper.ShowDeviceCaptureMapper;
import com.light.core.entity.AjaxResult;

/**
 * 设备抓图表接口实现类
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-20 15:05:05
 */
@Service
public class ShowDeviceCaptureServiceImpl extends ServiceImpl<ShowDeviceCaptureMapper, ShowDeviceCaptureDto>
    implements IShowDeviceCaptureService {

    @Resource
    private ShowDeviceCaptureMapper showDeviceCaptureMapper;

    @Override
    public List<ShowDeviceCaptureVo> getShowDeviceCaptureListByCondition(ShowDeviceCaptureConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        FuzzyQueryUtil.transferMeanBean(condition);
        return showDeviceCaptureMapper.getShowDeviceCaptureListByCondition(condition);
    }

    @Override
    public Long addShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo) {
        ShowDeviceCaptureDto showDeviceCapture = new ShowDeviceCaptureDto();
        BeanUtils.copyProperties(showDeviceCaptureBo, showDeviceCapture);
        showDeviceCapture.setIsDelete(StatusEnum.NOTDELETE.getCode());
        if (save(showDeviceCapture)) {
            return showDeviceCapture.getId();
        } else {
            return null;
        }
    }

    @Override
    public AjaxResult updateShowDeviceCapture(ShowDeviceCaptureBo showDeviceCaptureBo) {
        ShowDeviceCaptureDto showDeviceCapture = new ShowDeviceCaptureDto();
        BeanUtils.copyProperties(showDeviceCaptureBo, showDeviceCapture);
        if (updateById(showDeviceCapture)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public ShowDeviceCaptureVo getDetail(Long id) {
        ShowDeviceCaptureConditionBo condition = new ShowDeviceCaptureConditionBo();
        condition.setId(id);
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        List<ShowDeviceCaptureVo> list = showDeviceCaptureMapper.getShowDeviceCaptureListByCondition(condition);
        ShowDeviceCaptureVo vo = new ShowDeviceCaptureVo();
        if (!CollectionUtils.isEmpty(list)) {
            vo = list.get(0);
        }
        return vo;
    }

    @Override
    public ShowDeviceCaptureVo getDetailByNumber(String deviceNumber) {
        LambdaQueryWrapper<ShowDeviceCaptureDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ShowDeviceCaptureDto::getDeviceNumber, deviceNumber);
        lqw.eq(ShowDeviceCaptureDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.orderByDesc(ShowDeviceCaptureDto::getId);
        lqw.last("limit 1");
        ShowDeviceCaptureDto one = showDeviceCaptureMapper.selectOne(lqw);
        if (one == null) {
            return null;
        }
        ShowDeviceCaptureVo vo = new ShowDeviceCaptureVo();
        BeanUtils.copyProperties(one, vo);
        return vo;
    }
}