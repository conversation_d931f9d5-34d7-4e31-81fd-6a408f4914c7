package com.fh.cloud.screen.service.cs.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fh.cloud.screen.service.cs.entity.dto.CsLikeDto;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeConditionBo;
import com.fh.cloud.screen.service.cs.entity.vo.CsLikeVo;

/**
 * Cultural-Station文化小站喜欢记录表Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-19 10:36:39
 */
public interface CsLikeMapper extends BaseMapper<CsLikeDto> {

	List<CsLikeVo> getCsLikeListByCondition(CsLikeConditionBo condition);

	CsLikeVo getCsLikeByCondition(CsLikeConditionBo condition);

}
