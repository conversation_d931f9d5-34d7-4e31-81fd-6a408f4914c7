package com.fh.cloud.screen.service.enums;

/**
 * 发布状态：1未发布，2已发布
 *
 * <AUTHOR>
 */
public enum ScreenContentStatusType {
    /**
     * 未发布
     */
    NOT_PUBLISH(1),
    /***
     * 已发布
     */
    PUBLISH(2),

    /**
     * 正在展示（已发布 && 未超出有效期）
     */
    ON_SHOW(3),

    /**
     * 已过期（只要超出有效期，不看是否发布）
     */
    OUT_DATE(4);

    ;

    private int value;

    ScreenContentStatusType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}