package com.fh.cloud.screen.service.calendar.controller;

import com.fh.cloud.screen.service.calendar.api.SchoolCalendarApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayOfMonthVo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;
import com.fh.cloud.screen.service.calendar.service.ISchoolCalendarService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校历主表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 16:05:45
 */
@RestController
@RequestMapping("/calendar")
@Validated
@Api(tags = "校历主表")
public class SchoolCalendarController implements SchoolCalendarApi {

    @Autowired
    private ISchoolCalendarService schoolCalendarService;

    /**
     * 查询校历主表列表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询校历主表列表", httpMethod = "POST")
    public AjaxResult getSchoolCalendarListByCondition(@RequestBody SchoolCalendarListConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<SchoolCalendarVo> pageInfo =
            new PageInfo<>(schoolCalendarService.getSchoolCalendarListByCondition(condition));
        Map<String, Object> map = new HashMap<>(4);
        map.put("count", pageInfo.getTotal());
        map.put("schoolCalendarList", pageInfo.getList());
        return AjaxResult.success(map);
    }

    /**
     * 新增校历主表
     *
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增校历主表", httpMethod = "POST")
    public AjaxResult addSchoolCalendar(@RequestBody SchoolCalendarBo schoolCalendarBo) {
        boolean save = schoolCalendarService.addSchoolCalendar(schoolCalendarBo);
        if (save) {
            return AjaxResult.success("新增成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 修改校历主表
     *
     * @param schoolCalendarBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改校历主表", httpMethod = "POST")
    public AjaxResult updateSchoolCalendar(@RequestBody SchoolCalendarBo schoolCalendarBo) {
        if (null == schoolCalendarBo.getSchoolCalendarId()) {
            return AjaxResult.fail("校历主表id不能为空");
        }
        boolean update = schoolCalendarService.updateSchoolCalendar(schoolCalendarBo);
        if (update) {
            return AjaxResult.success("修改成功");
        }
        return AjaxResult.fail();
    }

    /**
     * 通过组织id查询校历主表详情
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/5/10 11:02
     */
    @GetMapping("/detail-by-organizationId")
    @ApiOperation(value = "通过组织id查询校历主表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("organizationId") Long organizationId) {
        return AjaxResult.success(schoolCalendarService.getDetail(organizationId));
    }

    /**
     * 根据月份和组织机构ID 获取校历信息
     *
     * @param attendanceMonth the attendance month 月份 （yyyy-MM）
     * @param organizationId the organization id 组织机构ID
     * @return
     */
    @Override
    @GetMapping("/getDayInfoByMonthAndOrgId")
    public AjaxResult<List<SchoolCalendarDayOfMonthVo>> getDayInfoByMonthAndOrgId(
        @RequestParam("attendanceMonth") String attendanceMonth, @RequestParam("organizationId") Long organizationId) {
        return AjaxResult.success(schoolCalendarService.getDayInfoByMonthAndOrgId(attendanceMonth, organizationId));
    }

    /**
     * 删除校历主表
     *
     * @param schoolCalendarId
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-04-26 16:05:45
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除校历主表", httpMethod = "GET")
    public AjaxResult delete(@RequestParam("schoolCalendarId") Long schoolCalendarId) {
        SchoolCalendarBo schoolCalendarBo = new SchoolCalendarBo();
        schoolCalendarBo.setSchoolCalendarId(schoolCalendarId);
        schoolCalendarBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        boolean delete = schoolCalendarService.updateSchoolCalendar(schoolCalendarBo);
        if (delete) {
            return AjaxResult.success("删除成功");
        }
        return AjaxResult.fail();
    }
}
