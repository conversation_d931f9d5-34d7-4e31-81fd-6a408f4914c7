package com.fh.cloud.screen.service.utils;

import cn.hutool.core.util.StrUtil;

import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
public class RandomUtil {
    public static final String BASE_NUMBER = "0123456789";
    public static final String BASE_CHAR = "abcdefghijklmnopqrstuvwxyz";
    public static final String BASE_UPPER_CHAR = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    public static final String BASE_ALL_CHAR = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public static final Integer CODE = 16;
    public static final Integer SECRET = 32;

    public static String randomString(String baseString, int length) {
        if (StrUtil.isEmpty(baseString)) {
            return "";
        } else {
            StringBuilder sb = new StringBuilder(length);
            if (length < 1) {
                length = 1;
            }

            int baseLength = baseString.length();

            for (int i = 0; i < length; ++i) {
                int number = randomInt(baseLength);
                sb.append(baseString.charAt(number));
            }

            return sb.toString();
        }
    }

    public static int randomInt(int limit) {
        return getRandom().nextInt(limit);
    }

    /**
     * 获取指定位数的随机数：小写字母+数字
     * 
     * @param length
     * @return
     */
    public static String randomString(int length) {
        return randomString(BASE_CHAR.concat(BASE_NUMBER), length);
    }

    /**
     * 获取指定位数的随机数：大写字母+小写字母+数字
     * 
     * @param length
     * @return
     */
    public static String randomStringAll(int length) {
        return randomString(BASE_ALL_CHAR.concat(BASE_NUMBER), length);
    }

    public static ThreadLocalRandom getRandom() {
        return ThreadLocalRandom.current();
    }

    public static void main(String[] args) {
        System.out.println(randomStringAll(16));
    }
}
