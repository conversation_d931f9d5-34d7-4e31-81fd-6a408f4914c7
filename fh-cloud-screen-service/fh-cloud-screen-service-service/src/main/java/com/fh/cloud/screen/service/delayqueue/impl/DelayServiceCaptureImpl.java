package com.fh.cloud.screen.service.delayqueue.impl;

import com.fh.cloud.screen.service.delayqueue.DelayService;
import com.fh.cloud.screen.service.delayqueue.entity.TaskDelayed;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo;
import com.fh.cloud.screen.service.device.service.IShowDeviceCaptureService;
import com.fh.cloud.screen.service.enums.DeviceCaptureStatusType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.List;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 截图延迟队列服务的实现,taskId存放show_device_capture表主键
 * 
 * <AUTHOR>
 * @date 2022/12/21 15:45
 */
@Slf4j
@Service("DelayServiceCaptureImpl")
public class DelayServiceCaptureImpl implements DelayService<ShowDeviceCaptureVo> {

    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;
    @Autowired
    private IShowDeviceCaptureService showDeviceCaptureService;

    /**
     * 专属截图延迟队列服务的延迟队列,是线程安全的
     */
    private final DelayQueue<TaskDelayed<ShowDeviceCaptureVo>> delayQueue = new DelayQueue<>();

    @Override
    public void init() {
        threadPoolExecutor.execute(() -> {
            log.info("启动截图处理线程:" + Thread.currentThread().getName());
            TaskDelayed<ShowDeviceCaptureVo> taskDelayed;
            while (true) {
                try {
                    taskDelayed = delayQueue.take();
                    ShowDeviceCaptureVo detail =
                        showDeviceCaptureService.getDetail(Long.valueOf(taskDelayed.getTaskId()));
                    if (detail != null
                        && !detail.getDeviceCaptureStatus().equals(DeviceCaptureStatusType.SUCCESS.getValue())) {
                        detail.setDeviceCaptureStatus(DeviceCaptureStatusType.FAIL.getValue());
                        ShowDeviceCaptureBo showDeviceCaptureBo = new ShowDeviceCaptureBo();
                        BeanUtils.copyProperties(detail, showDeviceCaptureBo);
                        showDeviceCaptureService.updateShowDeviceCapture(showDeviceCaptureBo);
                    }
                } catch (Exception e) {
                    log.error("执行截图超时处理异常:", e);
                }
            }
        });
    }

    @Override
    public boolean addToDelayQueueBatch(List<TaskDelayed<ShowDeviceCaptureVo>> dataList) {
        this.delayQueue.addAll(dataList);
        return true;
    }

    @Override
    public boolean addToDelayQueue(TaskDelayed<ShowDeviceCaptureVo> data) {
        boolean result = this.delayQueue.add(data);
        return result;
    }

    @Override
    public void removeFromDelayQueue(String taskId) {
        for (Iterator<TaskDelayed<ShowDeviceCaptureVo>> iterator = this.delayQueue.iterator(); iterator.hasNext();) {
            TaskDelayed<ShowDeviceCaptureVo> queue = iterator.next();
            if (queue.getTaskId().equals(taskId)) {
                this.delayQueue.remove(queue);
            }
        }
    }
}
