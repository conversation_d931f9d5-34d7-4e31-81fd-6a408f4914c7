package com.fh.cloud.screen.service.space.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 地点和设备关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("space_device_rel")
public class SpaceDeviceRel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键,无实际意义
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 区域id或者classesId
     */
    @TableField("space_info_id")
    private Long spaceInfoId;

    /**
     * 区域分组使用类型（结合分组类型使用）：1是行政教室，2不是行政教室
     */
    @TableField("space_group_use_type")
    private Integer spaceGroupUseType;

    /**
     * 展示设备id
     */
    @TableField("show_device_id")
    private Long showDeviceId;

    /**
     * 更新时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 所属组织ID
     */
    @TableField("organization_id")
    private Long organizationId;

    /**
     * 所属校区ID
     */
    @TableField("campus_id")
    private Long campusId;

    /**
     * FK区域组表主键
     */
    @TableField("space_group_id")
    private Long spaceGroupId;
}
