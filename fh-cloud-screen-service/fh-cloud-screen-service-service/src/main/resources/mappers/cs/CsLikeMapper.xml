<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.cs.mapper.CsLikeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.cs.entity.dto.CsLikeDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="topicName" column="topic_name"/>
        <result property="likeType" column="like_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="topicName != null and topicName != '' ">and topic_name like concat('%', #{topicName}, '%')</if>
			<if test="likeType != null ">and like_type = #{likeType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.topic_name
	 		,t.like_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from cs_like a
		 ) t

	</sql>

	<select id="getCsLikeListByCondition" resultType="com.fh.cloud.screen.service.cs.entity.vo.CsLikeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCsLikeByCondition" resultType="com.fh.cloud.screen.service.cs.entity.vo.CsLikeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>