<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.label.mapper.LabelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.label.entity.dto.LabelDto" id="BaseResultMap">
        <result property="labelId" column="label_id"/>
        <result property="parentLabelId" column="parent_label_id"/>
        <result property="level" column="level"/>
        <result property="type" column="type"/>
        <result property="labelName" column="label_name"/>
        <result property="labelSort" column="label_sort"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="posterType" column="poster_type"/>
        <result property="classesId" column="classes_id"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="labelId != null ">and label_id = #{labelId}</if>
            <if test="parentLabelId != null ">and parent_label_id = #{parentLabelId}</if>
            <if test="level != null ">and level = #{level}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="labelName != null and labelName != '' ">and label_name like concat('%', #{labelName}, '%')</if>
            <if test="labelSort != null ">and label_sort = #{labelSort}</if>
            <if test="organizationId != null ">and organization_id = #{organizationId}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="posterType != null ">and poster_type = #{posterType}</if>
            <if test="classesId != null">and classes_id = #{classesId}</if>
            <if test="pageNo == -1 and orderBy != null and  orderBy != '' ">order by ${orderBy}</if>
        </where>
    </sql>

    <sql id="common_select">
		select
	 		t.label_id
	 		,t.parent_label_id
	 		,t.level
	 		,t.type
	 		,t.label_name
	 		,t.label_sort
	 		,t.organization_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.poster_type
            ,t.classes_id
		from (
			 select a.* from label a
			 order by a.label_sort

		 ) t

	</sql>

    <select id="getLabelListByCondition" resultType="com.fh.cloud.screen.service.label.entity.vo.LabelVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>
</mapper>