<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.label.mapper.LabelFestivalRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.label.entity.dto.LabelFestivalRelDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="festivalCode" column="festival_code"/>
        <result property="labelId" column="label_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="festivalCode != null and festivalCode != '' ">and festival_code like concat('%', #{festivalCode}, '%')</if>
			<if test="labelId != null ">and label_id = #{labelId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="festivalCodes != null and festivalCodes.size() >0">
				and festival_code in
				<foreach collection="festivalCodes" open="(" close=")" separator="," item="festivalCode">
					#{festivalCode}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.festival_code
	 		,t.label_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.label_name
		from (
			 select a.*,b.label_name from label_festival_rel a
			 join label b on a.label_id = b.label_id and b.is_delete=0
		 ) t

	</sql>

	<select id="getLabelFestivalRelListByCondition" resultType="com.fh.cloud.screen.service.label.entity.vo.LabelFestivalRelVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>