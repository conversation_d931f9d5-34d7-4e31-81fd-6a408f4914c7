<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.label.mapper.LabelLibraryRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.label.entity.dto.LabelLibraryRelDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
        <result property="labelId" column="label_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="screenModuleLibraryId != null ">and screen_module_library_id = #{screenModuleLibraryId}</if>
			<if test="labelId != null ">and label_id = #{labelId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.screen_module_library_id
	 		,t.label_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.label_name
		from (
			 select a.*,b.label_name from label_library_rel a
			 join label b on a.label_id=b.label_id
			 where b.is_delete=0 and a.is_delete=0
		 ) t

	</sql>

	<select id="getLabelLibraryRelListByCondition" resultType="com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

    <select id="getLabelLibraryRelListByConditionOfLabel"
            resultType="com.fh.cloud.screen.service.label.entity.vo.LabelLibraryRelVo">
		select
			t.id
			 ,t.screen_module_library_id
			 ,t.label_id
			 ,t.create_time
			 ,t.create_by
			 ,t.update_time
			 ,t.update_by
			 ,t.is_delete
			 ,GROUP_CONCAT(distinct t.festival_id) as festivalIdConcat
			 ,GROUP_CONCAT(distinct t.type) as festivalTypesConcat
			 ,GROUP_CONCAT(distinct t.label_id) as labelIdConcat
		from (
				 select a.*,c.festival_code,c.festival_id,c.type from label_library_rel a
				 join label_festival_rel b on a.label_id = b.label_id and b.is_delete=0
				 join festival c on b.festival_code = c.festival_code and c.is_delete=0
				 <where>
					 <if test="id != null ">and a.id = #{id}</if>
					 <if test="screenModuleLibraryId != null ">and a.screen_module_library_id = #{screenModuleLibraryId}</if>
					 <if test="labelId != null ">and a.label_id = #{labelId}</if>
					 <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
					 <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
						 and a.screen_module_library_id in
						 <foreach collection="screenModuleLibraryIds" item="screenModuleLibraryId" separator="," open="(" close=")">
							 #{screenModuleLibraryId}
						 </foreach>
					 </if>
					 <if test="festivalIds != null and festivalIds.size() > 0">
						 and c.festival_id in
						 <foreach collection="festivalIds" item="festivalId" separator="," open="(" close=")">
							 #{festivalId}
						 </foreach>
					 </if>
				 </where>
			 ) t
		group by screen_module_library_id
	</select>
</mapper>