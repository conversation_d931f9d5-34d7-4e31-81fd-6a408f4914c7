<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.card.mapper.UserCardMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.card.entity.dto.UserCard" id="BaseResultMap">
	        <result property="userCardId" column="user_card_id"/>
	        <result property="cardNumber" column="card_number"/>
	        <result property="cardType" column="card_type"/>
	        <result property="cardDeviceModel" column="card_device_model"/>
	        <result property="userOid" column="user_oid"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getUserCardListByCondition" resultType="com.fh.cloud.screen.service.card.entity.vo.UserCardVo">
		select a.* from user_card a
	    <where>
	    				    <if test="userCardId != null ">and a.user_card_id = #{userCardId}</if>
						    <if test="cardNumber != null and cardNumber != ''">and a.card_number = #{cardNumber}</if>
						    <if test="cardType != null ">and a.card_type = #{cardType}</if>
						    <if test="cardDeviceModel != null ">and a.card_device_model = #{cardDeviceModel}</if>
						    <if test="userOid != null and userOid != ''">and a.user_oid = #{userOid}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>