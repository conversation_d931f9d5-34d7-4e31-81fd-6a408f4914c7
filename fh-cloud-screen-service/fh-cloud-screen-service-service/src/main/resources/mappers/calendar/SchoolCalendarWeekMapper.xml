<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.calendar.mapper.SchoolCalendarWeekMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarWeek" id="BaseResultMap">
        <result property="schoolCalendarWeekId" column="school_calendar_week_id"/>
        <result property="schoolCalendarId" column="school_calendar_id"/>
        <result property="type" column="type"/>
        <result property="week" column="week"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getSchoolCalendarWeekListByCondition"
            resultType="com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarWeekVo">
        select a.* from school_calendar_week a
        where
            a.school_calendar_id = #{schoolCalendarId}
            <if test="schoolCalendarWeekId != null ">and a.school_calendar_week_id =
                #{schoolCalendarWeekId}
            </if>
            <if test="type != null">and a.type = #{type}</if>
            <if test="week != null">and a.week = #{week}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>

    </select>
</mapper>