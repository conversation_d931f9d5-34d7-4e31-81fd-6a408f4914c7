<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.calendar.mapper.SchoolCalendarDayMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay" id="BaseResultMap">
        <result property="schoolCalendarDayId" column="school_calendar_day_id"/>
        <result property="schoolCalendarId" column="school_calendar_id"/>
        <result property="type" column="type"/>
        <result property="day" column="day"/>
        <result property="week" column="week"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getSchoolCalendarDayListByCondition"
            resultType="com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayVo">
        select a.* from school_calendar_day a
        where
            a.school_calendar_id =  #{schoolCalendarId}
            <if test="schoolCalendarDayId != null ">and a.school_calendar_day_id =
                #{schoolCalendarDayId}
            </if>
            <if test="type != null ">and a.type = #{type}</if>
            <if test="day != null and day != ''">and a.day = #{day}</if>
            <if test="week != null ">and a.week = #{week}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
            <if test="month != null and month != ''">and date_format(a.day, "%Y-%m") = #{month}</if>
    </select>
    <select id="getBySchoolCalendarIdAndMonth"
            resultType="com.fh.cloud.screen.service.calendar.entity.dto.SchoolCalendarDay">

        select a.* from school_calendar_day a
        where
            a.school_calendar_id =  #{schoolCalendarId}
            and date_format(a.day, "%Y-%m") = #{month}
    </select>
</mapper>