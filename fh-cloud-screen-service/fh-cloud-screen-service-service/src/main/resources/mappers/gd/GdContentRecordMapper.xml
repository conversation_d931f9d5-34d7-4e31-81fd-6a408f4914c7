<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.gd.mapper.GdContentRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.gd.entity.dto.GdContentRecordDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="gdOid" column="gd_oid"/>
        <result property="gdId" column="gd_id"/>
        <result property="fileInfoJson" column="file_info_json"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="gdOid != null and gdOid != '' ">and gd_oid = #{gdOid}</if>
			<if test="gdId != null and gdId != '' ">and gd_id = #{gdId}</if>
			<if test="fileInfoJson != null and fileInfoJson != '' ">and file_info_json like concat('%', #{fileInfoJson}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.organization_id
	 		,t.user_oid
	 		,t.gd_oid
	 		,t.gd_id
	 		,t.file_info_json
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from gd_content_record a
		 ) t

	</sql>

	<select id="getGdContentRecordListByCondition" resultType="com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getGdContentRecordByCondition" resultType="com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>