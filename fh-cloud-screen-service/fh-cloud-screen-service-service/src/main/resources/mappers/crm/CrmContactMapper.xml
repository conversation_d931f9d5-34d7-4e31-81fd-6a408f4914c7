<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.crm.mapper.CrmContactMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.crm.entity.dto.CrmContactDto" id="BaseResultMap">
        <result property="crmContactId" column="crm_contact_id"/>
        <result property="crmInfoId" column="crm_info_id"/>
        <result property="contactName" column="contact_name"/>
        <result property="contactWay" column="contact_way"/>
        <result property="contactDutiesType" column="contact_duties_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="crmContactId != null ">and crm_contact_id = #{crmContactId}</if>
			<if test="crmInfoId != null ">and crm_info_id = #{crmInfoId}</if>
			<if test="contactName != null and contactName != '' ">and contact_name like concat('%', #{contactName}, '%')</if>
			<if test="contactWay != null and contactWay != '' ">and contact_way like concat('%', #{contactWay}, '%')</if>
			<if test="contactDutiesType != null ">and contact_duties_type = #{contactDutiesType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="crmInfoIds != null and crmInfoIds.size()>0">
				and crm_info_id in
				<foreach collection="crmInfoIds" item="crmInfoId" open="(" separator="," close=")">
					#{crmInfoId}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.crm_contact_id
	 		,t.crm_info_id
	 		,t.contact_name
	 		,t.contact_way
	 		,t.contact_duties_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from crm_contact a
		 ) t

	</sql>

	<select id="getCrmContactListByCondition" resultType="com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCrmContactByCondition" resultType="com.fh.cloud.screen.service.crm.entity.vo.CrmContactVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>