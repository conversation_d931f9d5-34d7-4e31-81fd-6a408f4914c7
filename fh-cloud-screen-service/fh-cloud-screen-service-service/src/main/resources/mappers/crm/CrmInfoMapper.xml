<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.crm.mapper.CrmInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.crm.entity.dto.CrmInfoDto" id="BaseResultMap">
        <result property="crmInfoId" column="crm_info_id"/>
        <result property="saleName" column="sale_name"/>
        <result property="schoolName" column="school_name"/>
        <result property="provinceId" column="province_id"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityId" column="city_id"/>
        <result property="cityName" column="city_name"/>
        <result property="areaId" column="area_id"/>
        <result property="areaName" column="area_name"/>
        <result property="address" column="address"/>
        <result property="schoolCulturalFocus" column="school_cultural_focus"/>
        <result property="culturalWorked" column="cultural_worked"/>
        <result property="culturalInputType" column="cultural_input_type"/>
        <result property="leaderCulturalFocus" column="leader_cultural_focus"/>
        <result property="opportunityType" column="opportunity_type"/>
        <result property="opportunityReason" column="opportunity_reason"/>
        <result property="brandWorkingType" column="brand_working_type"/>
        <result property="brandAdapterType" column="brand_adapter_type"/>
        <result property="brandNotAdapterReason" column="brand_not_adapter_reason"/>
        <result property="brandModel" column="brand_model"/>
        <result property="brandNumber" column="brand_number"/>
        <result property="schoolLevelType" column="school_level_type"/>
        <result property="schoolRankingType" column="school_ranking_type"/>
        <result property="schoolScale" column="school_scale"/>
        <result property="schoolTrackProject" column="school_track_project"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="crmInfoId != null ">and crm_info_id = #{crmInfoId}</if>
			<if test="saleName != null and saleName != '' ">and sale_name like concat('%', #{saleName}, '%')</if>
			<if test="schoolName != null and schoolName != '' ">and school_name like concat('%', #{schoolName}, '%')</if>
			<if test="provinceId != null ">and province_id = #{provinceId}</if>
			<if test="provinceName != null and provinceName != '' ">and province_name like concat('%', #{provinceName}, '%')</if>
			<if test="cityId != null ">and city_id = #{cityId}</if>
			<if test="cityName != null and cityName != '' ">and city_name like concat('%', #{cityName}, '%')</if>
			<if test="areaId != null ">and area_id = #{areaId}</if>
			<if test="areaName != null and areaName != '' ">and area_name like concat('%', #{areaName}, '%')</if>
			<if test="address != null and address != '' ">and address like concat('%', #{address}, '%')</if>
			<if test="schoolCulturalFocus != null ">and school_cultural_focus = #{schoolCulturalFocus}</if>
			<if test="culturalWorked != null and culturalWorked != '' ">and cultural_worked like concat('%', #{culturalWorked}, '%')</if>
			<if test="culturalInputType != null ">and cultural_input_type = #{culturalInputType}</if>
			<if test="leaderCulturalFocus != null ">and leader_cultural_focus = #{leaderCulturalFocus}</if>
			<if test="opportunityType != null ">and opportunity_type = #{opportunityType}</if>
			<if test="opportunityReason != null and opportunityReason != '' ">and opportunity_reason like concat('%', #{opportunityReason}, '%')</if>
			<if test="brandWorkingType != null ">and brand_working_type = #{brandWorkingType}</if>
			<if test="brandAdapterType != null ">and brand_adapter_type = #{brandAdapterType}</if>
			<if test="brandNotAdapterReason != null and brandNotAdapterReason != '' ">and brand_not_adapter_reason like concat('%', #{brandNotAdapterReason}, '%')</if>
			<if test="brandModel != null and brandModel != '' ">and brand_model like concat('%', #{brandModel}, '%')</if>
			<if test="brandNumber != null and brandNumber != '' ">and brand_number like concat('%', #{brandNumber}, '%')</if>
			<if test="schoolLevelType != null ">and school_level_type = #{schoolLevelType}</if>
			<if test="schoolRankingType != null ">and school_ranking_type = #{schoolRankingType}</if>
			<if test="schoolScale != null and schoolScale != '' ">and school_scale like concat('%', #{schoolScale}, '%')</if>
			<if test="schoolTrackProject != null and schoolTrackProject != '' ">and school_track_project like concat('%', #{schoolTrackProject}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.crm_info_id
	 		,t.sale_name
	 		,t.school_name
	 		,t.province_id
	 		,t.province_name
	 		,t.city_id
	 		,t.city_name
	 		,t.area_id
	 		,t.area_name
	 		,t.address
	 		,t.school_cultural_focus
	 		,t.cultural_worked
	 		,t.cultural_input_type
	 		,t.leader_cultural_focus
	 		,t.opportunity_type
	 		,t.opportunity_reason
	 		,t.brand_working_type
	 		,t.brand_adapter_type
	 		,t.brand_not_adapter_reason
	 		,t.brand_model
	 		,t.brand_number
	 		,t.school_level_type
	 		,t.school_ranking_type
	 		,t.school_scale
	 		,t.school_track_project
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from crm_info a
		 ) t

	</sql>

	<select id="getCrmInfoListByCondition" resultType="com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getCrmInfoByCondition" resultType="com.fh.cloud.screen.service.crm.entity.vo.CrmInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>