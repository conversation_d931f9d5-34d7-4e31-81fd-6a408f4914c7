<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.er.mapper.ExamInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.er.entity.dto.ExamInfoDto" id="BaseResultMap">
        <result property="examInfoId" column="exam_info_id"/>
        <result property="examPlanId" column="exam_plan_id"/>
        <result property="examRoomName" column="exam_room_name"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="spaceGroupId" column="space_group_id"/>
        <result property="spaceGroupName" column="space_group_name"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="examInfoId != null ">and exam_info_id = #{examInfoId}</if>
			<if test="examPlanId != null ">and exam_plan_id = #{examPlanId}</if>
			<if test="examRoomName != null and examRoomName != '' ">and exam_room_name like concat('%', #{examRoomName}, '%')</if>
			<if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
			<if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="spaceGroupId != null ">and space_group_id = #{spaceGroupId}</if>
			<if test="spaceGroupName != null and spaceGroupName !='' ">and space_group_name = #{spaceGroupName}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.exam_info_id
	 		,t.exam_plan_id
	 		,t.exam_room_name
	 		,t.space_group_use_type
	 		,t.space_info_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.space_group_id
			,t.space_group_name
		from (
			 select a.* from er_exam_info a
			 <if test="subjectName != null and subjectName != ''">
				 join er_exam_info_subject b on a.exam_info_id = b.exam_info_id and b.is_delete=0
			     and b.subject_name like concat('%', #{subjectName}, '%')
			 </if>
		 ) t
	</sql>

	<select id="getExamInfoListByCondition" resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>