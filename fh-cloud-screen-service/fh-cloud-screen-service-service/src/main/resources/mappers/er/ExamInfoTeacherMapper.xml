<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.er.mapper.ExamInfoTeacherMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.er.entity.dto.ExamInfoTeacherDto" id="BaseResultMap">
        <result property="examInfoTeacherId" column="exam_info_teacher_id"/>
        <result property="examInfoSubjectId" column="exam_info_subject_id"/>
        <result property="examInfoId" column="exam_info_id"/>
        <result property="examPlanId" column="exam_plan_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="realName" column="real_name"/>
        <result property="userFromType" column="user_from_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="examInfoTeacherId != null ">and exam_info_teacher_id = #{examInfoTeacherId}</if>
            <if test="examInfoSubjectId != null ">and exam_info_subject_id = #{examInfoSubjectId}</if>
            <if test="examInfoId != null ">and exam_info_id = #{examInfoId}</if>
            <if test="examPlanId != null ">and exam_plan_id = #{examPlanId}</if>
            <if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
            <if test="realName != null and realName != '' ">and real_name like concat('%', #{realName}, '%')</if>
            <if test="userFromType != null ">and user_from_type = #{userFromType}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="examInfoSubjectIds != null and examInfoSubjectIds.size() >0">
                and exam_info_subject_id in
                <foreach collection="examInfoSubjectIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="common_select">
		select
	 		t.exam_info_teacher_id
			,t.exam_info_subject_id
			,t.exam_info_id
	 		,t.exam_plan_id
	 		,t.user_oid
	 		,t.real_name
	 		,t.user_from_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from er_exam_info_teacher a
		 ) t

	</sql>

    <select id="getExamInfoTeacherListByCondition"
            resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoTeacherVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <insert id="addExamInfoTeacherBatchByXML">
        insert into er_exam_info_teacher
        (exam_info_teacher_id,exam_info_subject_id,exam_info_id,exam_plan_id,user_oid,
        real_name,user_from_type)
        values
        <foreach collection="examInfoTeacherBos" item="item" separator=",">
            (null,#{item.examInfoSubjectId},#{item.examInfoId},#{item.examPlanId},
            #{item.userOid},#{item.realName},#{item.userFromType})
        </foreach>
    </insert>
</mapper>