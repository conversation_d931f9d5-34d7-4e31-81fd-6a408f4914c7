<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.er.mapper.ExamPlanGradeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.er.entity.dto.ExamPlanGradeDto" id="BaseResultMap">
        <result property="examPlanGradeId" column="exam_plan_grade_id"/>
        <result property="examPlanId" column="exam_plan_id"/>
        <result property="grade" column="grade"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="examPlanGradeId != null ">and exam_plan_grade_id = #{examPlanGradeId}</if>
			<if test="examPlanId != null ">and exam_plan_id = #{examPlanId}</if>
			<if test="grade != null and grade != '' ">and grade like concat('%', #{grade}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="examPlanIds != null and examPlanIds.size() > 0">
				and exam_plan_id in
				<foreach collection="examPlanIds" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.exam_plan_grade_id
	 		,t.exam_plan_id
	 		,t.grade
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from er_exam_plan_grade a
		 ) t

	</sql>

	<select id="getExamPlanGradeListByCondition" resultType="com.fh.cloud.screen.service.er.entity.vo.ExamPlanGradeVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>