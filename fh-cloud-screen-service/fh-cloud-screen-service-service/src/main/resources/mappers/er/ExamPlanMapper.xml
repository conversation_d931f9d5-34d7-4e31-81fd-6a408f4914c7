<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.er.mapper.ExamPlanMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.er.entity.dto.ExamPlanDto" id="BaseResultMap">
        <result property="examPlanId" column="exam_plan_id"/>
        <result property="examPlanName" column="exam_plan_name"/>
        <result property="organizationId" column="organization_id"/>
        <result property="examPlanStartTime" column="exam_plan_start_time"/>
        <result property="examPlanEndTime" column="exam_plan_end_time"/>
        <result property="examPlanRemark" column="exam_plan_remark"/>
        <result property="examPlanType" column="exam_plan_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="isSignIn" column="is_sign_in"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="examPlanId != null ">and exam_plan_id = #{examPlanId}</if>
			<if test="examPlanName != null and examPlanName != '' ">and exam_plan_name like concat('%', #{examPlanName}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="examPlanStartTime != null ">and exam_plan_start_time = #{examPlanStartTime}</if>
			<if test="examPlanEndTime != null ">and exam_plan_end_time = #{examPlanEndTime}</if>
			<if test="examPlanRemark != null and examPlanRemark != '' ">and exam_plan_remark like concat('%', #{examPlanRemark}, '%')</if>
			<if test="examPlanType != null ">and exam_plan_type = #{examPlanType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="isSignIn != null ">and is_sign_in = #{isSignIn}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.exam_plan_id
	 		,t.exam_plan_name
	 		,t.organization_id
	 		,t.exam_plan_start_time
	 		,t.exam_plan_end_time
	 		,t.exam_plan_remark
	 		,t.exam_plan_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.is_sign_in
		from (
			 select a.* from er_exam_plan a
		 ) t

	</sql>

	<select id="getExamPlanListByCondition" resultType="com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>