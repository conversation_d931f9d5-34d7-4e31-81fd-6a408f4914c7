<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.er.mapper.ExamInfoSubjectMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.er.entity.dto.ExamInfoSubjectDto" id="BaseResultMap">
        <result property="examInfoSubjectId" column="exam_info_subject_id"/>
        <result property="examInfoId" column="exam_info_id"/>
        <result property="examPlanId" column="exam_plan_id"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="subjectName" column="subject_name"/>
        <result property="atNoStart" column="at_no_start"/>
        <result property="atNoEnd" column="at_no_end"/>
        <result property="examStartTime" column="exam_start_time"/>
        <result property="examEndTime" column="exam_end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="typeId" column="type_id"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="examInfoSubjectId != null ">and exam_info_subject_id = #{examInfoSubjectId}</if>
            <if test="examInfoId != null ">and exam_info_id = #{examInfoId}</if>
            <if test="examPlanId != null ">and exam_plan_id = #{examPlanId}</if>
            <if test="subjectCode != null and subjectCode != '' ">and subject_code like concat('%', #{subjectCode},
                '%')
            </if>
            <if test="subjectName != null and subjectName != '' ">and subject_name like concat('%', #{subjectName},
                '%')
            </if>
            <if test="atNoStart != null and atNoStart != '' ">and at_no_start like concat('%', #{atNoStart}, '%')</if>
            <if test="atNoEnd != null and atNoEnd != '' ">and at_no_end like concat('%', #{atNoEnd}, '%')</if>
            <if test="examStartTime != null ">and exam_start_time = #{examStartTime}</if>
            <if test="examEndTime != null ">and exam_end_time = #{examEndTime}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="typeId != null ">and type_id = #{typeId}</if>
            <if test="examInfoIds != null and examInfoIds.size() >0">
                and exam_info_id in
                <foreach collection="examInfoIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="common_select">
        select
        <!-- 如果主键不是id，这里需要修改 -->
        t.exam_info_subject_id
        ,t.exam_info_id
        ,t.exam_plan_id
        ,t.subject_code
        ,t.subject_name
        ,t.at_no_start
        ,t.at_no_end
        ,t.exam_start_time
        ,t.exam_end_time
        ,t.create_time
        ,t.create_by
        ,t.update_time
        ,t.update_by
        ,t.is_delete
        ,t.type_id
        from (
        select a.* from er_exam_info_subject a
        ) t

    </sql>

    <select id="getExamInfoSubjectListByCondition"
            resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <select id="getExamInfoSubjectListBySpaceAndDate"
            resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo">
		select b.*,c.update_time as planUpdateTime
		from er_exam_info a
		join er_exam_info_subject b on a.exam_info_id=b.exam_info_id
		join er_exam_plan c on c.exam_plan_id=a.exam_plan_id
		where
		a.is_delete=0 and b.is_delete=0
		and a.space_group_use_type=#{spaceGroupUseType}
		and a.space_info_id=#{spaceInfoId}
		and DATE_FORMAT(b.exam_start_time,'%Y-%m-%d') = DATE_FORMAT(#{date},'%Y-%m-%d')
		and c.exam_plan_type=2
	</select>

    <select id="getNowExamInfoByCondition" resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo">
        select c.*,a.exam_plan_name,a.exam_plan_remark,b.exam_room_name
        from er_exam_plan a
        join er_exam_info b on a.exam_plan_id=b.exam_plan_id
        join er_exam_info_subject c on b.exam_info_id=c.exam_info_id
        where
        a.is_delete=0 and b.is_delete=0 and c.is_delete=0
        and b.space_group_use_type=#{spaceGroupUseType}
        and b.space_info_id=#{spaceInfoId}
        and DATE_FORMAT(c.exam_start_time,'%Y-%m-%d') = DATE_FORMAT(#{examStartTime},'%Y-%m-%d')
        and a.exam_plan_type=2
        <if test="orderBy != null">
            order by ${orderBy}
        </if>
    </select>

    <select id="getExamInfoSubjectListByOrganizationId"
            resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo">
        select c.*,b.space_group_use_type,b.space_info_id,b.exam_room_name
		from er_exam_plan a
		join er_exam_info b on a.exam_plan_id=b.exam_plan_id
		join er_exam_info_subject c on b.exam_info_id=c.exam_info_id
		where
		a.is_delete=0 and b.is_delete=0 and c.is_delete=0
		and a.organization_id=#{organizationId}
    </select>
</mapper>