<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.er.mapper.ExamInfoStudentMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.er.entity.dto.ExamInfoStudentDto" id="BaseResultMap">
        <result property="examInfoStudentId" column="exam_info_student_id"/>
        <result property="examInfoSubjectId" column="exam_info_subject_id"/>
        <result property="examInfoId" column="exam_info_id"/>
        <result property="examPlanId" column="exam_plan_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="realName" column="real_name"/>
        <result property="userFromType" column="user_from_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="attendanceTime" column="attendance_time"/>
        <result property="attendanceStatus" column="attendance_status"/>
        <result property="classesId" column="classes_id"/>
        <result property="grade" column="grade"/>
        <result property="registrationNumber" column="registration_number"/>
        <result property="seatNumber" column="seat_number"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="examInfoStudentId != null ">and exam_info_student_id = #{examInfoStudentId}</if>
            <if test="examInfoSubjectId != null ">and exam_info_subject_id = #{examInfoSubjectId}</if>
            <if test="examInfoId != null ">and exam_info_id = #{examInfoId}</if>
            <if test="examPlanId != null ">and exam_plan_id = #{examPlanId}</if>
            <if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
            <if test="realName != null and realName != '' ">and real_name like concat('%', #{realName}, '%')</if>
            <if test="userFromType != null ">and user_from_type = #{userFromType}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="attendanceTime != null">and attendance_time = #{attendanceTime}</if>
            <if test="attendanceStatus != null ">and attendance_status = #{attendanceStatus}</if>
            <if test="classesId != null ">and classes_id = #{classesId}</if>
            <if test="grade != null ">and grade = #{grade}</if>
            <if test="registrationNumber != null ">and registration_number = #{registrationNumber}</if>
            <if test="seatNumber != null ">and seat_number = #{seatNumber}</if>
            <if test="examInfoSubjectIds != null and examInfoSubjectIds.size() >0">
                and exam_info_subject_id in
                <foreach collection="examInfoSubjectIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="attendanceStatusList != null and attendanceStatusList.size() >0">
                and attendance_status in
                <foreach collection="attendanceStatusList" open="(" close=")" separator="," item="attendanceStatusItem">
                    #{attendanceStatusItem}
                </foreach>
            </if>
        </where>
    </sql>

    <sql id="common_select">
		select
	 		t.exam_info_student_id
		    ,t.exam_info_subject_id
		    ,t.exam_info_id
	 		,t.exam_plan_id
	 		,t.user_oid
	 		,t.real_name
	 		,t.user_from_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		    ,t.attendance_time
			,t.attendance_status
			,t.classes_id
			,t.grade
		    ,t.registration_number
		    ,t.seat_number
		from (
			 select a.* from er_exam_info_student a
		 ) t

	</sql>

    <select id="getExamInfoStudentListByCondition"
            resultType="com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <insert id="addExamInfoStudentBatchByXML">
        insert into er_exam_info_student
        (exam_info_subject_id,exam_info_id,exam_plan_id,user_oid,
        real_name,user_from_type,classes_id,grade,registration_number,seat_number)
        values
        <foreach collection="examInfoStudentBos" item="item" separator=",">
            (#{item.examInfoSubjectId},#{item.examInfoId},#{item.examPlanId},
            #{item.userOid},#{item.realName},#{item.userFromType},#{item.classesId},#{item.grade},#{item.registrationNumber},#{item.seatNumber})
        </foreach>
    </insert>
</mapper>