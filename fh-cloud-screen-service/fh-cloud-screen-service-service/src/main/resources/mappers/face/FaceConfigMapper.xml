<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.face.mapper.FaceConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.face.entity.dto.FaceConfigDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="threshold" column="threshold"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="faceBrandType" column="face_brand_type"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="threshold != null and threshold != '' ">and threshold like concat('%', #{threshold}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="faceBrandType != null ">and face_brand_type = #{faceBrandType}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.organization_id
	 		,t.threshold
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.face_brand_type
		from (
			 select a.* from face_config a
		 ) t

	</sql>

	<select id="getFaceConfigListByCondition" resultType="com.fh.cloud.screen.service.face.entity.vo.FaceConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>