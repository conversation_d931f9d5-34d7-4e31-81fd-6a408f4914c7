<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.face.mapper.FaceRecordStudentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.face.entity.dto.FaceRecordStudentDto" id="BaseResultMap">
        <result property="faceRecordStudentId" column="face_record_student_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="sourceType" column="source_type"/>
        <result property="tipMessage" column="tip_message"/>
        <result property="faceStatus" column="face_status"/>
        <result property="faceMediaUrl" column="face_media_url"/>
        <result property="faceMediaUrlCompress" column="face_media_url_compress"/>
        <result property="faceMediaName" column="face_media_name"/>
        <result property="faceMediaNameOri" column="face_media_name_ori"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="organizationId" column="organization_id"/>
		<result property="faceMediaId" column="face_media_id"/>
		<result property="faceMediaIdCompress" column="face_media_id_compress"/>
		<result property="realName" column="real_name"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="faceRecordStudentId != null ">and face_record_student_id = #{faceRecordStudentId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="tipMessage != null and tipMessage != '' ">and tip_message like concat('%', #{tipMessage}, '%')</if>
			<if test="faceStatus != null ">and face_status = #{faceStatus}</if>
			<if test="faceMediaUrl != null and faceMediaUrl != '' ">and face_media_url like concat('%', #{faceMediaUrl}, '%')</if>
			<if test="faceMediaUrlCompress != null and faceMediaUrlCompress != '' ">and face_media_url_compress like concat('%', #{faceMediaUrlCompress}, '%')</if>
			<if test="faceMediaName != null and faceMediaName != '' ">and face_media_name like concat('%', #{faceMediaName}, '%')</if>
			<if test="realName != null and realName != '' ">and real_name like concat('%', #{realName}, '%')</if>
			<if test="faceMediaNameOri != null and faceMediaNameOri != '' ">and face_media_name_ori like concat('%', #{faceMediaNameOri}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="faceMediaId != null and faceMediaId !='' ">and face_media_id = #{faceMediaId}</if>
			<if test="faceMediaIdCompress != null and faceMediaIdCompress !='' ">and face_media_id_compress = #{faceMediaIdCompress}</if>
			<if test="realNames != null and realNames.size() >0">
				and face_media_name in
				<foreach collection="realNames" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="userOids != null and userOids.size() >0">
				and user_oid in
				<foreach collection="userOids" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
			<if test="groupByUserOid != null and groupByUserOid !=''">
				group by user_oid
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.face_record_student_id
	 		,t.user_oid
	 		,t.source_type
	 		,t.tip_message
	 		,t.face_status
	 		,t.face_media_url
	 		,t.face_media_url_compress
	 		,t.face_media_name
	 		,t.face_media_name_ori
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.organization_id
			,t.face_media_id
			,t.face_media_id_compress
			,t.real_name
		from (
			 select a.* from face_record_student a
		 ) t

	</sql>

    <select id="getFaceRecordStudentListByCondition" resultType="com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<update id="updateRealNameByUserOid" parameterType="map">
		update face_record_student set real_name =#{realName} where user_oid=#{userOid}
	</update>
</mapper>