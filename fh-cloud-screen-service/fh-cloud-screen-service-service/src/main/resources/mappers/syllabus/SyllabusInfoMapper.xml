<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.syllabus.mapper.SyllabusInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.syllabus.entity.dto.SyllabusInfoDto" id="BaseResultMap">
        <result property="syllabusId" column="syllabus_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="weekId" column="week_id"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="source" column="source"/>
        <result property="classesId" column="classes_id"/>
        <result property="classesName" column="classes_name"/>
        <result property="subjectCode" column="subject_code"/>
        <result property="subjectName" column="subject_name"/>
        <result property="teacherName" column="teacher_name"/>
        <result property="doubleSubjectCode" column="double_subject_code"/>
        <result property="doubleSubjectName" column="double_subject_name"/>
        <result property="doubleTeacherName" column="double_teacher_name"/>
		<result property="createTime" column="create_time"/>
		<result property="createBy" column="create_by"/>
		<result property="updateTime" column="update_time"/>
		<result property="updateBy" column="update_by"/>
		<result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="syllabusId != null ">and syllabus_id = #{syllabusId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="weekId != null ">and week_id = #{weekId}</if>
			<if test="sort != null ">and sort = #{sort}</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="source != null ">and source = #{source}</if>
			<if test="classesId != null ">and classes_id = #{classesId}</if>
			<if test="classesName != null and classesName != '' ">and classes_name like concat('%', #{classesName}, '%')</if>
			<if test="subjectCode != null and subjectCode != '' ">and subject_code like concat('%', #{subjectCode}, '%')</if>
			<if test="subjectName != null and subjectName != '' ">and subject_name like concat('%', #{subjectName}, '%')</if>
			<if test="teacherName != null and teacherName != '' ">and teacher_name like concat('%', #{teacherName}, '%')</if>
			<if test="doubleSubjectCode != null and doubleSubjectCode != '' ">and double_subject_code like concat('%', #{doubleSubjectCode}, '%')</if>
			<if test="doubleSubjectName != null and doubleSubjectName != '' ">and double_subject_name like concat('%', #{doubleSubjectName}, '%')</if>
			<if test="doubleTeacherName != null and doubleTeacherName != '' ">and double_teacher_name like concat('%', #{doubleTeacherName}, '%')</if>
			<if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
			<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.syllabus_id
	 		,t.organization_id
	 		,t.week_id
	 		,t.sort
	 		,t.status
	 		,t.source
	 		,t.classes_id
	 		,t.classes_name
	 		,t.subject_code
	 		,t.subject_name
	 		,t.teacher_name
	 		,t.double_subject_code
	 		,t.double_subject_name
	 		,t.double_teacher_name
			,t.create_time
			,t.create_by
			,t.update_time
			,t.update_by
			,t.is_delete
		from (
			 select a.* from syllabus_info a
		 ) t

	</sql>

	<select id="getSyllabusInfoListByCondition" resultType="com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		<if test=" pageNo != null and pageNo == -1 and orderBy != null and orderBy != ''">
			order by ${orderBy}
		</if>
	</select>

	<select id="getSyllabusInfoByCondition" resultType="com.fh.cloud.screen.service.syllabus.entity.vo.SyllabusInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>