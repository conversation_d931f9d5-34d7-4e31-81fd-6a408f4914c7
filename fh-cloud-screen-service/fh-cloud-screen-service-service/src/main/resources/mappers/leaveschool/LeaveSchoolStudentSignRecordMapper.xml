<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolStudentSignRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolStudentSignRecordDto" id="BaseResultMap">
        <result property="leaveSchoolStudentSignRecordId" column="leave_school_student_sign_record_id"/>
        <result property="studentId" column="student_id"/>
        <result property="studentName" column="student_name"/>
        <result property="identityCardNumber" column="identity_card_number"/>
        <result property="organizationId" column="organization_id"/>
        <result property="organizationName" column="organization_name"/>
        <result property="classesId" column="classes_id"/>
        <result property="signTime" column="sign_time"/>
        <result property="signType" column="sign_type"/>
        <result property="signTypeName" column="sign_type_name"/>
        <result property="signWay" column="sign_way"/>
        <result property="signWayName" column="sign_way_name"/>
        <result property="thirdSchoolId" column="third_school_id"/>
        <result property="thirdSchoolName" column="third_school_name"/>
        <result property="thirdStudentUserId" column="third_student_user_id"/>
        <result property="thirdStudentName" column="third_student_name"/>
        <result property="thirdCardNumber" column="third_card_number"/>
        <result property="thirdSignRecordId" column="third_sign_record_id"/>
        <result property="appCode" column="app_code"/>
        <result property="sourceType" column="source_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="leaveSchoolStudentSignRecordId != null ">and leave_school_student_sign_record_id = #{leaveSchoolStudentSignRecordId}</if>
			<if test="studentId != null ">and student_id = #{studentId}</if>
			<if test="studentName != null and studentName != '' ">and student_name like concat('%', #{studentName}, '%')</if>
			<if test="identityCardNumber != null and identityCardNumber != '' ">and identity_card_number like concat('%', #{identityCardNumber}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="organizationName != null and organizationName != '' ">and organization_name like concat('%', #{organizationName}, '%')</if>
			<if test="classesId != null ">and classes_id = #{classesId}</if>
			<if test="signTime != null ">and sign_time = #{signTime}</if>
			<if test="signType != null ">and sign_type = #{signType}</if>
			<if test="signTypeName != null and signTypeName != '' ">and sign_type_name like concat('%', #{signTypeName}, '%')</if>
			<if test="signWay != null ">and sign_way = #{signWay}</if>
			<if test="signWayName != null and signWayName != '' ">and sign_way_name like concat('%', #{signWayName}, '%')</if>
			<if test="thirdSchoolId != null and thirdSchoolId != '' ">and third_school_id like concat('%', #{thirdSchoolId}, '%')</if>
			<if test="thirdSchoolName != null and thirdSchoolName != '' ">and third_school_name like concat('%', #{thirdSchoolName}, '%')</if>
			<if test="thirdStudentUserId != null and thirdStudentUserId != '' ">and third_student_user_id like concat('%', #{thirdStudentUserId}, '%')</if>
			<if test="thirdStudentName != null and thirdStudentName != '' ">and third_student_name like concat('%', #{thirdStudentName}, '%')</if>
			<if test="thirdCardNumber != null and thirdCardNumber != '' ">and third_card_number like concat('%', #{thirdCardNumber}, '%')</if>
			<if test="thirdSignRecordId != null and thirdSignRecordId != '' ">and third_sign_record_id like concat('%', #{thirdSignRecordId}, '%')</if>
			<if test="appCode != null ">and app_code = #{appCode}</if>
			<if test="sourceType != null ">and source_type = #{sourceType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.leave_school_student_sign_record_id
	 		,t.student_id
	 		,t.student_name
	 		,t.identity_card_number
	 		,t.organization_id
	 		,t.organization_name
	 		,t.classes_id
	 		,t.sign_time
	 		,t.sign_type
	 		,t.sign_type_name
	 		,t.sign_way
	 		,t.sign_way_name
	 		,t.third_school_id
	 		,t.third_school_name
	 		,t.third_student_user_id
	 		,t.third_student_name
	 		,t.third_card_number
	 		,t.third_sign_record_id
	 		,t.app_code
	 		,t.source_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from leave_school_student_sign_record a
		 ) t

	</sql>

	<select id="getLeaveSchoolStudentSignRecordListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolStudentSignRecordByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolStudentSignRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>