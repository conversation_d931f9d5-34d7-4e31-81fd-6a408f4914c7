<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolRecordMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordDto" id="BaseResultMap">
        <result property="leaveSchoolRecordId" column="leave_school_record_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="leaveSchoolType" column="leave_school_type"/>
        <result property="leaveSchoolDay" column="leave_school_day"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="leaveSchoolRecordId != null ">and leave_school_record_id = #{leaveSchoolRecordId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
			<if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
			<if test="leaveSchoolType != null ">and leave_school_type = #{leaveSchoolType}</if>
			<if test="leaveSchoolDay != null ">and leave_school_day = #{leaveSchoolDay}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="spaceInfoIds != null and spaceInfoIds.size() != 0">
				and space_info_id in
				<foreach collection="spaceInfoIds" item="item" index="index" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="leaveSchoolRecordId != null ">and a.leave_school_record_id = #{leaveSchoolRecordId}</if>
			<if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
			<if test="campusId != null ">and a.campus_id = #{campusId}</if>
			<if test="spaceInfoId != null ">and a.space_info_id = #{spaceInfoId}</if>
			<if test="spaceGroupUseType != null ">and a.space_group_use_type = #{spaceGroupUseType}</if>
			<if test="leaveSchoolType != null ">and a.leave_school_type = #{leaveSchoolType}</if>
			<if test="leaveSchoolDay != null ">and a.leave_school_day = #{leaveSchoolDay}</if>
			<if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
			<if test="spaceInfoIds != null and spaceInfoIds.size() != 0">
				and a.space_info_id in
				<foreach collection="spaceInfoIds" item="item" index="index" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.leave_school_record_id
	 		,t.organization_id
	 		,t.campus_id
	 		,t.space_info_id
	 		,t.space_group_use_type
	 		,t.leave_school_type
	 		,t.leave_school_day
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			<if test="queryAutoConfirmTime">
				,t.auto_confirm_time
			</if>
		from (
			select a.*
			<if test="queryAutoConfirmTime">
				, sc.auto_confirm_time
			</if>
			from leave_school_record a
			<if test="queryAutoConfirmTime">
				join leave_school_config sc on sc.organization_id = a.organization_id and sc.campus_id = a.campus_id and sc.is_delete = 0
			</if>
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLeaveSchoolRecordListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolRecordByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>