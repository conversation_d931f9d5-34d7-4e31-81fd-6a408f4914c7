<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolBroadcastInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolBroadcastInfoDto" id="BaseResultMap">
        <result property="broadcastInfoId" column="broadcast_info_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="broadcastContent" column="broadcast_content"/>
        <result property="broadcastId" column="broadcast_id"/>
        <result property="broadcastUrl" column="broadcast_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="playTimes" column="play_times"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="broadcastInfoId != null ">and broadcast_info_id = #{broadcastInfoId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="broadcastContent != null and broadcastContent != '' ">and broadcast_content like concat('%', #{broadcastContent}, '%')</if>
			<if test="broadcastId != null and broadcastId != '' ">and broadcast_id like concat('%', #{broadcastId}, '%')</if>
			<if test="broadcastUrl != null and broadcastUrl != '' ">and broadcast_url like concat('%', #{broadcastUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="playTimes != null ">and play_times = #{playTimes}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="broadcastInfoId != null ">and broadcast_info_id = #{broadcastInfoId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="broadcastContent != null and broadcastContent != '' ">and broadcast_content like concat('%', #{broadcastContent}, '%')</if>
			<if test="broadcastId != null and broadcastId != '' ">and broadcast_id like concat('%', #{broadcastId}, '%')</if>
			<if test="broadcastUrl != null and broadcastUrl != '' ">and broadcast_url like concat('%', #{broadcastUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="playTimes != null ">and play_times = #{playTimes}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.broadcast_info_id
	 		,t.organization_id
	 		,t.campus_id
	 		,t.broadcast_content
	 		,t.broadcast_id
	 		,t.broadcast_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.play_times
		from (
			select a.* from leave_school_broadcast_info a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLeaveSchoolBroadcastInfoListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolBroadcastInfoByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolBroadcastInfoVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>