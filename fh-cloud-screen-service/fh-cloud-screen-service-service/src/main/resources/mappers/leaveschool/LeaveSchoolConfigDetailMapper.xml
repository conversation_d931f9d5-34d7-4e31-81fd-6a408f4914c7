<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolConfigDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDetailDto" id="BaseResultMap">
        <result property="leaveSchoolConfigDetailId" column="leave_school_config_detail_id"/>
        <result property="leaveSchoolConfigId" column="leave_school_config_id"/>
        <result property="grade" column="grade"/>
        <result property="weekDay" column="week_day"/>
        <result property="leaveSchoolStartTime" column="leave_school_start_time"/>
        <result property="leaveSchoolEndTime" column="leave_school_end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="leaveSchoolConfigDetailId != null ">and leave_school_config_detail_id = #{leaveSchoolConfigDetailId}</if>
			<if test="leaveSchoolConfigId != null ">and leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="grade != null and grade != '' ">and grade = #{grade}</if>
			<if test="weekDay != null ">and week_day = #{weekDay}</if>
			<if test="leaveSchoolStartTime != null ">and leave_school_start_time = #{leaveSchoolStartTime}</if>
			<if test="leaveSchoolEndTime != null ">and leave_school_end_time = #{leaveSchoolEndTime}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="leaveSchoolConfigDetailId != null ">and leave_school_config_detail_id = #{leaveSchoolConfigDetailId}</if>
			<if test="leaveSchoolConfigId != null ">and leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="grade != null and grade != '' ">and grade = #{grade}</if>
			<if test="weekDay != null ">and week_day = #{weekDay}</if>
			<if test="leaveSchoolStartTime != null ">and leave_school_start_time = #{leaveSchoolStartTime}</if>
			<if test="leaveSchoolEndTime != null ">and leave_school_end_time = #{leaveSchoolEndTime}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.leave_school_config_detail_id
	 		,t.leave_school_config_id
	 		,t.grade
	 		,t.week_day
	 		,t.leave_school_start_time
	 		,t.leave_school_end_time
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from leave_school_config_detail a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLeaveSchoolConfigDetailListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolConfigDetailByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDetailVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>