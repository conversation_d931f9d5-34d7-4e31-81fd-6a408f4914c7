<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolConfigDeviceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDeviceDto" id="BaseResultMap">
        <result property="leaveSchoolConfigDeviceId" column="leave_school_config_device_id"/>
        <result property="leaveSchoolConfigId" column="leave_school_config_id"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="leaveSchoolConfigDeviceId != null ">and leave_school_config_device_id = #{leaveSchoolConfigDeviceId}</if>
			<if test="leaveSchoolConfigId != null ">and leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="showDeviceIds != null and showDeviceIds.size() != 0">
				and show_device_id in
				<foreach collection="showDeviceIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
			</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="leaveSchoolConfigDeviceId != null ">and leave_school_config_device_id = #{leaveSchoolConfigDeviceId}</if>
			<if test="leaveSchoolConfigId != null ">and leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.leave_school_config_device_id
	 		,t.leave_school_config_id
	 		,t.show_device_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from leave_school_config_device a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLeaveSchoolConfigDeviceListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolConfigDeviceByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getLeaveSchoolConfigDeviceList"
			resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigDeviceVo">
		select d.*,
			sd.device_number deviceNumber,
			sd.device_name deviceName,
			sd.device_type deviceType,
			sd.device_pattern devicePattern,
			sd.device_status deviceStatus,
			sd.device_full_type deviceFullType
		from leave_school_config_device d
		join leave_school_config c on d.leave_school_config_id = c.leave_school_config_id
		join show_device sd on d.show_device_id = sd.show_device_id and sd.is_delete = 0
		left join space_device_rel dr on sd.show_device_id = dr.show_device_id and dr.is_delete = 0
		<where>
			<if test="isDelete != null">and d.is_delete = #{isDelete}</if>
			<if test="leaveSchoolConfigId != null">and d.leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="spaceInfoIds != null and spaceInfoIds.size() != 0">
				and dr.space_info_id in
				<foreach collection="spaceInfoIds" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			<if test="spaceGroupUseType != null">
				and dr.space_group_use_type = #{spaceGroupUseType}
			</if>
			<if test="organizationId != null">and c.organization_id = #{organizationId}</if>
			<if test="campusId != null">and c.campus_id = #{campusId}</if>
		</where>
	</select>
</mapper>