<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolRecordOperateMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolRecordOperateDto" id="BaseResultMap">
        <result property="leaveSchoolRecordOperateId" column="leave_school_record_operate_id"/>
        <result property="leaveSchoolRecordId" column="leave_school_record_id"/>
        <result property="leaveSchoolType" column="leave_school_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="leaveSchoolRecordOperateId != null ">and leave_school_record_operate_id = #{leaveSchoolRecordOperateId}</if>
			<if test="leaveSchoolRecordId != null ">and leave_school_record_id = #{leaveSchoolRecordId}</if>
			<if test="leaveSchoolType != null ">and leave_school_type = #{leaveSchoolType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="neLeaveSchoolType != null">and leave_school_type != #{neLeaveSchoolType}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="leaveSchoolRecordOperateId != null ">and leave_school_record_operate_id = #{leaveSchoolRecordOperateId}</if>
			<if test="leaveSchoolRecordId != null ">and leave_school_record_id = #{leaveSchoolRecordId}</if>
			<if test="leaveSchoolType != null ">and leave_school_type = #{leaveSchoolType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="neLeaveSchoolType != null">and leave_school_type != #{neLeaveSchoolType}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.leave_school_record_operate_id
	 		,t.leave_school_record_id
	 		,t.leave_school_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from leave_school_record_operate a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLeaveSchoolRecordOperateListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordOperateVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolRecordOperateByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolRecordOperateVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>