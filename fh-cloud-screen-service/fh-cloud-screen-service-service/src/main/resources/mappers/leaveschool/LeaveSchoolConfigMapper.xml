<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.leaveschool.mapper.LeaveSchoolConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.leaveschool.entity.dto.LeaveSchoolConfigDto" id="BaseResultMap">
        <result property="leaveSchoolConfigId" column="leave_school_config_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="leaveSchoolGradeType" column="leave_school_grade_type"/>
        <result property="leaveSchoolWeekType" column="leave_school_week_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
		<result property="autoConfirmTime" column="auto_confirm_time"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="leaveSchoolConfigId != null ">and leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="leaveSchoolGradeType != null ">and leave_school_grade_type = #{leaveSchoolGradeType}</if>
			<if test="leaveSchoolWeekType != null ">and leave_school_week_type = #{leaveSchoolWeekType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="autoConfirmTime != null">and auto_confirm_time = #{autoConfirmTime}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="leaveSchoolConfigId != null ">and leave_school_config_id = #{leaveSchoolConfigId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="leaveSchoolGradeType != null ">and leave_school_grade_type = #{leaveSchoolGradeType}</if>
			<if test="leaveSchoolWeekType != null ">and leave_school_week_type = #{leaveSchoolWeekType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="autoConfirmTime != null">and auto_confirm_time = #{autoConfirmTime}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.leave_school_config_id
	 		,t.organization_id
	 		,t.campus_id
	 		,t.leave_school_grade_type
	 		,t.leave_school_week_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.auto_confirm_time
		from (
			select a.* from leave_school_config a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLeaveSchoolConfigListByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLeaveSchoolConfigByCondition" resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getLeaveSchoolConfigBySpaceInfo"
			resultType="com.fh.cloud.screen.service.leaveschool.entity.vo.LeaveSchoolConfigVo">
		select lsc.*
		from leave_school_config lsc
		join leave_school_config_device lscd on lsc.leave_school_config_id = lscd.leave_school_config_id and lscd.is_delete = 0
		join show_device sd on lscd.show_device_id = sd.show_device_id and sd.is_delete = 0
		join space_device_rel sdr on sd.show_device_id = sdr.show_device_id and sdr.is_delete = 0
		<where>
			lsc.organization_id = #{organizationId}
			and sdr.space_info_id = #{spaceInfoId}
			and sdr.space_group_use_type = #{spaceGroupUseType}
			and lsc.is_delete = 0
			and lscd.show_device_id = #{showDeviceId}
		</where>
		limit 1
	</select>
</mapper>