<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.device.mapper.ShowDeviceMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.device.entity.dto.ShowDevice" id="BaseResultMap">
        <result property="showDeviceId" column="show_device_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="deviceNumber" column="device_number"/>
        <result property="deviceType" column="device_type"/>
        <result property="devicePattern" column="device_pattern"/>
        <result property="deviceFullType" column="device_full_type"/>
        <result property="deviceMacAddress" column="device_mac_address"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="shipmentNo" column="shipment_no"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceBrand" column="device_brand"/>
        <result property="deviceModel" column="device_model"/>
        <result property="productSerialNumber" column="product_serial_number"/>
        <result property="clientVersion" column="client_version"/>
        <result property="pushType" column="push_type"/>
        <result property="devicePosterDuration" column="device_poster_duration"/>
        <result property="arcsoftFaceCode" column="arcsoft_face_code"/>
        <result property="patrolRemark" column="patrol_remark"/>
        <result property="patrolType" column="patrol_type"/>
        <result property="deviceThemeType" column="device_theme_type"/>
        <result property="faceModType" column="face_mod_type"/>
        <result property="superviseState" column="supervise_state"/>
    </resultMap>

    <select id="getShowDeviceListByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select a.* from show_device a
        <where>
            <if test="showDeviceId != null ">and a.show_device_id = #{showDeviceId}</if>
            <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
            <if test="deviceNumber != null and deviceNumber != ''">and a.device_number like concat('%', #{deviceNumber},
                '%')
            </if>
            <if test="deviceActivation != null and deviceActivation != ''">and a.device_activation=#{deviceActivation}
            </if>
            <if test="deviceType != null ">and a.device_type = #{deviceType}</if>
            <if test="devicePattern != null ">and a.device_pattern = #{devicePattern}</if>
            <if test="deviceFullType != null ">and a.device_full_type = #{deviceFullType}</if>
            <if test="deviceMacAddress != null and deviceMacAddress != ''">and a.device_mac_address =
                #{deviceMacAddress}
            </if>
            <if test="deviceStatus != null ">and a.device_status = #{deviceStatus}</if>
            <if test="createTime != null">
                and date_format(a.create_time, '%Y-%m-%d') = DATE_FORMAT(#{createTime},'%Y-%m-%d')
            </if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
            <if test="shipmentNo != null and shipmentNo != ''">and a.shipment_no = #{shipmentNo}</if>
            <if test="deviceName != null and deviceName != '' ">and a.device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="deviceBrand != null ">and a.device_brand = #{deviceBrand}</if>
            <if test="deviceModel != null and deviceModel != '' ">and a.device_model like concat('%', #{deviceModel},
                '%')
            </if>
            <if test="productSerialNumber != null and productSerialNumber != '' ">and a.roduct_serial_number like
                concat('%', #{productSerialNumber}, '%')
            </if>
            <if test="clientVersion != null and clientVersion != '' ">and a.client_version like concat('%',
                #{clientVersion}, '%')
            </if>
            <if test="pushType != null ">and a.push_type = #{pushType}</if>
            <if test="arcsoftFaceCode != null and arcsoftFaceCode != '' ">and a.arcsoft_face_code = #{arcsoftFaceCode}</if>
            <if test="patrolRemark != null and patrolRemark != '' ">and a.patrol_remark like concat('%', #{patrolRemark},'%')</if>
            <if test="patrolType != null">and a.patrol_type = #{patrolType}</if>
            <if test="deviceThemeType != null">and a.device_theme_type = #{deviceThemeType}</if>
            <if test="faceModType != null">and a.face_mod_type = #{faceModType}</if>
            <if test="superviseState != null">and a.supervise_state = #{superviseState}</if>
        </where>
    </select>

    <select id="listShowDeviceVoBySpaceGroupIdsOfXz" parameterType="map"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select distinct sd.* from space_device_rel sdr
        join show_device sd on sdr.show_device_id = sd.show_device_id and sd.is_delete=0
        where sdr.is_delete=0 and sdr.space_group_use_type=1
        <if test="organizationId != null">
            and sdr.organization_id=#{organizationId}
        </if>
        <if test="campusId != null">
            and sdr.campus_id=#{campusId}
        </if>
        <if test="classesIds != null and classesIds.size() >0">
            and sdr.space_info_id in
            <foreach collection="classesIds" open="(" close=")" separator="," item="classesId">
                #{classesId}
            </foreach>
        </if>
        <if test="spaceGroupIds != null and spaceGroupIds.size() > 0">
            and sdr.space_group_id in
            <foreach collection="spaceGroupIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listShowDeviceVoBySpaceGroupIdsOfNotXz" parameterType="map"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select distinct sd.* from space_group sg
        join space_info si on sg.space_group_id = si.space_group_id and si.is_delete=0
        <if test="organizationId != null">
            and si.organization_id=#{organizationId}
        </if>
        <if test="campusId != null">
            and si.campus_id=#{campusId}
        </if>
        <if test="spaceInfoIds != null and spaceInfoIds.size() >0">
            and si.space_info_id in
            <foreach collection="spaceInfoIds" open="(" close=")" item="spaceInfoId" separator=",">
                #{spaceInfoId}
            </foreach>
        </if>
        join space_device_rel sdr on si.space_info_id = sdr.space_info_id and sdr.is_delete=0 and
        sdr.space_group_use_type=2
        join show_device sd on sdr.show_device_id = sd.show_device_id and sd.is_delete=0
        where sg.is_delete=0
        <if test="spaceGroupIds != null and spaceGroupIds.size() > 0">
            and sg.space_group_id in
            <foreach collection="spaceGroupIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listShowDeviceDataByConditionOfXz"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select distinct a.*,sdr.space_info_id as spaceInfoId,1 as spaceGroupUseType,sdr.campus_id as campusId
        ,sdr.id as spaceDeviceRelId,g.space_group_name as spaceGroupName
        from show_device a
        join space_device_rel sdr on a.show_device_id = sdr.show_device_id and sdr.is_delete=0 and
        sdr.space_group_use_type=1
        join space_group g on g.space_group_use_type = 1
        <where>
            <if test="showDeviceId != null ">and a.show_device_id = #{showDeviceId}</if>
            <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
            <if test="deviceNumber != null and deviceNumber != ''">and a.device_number = #{deviceNumber}</if>
            <if test="deviceType != null ">and a.device_type = #{deviceType}</if>
            <if test="devicePattern != null ">and a.device_pattern = #{devicePattern}</if>
            <if test="deviceFullType != null ">and a.device_full_type = #{deviceFullType}</if>
            <if test="deviceMacAddress != null and deviceMacAddress != ''">and a.device_mac_address =
                #{deviceMacAddress}
            </if>
            <if test="deviceStatus != null ">and a.device_status = #{deviceStatus}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>

            <if test="spaceGroupId != null ">
                and sdr.space_group_id = #{spaceGroupId}
            </if>
            <if test="organizationId != null ">
                and sdr.organization_id=#{organizationId}
            </if>
            <if test="campusId != null ">
                and sdr.campus_id=#{campusId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and a.device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="patrolRemark != null and patrolRemark != '' ">and a.patrol_remark like concat('%', #{patrolRemark},'%')</if>
            <if test="patrolType != null">and a.patrol_type = #{patrolType}</if>
            <if test="spaceInfoIds != null and spaceInfoIds.size() >0">
                and sdr.space_info_id in
                <foreach collection="spaceInfoIds" open="(" close=")" item="spaceInfoId" separator=",">
                    #{spaceInfoId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="listShowDeviceDataByConditionOfNotXz"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select distinct a.*,si.space_info_name as spaceInfoName,si.space_info_id as spaceInfoId,2 as spaceGroupUseType,
        si.campus_id as campusId,sdr.id as spaceDeviceRelId,g.space_group_name as spaceGroupName
        from show_device a
        join space_device_rel sdr on a.show_device_id = sdr.show_device_id and sdr.is_delete=0 and
        sdr.space_group_use_type=2
        join space_info si on sdr.space_info_id = si.space_info_id and si.is_delete=0
        join space_group g on si.space_group_id = g.space_group_id
        <where>
            <if test="showDeviceId != null ">and a.show_device_id = #{showDeviceId}</if>
            <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
            <if test="deviceNumber != null and deviceNumber != ''">and a.device_number = #{deviceNumber}</if>
            <if test="deviceType != null ">and a.device_type = #{deviceType}</if>
            <if test="devicePattern != null ">and a.device_pattern = #{devicePattern}</if>
            <if test="deviceFullType != null ">and a.device_full_type = #{deviceFullType}</if>
            <if test="deviceMacAddress != null and deviceMacAddress != ''">and a.device_mac_address =
                #{deviceMacAddress}
            </if>
            <if test="deviceStatus != null ">and a.device_status = #{deviceStatus}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>

            <if test="spaceGroupId != null ">
                and si.space_group_id = #{spaceGroupId}
            </if>
            <if test="organizationId != null ">
                and si.organization_id=#{organizationId}
            </if>
            <if test="campusId != null ">
                and si.campus_id=#{campusId}
            </if>
            <if test="deviceName != null and deviceName != ''">
                and a.device_name like concat('%', #{deviceName}, '%')
            </if>
            <if test="patrolRemark != null and patrolRemark != '' ">and a.patrol_remark like concat('%', #{patrolRemark},'%')</if>
            <if test="patrolType != null">and a.patrol_type = #{patrolType}</if>
        </where>
    </select>

    <select id="listShowDeviceDataByIds" parameterType="map"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select * from show_device where is_delete =0 and show_device_id in
        <foreach collection="showDeviceIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="listShowDeviceBindByOrganizationId" parameterType="map"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select  a.*,b.space_group_use_type,b.space_info_id from show_device a
        join space_device_rel b on a.show_device_id = b.show_device_id and b.is_delete=0 and b.organization_id=#{organizationId}
        where a.is_delete=0 and a.organization_id=#{organizationId}
    </select>

    <select id="listBySpaceInfoIdsOfNotXz" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select sd.* from space_info si
        left join space_device_rel sdr on sdr.space_info_id=si.space_info_id and sdr.space_group_use_type=2
        left join show_device sd on sd.show_device_id=sdr.show_device_id
        where
        sd.is_delete=0
        and si.space_info_id in
        <foreach collection="spaceIfoIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="listBySpaceInfoIdsOfXz" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select sd.* from space_info si
        left join space_device_rel sdr on sdr.space_info_id=si.space_info_id and sdr.space_group_use_type=1
        left join show_device sd on sd.show_device_id=sdr.show_device_id
        where
        sd.is_delete=0
        and si.space_info_id in
        <foreach collection="spaceInfoIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="listShowDeviceBindByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select  a.*,b.space_group_use_type,b.space_info_id,b.space_group_id,b.campus_id from show_device a
        join space_device_rel b
            on a.show_device_id = b.show_device_id
            and b.is_delete=0
            and b.organization_id=a.organization_id
        where a.is_delete=0
        <if test="organizationId != null">and a.organization_id=#{organizationId}</if>
        <if test="organizationIds != null and organizationIds.size() != 0">
            and a.organization_id in
            <foreach collection="organizationIds" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="campusId != null">and b.campus_id = #{campusId}</if>
        <if test="spaceInfoId != null">and b.space_info_id = #{spaceInfoId}</if>
        <if test="spaceGroupId != null">and b.space_group_id = #{spaceGroupId}</if>
        <if test="spaceGroupUseType != null">and b.space_group_use_type = #{spaceGroupUseType}</if>
        <if test="showDeviceNumbers != null and showDeviceNumbers.size() != 0">
            and a.device_number in
            <foreach collection="showDeviceNumbers" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spaceInfoBos != null and spaceInfoBos.size() != 0">
            and (
            <foreach collection="spaceInfoBos" index="index" item="item" separator="or">
                (b.space_info_id = #{item.spaceInfoId}
                and b.space_group_id = #{item.spaceGroupId}
                and b.space_group_use_type = #{item.spaceGroupUseType})
            </foreach>
            )
        </if>
        <if test="faceModType != null">and a.face_mod_type = #{faceModType}</if>
        <if test="superviseState != null">and a.supervise_state = #{superviseState}</if>
    </select>

    <select id="listShowDeviceByDeviceIdsOfBind"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo">
        select sd.*,sdr.space_info_id,sdr.space_group_use_type
        from show_device sd
                 join space_device_rel sdr on sd.show_device_id = sdr.show_device_id and sdr.is_delete=0
        where sd.is_delete=0
        <if test="deviceIds != null and deviceIds.size()>0">
            and sd.show_device_id in
            <foreach collection="deviceIds" open="(" close=")" separator="," item="deviceId">
                #{deviceId}
            </foreach>
        </if>
    </select>
</mapper>