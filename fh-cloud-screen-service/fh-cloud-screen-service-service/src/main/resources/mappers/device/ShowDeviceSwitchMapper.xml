<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.device.mapper.ShowDeviceSwitchMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.device.entity.dto.ShowDeviceSwitch" id="BaseResultMap">
	        <result property="showDeviceSwitchId" column="show_device_switch_id"/>
	        <result property="organizationId" column="organization_id"/>
	        <result property="campusId" column="campus_id"/>
	        <result property="week" column="week"/>
	        <result property="onTime" column="on_time"/>
	        <result property="offTime" column="off_time"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getShowDeviceSwitchListByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceSwitchVo">
		select a.* from show_device_switch a
	    <where>
	    				    <if test="showDeviceSwitchId != null ">and a.show_device_switch_id = #{showDeviceSwitchId}</if>
						    <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
						    <if test="campusId != null ">and a.campus_id = #{campusId}</if>
						    <if test="week != null ">and a.week = #{week}</if>
						    <if test="onTime != null and onTime != ''">and a.on_time = #{onTime}</if>
						    <if test="offTime != null and offTime != ''">and a.off_time = #{offTime}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>