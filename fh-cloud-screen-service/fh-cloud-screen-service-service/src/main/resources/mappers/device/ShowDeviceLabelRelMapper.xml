<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.device.mapper.ShowDeviceLabelRelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLabelRelDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="labelId" column="label_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
            <if test="labelId != null ">and label_id = #{labelId}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
        </where>
    </sql>

    <sql id="common_select">
        select
        <!-- 如果主键不是id，这里需要修改 -->
        t.id
        ,t.show_device_id
        ,t.label_id
        ,t.create_time
        ,t.create_by
        ,t.update_time
        ,t.update_by
        ,t.is_delete
        ,t.label_name
        from (
        select a.*,b.label_name from show_device_label_rel a
        join label b on a.label_id=b.label_id
        where a.is_delete=0 and b.is_delete=0
        ) t

    </sql>

    <select id="getShowDeviceLabelRelListByCondition"
            resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>
</mapper>