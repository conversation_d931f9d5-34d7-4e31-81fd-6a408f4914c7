<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.device.mapper.ShowDeviceFullCustomMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.device.entity.dto.ShowDeviceFullCustomDto" id="BaseResultMap">
        <result property="fullCustomId" column="full_custom_id"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="deviceNumber" column="device_number"/>
        <result property="deviceFullType" column="device_full_type"/>
        <result property="customTime" column="custom_time"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="week" column="week"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="fullCustomId != null ">and full_custom_id = #{fullCustomId}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="deviceNumber != null and deviceNumber != '' ">and device_number like concat('%', #{deviceNumber}, '%')</if>
			<if test="deviceFullType != null ">and device_full_type = #{deviceFullType}</if>
			<if test="customTime != null ">and custom_time = #{customTime}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="week != null ">and week = #{week}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.full_custom_id
	 		,t.show_device_id
	 		,t.device_number
	 		,t.device_full_type
	 		,t.custom_time
	 		,t.organization_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.week
		from (
			 select a.* from show_device_full_custom a
		 ) t

	</sql>

	<select id="getShowDeviceFullCustomListByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getShowDeviceFullCustomByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceFullCustomVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <select id="getExistOrganizationIds" resultType="java.lang.Long">
		select distinct organization_id from show_device_full_custom where is_delete=0
	</select>
</mapper>