<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.device.mapper.ShowDeviceLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.device.entity.dto.ShowDeviceLogDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="deviceNumber" column="device_number"/>
        <result property="deviceLogStatus" column="device_log_status"/>
        <result property="deviceLogFileOid" column="device_log_file_oid"/>
        <result property="deviceLogMediaUrl" column="device_log_media_url"/>
        <result property="deviceLogMediaName" column="device_log_media_name"/>
        <result property="deviceLogMediaNameOri" column="device_log_media_name_ori"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="deviceNumber != null and deviceNumber != '' ">and device_number like concat('%', #{deviceNumber}, '%')</if>
			<if test="deviceLogStatus != null ">and device_log_status = #{deviceLogStatus}</if>
			<if test="deviceLogFileOid != null and deviceLogFileOid != '' ">and device_log_file_oid like concat('%', #{deviceLogFileOid}, '%')</if>
			<if test="deviceLogMediaUrl != null and deviceLogMediaUrl != '' ">and device_log_media_url like concat('%', #{deviceLogMediaUrl}, '%')</if>
			<if test="deviceLogMediaName != null and deviceLogMediaName != '' ">and device_log_media_name like concat('%', #{deviceLogMediaName}, '%')</if>
			<if test="deviceLogMediaNameOri != null and deviceLogMediaNameOri != '' ">and device_log_media_name_ori like concat('%', #{deviceLogMediaNameOri}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.show_device_id
	 		,t.organization_id
	 		,t.device_number
	 		,t.device_log_status
	 		,t.device_log_file_oid
	 		,t.device_log_media_url
	 		,t.device_log_media_name
	 		,t.device_log_media_name_ori
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from show_device_log a
		 ) t

	</sql>

	<select id="getShowDeviceLogListByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getShowDeviceLogByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLogVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>