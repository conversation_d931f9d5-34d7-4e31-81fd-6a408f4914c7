<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.device.mapper.ShowDeviceCaptureMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.device.entity.dto.ShowDeviceCaptureDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="deviceNumber" column="device_number"/>
        <result property="deviceCaptureStatus" column="device_capture_status"/>
        <result property="deviceCaptureFileOid" column="device_capture_file_oid"/>
        <result property="deviceCaptureMediaUrl" column="device_capture_media_url"/>
        <result property="deviceCaptureMediaUrlCompress" column="device_capture_media_url_compress"/>
        <result property="deviceCaptureMediaName" column="device_capture_media_name"/>
        <result property="deviceCaptureMediaNameOri" column="device_capture_media_name_ori"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="deviceNumber != null and deviceNumber != '' ">and device_number like concat('%', #{deviceNumber}, '%')</if>
			<if test="deviceCaptureStatus != null ">and device_capture_status = #{deviceCaptureStatus}</if>
			<if test="deviceCaptureFileOid != null and deviceCaptureFileOid != '' ">and device_capture_file_oid like concat('%', #{deviceCaptureFileOid}, '%')</if>
			<if test="deviceCaptureMediaUrl != null and deviceCaptureMediaUrl != '' ">and device_capture_media_url like concat('%', #{deviceCaptureMediaUrl}, '%')</if>
			<if test="deviceCaptureMediaUrlCompress != null and deviceCaptureMediaUrlCompress != '' ">and device_capture_media_url_compress like concat('%', #{deviceCaptureMediaUrlCompress}, '%')</if>
			<if test="deviceCaptureMediaName != null and deviceCaptureMediaName != '' ">and device_capture_media_name like concat('%', #{deviceCaptureMediaName}, '%')</if>
			<if test="deviceCaptureMediaNameOri != null and deviceCaptureMediaNameOri != '' ">and device_capture_media_name_ori like concat('%', #{deviceCaptureMediaNameOri}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
			t.id
	 		,t.show_device_id
	 		,t.organization_id
	 		,t.device_number
	 		,t.device_capture_status
	 		,t.device_capture_file_oid
	 		,t.device_capture_media_url
	 		,t.device_capture_media_url_compress
	 		,t.device_capture_media_name
	 		,t.device_capture_media_name_ori
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from show_device_capture a
		 ) t

	</sql>

	<select id="getShowDeviceCaptureListByCondition" resultType="com.fh.cloud.screen.service.device.entity.vo.ShowDeviceCaptureVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>