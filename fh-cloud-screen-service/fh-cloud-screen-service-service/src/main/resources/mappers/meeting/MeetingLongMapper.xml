<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.meeting.mapper.MeetingLongMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.meeting.entity.dto.MeetingLongDto" id="BaseResultMap">
        <result property="meetingLongId" column="meeting_long_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="title" column="title"/>
        <result property="meetingStartDate" column="meeting_start_date"/>
        <result property="meetingEndDate" column="meeting_end_date"/>
        <result property="meetingStartTime" column="meeting_start_time"/>
        <result property="meetingEndTime" column="meeting_end_time"/>
        <result property="normalSignInTime" column="normal_sign_in_time"/>
        <result property="isSignIn" column="is_sign_in"/>
        <result property="content" column="content"/>
        <result property="note" column="note"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="meetingUserType" column="meeting_user_type"/>
        <result property="meetingUuid" column="meeting_uuid"/>
        <result property="weeks" column="weeks"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="meetingLongId != null ">and meeting_long_id = #{meetingLongId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
			<if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
			<if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
			<if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
			<if test="queryRepeat == false">
				<if test="meetingStartDate != null ">and meeting_start_date = #{meetingStartDate}</if>
				<if test="meetingEndDate != null ">and meeting_end_date = #{meetingEndDate}</if>
				<if test="meetingStartTime != null ">and meeting_start_time = #{meetingStartTime}</if>
				<if test="meetingEndTime != null ">and meeting_end_time = #{meetingEndTime}</if>
			</if>
			<if test="normalSignInTime != null ">and normal_sign_in_time = #{normalSignInTime}</if>
			<if test="isSignIn != null ">and is_sign_in = #{isSignIn}</if>
			<if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
			<if test="note != null and note != '' ">and note like concat('%', #{note}, '%')</if>
			<if test="status != null ">and status = #{status}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="meetingUserType != null ">and meeting_user_type = #{meetingUserType}</if>
			<if test="meetingUuid != null and meetingUuid != '' ">and meeting_uuid like concat('%', #{meetingUuid}, '%')</if>
			<if test="meetingName != null ">and meetingName like concat('%', #{meetingName}, '%')</if>
			<if test="weeks != null and weeks !=''">and weeks like concat('%', #{weeks}, '%')</if>
			<if test="queryRepeat == true">
				and(( DATE_FORMAT(meeting_start_date,'%Y-%m-%d') >= DATE_FORMAT(#{meetingStartDate},'%Y-%m-%d') AND
				DATE_FORMAT(meeting_start_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{meetingEndDate},'%Y-%m-%d'))
				OR (DATE_FORMAT(meeting_start_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{meetingStartDate},'%Y-%m-%d') AND DATE_FORMAT(meeting_end_date,'%Y-%m-%d') >=
				DATE_FORMAT(#{meetingEndDate},'%Y-%m-%d'))
				OR (DATE_FORMAT(meeting_end_date,'%Y-%m-%d') >= DATE_FORMAT(#{meetingStartDate},'%Y-%m-%d') AND DATE_FORMAT(meeting_end_date,'%Y-%m-%d') &lt;=
				DATE_FORMAT(#{meetingEndDate},'%Y-%m-%d')))
			</if>
			<if test="containsDate != null">
				and DATE_FORMAT(meeting_start_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{containsDate},'%Y-%m-%d')
				and DATE_FORMAT(#{containsDate},'%Y-%m-%d') &lt;= DATE_FORMAT(meeting_end_date,'%Y-%m-%d')
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.meeting_long_id
	 		,t.organization_id
	 		,t.space_group_use_type
	 		,t.space_info_id
	 		,t.user_oid
	 		,t.title
	 		,t.meeting_start_date
		    ,t.meeting_end_date
	 		,t.meeting_start_time
	 		,t.meeting_end_time
	 		,t.normal_sign_in_time
	 		,t.is_sign_in
	 		,t.content
	 		,t.note
	 		,t.status
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.meeting_user_type
	 		,t.meeting_uuid
			,t.weeks
		from (
			 select a.* from meeting_long a
		 ) t

	</sql>

	<select id="getMeetingLongListByCondition" resultType="com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getMeetingLongByCondition" resultType="com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<select id="getMeetingRelationList" resultType="com.fh.cloud.screen.service.meeting.entity.vo.MeetingLongVo">
		select t.*
		from (
		select a.*,
		si.space_info_name as meetingName,
		si.user_capacity,
		si.computer_use,
		si.network_use,
		si.shadow_use,
		count(mu.meeting_long_user_id) as userCount
		from meeting_long a
		join space_info si on a.space_info_id=si.space_info_id
		join meeting_long_user mu on a.meeting_long_id=mu.meeting_long_id
		where a.space_group_use_type=2
		group by a.meeting_long_id
		<if test="orderByMeetingNameFlag != null ">
			order by si.space_info_id DESC
		</if>
		) t
		<include refid="common_where"></include>
	</select>

</mapper>