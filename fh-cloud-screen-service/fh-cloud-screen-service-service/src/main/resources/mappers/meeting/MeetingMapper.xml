<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.meeting.mapper.MeetingMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.meeting.entity.dto.MeetingDto" id="BaseResultMap">
        <result property="meetingId" column="meeting_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="title" column="title"/>
        <result property="meetingDate" column="meeting_date"/>
        <result property="meetingStartTime" column="meeting_start_time"/>
        <result property="meetingEndTime" column="meeting_end_time"/>
        <result property="isSignIn" column="is_sign_in"/>
        <result property="content" column="content"/>
        <result property="note" column="note"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="meetingUserType" column="meeting_user_type"/>
        <result property="normalSignInTime" column="normal_sign_in_time"/>
        <result property="meetingUuid" column="meeting_uuid"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="meetingId != null ">and meeting_id = #{meetingId}</if>
            <if test="organizationId != null ">and organization_id = #{organizationId}</if>
            <if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
            <if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
            <if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
            <if test="title != null and title != '' ">and title like concat('%', #{title}, '%')</if>
            <if test="meetingDate != null ">and DATE_FORMAT(meeting_date,'%Y-%m-%d') =
                DATE_FORMAT(#{meetingDate},'%Y-%m-%d')
            </if>
            <!-- 该条件暂时不用，代码层面已注释传参 -->
            <if test="startDate != null ">and DATE_FORMAT(meeting_date,'%Y-%m-%d') >=
                DATE_FORMAT(#{startDate},'%Y-%m-%d')
            </if>
            <if test="queryRepeat == true">
                and DATE_FORMAT(meeting_date,'%Y-%m-%d') >= DATE_FORMAT(#{meetingStartDate},'%Y-%m-%d')
                and DATE_FORMAT(meeting_date,'%Y-%m-%d') &lt;= DATE_FORMAT(#{meetingEndDate},'%Y-%m-%d')
            </if>
            <if test="meetingStartTime != null ">and meeting_start_time = #{meetingStartTime}</if>
            <if test="meetingEndTime != null ">and meeting_end_time = #{meetingEndTime}</if>
            <if test="isSignIn != null ">and is_sign_in = #{isSignIn}</if>
            <if test="content != null and content != '' ">and content like concat('%', #{content}, '%')</if>
            <if test="note != null and note != '' ">and note like concat('%', #{note}, '%')</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="meetingName != null ">and meetingName like concat('%', #{meetingName}, '%')</if>
            <if test="meetingIds != null and meetingIds.size()>0">
                and meeting_id in
                <foreach item="item" index="index" collection="meetingIds" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="notEnd !=null">and status != 4</if>
            <if test="meetingUserType != null ">and meeting_user_type = #{meetingUserType}</if>
            <if test="normalSignInTime != null ">and normal_sign_in_time = #{normalSignInTime}</if>
            <if test="meetingUuid != null ">and meeting_uuid = #{meetingUuid}</if>
        </where>
    </sql>

    <sql id="common_select">
        select
        t.meeting_id
        ,t.organization_id
        ,t.space_group_use_type
        ,t.space_info_id
        ,t.user_oid
        ,t.title
        ,t.meeting_date
        ,t.meeting_start_time
        ,t.meeting_end_time
        ,t.is_sign_in
        ,t.content
        ,t.note
        ,t.status
        ,t.create_time
        ,t.create_by
        ,t.update_time
        ,t.update_by
        ,t.is_delete
        ,t.meeting_user_type
        ,t.normal_sign_in_time
        ,t.meeting_uuid
        from (
        select a.* from meeting a
        <if test="orderBy != null">
            order by ${orderBy}
        </if>
        ) t

    </sql>

    <select id="getMeetingListByCondition" resultType="com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <select id="getMeetingRelationList" resultType="com.fh.cloud.screen.service.meeting.entity.vo.MeetingVo">
        select t.*
        from (
        select a.*,
        CASE 
            WHEN a.space_group_use_type = 1 THEN ci.remark
            WHEN a.space_group_use_type = 2 THEN si.space_info_name
            ELSE si.space_info_name
        END as meetingName,
        CASE 
            WHEN a.space_group_use_type = 1 THEN ci.user_capacity
            WHEN a.space_group_use_type = 2 THEN si.user_capacity
            ELSE si.user_capacity
        END as user_capacity,
        CASE 
            WHEN a.space_group_use_type = 1 THEN ci.computer_use
            WHEN a.space_group_use_type = 2 THEN si.computer_use
            ELSE si.computer_use
        END as computer_use,
        CASE 
            WHEN a.space_group_use_type = 1 THEN ci.network_use
            WHEN a.space_group_use_type = 2 THEN si.network_use
            ELSE si.network_use
        END as network_use,
        CASE 
            WHEN a.space_group_use_type = 1 THEN ci.shadow_use
            WHEN a.space_group_use_type = 2 THEN si.shadow_use
            ELSE si.shadow_use
        END as shadow_use,
        count(mu.meeting_user_id) as userCount
        from meeting a
        left join space_info si on a.space_info_id = si.space_info_id and a.space_group_use_type = 2
        left join classes_info ci on a.space_info_id = ci.classes_id and a.space_group_use_type = 1
        join meeting_user mu on a.meeting_id = mu.meeting_id
        where (a.space_group_use_type = 1 OR a.space_group_use_type = 2)
        group by a.meeting_id
        <if test="orderByMeetingNameFlag != null ">
            order by 
            CASE 
                WHEN a.space_group_use_type = 1 THEN ci.classes_id
                WHEN a.space_group_use_type = 2 THEN si.space_info_id
                ELSE si.space_info_id
            END DESC
        </if>
        ) t
        <include refid="common_where"></include>
    </select>
</mapper>