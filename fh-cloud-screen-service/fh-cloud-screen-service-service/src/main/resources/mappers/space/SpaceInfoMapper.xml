<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.space.mapper.SpaceInfoMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.space.entity.dto.SpaceInfo" id="BaseResultMap">
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="spaceGroupId" column="space_group_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="spaceInfoName" column="space_info_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="computerUse" column="computer_use"/>
        <result property="networkUse" column="network_use"/>
        <result property="shadowUse" column="shadow_use"/>
        <result property="meetingUse" column="meeting_use"/>
        <result property="userCapacity" column="user_capacity"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="getSpaceInfoListByCondition" resultType="com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo">
        select a.*,2 as spaceGroupUseType from space_info a
        <where>
            <if test="spaceInfoId != null ">and a.space_info_id = #{spaceInfoId}</if>
            <if test="spaceGroupId != null ">and a.space_group_id = #{spaceGroupId}</if>
            <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
            <if test="campusId != null ">and a.campus_id = #{campusId}</if>
            <if test="spaceInfoName != null and spaceInfoName != ''">and a.space_info_name = #{spaceInfoName}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null">and a.is_delete = #{isDelete}</if>
            <if test="computerUse != null ">and a.computer_use = #{computerUse}</if>
            <if test="networkUse != null ">and a.network_use = #{networkUse}</if>
            <if test="shadowUse != null ">and a.shadow_use = #{shadowUse}</if>
            <if test="meetingUse != null ">and a.meeting_use = #{meetingUse}</if>
            <if test="userCapacity != null ">and a.user_capacity = #{userCapacity}</if>
            <if test="remark != null and remark != ''">and a.remark = #{remark}</if>

            <if test="spaceInfoIds != null and spaceInfoIds.size() > 0">
                and a.space_info_id in
                <foreach collection="spaceInfoIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="startUpdateTime != null">
                and update_time <![CDATA[  >= ]]> #{startUpdateTime}
            </if>

            <if test="endUpdateTime != null">
                and update_time <![CDATA[  <= ]]> #{endUpdateTime}
            </if>
        </where>
    </select>
</mapper>