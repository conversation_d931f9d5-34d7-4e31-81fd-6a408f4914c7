<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.space.mapper.SpaceDeviceRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.space.entity.dto.SpaceDeviceRel" id="BaseResultMap">
	        <result property="id" column="id"/>
	        <result property="spaceInfoId" column="space_info_id"/>
	        <result property="spaceGroupUseType" column="space_group_use_type"/>
	        <result property="showDeviceId" column="show_device_id"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
			<result property="organizationId" column="organization_id"/>
			<result property="campusId" column="campus_id"/>
			<result property="spaceGroupId" column="space_group_id"/>
	    </resultMap>

	<select id="getSpaceDeviceRelListByCondition" resultType="com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo">
		select a.* from space_device_rel a
	    <where>
	    				    <if test="id != null ">and a.id = #{id}</if>
						    <if test="spaceInfoId != null ">and a.space_info_id = #{spaceInfoId}</if>
						    <if test="spaceGroupUseType != null ">and a.space_group_use_type = #{spaceGroupUseType}</if>
						    <if test="showDeviceId != null ">and a.show_device_id = #{showDeviceId}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
							<if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
							<if test="campusId != null ">and a.campus_id = #{campusId}</if>
							<if test="spaceGroupId != null ">and a.space_group_id = #{spaceGroupId}</if>
				    </where>
	</select>
</mapper>