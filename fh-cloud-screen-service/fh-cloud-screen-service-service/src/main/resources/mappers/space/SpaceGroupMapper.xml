<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.space.mapper.SpaceGroupMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.space.entity.dto.SpaceGroup" id="BaseResultMap">
        <result property="spaceGroupId" column="space_group_id"/>
        <result property="spaceGroupType" column="space_group_type"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="spaceGroupName" column="space_group_name"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getSpaceGroupListByCondition" resultType="com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo">
        select a.* from space_group a
        <where>
            <if test="spaceGroupId != null ">and a.space_group_id = #{spaceGroupId}</if>
            <if test="spaceGroupType != null ">and a.space_group_type = #{spaceGroupType}</if>
            <if test="spaceGroupUseType != null ">and a.space_group_use_type =
                #{spaceGroupUseType}
            </if>
            <if test="spaceGroupName != null and spaceGroupName != ''">and a.space_group_name = #{spaceGroupName}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
        </where>
    </select>

    <select id="listSpaceGroupVoBySpaceGroupIds" parameterType="map"
            resultType="com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo">
        select * from space_group
        where is_delete=0 and space_group_id in
        <foreach collection="spaceGroupIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>
</mapper>