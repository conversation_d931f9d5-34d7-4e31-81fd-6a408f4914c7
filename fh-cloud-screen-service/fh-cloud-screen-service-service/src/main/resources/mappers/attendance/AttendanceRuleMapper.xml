<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.attendance.mapper.AttendanceRuleMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.attendance.entity.dto.AttendanceRule" id="BaseResultMap">
	        <result property="attendanceRuleId" column="attendance_rule_id"/>
	        <result property="organizationId" column="organization_id"/>
	        <result property="attendanceType" column="attendance_type"/>
	        <result property="attendanceModeNum" column="attendance_mode_num"/>
	        <result property="gradeSameType" column="grade_same_type"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getAttendanceRuleListByCondition" resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceRuleVo">
		select a.* from attendance_rule a
	    <where>
	    				    <if test="attendanceRuleId != null ">and a.attendance_rule_id = #{attendanceRuleId}</if>
						    <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
						    <if test="attendanceType != null ">and a.attendance_type = #{attendanceType}</if>
						    <if test="attendanceModeNum != null ">and a.attendance_mode_num = #{attendanceModeNum}</if>
						    <if test="gradeSameType != null ">and a.grade_same_type = #{gradeSameType}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>