<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.attendance.mapper.AttendanceUserDetailMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUserDetail" id="BaseResultMap">
	        <result property="attendanceUserDetailId" column="attendance_user_detail_id"/>
	        <result property="attendanceUserId" column="attendance_user_id"/>
	        <result property="attendanceRuleDayIndex" column="attendance_rule_day_index"/>
		<result property="signInDeviceNumber" column="sign_in_device_number"/>
		<result property="signOutDeviceNumber" column="sign_out_device_number"/>
		<result property="signInAddress" column="sign_in_address"/>
		<result property="signOutAddress" column="sign_out_address"/>
	        <result property="signInTime" column="sign_in_time"/>
	        <result property="signOutTime" column="sign_out_time"/>
	        <result property="signInRecordType" column="sign_in_record_type"/>
		    <result property="signOutRecordType" column="sign_out_record_type"/>
			<result property="onlySign" column="only_sign"/>
			<result property="signInAttendanceMethod" column="sign_in_attendance_method"/>
			<result property="signOutAttendanceMethod" column="sign_out_attendance_method"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getAttendanceUserDetailListByCondition" resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo">
		select a.* from attendance_user_detail a
	    <where>
	    				    <if test="attendanceUserDetailId != null ">and a.attendance_user_detail_id = #{attendanceUserDetailId}</if>
						    <if test="attendanceUserId != null ">and a.attendance_user_id = #{attendanceUserId}</if>
						    <if test="attendanceRuleDayIndex != null ">and a.attendance_rule_day_index = #{attendanceRuleDayIndex}</if>
						    <if test="signInTime != null and signInTime != ''">and a.sign_in_time = #{signInTime}</if>
						    <if test="signOutTime != null and signOutTime != ''">and a.sign_out_time = #{signOutTime}</if>
						    <if test="signInRecordType != null ">and a.sign_in_record_type = #{signInRecordType}</if>
							<if test="signOutRecordType != null ">and a.sign_out_record_type = #{signOutRecordType}</if>
							<if test="onlySign != null ">and a.only_sign = #{onlySign}</if>
							<if test="signInAttendanceMethod != null ">and a.sign_in_attendance_method = #{signInAttendanceMethod}</if>
							<if test="signOutAttendanceMethod != null ">and a.sign_out_attendance_method = #{signOutAttendanceMethod}</if>
							<if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>