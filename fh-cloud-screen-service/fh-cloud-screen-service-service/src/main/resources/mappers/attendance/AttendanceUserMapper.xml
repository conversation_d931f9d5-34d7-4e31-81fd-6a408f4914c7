<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.attendance.mapper.AttendanceUserMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.attendance.entity.dto.AttendanceUser" id="BaseResultMap">
	        <result property="attendanceUserId" column="attendance_user_id"/>
	        <result property="attendanceRecordType" column="attendance_record_type"/>
	        <result property="attendanceRuleId" column="attendance_rule_id"/>
	        <result property="attendanceType" column="attendance_type"/>
	        <result property="attendanceDate" column="attendance_date"/>
	        <result property="userOid" column="user_oid"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getAttendanceUserListByCondition" resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo">
		select a.* from attendance_user a
	    <where>
	    				    <if test="attendanceUserId != null ">and a.attendance_user_id = #{attendanceUserId}</if>
						    <if test="attendanceRecordType != null ">and a.attendance_record_type = #{attendanceRecordType}</if>
						    <if test="attendanceRuleId != null ">and a.attendance_rule_id = #{attendanceRuleId}</if>
						    <if test="attendanceType != null ">and a.attendance_type = #{attendanceType}</if>
						    <if test="attendanceDate != null and attendanceDate != ''">and a.attendance_date = #{attendanceDate}</if>
						    <if test="attendanceDay != null and attendanceDay != ''">and a.attendance_day = #{attendanceDay}</if>
						    <if test="attendanceMonth != null and attendanceMonth != ''">and a.attendance_month = #{attendanceMonth}</if>
						    <if test="userOid != null and userOid != ''">and a.user_oid = #{userOid}</if>


						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>

							<if test="userOids != null and userOids.size() != 0">
								and a.user_oid in
								<foreach collection="userOids" open="(" close=")" separator="," item="item">
									#{item}
								</foreach>
							</if>
				    </where>
	</select>
	<select id="getSignInListByUserOidsAndDateDay"
			resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo">
		select
			u.*
		from attendance_user u INNER JOIN  attendance_user_detail a ON a.attendance_user_id = u.attendance_user_id
		where a.sign_in_time is not null and u.attendance_day = #{date}
		  and a.is_delete = 0 and u.is_delete = 0
		 and u.user_oid in
		<foreach collection="userOids" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
		group by u.attendance_user_id
	</select>

	<select id="getAttendanceUserExportListByCondition"
			resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserShowVo">
		select au.attendance_rule_id,au.attendance_type,au.user_oid,au.attendance_day,aud.sign_in_address,aud.sign_in_time,aud.sign_in_record_type ,aud.attendance_rule_day_index,aud.sign_in_attendance_method
		,aud.sign_in_attendance_method,aud.sign_out_attendance_method,au.attendance_record_type
		from attendance_user au
		left join attendance_user_detail aud on au.attendance_user_id = aud.attendance_user_id and aud.is_delete=0 and aud.attendance_rule_day_index=#{attendanceRuleDayIndex}
		<where>
			<if test="attendanceUserId != null ">and au.attendance_user_id = #{attendanceUserId}</if>
			<if test="attendanceRecordType != null ">and au.attendance_record_type = #{attendanceRecordType}</if>
			<if test="attendanceRuleId != null ">and au.attendance_rule_id = #{attendanceRuleId}</if>
			<if test="attendanceType != null ">and au.attendance_type = #{attendanceType}</if>
			<if test="attendanceDate != null and attendanceDate != ''">and au.attendance_date = #{attendanceDate}</if>
			<if test="attendanceDay != null and attendanceDay != ''">and au.attendance_day = #{attendanceDay}</if>
			<if test="attendanceMonth != null and attendanceMonth != ''">and au.attendance_month = #{attendanceMonth}</if>
			<if test="userOid != null and userOid != ''">and au.user_oid = #{userOid}</if>
			<if test="createTime != null and createTime != ''">and au.create_time = #{createTime}</if>
			<if test="createBy != null and createBy != ''">and au.create_by = #{createBy}</if>
			<if test="updateTime != null and updateTime != ''">and au.update_time = #{updateTime}</if>
			<if test="updateBy != null and updateBy != ''">and au.update_by = #{updateBy}</if>
			<if test="isDelete != null ">and au.is_delete = #{isDelete}</if>
			<if test="userOids != null and userOids.size() != 0">
				and au.user_oid in
				<foreach collection="userOids" open="(" close=")" separator="," item="item">
					#{item}
				</foreach>
			</if>
		</where>
	</select>
</mapper>