<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.attendance.mapper.AttendanceLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.attendance.entity.dto.AttendanceLog" id="BaseResultMap">
	        <result property="attendanceLogId" column="attendance_log_id"/>
	        <result property="attendanceRuleId" column="attendance_rule_id"/>
			<result property="attendanceType" column="attendance_type"/>
	        <result property="deviceNumber" column="device_number"/>
	        <result property="cardNumber" column="card_number"/>
            <result property="address" column="address"/>
	        <result property="userOid" column="user_oid"/>
	        <result property="attendanceTime" column="attendance_time"/>
	        <result property="signType" column="sign_type"/>
			<result property="attendanceMethod" column="attendance_method"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getAttendanceLogListByCondition" resultType="com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogVo">
		select a.* from attendance_log a
	    <where>
	    				    <if test="attendanceLogId != null ">and a.attendance_log_id = #{attendanceLogId}</if>
						    <if test="attendanceRuleId != null ">and a.attendance_rule_id = #{attendanceRuleId}</if>
							<if test="attendanceType != null ">and a.attendance_type = #{attendanceType}</if>
						    <if test="deviceNumber != null and deviceNumber != ''">and a.device_number = #{deviceNumber}</if>
						    <if test="cardNumber != null and cardNumber != ''">and a.card_number = #{cardNumber}</if>
                            <if test="address != null and address != ''">and a.address = #{address}</if>
						    <if test="userOid != null and userOid != ''">and a.user_oid = #{userOid}</if>
						    <if test="attendanceTime != null and attendanceTime != ''">
						    	and  date_format(a.attendance_time, "%Y-%m-%d") = #{attendanceTime}
							</if>
						    <if test="signType != null ">and a.sign_type = #{signType}</if>
							<if test="attendanceDay != null and attendanceDay != ''">
								and   date_format(a.attendance_time, "%Y-%m-%d") = #{attendanceDay}
							</if>
							<if test="attendanceMethod != null"> and a.attendance_method = #{attendanceMethod}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>