<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.festival.mapper.FestivalMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.festival.entity.dto.FestivalDto" id="BaseResultMap">
        <result property="festivalId" column="festival_id"/>
        <result property="type" column="type"/>
        <result property="year" column="year"/>
        <result property="name" column="name"/>
        <result property="festivalCode" column="festival_code"/>
        <result property="festivalDay" column="festival_day"/>
        <result property="startDay" column="start_day"/>
        <result property="endDay" column="end_day"/>
        <result property="presetType" column="preset_type"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="festivalId != null ">and festival_id = #{festivalId}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="year != null ">and year = #{year}</if>
            <if test="name != null and name != '' ">and name like concat('%', #{name}, '%')</if>
            <if test="festivalCode != null and festivalCode != '' ">and festival_code = #{festivalCode}</if>
            <if test="festivalDay != null  ">and festival_day = #{festivalDay}</if>
            <if test="startDay != null ">and start_day = #{startDay}</if>
            <if test="endDay != null ">and end_day = #{endDay}</if>
            <if test="presetType != null ">and preset_type = #{presetType}</if>
            <if test="organizationId != null ">and organization_id = #{organizationId}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="neType != null ">and type != #{neType}</if>
            <if test="festivalMonth != null and festivalMonth != ''">and date_format(festival_day, "%Y-%m") =
                #{festivalMonth}
            </if>
            <if test="customizeStartAndEedMonth != null and customizeStartAndEedMonth != ''">and
                (date_format(start_day, "%Y-%m") &lt;= #{customizeStartAndEedMonth} AND date_format(end_day, "%Y-%m") >=
                #{customizeStartAndEedMonth})
            </if>
        </where>
    </sql>

    <sql id="common_select">
		select
	 		t.festival_id
	 		,t.type
	 		,t.year
	 		,t.name
	 		,t.festival_code
	 		,t.festival_day
	 		,t.start_day
	 		,t.end_day
	 		,t.preset_type
	 		,t.organization_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from festival a
			 order by a.start_day desc
		 ) t

	</sql>

    <select id="getFestivalListByCondition" resultType="com.fh.cloud.screen.service.festival.entity.vo.FestivalVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>

    <select id="getFestivalListByDate" resultType="com.fh.cloud.screen.service.festival.entity.vo.FestivalVo">
        select a.* from festival a
        where a.is_delete=0
        <if test="festivalDay != null  ">and festival_day = #{festivalDay}</if>
        <if test="startDay != null ">and start_day = #{startDay}</if>
        <if test="endDay != null ">and end_day = #{endDay}</if>
    </select>

    <select id="getFestivalLibraryRelList" resultType="com.fh.cloud.screen.service.festival.entity.vo.FestivalVo">
        select a.festival_code,b.screen_module_library_id from label_festival_rel a
        join label_library_rel b on b.label_id =a.label_id
        where
        a.is_delete=0 and b.is_delete=0
        <if test="festivalCodes != null and festivalCodes.size() >0">
            and a.festival_code in
            <foreach collection="festivalCodes" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>