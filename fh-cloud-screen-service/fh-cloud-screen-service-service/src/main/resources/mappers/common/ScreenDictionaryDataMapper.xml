<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.common.mapper.ScreenDictionaryDataMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.common.entity.dto.DictionaryData" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="dictType" column="dict_type"/>
        <result property="dictValue" column="dict_value"/>
        <result property="dictLabel" column="dict_label"/>
        <result property="dictSort" column="dict_sort"/>
        <result property="dictDataRemark" column="dict_data_remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="organizationId" column="organization_id"/>
        <result property="type" column="type"/>
    </resultMap>

    <select id="getDictionaryDataListByCondition"
            resultType="com.fh.cloud.screen.service.common.entity.vo.DictionaryDataVo">
        select a.* from dictionary_data a
        <where>
            <if test="id != null ">and a.id = #{id}</if>
            <if test="dictType != null and dictType != ''">and a.dict_type = #{dictType}</if>
            <if test="dictValue != null and dictValue != ''">and a.dict_value = #{dictValue}</if>
            <if test="dictLabel != null and dictLabel != ''">and a.dict_label = #{dictLabel}</if>
            <if test="dictSort != null and dictSort != ''">and a.dict_sort = #{dictSort}</if>
            <if test="dictDataRemark != null and dictDataRemark != ''">and a.dict_data_remark =
                #{dictDataRemark}
            </if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
            <if test="organizationId != null">and a.organization_id=#{organizationId}</if>
            <if test="type != null ">and a.type=#{type}</if>
        </where>
        order by a.dict_sort asc
    </select>

</mapper>