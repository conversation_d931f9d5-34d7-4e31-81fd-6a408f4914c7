<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.rest.mapper.WorkRestDayMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.rest.entity.dto.WorkRestDay" id="BaseResultMap">
	        <result property="workRestDayId" column="work_rest_day_id"/>
	        <result property="workRestGradeId" column="work_rest_grade_id"/>
	        <result property="workRestId" column="work_rest_id"/>
	        <result property="courseClassesPosition" column="course_classes_position"/>
	        <result property="courseClassesName" column="course_classes_name"/>
	        <result property="courseClassesType" column="course_classes_type"/>
	        <result property="courseClassesIndex" column="course_classes_index"/>
	        <result property="week" column="week"/>
	        <result property="dayType" column="day_type"/>
	        <result property="startTime" column="start_time"/>
	        <result property="endTime" column="end_time"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getWorkRestDayListByCondition" resultType="com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo">
		select a.* from work_rest_day a
	    <where>
	    				    <if test="workRestDayId != null ">and a.work_rest_day_id = #{workRestDayId}</if>
						    <if test="workRestGradeId != null ">and a.work_rest_grade_id = #{workRestGradeId}</if>
						    <if test="workRestId != null ">and a.work_rest_id = #{workRestId}</if>
						    <if test="courseClassesPosition != null ">and a.course_classes_position = #{courseClassesPosition}</if>
						    <if test="courseClassesName != null and courseClassesName != ''">and a.course_classes_name = #{courseClassesName}</if>
						    <if test="courseClassesType != null and courseClassesType != ''">and a.course_classes_type = #{courseClassesType}</if>
						    <if test="courseClassesIndex != null ">and a.course_classes_index = #{courseClassesIndex}</if>
						    <if test="week != null ">and a.week = #{week}</if>
						    <if test="dayType != null ">and a.day_type = #{dayType}</if>
						    <if test="startTime != null and startTime != ''">and a.start_time = #{startTime}</if>
						    <if test="endTime != null and endTime != ''">and a.end_time = #{endTime}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>

    <select id="getWorkRestDayListByGrade"
            resultType="com.fh.cloud.screen.service.rest.entity.vo.WorkRestDayVo">
		select wsd.*
		from work_rest_day wsd
	    join work_rest wr on wsd.work_rest_id = wr.work_rest_id and wr.is_delete=0
		join work_rest_grade wrg on wsd.work_rest_grade_id and wrg.is_delete=0
		where wsd.is_delete=0 and wr.organization_id=#{organizationId} and (wrg.grade = '' or wrg.grade=#{grade})

	</select>
</mapper>