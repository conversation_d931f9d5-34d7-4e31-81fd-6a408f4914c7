<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.rest.mapper.WorkRestMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.rest.entity.dto.WorkRest" id="BaseResultMap">
	        <result property="workRestId" column="work_rest_id"/>
	        <result property="organizationId" column="organization_id"/>
	        <result property="campusId" column="campus_id"/>
	        <result property="name" column="name"/>
	        <result property="status" column="status"/>
	        <result property="gradeSameType" column="grade_same_type"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getWorkRestListByCondition" resultType="com.fh.cloud.screen.service.rest.entity.vo.WorkRestVo">
		select a.* from work_rest a
	    <where>
	    				    <if test="workRestId != null ">and a.work_rest_id = #{workRestId}</if>
						    <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
						    <if test="campusId != null ">and a.campus_id = #{campusId}</if>
						    <if test="name != null and name != ''">and a.name = #{name}</if>
						    <if test="status != null ">and a.status = #{status}</if>
						    <if test="gradeSameType != null ">and a.grade_same_type = #{gradeSameType}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>