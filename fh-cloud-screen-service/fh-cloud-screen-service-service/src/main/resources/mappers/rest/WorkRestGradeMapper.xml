<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.rest.mapper.WorkRestGradeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.rest.entity.dto.WorkRestGrade" id="BaseResultMap">
	        <result property="workRestGradeId" column="work_rest_grade_id"/>
	        <result property="workRestId" column="work_rest_id"/>
	        <result property="grade" column="grade"/>
	        <result property="weekSameType" column="week_same_type"/>
	        <result property="courseNumAm" column="course_num_am"/>
	        <result property="courseNumPm" column="course_num_pm"/>
	        <result property="courseNumNt" column="course_num_nt"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getWorkRestGradeListByCondition" resultType="com.fh.cloud.screen.service.rest.entity.vo.WorkRestGradeVo">
		select a.* from work_rest_grade a
	    <where>
	    				    <if test="workRestGradeId != null ">and a.work_rest_grade_id = #{workRestGradeId}</if>
						    <if test="workRestId != null ">and a.work_rest_id = #{workRestId}</if>
						    <if test="grade != null and grade != ''">and a.grade = #{grade}</if>
						    <if test="weekSameType != null ">and a.week_same_type = #{weekSameType}</if>
						    <if test="courseNumAm != null ">and a.course_num_am = #{courseNumAm}</if>
						    <if test="courseNumPm != null ">and a.course_num_pm = #{courseNumPm}</if>
						    <if test="courseNumNt != null ">and a.course_num_nt = #{courseNumNt}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>