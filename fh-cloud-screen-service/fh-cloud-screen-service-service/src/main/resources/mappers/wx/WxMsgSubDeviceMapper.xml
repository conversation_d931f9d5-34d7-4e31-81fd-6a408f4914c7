<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.wx.mapper.WxMsgSubDeviceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.wx.entity.dto.WxMsgSubDeviceDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="deviceNumber" column="device_number"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and user_oid = #{userOid}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="deviceNumber != null and deviceNumber != '' ">and device_number like concat('%', #{deviceNumber}, '%')</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and a.id = #{id}</if>
			<if test="userOid != null and userOid != '' ">and a.user_oid = #{userOid}</if>
			<if test="showDeviceId != null ">and a.show_device_id = #{showDeviceId}</if>
			<if test="deviceNumber != null and deviceNumber != '' ">and a.device_number like concat('%', #{deviceNumber}, '%')</if>
			<if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
			<if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.user_oid
	 		,t.show_device_id
	 		,t.device_number
	 		,t.organization_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from wx_msg_sub_device a
			join show_device sd on sd.show_device_id = a.show_device_id and sd.is_delete = 0
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getWxMsgSubDeviceListByCondition" resultType="com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubDeviceVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getWxMsgSubDeviceByCondition" resultType="com.fh.cloud.screen.service.wx.entity.vo.WxMsgSubDeviceVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>