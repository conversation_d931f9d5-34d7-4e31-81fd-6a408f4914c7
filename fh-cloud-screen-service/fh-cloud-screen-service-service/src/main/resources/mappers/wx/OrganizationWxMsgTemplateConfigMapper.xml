<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.wx.mapper.OrganizationWxMsgTemplateConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.wx.entity.dto.OrganizationWxMsgTemplateConfigDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="templateId" column="template_id"/>
        <result property="url" column="url"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="templateId != null and templateId != '' ">and template_id like concat('%', #{templateId}, '%')</if>
			<if test="url != null and url != '' ">and url like concat('%', #{url}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="templateId != null and templateId != '' ">and template_id like concat('%', #{templateId}, '%')</if>
			<if test="url != null and url != '' ">and url like concat('%', #{url}, '%')</if>
			<if test="type != null ">and type = #{type}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.organization_id
	 		,t.template_id
	 		,t.url
	 		,t.type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from organization_wx_msg_template_config a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getOrganizationWxMsgTemplateConfigListByCondition" resultType="com.fh.cloud.screen.service.wx.entity.vo.OrganizationWxMsgTemplateConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getOrganizationWxMsgTemplateConfigByCondition" resultType="com.fh.cloud.screen.service.wx.entity.vo.OrganizationWxMsgTemplateConfigVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>
</mapper>