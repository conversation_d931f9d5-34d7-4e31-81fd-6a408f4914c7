<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenPwdMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenPwd" id="BaseResultMap">
	        <result property="screenPwdId" column="screen_pwd_id"/>
	        <result property="organizationId" column="organization_id"/>
	        <result property="pwdValue" column="pwd_value"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getScreenPwdListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPwdVo">
		select a.* from screen_pwd a
	    <where>
	    				    <if test="screenPwdId != null ">and a.screen_pwd_id = #{screenPwdId}</if>
						    <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
						    <if test="pwdValue != null and pwdValue != ''">and a.pwd_value = #{pwdValue}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>