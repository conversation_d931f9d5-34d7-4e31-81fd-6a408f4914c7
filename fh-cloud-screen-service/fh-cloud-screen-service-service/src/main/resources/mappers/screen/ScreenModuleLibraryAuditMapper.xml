<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryAuditMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryAuditDto" id="BaseResultMap">
        <result property="screenModuleLibraryAuditId" column="screen_module_library_audit_id"/>
        <result property="moduleName" column="module_name"/>
        <result property="moduleGroupType" column="module_group_type"/>
        <result property="presetType" column="preset_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="parentScreenModuleLibraryId" column="parent_screen_module_library_id"/>
        <result property="librarySort" column="library_sort"/>
        <result property="isPoster" column="is_poster"/>
        <result property="auditType" column="audit_type"/>
        <result property="reason" column="reason"/>
        <result property="auditUser" column="audit_user"/>
        <result property="auditTime" column="audit_time"/>
        <result property="releaseType" column="release_type"/>
        <result property="devicePattern" column="device_pattern"/>
        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenModuleLibraryAuditId != null ">and screen_module_library_audit_id = #{screenModuleLibraryAuditId}</if>
			<if test="moduleName != null and moduleName != '' ">and module_name like concat('%', #{moduleName}, '%')</if>
			<if test="moduleGroupType != null ">and module_group_type = #{moduleGroupType}</if>
			<if test="presetType != null ">and preset_type = #{presetType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="parentScreenModuleLibraryId != null ">and parent_screen_module_library_id = #{parentScreenModuleLibraryId}</if>
			<if test="librarySort != null ">and library_sort = #{librarySort}</if>
			<if test="isPoster != null ">and is_poster = #{isPoster}</if>
			<if test="auditType != null ">and audit_type = #{auditType}</if>
			<if test="reason != null and reason != '' ">and reason like concat('%', #{reason}, '%')</if>
			<if test="auditUser != null and auditUser != '' ">and audit_user like concat('%', #{auditUser}, '%')</if>
			<if test="auditTime != null ">and audit_time = #{auditTime}</if>
			<if test="releaseType != null ">and release_type = #{releaseType}</if>
			<if test="devicePattern != null ">and device_pattern = #{devicePattern}</if>
			<if test="screenModuleLibraryId != null ">and screen_module_library_id = #{screenModuleLibraryId}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="screenModuleLibraryAuditId != null ">and screen_module_library_audit_id = #{screenModuleLibraryAuditId}</if>
			<if test="moduleName != null and moduleName != '' ">and module_name like concat('%', #{moduleName}, '%')</if>
			<if test="moduleGroupType != null ">and module_group_type = #{moduleGroupType}</if>
			<if test="presetType != null ">and preset_type = #{presetType}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="parentScreenModuleLibraryId != null ">and parent_screen_module_library_id = #{parentScreenModuleLibraryId}</if>
			<if test="librarySort != null ">and library_sort = #{librarySort}</if>
			<if test="isPoster != null ">and is_poster = #{isPoster}</if>
			<if test="auditType != null ">and audit_type = #{auditType}</if>
			<if test="reason != null and reason != '' ">and reason like concat('%', #{reason}, '%')</if>
			<if test="auditUser != null and auditUser != '' ">and audit_user like concat('%', #{auditUser}, '%')</if>
			<if test="auditTime != null ">and audit_time = #{auditTime}</if>
			<if test="releaseType != null ">and release_type = #{releaseType}</if>
			<if test="devicePattern != null ">and device_pattern = #{devicePattern}</if>
			<if test="screenModuleLibraryId != null ">and screen_module_library_id = #{screenModuleLibraryId}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="screenModuleLibraryAuditIds != null and screenModuleLibraryAuditIds.size() >0">
				and screen_module_library_audit_id in
				<foreach collection="screenModuleLibraryAuditIds" item="screenModuleLibraryAuditIdItem" open="(" close=")" separator=",">
					#{screenModuleLibraryAuditIdItem}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_module_library_audit_id
	 		,t.module_name
	 		,t.module_group_type
	 		,t.preset_type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.parent_screen_module_library_id
	 		,t.library_sort
	 		,t.is_poster
	 		,t.audit_type
	 		,t.reason
	 		,t.audit_user
	 		,t.audit_time
	 		,t.release_type
	 		,t.device_pattern
	 		,t.screen_module_library_id
		from (
			select a.* from screen_module_library_audit a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getScreenModuleLibraryAuditListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenModuleLibraryAuditByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <select id="getScreenModuleLibraryAuditWithLabelNameListByCondition"
			resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo">
		select smla.*, l.label_name labelName, l.label_id labelId, l.organization_id organizationId, l.classes_id classesId
		from (<include refid="common_select"></include>) smla
		left join label_library_audit_rel llar
			on smla.screen_module_library_audit_id = llar.screen_module_library_audit_id
			and llar.is_delete = 0
		left join label l on l.label_id = llar.label_id and l.is_delete = 0
		<where>
			<if test="labelId != null">
			    and l.label_id = #{labelId}
			</if>
			<if test="organizationId != null">
				and l.organization_id = #{organizationId}
			</if>
			<if test="classesId != null">
				and l.classes_id = #{classesId}
			</if>
		</where>
	</select>

	<select id="getScreenModuleLibraryAuditWithLabelNameByCondition"
			resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryAuditVo">
		select smla.*, l.label_name labelName, l.label_id labelId, l.organization_id organizationId, l.classes_id classesId
		from (<include refid="common_select"></include>) smla
		left join label_library_audit_rel llar
		on smla.screen_module_library_audit_id = llar.screen_module_library_audit_id
		and llar.is_delete = 0
		left join label l on l.label_id = llar.label_id and l.is_delete = 0
		<where>
			<if test="labelId != null">
				and l.label_id = #{labelId}
			</if>
			<if test="organizationId != null">
				and l.organization_id = #{organizationId}
			</if>
			<if test="classesId != null">
				and l.classes_id = #{classesId}
			</if>
		</where>
		limit 1
	</select>


</mapper>