<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryCollectMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryCollectDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
        <result property="userOid" column="user_oid"/>
        <result property="organizationId" column="organization_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="isSchoolPoster" column="is_school_poster"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="screenModuleLibraryId != null ">and screen_module_library_id = #{screenModuleLibraryId}</if>
            <if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
            <if test="organizationId != null ">and organization_id = #{organizationId}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="isSchoolPoster != null">and is_school_poster=#{isSchoolPoster}</if>
        </where>
    </sql>

    <sql id="common_select">
		select
			t.id
	 		,t.screen_module_library_id
	 		,t.user_oid
	 		,t.organization_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,is_school_poster
		from (
			 select a.* from screen_module_library_collect a
			 order by a.id DESC
		 ) t

	</sql>

    <select id="getScreenModuleLibraryCollectListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>


    <select id="getHotPosterModuleLibraryIds" resultType="java.lang.Long">
		select screen_module_library_id from
			( select a.screen_module_library_id ,COUNT(a.screen_module_library_id) as count from screen_module_library_collect a
					join screen_module_library b on a.screen_module_library_id=b.screen_module_library_id
			    where a.is_delete=0 and b.is_delete=0 and a.is_school_poster=2
			  group by screen_module_library_id order by  count DESC ,a.create_time DESC LIMIT #{limit}
			) as a
	</select>


    <select id="getLibraryIdPageListByUserOidAndPattern"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryCollectVo">
        select a.*
        from screen_module_library_collect a
        join screen_module_library b on a.screen_module_library_id=b.screen_module_library_id
        join screen_module_library_media c on b.screen_module_library_id=c.screen_module_library_id
        where a.is_delete=0 and b.is_delete=0 and c.is_delete=0
        <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
        <if test="userOid != null and userOid != '' ">and a.user_oid = #{userOid}</if>
        <if test="pattern !=null">and c.device_pattern=#{pattern}</if>
        <if test="moduleName != null and moduleName != ''">and b.module_name like concat('%', #{moduleName}, '%')</if>
        group by a.screen_module_library_id
        order by a.id DESC
    </select>
</mapper>