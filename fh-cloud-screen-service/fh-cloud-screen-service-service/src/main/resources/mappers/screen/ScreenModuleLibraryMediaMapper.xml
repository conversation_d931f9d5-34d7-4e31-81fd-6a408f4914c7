<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMediaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMedia">
        <id column="screen_module_library_media_id" property="screenModuleLibraryMediaId"/>
        <result column="screen_module_library_id" property="screenModuleLibraryId"/>
        <result column="screen_module_library_media_url" property="screenModuleLibraryMediaUrl"/>
        <result column="screen_module_library_media_url_compress" property="screenModuleLibraryMediaUrlCompress"/>
        <result column="screen_module_library_media_url_cover" property="screenModuleLibraryMediaUrlCover"/>
        <result column="screen_module_library_media_name" property="screenModuleLibraryMediaName"/>
        <result column="screen_module_library_media_name_ori" property="screenModuleLibraryMediaNameOri"/>
        <result column="screen_content_media_id" property="screenContentMediaId"/>
        <result column="screen_content_media_id_compress" property="screenContentMediaIdCompress"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="is_delete" property="isDelete"/>
        <result column="device_pattern" property="devicePattern"/>
        <result column="screen_content_media_md5" property="screenContentMediaMd5"/>
        <result column="media_sort" property="mediaSort"/>
        <result column="media_source" property="mediaSource"/>
        <result column="third_id" property="thirdId"/>
    </resultMap>

    <select id="getScreenModuleLibraryMediaListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo">
        select a.*,b.module_group_type,b.module_name
        from screen_module_library_media a
        join screen_module_library b on a.screen_module_library_id=b.screen_module_library_id and b.is_delete=0 and
        a.is_delete=0
        <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() >0">
            left
        </if>
        join label_library_rel llr on b.screen_module_library_id=llr.screen_module_library_id and llr.is_delete=0
        <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() >0">
            left
        </if>
        join label l on llr.label_id=l.label_id and l.is_delete=0
        <where>
            <if test="moduleGroupType != null">and b.module_group_type = #{moduleGroupType}</if>
            <if test="screenModuleLibraryMediaId != null and screenModuleLibraryMediaId != ''">
                and a.screen_module_library_media_id = #{screenModuleLibraryMediaId}
            </if>
            <if test="screenModuleLibraryId != null ">and a.screen_module_library_id = #{screenModuleLibraryId}</if>
            <if test="screenModuleLibraryMediaUrl != null and screenModuleLibraryMediaUrl != ''">and
                a.screen_module_library_media_url =#{screenModuleLibraryMediaUrl}
            </if>
            <if test="screenModuleLibraryMediaUrlCompress != null and screenModuleLibraryMediaUrlCompress != ''">and
                a.screen_module_library_media_url_compress = #{screenModuleLibraryMediaUrlCompress}
            </if>
            <if test="screenModuleLibraryMediaUrlCover != null and screenModuleLibraryMediaUrlCover != ''">and
                a.screen_module_library_media_url_cover = #{screenModuleLibraryMediaUrlCover}
            </if>
            <if test="screenModuleLibraryMediaName != null and screenModuleLibraryMediaName != ''">and
                a.screen_content_media_name =#{screen_module_library_media_name}
            </if>
            <if test="screenModuleLibraryMediaNameOri != null and screenModuleLibraryMediaNameOri != ''">and
                a.screen_module_library_media_name_ori = #{screenModuleLibraryMediaNameOri}
            </if>
            <if test="screenContentMediaId != null and screenContentMediaId != ''">and a.screen_content_media_id =
                #{screenContentMediaId}
            </if>
            <if test="screenContentMediaIdCompress != null and screenContentMediaIdCompress != ''">and
                a.screen_content_media_id_compress = #{screenContentMediaIdCompress}
            </if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="devicePattern !=null">and a.device_pattern=#{devicePattern}</if>
            <if test="dictionaryDataOrganizationId !=null">and l.organization_id=#{dictionaryDataOrganizationId}</if>
            <if test="mediaSort != null">and a.media_sort=#{mediaSort}</if>
            <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id in
                <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="mediaSource != null">and a.media_source = #{mediaSource}</if>
            <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
        </where>
        group by a.screen_module_library_media_id
    </select>

    <select id="getPosterList" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo">

        select smlm.*,
        sml.module_name,
        sml.module_group_type,
        l.label_name as moduleGroupTypeName,
        l.parent_label_id,
        l.organization_id
        from screen_module_library_media smlm
        join screen_module_library sml on sml.screen_module_library_id=smlm.screen_module_library_id
        join label_library_rel llr on sml.screen_module_library_id=llr.screen_module_library_id
        join label l on llr.label_id=l.label_id
        where smlm.is_delete=0 and sml.is_delete=0 and llr.is_delete=0 and l.is_delete=0
        <if test="devicePattern !=null">and smlm.device_pattern=#{devicePattern}</if>
        <!--        <if test="libraryPattern != null ">and sml.library_pattern=#{libraryPattern}</if>-->
        <if test="dictionaryDataOrganizationId != null">and l.organization_id=#{dictionaryDataOrganizationId}</if>
        <if test="dictionaryDataClassesId != null">and l.classes_id = #{dictionaryDataClassesId}</if>
        <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
            and sml.screen_module_library_id in
            <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="mediaSource != null">and smlm.media_source = #{mediaSource}</if>
        <if test="thirdId != null and thirdId != ''">and smlm.third_id = #{thirdId}</if>
        group by smlm.screen_module_library_media_id
        order by l.label_sort ASC,sml.library_sort ASC,sml.screen_module_library_id
        ASC,smlm.screen_module_library_media_id ASC
    </select>

    <select id="getLabelModuleLibraryMediaList"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo">
        select smlm.*,sml.module_name,sml.module_group_type
        from screen_module_library_media smlm
        join screen_module_library sml on sml.screen_module_library_id=smlm.screen_module_library_id and sml.is_delete=0
        join label_library_rel llr on sml.screen_module_library_id = llr.screen_module_library_id and llr.is_delete=0
        join label l on l.label_id=llr.label_id
        where smlm.is_delete=0
        <if test="dictionaryDataOrganizationId != null">and l.organization_id=#{dictionaryDataOrganizationId}</if>
        <if test="labelIds != null and labelIds.size()>0">
            and llr.label_id in
            <foreach collection="labelIds" open="(" close=")" separator="," item="labelId">
                #{labelId}
            </foreach>
        </if>
        <if test="mediaSource != null">and smlm.media_source = #{mediaSource}</if>
        <if test="thirdId != null and thirdId != ''">and smlm.third_id = #{thirdId}</if>
        group by smlm.screen_module_library_media_id
        order by l.label_sort ASC,sml.library_sort ASC,smlm.screen_module_library_media_id ASC
    </select>

    <select id="getModuleGroupListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryGroupVo">
        select a.*, GROUP_CONCAT(DISTINCT d.screen_module_library_id order by d.library_sort, d.screen_module_library_id) as
        stringLibraryIds
        from label a
        join label b on a.parent_label_id = b.label_id and b.is_delete=0
        join label_library_rel c on a.label_id=c.label_id and c.is_delete=0
        join screen_module_library d on c.screen_module_library_id=d.screen_module_library_id and d.is_delete=0
        join screen_module_library_media e on d.screen_module_library_id=e.screen_module_library_id and e.is_delete=0
        where a.is_delete=0
        <if test="devicePattern !=null">and e.device_pattern=#{devicePattern}</if>
        <if test="dictionaryDataOrganizationId != null">and a.organization_id=#{dictionaryDataOrganizationId}</if>
        <if test="dictionaryDataClassesId != null">and a.classes_id = #{dictionaryDataClassesId}</if>
        <if test="moduleDefaultH5LabelGroup != null"> and b.label_id=#{moduleDefaultH5LabelGroup}</if>
        <if test="mediaSource != null">and e.media_source = #{mediaSource}</if>
        <if test="thirdId != null and thirdId != ''">and e.third_id = #{thirdId}</if>
        group by a.label_id
        order by b.label_sort,a.label_sort
        limit #{pageSize}
    </select>

    <select id="getPosterLabelRel" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaVo">
        <!-- 该sql的图片排序未生效，已使用代码排序 -->
        select smlm.*,
        sml.module_name,
        sml.module_group_type,
        l.label_name as moduleGroupTypeName,
        GROUP_CONCAT(l.label_id) as labelIdConcat,
        l.organization_id
        from screen_module_library_media smlm
        join screen_module_library sml on sml.screen_module_library_id=smlm.screen_module_library_id
        join label_library_rel llr on sml.screen_module_library_id=llr.screen_module_library_id
        join label l on llr.label_id=l.label_id
        where smlm.is_delete=0 and sml.is_delete=0 and llr.is_delete=0 and l.is_delete=0
        <if test="devicePattern !=null">and smlm.device_pattern=#{devicePattern}</if>
        <if test="dictionaryDataOrganizationId != null">and l.organization_id=#{dictionaryDataOrganizationId}</if>
        <if test="dictionaryDataClassesId != null">and l.classes_id = #{dictionaryDataClassesId}</if>
        <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
            and sml.screen_module_library_id in
            <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="mediaSource != null">and smlm.media_source = #{mediaSource}</if>
        <if test="thirdId != null and thirdId != ''">and smlm.third_id = #{thirdId}</if>
        group by smlm.screen_module_library_media_id
        order by l.label_sort ASC,sml.library_sort ASC,sml.screen_module_library_id
        ASC,smlm.screen_module_library_media_id ASC
    </select>

</mapper>
