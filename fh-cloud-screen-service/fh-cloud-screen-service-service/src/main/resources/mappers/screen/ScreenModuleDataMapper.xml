<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleDataMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleData" id="BaseResultMap">
        <result property="screenModuleDataId" column="screen_module_data_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
        <result property="moduleSource" column="module_source"/>
        <result property="customModuleName" column="custom_module_name"/>
        <result property="customModuleGroupType" column="custom_module_group_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <select id="getScreenModuleDataListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo">
        select a.*,b.module_group_type,b.module_name,b.is_poster from screen_module_data a
        left join screen_module_library b on a.screen_module_library_id = b.screen_module_library_id
        <where>
            <if test="screenModuleDataId != null ">and a.screen_module_data_id = #{screenModuleDataId}</if>
            <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
            <if test="screenModuleLibraryId != null ">and a.screen_module_library_id = #{screenModuleLibraryId}</if>
            <if test="moduleSource != null ">and a.module_source = #{moduleSource}</if>
            <if test="customModuleName != null and customModuleName != ''">and a.custom_module_name =
                #{customModuleName}
            </if>
            <if test="customModuleGroupType != null ">and a.custom_module_group_type = #{customModuleGroupType}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
            <if test="moduleGroupType != null">
                <!-- 控制分组显示类型 -->
                and((a.module_source =1 and b.module_group_type=#{moduleGroupType}) or (a.module_source =2 and
                a.custom_module_group_type=#{moduleGroupType}))
            </if>
        </where>
        order by a.module_source,b.module_group_type,b.create_time,a.custom_module_group_type,a.create_time
    </select>

    <select id="listByScreenSceneIds" parameterType="map"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo">
        select
        a.screen_module_data_id,a.organization_id,if(b.screen_module_library_id!=0,b.screen_module_library_id,a.screen_module_library_id)
        as screen_module_library_id,a.module_source,a.custom_module_name
        ,a.custom_module_group_type,a.create_time,a.update_time,a.create_by,a.update_by,a.is_delete,b.screen_scene_id,b.screen_module_library_sel_ids
        ,c.module_group_type,c.module_name
        from screen_module_data a
        join screen_scene_module_rel b on a.screen_module_data_id = b.screen_module_data_id and b.is_delete=0
        left join screen_module_library c on a.screen_module_library_id = c.screen_module_library_id
        where a.is_delete=0 and b.screen_scene_id in
        <foreach collection="screenSceneIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="listCustomModuleDataVosByName"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleDataVo">
        select a.* from screen_module_data a
        left join screen_module_library b on a.screen_module_library_id = b.screen_module_library_id
        where a.organization_id=#{organizationId} and a.is_delete=0
        and((a.module_source =1 and b.module_group_type=#{customModuleGroupType} and b.module_name =#{customModuleName})
        or (a.module_source =2 and
        a.custom_module_group_type=#{customModuleGroupType} and a.custom_module_name = #{customModuleName} ))
        <if test="screenModuleDataId != null">
            and a.screen_module_data_id != #{screenModuleDataId}
        </if>
    </select>
</mapper>