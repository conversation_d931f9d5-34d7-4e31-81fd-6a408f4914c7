<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSignatureContentMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureContentDto" id="BaseResultMap">
        <result property="screenSignatureContentId" column="screen_signature_content_id"/>
        <result property="screenModuleDataId" column="screen_module_data_id"/>
        <result property="screenSignatureContentTitle" column="screen_signature_content_title"/>
        <result property="screenSignatureContentTxt" column="screen_signature_content_txt"/>
        <result property="classesId" column="classes_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="createUserClassesName" column="create_user_classes_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenSignatureContentId != null ">and screen_signature_content_id = #{screenSignatureContentId}</if>
			<if test="screenModuleDataId != null ">and screen_module_data_id = #{screenModuleDataId}</if>
			<if test="screenSignatureContentTitle != null and screenSignatureContentTitle != '' ">and screen_signature_content_title like concat('%', #{screenSignatureContentTitle}, '%')</if>
			<if test="screenSignatureContentTxt != null and screenSignatureContentTxt != '' ">and screen_signature_content_txt like concat('%', #{screenSignatureContentTxt}, '%')</if>
			<if test="classesId != null ">and classes_id = #{classesId}</if>
			<if test="createUserName != null and createUserName != '' ">and create_user_name like concat('%', #{createUserName}, '%')</if>
			<if test="createUserClassesName != null and createUserClassesName != '' ">and create_user_classes_name like concat('%', #{createUserClassesName}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="organizationId != null">and organization_id=#{organizationId} </if>
		</where>
		<if test="orderBy == null or orderBy==''">
			order by create_time desc
		</if>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.screen_signature_content_id
	 		,t.screen_module_data_id
	 		,t.screen_signature_content_title
	 		,t.screen_signature_content_txt
	 		,t.classes_id
	 		,t.create_time
	 		,t.create_by
	 		,t.create_user_name
	 		,t.create_user_classes_name
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.organization_id
		from (
			 select a.*,smd.organization_id from screen_signature_content a
			 join screen_module_data smd on a.screen_module_data_id = smd.screen_module_data_id
		 ) t
	</sql>

	<select id="getScreenSignatureContentListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenSignatureContentByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureContentVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>