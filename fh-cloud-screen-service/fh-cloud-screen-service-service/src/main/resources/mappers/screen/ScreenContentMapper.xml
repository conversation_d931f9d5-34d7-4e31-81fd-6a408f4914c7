<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenContentMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenContent" id="BaseResultMap">
        <result property="screenContentId" column="screen_content_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="classesId" column="classes_id"/>
        <result property="scopeType" column="scope_type"/>
        <result property="screenModuleDataId" column="screen_module_data_id"/>
        <result property="screenContentStatus" column="screen_content_status"/>
        <result property="screenContentType" column="screen_content_type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="screenContentSource" column="screen_content_source"/>
        <result property="contentNice" column="content_nice"/>
    </resultMap>

    <select id="getScreenContentListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenContentVo">
        select t.* from (
        select distinct a.*,scd.screen_content_title,scd.screen_content_txt from screen_content a
        left join screen_content_detail scd on a.screen_content_id = scd.screen_content_id
        ) t
        <where>
            <if test="screenContentId != null ">and screen_content_id = #{screenContentId}</if>
            <if test="organizationId != null ">and organization_id = #{organizationId}</if>
            <if test="campusId != null ">and (campus_id = #{campusId}
                <if test="singleShow == false">
                    or campus_id =0
                </if>
            )</if>
            <if test="classesId != null ">and (classes_id = #{classesId}
                <if test="screenIndexShow == true">
                    or classes_id=0
                </if>
            )</if>
            <if test="scopeType != null ">and scope_type = #{scopeType}</if>
            <if test="screenModuleDataId != null ">and screen_module_data_id = #{screenModuleDataId}</if>
            <if test="screenContentStatus != null ">and screen_content_status = #{screenContentStatus}</if>
            <if test="screenContentType != null ">and screen_content_type = #{screenContentType}</if>
<!--            <if test="startTime != null and startTime != ''">and start_time = #{startTime}</if>-->
            <if test="endTime != null"> and DATE_FORMAT(end_time,'%Y-%m-%d') = DATE_FORMAT(#{endTime},'%Y-%m-%d')</if>
            <if test="createTime != null and createTime != ''">and create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
            <if test="screenContentSource != null ">and screen_content_source = #{screenContentSource}</if>
            <if test="contentNice != null ">and content_nice = #{contentNice}</if>
            <if test="screenContentTitle != null and screenContentTitle != ''">and screen_content_title like
                concat('%',#{screenContentTitle},'%')
            </if>
            <if test="screenContentTxt != null and screenContentTxt != ''">and screen_content_txt like
                concat('%',#{screenContentTxt},'%')
            </if>
            <if test="screenModuleDataIds != null and screenModuleDataIds.size() > 0">
                and screen_module_data_id in
                <foreach collection="screenModuleDataIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="nowDate != null">
                and (<![CDATA[ DATE_FORMAT(start_time,'%Y-%m-%d') <= DATE_FORMAT(#{nowDate},'%Y-%m-%d')]]> or  start_time is null)
                and (<![CDATA[ DATE_FORMAT(end_time,'%Y-%m-%d') >= DATE_FORMAT(#{nowDate},'%Y-%m-%d')]]> or end_time is null)
            </if>
            <if test="overdueDate != null">
                and (<![CDATA[ DATE_FORMAT(end_time,'%Y-%m-%d') < DATE_FORMAT(#{overdueDate},'%Y-%m-%d')]]>)
            </if>
        </where>
        group by screen_content_id
        <if test=" pageNo != null and pageNo == -1 and orderBy != null and orderBy != ''">
            order by ${orderBy}
        </if>
    </select>

    <select id="countFirstRecord" parameterType="map" resultType="java.lang.Integer">
        select 1 from screen_content
        where scope_type=#{scopeType} and screen_module_data_id=#{screenModuleDataId} and update_time <![CDATA[ > ]]> #{updateTime} and is_delete=0
        <if test="campusId != null">
            and (campus_id=#{campusId} or campus_id =0)
        </if>
        <if test="classesId != null">
            and classes_id=#{classesId}
        </if>
        limit 1
    </select>
</mapper>