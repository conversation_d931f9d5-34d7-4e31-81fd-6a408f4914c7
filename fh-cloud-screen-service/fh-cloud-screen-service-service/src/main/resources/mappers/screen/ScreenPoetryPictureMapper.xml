<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenPoetryPictureMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryPictureDto" id="BaseResultMap">
        <result property="screenPoetryPictureId" column="screen_poetry_picture_id"/>
		<result property="organizationId" column="organization_id"/>
        <result property="screenPoetryPictureMediaUrl" column="screen_poetry_picture_media_url"/>
        <result property="screenPoetryPictureMediaName" column="screen_poetry_picture_media_name"/>
        <result property="screenPoetryPictureMediaNameOri" column="screen_poetry_picture_media_name_ori"/>
        <result property="screenPoetryPictureMediaId" column="screen_poetry_picture_media_id"/>
        <result property="screenPoetryPictureMediaMd5" column="screen_poetry_picture_media_md5"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenPoetryPictureId != null ">and screen_poetry_picture_id = #{screenPoetryPictureId}</if>
			<if test="organizationId != null">and organization_id = #{organizationId}</if>
			<if test="screenPoetryPictureMediaUrl != null and screenPoetryPictureMediaUrl != '' ">and screen_poetry_picture_media_url like concat('%', #{screenPoetryPictureMediaUrl}, '%')</if>
			<if test="screenPoetryPictureMediaName != null and screenPoetryPictureMediaName != '' ">and screen_poetry_picture_media_name like concat('%', #{screenPoetryPictureMediaName}, '%')</if>
			<if test="screenPoetryPictureMediaNameOri != null and screenPoetryPictureMediaNameOri != '' ">and screen_poetry_picture_media_name_ori like concat('%', #{screenPoetryPictureMediaNameOri}, '%')</if>
			<if test="screenPoetryPictureMediaId != null and screenPoetryPictureMediaId != '' ">and screen_poetry_picture_media_id like concat('%', #{screenPoetryPictureMediaId}, '%')</if>
			<if test="screenPoetryPictureMediaMd5 != null and screenPoetryPictureMediaMd5 != '' ">and screen_poetry_picture_media_md5 like concat('%', #{screenPoetryPictureMediaMd5}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_poetry_picture_id
			,t.organization_id
	 		,t.screen_poetry_picture_media_url
	 		,t.screen_poetry_picture_media_name
	 		,t.screen_poetry_picture_media_name_ori
	 		,t.screen_poetry_picture_media_id
	 		,t.screen_poetry_picture_media_md5
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from screen_poetry_picture a
		 ) t

	</sql>

	<select id="getScreenPoetryPictureListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenPoetryPictureByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryPictureVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>