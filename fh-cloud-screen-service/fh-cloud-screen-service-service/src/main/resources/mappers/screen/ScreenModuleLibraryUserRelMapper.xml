<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryUserRelMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryUserRelDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userOid" column="user_oid"/>
        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="common_where">
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="userOid != null and userOid != '' ">and user_oid like concat('%', #{userOid}, '%')</if>
            <if test="screenModuleLibraryId != null ">and screen_module_library_id = #{screenModuleLibraryId}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
        </where>
    </sql>

    <sql id="common_select">
		select
			t.id
	 		,t.user_oid
	 		,t.screen_module_library_id
	 		,t.type
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from screen_module_library_user_rel a
		 ) t

	</sql>

    <select id="getScreenModuleLibraryUserRelListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryUserRelVo">
        <include refid="common_select"></include>
        <include refid="common_where"></include>
    </select>
</mapper>