<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSignatureMessageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenSignatureMessageDto" id="BaseResultMap">
        <result property="screenSignatureMessageId" column="screen_signature_message_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="screenSignatureMessageContent" column="screen_signature_message_content"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenSignatureMessageId != null ">and screen_signature_message_id = #{screenSignatureMessageId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="screenSignatureMessageContent != null and screenSignatureMessageContent != '' ">and screen_signature_message_content like concat('%', #{screenSignatureMessageContent}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.screen_signature_message_id
	 		,t.organization_id
	 		,t.screen_signature_message_content
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from screen_signature_message a
		 ) t

	</sql>

	<select id="getScreenSignatureMessageListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenSignatureMessageByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSignatureMessageVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>