<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSceneAuditMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneAuditDto" id="BaseResultMap">
        <result property="screenSceneAuditId" column="screen_scene_audit_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="screenSceneName" column="screen_scene_name"/>
        <result property="screenSceneLayout" column="screen_scene_layout"/>
        <result property="spaceGroupId" column="space_group_id"/>
        <result property="screenSceneType" column="screen_scene_type"/>
        <result property="screenDevicePattern" column="screen_device_pattern"/>
        <result property="screenIndex" column="screen_index"/>
        <result property="screenPlayName" column="screen_play_name"/>
        <result property="screenPlayIndex" column="screen_play_index"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="publishType" column="publish_type"/>
        <result property="deviceFullType" column="device_full_type"/>
        <result property="auditType" column="audit_type"/>
        <result property="reason" column="reason"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="screenModuleData" column="screen_module_data"/>
        <result property="param" column="param"/>
		<result property="startDate" column="start_date"/>
		<result property="endDate" column="end_date"/>
		<result property="weeks" column="weeks"/>
		<result property="parentOrganizationId" column="parent_organization_id"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenSceneAuditId != null ">and screen_scene_audit_id = #{screenSceneAuditId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
			<if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="screenSceneName != null and screenSceneName != '' ">and screen_scene_name like concat('%', #{screenSceneName}, '%')</if>
			<if test="screenSceneLayout != null and screenSceneLayout != '' ">and screen_scene_layout like concat('%', #{screenSceneLayout}, '%')</if>
			<if test="spaceGroupId != null ">and space_group_id = #{spaceGroupId}</if>
			<if test="screenSceneType != null ">and screen_scene_type = #{screenSceneType}</if>
			<if test="screenDevicePattern != null ">and screen_device_pattern = #{screenDevicePattern}</if>
			<if test="screenIndex != null ">and screen_index = #{screenIndex}</if>
			<if test="screenPlayName != null and screenPlayName != '' ">and screen_play_name like concat('%', #{screenPlayName}, '%')</if>
			<if test="screenPlayIndex != null ">and screen_play_index = #{screenPlayIndex}</if>
			<if test="startTime != null ">and start_time = #{startTime}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>
			<if test="publishType != null ">and publish_type = #{publishType}</if>
			<if test="deviceFullType != null ">and device_full_type = #{deviceFullType}</if>
			<if test="auditType != null ">and audit_type = #{auditType}</if>
			<if test="reason != null and reason != '' ">and reason like concat('%', #{reason}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="screenModuleData != null and screenModuleData != '' ">and screen_module_data like concat('%', #{screenModuleData}, '%')</if>
			<if test="param != null and param != '' ">and param like concat('%', #{param}, '%')</if>
			<if test="auditUser != null and auditUser != ''">and audit_user = #{auditUser}</if>
			<if test="auditTime != null">and audit_time = #{auditTime}</if>
			<if test="startDate != null">and start_date = #{startDate}</if>
			<if test="endDate != null">and end_date = #{endDate}</if>
			<if test="weeks != null and weeks != ''">and weeks = #{weeks}</if>
			<if test="parentOrganizationId != null">and parent_organization_id = #{parentOrganizationId}</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="screenSceneAuditId != null ">and screen_scene_audit_id = #{screenSceneAuditId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
			<if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="screenSceneName != null and screenSceneName != '' ">and screen_scene_name like concat('%', #{screenSceneName}, '%')</if>
			<if test="screenSceneLayout != null and screenSceneLayout != '' ">and screen_scene_layout like concat('%', #{screenSceneLayout}, '%')</if>
			<if test="spaceGroupId != null ">and space_group_id = #{spaceGroupId}</if>
			<if test="screenSceneType != null ">and screen_scene_type = #{screenSceneType}</if>
			<if test="screenDevicePattern != null ">and screen_device_pattern = #{screenDevicePattern}</if>
			<if test="screenIndex != null ">and screen_index = #{screenIndex}</if>
			<if test="screenPlayName != null and screenPlayName != '' ">and screen_play_name like concat('%', #{screenPlayName}, '%')</if>
			<if test="screenPlayIndex != null ">and screen_play_index = #{screenPlayIndex}</if>
			<if test="startTime != null ">and start_time = #{startTime}</if>
			<if test="endTime != null ">and end_time = #{endTime}</if>
			<if test="publishType != null ">and publish_type = #{publishType}</if>
			<if test="deviceFullType != null ">and device_full_type = #{deviceFullType}</if>
			<if test="auditType != null ">and audit_type = #{auditType}</if>
			<if test="reason != null and reason != '' ">and reason like concat('%', #{reason}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
			<if test="screenModuleData != null and screenModuleData != '' ">and screen_module_data like concat('%', #{screenModuleData}, '%')</if>
			<if test="param != null and param != '' ">and param like concat('%', #{param}, '%')</if>
			<if test="auditUser != null and auditUser != ''">and audit_user = #{auditUser}</if>
			<if test="auditTime != null">and audit_time = #{auditTime}</if>
			<if test="startDate != null">and start_date = #{startDate}</if>
			<if test="endDate != null">and end_date = #{endDate}</if>
			<if test="weeks != null and weeks != ''">and weeks = #{weeks}</if>
			<if test="parentOrganizationId != null">and parent_organization_id = #{parentOrganizationId}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_scene_audit_id
	 		,t.organization_id
	 		,t.campus_id
	 		,t.space_info_id
	 		,t.space_group_use_type
	 		,t.show_device_id
	 		,t.screen_scene_name
	 		,t.screen_scene_layout
	 		,t.space_group_id
	 		,t.screen_scene_type
	 		,t.screen_device_pattern
	 		,t.screen_index
	 		,t.screen_play_name
	 		,t.screen_play_index
	 		,t.start_time
	 		,t.end_time
	 		,t.publish_type
	 		,t.device_full_type
	 		,t.audit_type
	 		,t.reason
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.audit_user
			,t.audit_time
			,t.start_date
			,t.end_date
			,t.weeks
			,t.parent_organization_id
		from (
			select a.* from screen_scene_audit a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getScreenSceneAuditListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		order by update_time desc
	</select>

	<select id="getScreenSceneAuditListByPoint" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo">
		select
			ssa.screen_scene_audit_id,
			ssa.organization_id,
			ssa.campus_id,
			ssa.space_info_id,
			ssa.space_group_use_type,
			ssa.show_device_id,
			ssa.screen_scene_name,
			ssa.screen_scene_layout,
			ssa.space_group_id,
			ssa.screen_scene_type,
			ssa.screen_device_pattern,
			ssa.screen_index,
			ssa.screen_play_name,
			ssa.screen_play_index,
			ssa.start_time,
			ssa.end_time,
			ssa.publish_type,
			ssa.audit_type,
			ssa.reason,
			ssa.create_time,
			ssa.create_by,
			ssa.update_time,
			ssa.update_by,
			ssa.is_delete,
			ssa.audit_user,
			ssa.audit_time,
			sd.device_name,
			sd.device_number,
			sd.device_pattern,
			sd.device_status,
			sd.device_type,
			sd.device_poster_duration,
			sd.push_type,
			sd.device_full_type,
			si.space_info_name
		from (<include refid="common_select"></include>) ssa
		join show_device sd on ssa.show_device_id = sd.show_device_id
		join space_device_rel sdr on sdr.show_device_id = sd.show_device_id
		left join space_info si on si.space_info_id = sdr.space_info_id and sdr.space_group_use_type = 2
		order by ssa.update_time desc
	</select>

	<select id="getScreenSceneAuditByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

	<update id="deleteToAuditByShowDeviceId">
		update screen_scene_audit
		set is_delete = 1
		where
			audit_type = #{auditType}
		and show_device_id = #{showDeviceId}
	</update>

    <select id="getScreenSceneAuditCount"
			resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneAuditCountVo">
		select ifnull(sum(if(audit_type = 1, 1, 0)), 0) toAuditCount,
			ifnull(sum(if(audit_type = 2, 1, 0)), 0) auditPassCount,
			ifnull(sum(if(audit_type = 3, 1, 0)), 0) auditRejectCount
		from screen_scene_audit
		<include refid="common_where">
		</include>
	</select>

</mapper>