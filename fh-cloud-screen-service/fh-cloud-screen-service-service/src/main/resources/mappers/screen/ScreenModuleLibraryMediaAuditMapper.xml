<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMediaAuditMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibraryMediaAuditDto" id="BaseResultMap">
        <result property="screenModuleLibraryMediaAuditId" column="screen_module_library_media_audit_id"/>
        <result property="screenModuleLibraryAuditId" column="screen_module_library_audit_id"/>
        <result property="screenModuleLibraryMediaUrl" column="screen_module_library_media_url"/>
        <result property="screenModuleLibraryMediaUrlCompress" column="screen_module_library_media_url_compress"/>
        <result property="screenModuleLibraryMediaUrlCover" column="screen_module_library_media_url_cover"/>
        <result property="screenModuleLibraryMediaName" column="screen_module_library_media_name"/>
        <result property="screenModuleLibraryMediaNameOri" column="screen_module_library_media_name_ori"/>
        <result property="screenContentMediaId" column="screen_content_media_id"/>
        <result property="screenContentMediaIdCompress" column="screen_content_media_id_compress"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="devicePattern" column="device_pattern"/>
        <result property="screenContentMediaMd5" column="screen_content_media_md5"/>
        <result property="mediaSort" column="media_sort"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenModuleLibraryMediaAuditId != null ">and screen_module_library_media_audit_id = #{screenModuleLibraryMediaAuditId}</if>
			<if test="screenModuleLibraryAuditId != null ">and screen_module_library_audit_id = #{screenModuleLibraryAuditId}</if>
			<if test="screenModuleLibraryMediaUrl != null and screenModuleLibraryMediaUrl != '' ">and screen_module_library_media_url like concat('%', #{screenModuleLibraryMediaUrl}, '%')</if>
			<if test="screenModuleLibraryMediaUrlCompress != null and screenModuleLibraryMediaUrlCompress != '' ">and screen_module_library_media_url_compress like concat('%', #{screenModuleLibraryMediaUrlCompress}, '%')</if>
			<if test="screenModuleLibraryMediaUrlCover != null and screenModuleLibraryMediaUrlCover != '' ">and screen_module_library_media_url_cover like concat('%', #{screenModuleLibraryMediaUrlCover}, '%')</if>
			<if test="screenModuleLibraryMediaName != null and screenModuleLibraryMediaName != '' ">and screen_module_library_media_name like concat('%', #{screenModuleLibraryMediaName}, '%')</if>
			<if test="screenModuleLibraryMediaNameOri != null and screenModuleLibraryMediaNameOri != '' ">and screen_module_library_media_name_ori like concat('%', #{screenModuleLibraryMediaNameOri}, '%')</if>
			<if test="screenContentMediaId != null and screenContentMediaId != '' ">and screen_content_media_id like concat('%', #{screenContentMediaId}, '%')</if>
			<if test="screenContentMediaIdCompress != null and screenContentMediaIdCompress != '' ">and screen_content_media_id_compress like concat('%', #{screenContentMediaIdCompress}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="devicePattern != null ">and device_pattern = #{devicePattern}</if>
			<if test="screenContentMediaMd5 != null and screenContentMediaMd5 != '' ">and screen_content_media_md5 like concat('%', #{screenContentMediaMd5}, '%')</if>
			<if test="mediaSort != null ">and media_sort = #{mediaSort}</if>
			<if test="screenModuleLibraryAuditIds != null and screenModuleLibraryAuditIds.size() >0">
				and screen_module_library_audit_id in
				<foreach collection="screenModuleLibraryAuditIds" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="screenModuleLibraryMediaAuditId != null ">and screen_module_library_media_audit_id = #{screenModuleLibraryMediaAuditId}</if>
			<if test="screenModuleLibraryAuditId != null ">and screen_module_library_audit_id = #{screenModuleLibraryAuditId}</if>
			<if test="screenModuleLibraryMediaUrl != null and screenModuleLibraryMediaUrl != '' ">and screen_module_library_media_url like concat('%', #{screenModuleLibraryMediaUrl}, '%')</if>
			<if test="screenModuleLibraryMediaUrlCompress != null and screenModuleLibraryMediaUrlCompress != '' ">and screen_module_library_media_url_compress like concat('%', #{screenModuleLibraryMediaUrlCompress}, '%')</if>
			<if test="screenModuleLibraryMediaUrlCover != null and screenModuleLibraryMediaUrlCover != '' ">and screen_module_library_media_url_cover like concat('%', #{screenModuleLibraryMediaUrlCover}, '%')</if>
			<if test="screenModuleLibraryMediaName != null and screenModuleLibraryMediaName != '' ">and screen_module_library_media_name like concat('%', #{screenModuleLibraryMediaName}, '%')</if>
			<if test="screenModuleLibraryMediaNameOri != null and screenModuleLibraryMediaNameOri != '' ">and screen_module_library_media_name_ori like concat('%', #{screenModuleLibraryMediaNameOri}, '%')</if>
			<if test="screenContentMediaId != null and screenContentMediaId != '' ">and screen_content_media_id like concat('%', #{screenContentMediaId}, '%')</if>
			<if test="screenContentMediaIdCompress != null and screenContentMediaIdCompress != '' ">and screen_content_media_id_compress like concat('%', #{screenContentMediaIdCompress}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="devicePattern != null ">and device_pattern = #{devicePattern}</if>
			<if test="screenContentMediaMd5 != null and screenContentMediaMd5 != '' ">and screen_content_media_md5 like concat('%', #{screenContentMediaMd5}, '%')</if>
			<if test="mediaSort != null ">and media_sort = #{mediaSort}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_module_library_media_audit_id
	 		,t.screen_module_library_audit_id
	 		,t.screen_module_library_media_url
	 		,t.screen_module_library_media_url_compress
	 		,t.screen_module_library_media_url_cover
	 		,t.screen_module_library_media_name
	 		,t.screen_module_library_media_name_ori
	 		,t.screen_content_media_id
	 		,t.screen_content_media_id_compress
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
	 		,t.device_pattern
	 		,t.screen_content_media_md5
	 		,t.media_sort
		from (
			select a.* from screen_module_library_media_audit a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getScreenModuleLibraryMediaAuditListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenModuleLibraryMediaAuditByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryMediaAuditVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>