<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenContentSpecialSpaceGroupRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecialSpaceGroupRel" id="BaseResultMap">
	        <result property="id" column="id"/>
	        <result property="screenContentSpecialId" column="screen_content_special_id"/>
	        <result property="spaceGroupId" column="space_group_id"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	    </resultMap>

	<select id="getScreenContentSpecialSpaceGroupRelListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialSpaceGroupRelVo">
		select a.* from screen_content_special_space_group_rel a
	    <where>
	    				    <if test="id != null ">and a.id = #{id}</if>
						    <if test="screenContentSpecialId != null ">and a.screen_content_special_id = #{screenContentSpecialId}</if>
						    <if test="spaceGroupId != null ">and a.space_group_id = #{spaceGroupId}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
				    </where>
	</select>
</mapper>