<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSceneMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenScene" id="BaseResultMap">
	        <result property="screenSceneId" column="screen_scene_id"/>
	        <result property="organizationId" column="organization_id"/>
	        <result property="campusId" column="campus_id"/>
	        <result property="screenSceneName" column="screen_scene_name"/>
	        <result property="screenSceneLayout" column="screen_scene_layout"/>
	        <result property="spaceGroupId" column="space_group_id"/>
	        <result property="screenSceneType" column="screen_scene_type"/>
	        <result property="screenDevicePattern" column="screen_device_pattern"/>
	        <result property="screenIndex" column="screen_index"/>
	        <result property="screenPlayName" column="screen_play_name"/>
	        <result property="screenPlayIndex" column="screen_play_index"/>
	        <result property="startTime" column="start_time"/>
	        <result property="endTime" column="end_time"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	        <result property="spaceInfoId" column="space_info_id"/>
	        <result property="spaceGroupUseType" column="space_group_use_type"/>
	        <result property="showDeviceId" column="show_device_id"/>
	        <result property="publishType" column="publish_type"/>
	        <result property="deviceFullType" column="device_full_type"/>
	        <result property="startDate" column="start_date"/>
	        <result property="endDate" column="end_date"/>
	        <result property="weeks" column="weeks"/>
	    </resultMap>

	<select id="getScreenSceneListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo">
		select a.* from screen_scene a
	    <where>
	    				    <if test="screenSceneId != null ">and a.screen_scene_id = #{screenSceneId}</if>
						    <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
						    <if test="campusId != null ">and (a.campus_id = #{campusId}
								<if test="singleShow == false">
									or a.campus_id=0
								</if>
						    )</if>
						    <if test="screenSceneName != null and screenSceneName != ''">and a.screen_scene_name = #{screenSceneName}</if>
						    <if test="screenSceneLayout != null and screenSceneLayout != ''">and a.screen_scene_layout = #{screenSceneLayout}</if>
						    <if test="spaceGroupId != null ">and a.space_group_id = #{spaceGroupId}</if>
						    <if test="screenSceneType != null ">and a.screen_scene_type = #{screenSceneType}</if>
						    <if test="screenDevicePattern != null ">and a.screen_device_pattern = #{screenDevicePattern}</if>
						    <if test="screenIndex != null ">and a.screen_index = #{screenIndex}</if>
						    <if test="screenPlayName != null and screenPlayName != ''">and a.screen_play_name = #{screenPlayName}</if>
						    <if test="screenPlayIndex != null ">and a.screen_play_index = #{screenPlayIndex}</if>
						    <if test="startTime != null and startTime != ''">and a.start_time = #{startTime}</if>
						    <if test="endTime != null and endTime != ''">and a.end_time = #{endTime}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
						    <if test="spaceInfoId != null ">and a.space_info_id = #{spaceInfoId}</if>
						    <if test="spaceGroupUseType != null ">and a.space_group_use_type = #{spaceGroupUseType}</if>
						    <if test="showDeviceId != null ">and a.show_device_id = #{showDeviceId}</if>
						    <if test="publishType != null ">and a.publish_type = #{publishType}</if>
						    <if test="deviceFullType != null ">and a.device_full_type = #{deviceFullType}</if>
						    <if test="startDate != null ">and a.start_date = #{startDate}</if>
						    <if test="endDate != null ">and a.end_date = #{endDate}</if>
						    <if test="weeks != null and weeks != ''">and a.weeks = #{weeks}</if>
							<if test="showDeviceIds != null and showDeviceIds.size() >0">
								and a.show_device_id in
								<foreach collection="showDeviceIds" open="(" close=")" separator="," item="item">
									#{item}
								</foreach>
							</if>
							<if test="parentOrganizationId != null">and a.parent_organization_id = #{parentOrganizationId}</if>
				    </where>
	</select>

	<select id="listScreenSceneVoByScreenSceneIds" parameterType="map" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneVo">
		select * from screen_scene
		where screen_scene_id in
		<foreach collection="sceneIds" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
		<if test="isDelete != null">
			and is_delete=#{isDelete}
		</if>
	</select>
</mapper>