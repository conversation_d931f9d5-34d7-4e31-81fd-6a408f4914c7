<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenContentSpecialMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenContentSpecial" id="BaseResultMap">
	        <result property="screenContentSpecialId" column="screen_content_special_id"/>
	        <result property="organizationId" column="organization_id"/>
	        <result property="campusId" column="campus_id"/>
	        <result property="screenTemplateType" column="screen_template_type"/>
	        <result property="screenContentTitle" column="screen_content_title"/>
	        <result property="screenContentTxt" column="screen_content_txt"/>
	        <result property="screenContentUrl" column="screen_content_url"/>
	        <result property="screenContentMediaUrl" column="screen_content_media_url"/>
	        <result property="screenContentMediaUrlCompress" column="screen_content_media_url_compress"/>
	        <result property="screenContentMediaUrlCover" column="screen_content_media_url_cover"/>
	        <result property="screenContentMediaName" column="screen_content_media_name"/>
	        <result property="screenContentMediaNameOri" column="screen_content_media_name_ori"/>
	        <result property="startTime" column="start_time"/>
	        <result property="endTime" column="end_time"/>
	        <result property="screenContentStatus" column="screen_content_status"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
			<result property="screenContentMediaId" column="screen_content_media_id"/>
			<result property="screenContentMediaIdCompress" column="screen_content_media_id_compress"/>
			<result property="callContent" column="call_content"/>
			<result property="signContent" column="sign_content"/>
			<result property="screenContentSource" column="screen_content_source"/>
	    </resultMap>

	<select id="getScreenContentSpecialListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo">
		select a.*,GROUP_CONCAT(c.space_group_name) as spaceGroupNameConcat from screen_content_special a
		left join screen_content_special_space_group_rel b on a.screen_content_special_id = b.screen_content_special_id and b.is_delete=0
		left join space_group c on b.space_group_id = c.space_group_id and c.is_delete=0
	    <where>
	    				    <if test="screenContentSpecialId != null ">and a.screen_content_special_id = #{screenContentSpecialId}</if>
						    <if test="organizationId != null ">and a.organization_id = #{organizationId}</if>
						    <if test="campusId != null ">and (a.campus_id = #{campusId}
						    	<if test="singleShow == false">
									or a.campus_id =0
								</if>
						    )</if>
						    <if test="screenTemplateType != null ">and a.screen_template_type = #{screenTemplateType}</if>
						    <if test="screenContentTitle != null and screenContentTitle != ''">and a.screen_content_title = #{screenContentTitle}</if>
						    <if test="screenContentTxt != null and screenContentTxt != ''">and a.screen_content_txt = #{screenContentTxt}</if>
						    <if test="screenContentUrl != null and screenContentUrl != ''">and a.screen_content_url = #{screenContentUrl}</if>
						    <if test="screenContentMediaUrl != null and screenContentMediaUrl != ''">and a.screen_content_media_url = #{screenContentMediaUrl}</if>
						    <if test="screenContentMediaUrlCompress != null and screenContentMediaUrlCompress != ''">and a.screen_content_media_url_compress = #{screenContentMediaUrlCompress}</if>
						    <if test="screenContentMediaUrlCover != null and screenContentMediaUrlCover != ''">and a.screen_content_media_url_cover = #{screenContentMediaUrlCover}</if>
						    <if test="screenContentMediaName != null and screenContentMediaName != ''">and a.screen_content_media_name = #{screenContentMediaName}</if>
						    <if test="screenContentMediaNameOri != null and screenContentMediaNameOri != ''">and a.screen_content_media_name_ori = #{screenContentMediaNameOri}</if>
						    <if test="startTime != null and startTime != ''">and a.start_time = #{startTime}</if>
						    <if test="endTime != null and endTime != ''">and a.end_time = #{endTime}</if>
						    <if test="screenContentStatus != null and screenContentStatus != ''">and a.screen_content_status = #{screenContentStatus}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
						    <if test="screenContentSource != null ">and a.screen_content_source = #{screenContentSource}</if>
							<if test="screenContentMediaId != null and screenContentMediaId != ''">and a.screen_content_media_id = #{screenContentMediaId}</if>
							<if test="screenContentMediaIdCompress != null and screenContentMediaIdCompress != ''">and a.screen_content_media_id_compress = #{screenContentMediaIdCompress}</if>
							<if test="callContent != null and callContent != ''">and a.call_content = #{callContent}</if>
							<if test="signContent != null and signContent != ''">and a.sign_content = #{signContent}</if>
		</where>
		group by a.screen_content_special_id
	</select>

	<select id="listScreenContentSpecialVosBySpaceGroupId" parameterType="map" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenContentSpecialVo">
		select a.* from  screen_content_special a
		join screen_content_special_space_group_rel b on a.screen_content_special_id = b.screen_content_special_id and b.is_delete=0
		where a.is_delete=0 and a.screen_content_status=2 and a.organization_id=#{organizationId}
		<if test="campusId != null">
			and (a.campus_id=0 or a.campus_id=#{campusId})
		</if>
		and b.space_group_id=#{spaceGroupId}
		<if test="nowDate != null">
			and (<![CDATA[ DATE_FORMAT(start_time,'%Y-%m-%d') <= DATE_FORMAT(#{nowDate},'%Y-%m-%d')]]> or  start_time is null)
			and (<![CDATA[ DATE_FORMAT(end_time,'%Y-%m-%d') >= DATE_FORMAT(#{nowDate},'%Y-%m-%d')]]> or end_time is null)
		</if>
	</select>
</mapper>