<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSceneThirdMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneThirdDto" id="BaseResultMap">
        <result property="screenSceneThirdId" column="screen_scene_third_id"/>
        <result property="screenSceneThirdName" column="screen_scene_third_name"/>
        <result property="screenSceneThirdType" column="screen_scene_third_type"/>
        <result property="screenSceneThirdStartTime" column="screen_scene_third_start_time"/>
        <result property="screenSceneThirdEndTime" column="screen_scene_third_end_time"/>
        <result property="organizationId" column="organization_id"/>
        <result property="campusId" column="campus_id"/>
        <result property="spaceInfoId" column="space_info_id"/>
        <result property="spaceGroupUseType" column="space_group_use_type"/>
        <result property="showDeviceId" column="show_device_id"/>
        <result property="screenSceneThirdShowUrl" column="screen_scene_third_show_url"/>
        <result property="screenSceneThirdPostUrl" column="screen_scene_third_post_url"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="appCode" column="app_code"/>
        <result property="screenSceneThirdDelayMinute" column="screen_scene_third_delay_minute"/>
        <result property="thirdBizId" column="third_biz_id"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenSceneThirdId != null ">and screen_scene_third_id = #{screenSceneThirdId}</if>
			<if test="screenSceneThirdName != null and screenSceneThirdName != '' ">and screen_scene_third_name like concat('%', #{screenSceneThirdName}, '%')</if>
			<if test="screenSceneThirdType != null ">and screen_scene_third_type = #{screenSceneThirdType}</if>
			<if test="screenSceneThirdStartTime != null ">and screen_scene_third_start_time = #{screenSceneThirdStartTime}</if>
			<if test="screenSceneThirdEndTime != null ">and screen_scene_third_end_time = #{screenSceneThirdEndTime}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="campusId != null ">and campus_id = #{campusId}</if>
			<if test="spaceInfoId != null ">and space_info_id = #{spaceInfoId}</if>
			<if test="spaceGroupUseType != null ">and space_group_use_type = #{spaceGroupUseType}</if>
			<if test="showDeviceId != null ">and show_device_id = #{showDeviceId}</if>
			<if test="screenSceneThirdShowUrl != null and screenSceneThirdShowUrl != '' ">and screen_scene_third_show_url like concat('%', #{screenSceneThirdShowUrl}, '%')</if>
			<if test="screenSceneThirdPostUrl != null and screenSceneThirdPostUrl != '' ">and screen_scene_third_post_url like concat('%', #{screenSceneThirdPostUrl}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="appCode != null and appCode != ''">and app_code = #{appCode}</if>
			<if test="screenSceneThirdDelayMinute != null">and screen_scene_third_delay_minute = #{screenSceneThirdDelayMinute}</if>
			<if test="thirdBizId != null and thirdBizId != ''">and third_biz_id = #{thirdBizId}</if>
			<if test="startUpdateTime != null">
				and update_time <![CDATA[  >= ]]> #{startUpdateTime}
			</if>

			<if test="endUpdateTime != null">
				and update_time <![CDATA[  <= ]]> #{endUpdateTime}
			</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.screen_scene_third_id
	 		,t.screen_scene_third_name
	 		,t.screen_scene_third_type
	 		,t.screen_scene_third_start_time
	 		,t.screen_scene_third_end_time
	 		,t.organization_id
	 		,t.campus_id
	 		,t.space_info_id
	 		,t.space_group_use_type
	 		,t.show_device_id
	 		,t.screen_scene_third_show_url
	 		,t.screen_scene_third_post_url
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
			,t.app_code
			,t.screen_scene_third_delay_minute
			,t.third_biz_id
		from (
			 select a.* from screen_scene_third a
		 ) t

	</sql>

	<select id="getScreenSceneThirdListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenSceneThirdVoListBySpaceAndDeviceAndDate"
			resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneThirdVo">
		select sst.screen_scene_third_id,sst.screen_scene_third_name,sst.screen_scene_third_type,sst.screen_scene_third_start_time,sst.screen_scene_third_end_time,sst.organization_id,
			   sst.campus_id,sst.space_info_id,sst.space_group_use_type,sst.show_device_id,sst.screen_scene_third_show_url,sst.screen_scene_third_post_url,sst.app_code,sst.screen_scene_third_delay_minute,sst.third_biz_id
		,sst.update_time
		from screen_scene_third sst
		where sst.space_info_id=#{spaceInfoId} and sst.space_group_use_type=#{spaceGroupUseType}
		  <if test="showDeviceId != null">
			  and sst.show_device_id=#{showDeviceId}
		  </if>
		and DATE_FORMAT(sst.screen_scene_third_start_time,'%Y-%m-%d') = DATE_FORMAT(#{date},'%Y-%m-%d')
		and sst.is_delete=0
	</select>
</mapper>