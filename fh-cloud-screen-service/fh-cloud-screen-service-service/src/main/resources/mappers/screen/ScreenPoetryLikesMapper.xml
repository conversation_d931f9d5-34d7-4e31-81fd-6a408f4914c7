<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenPoetryLikesMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenPoetryLikesDto" id="BaseResultMap">
        <result property="screenPoetryLikesId" column="screen_poetry_likes_id"/>
        <result property="screenPoetryContentId" column="screen_poetry_content_id"/>
        <result property="likesNum" column="likes_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenPoetryLikesId != null ">and screen_poetry_likes_id = #{screenPoetryLikesId}</if>
			<if test="screenPoetryContentId != null ">and screen_poetry_content_id = #{screenPoetryContentId}</if>
			<if test="likesNum != null ">and likes_num = #{likesNum}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
	 		t.screen_poetry_likes_id
	 		,t.screen_poetry_content_id
	 		,t.likes_num
	 		,t.create_time
	 		,t.update_time
	 		,t.is_delete
		from (
			 select a.* from screen_poetry_likes a
		 ) t

	</sql>

	<select id="getScreenPoetryLikesListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenPoetryLikesByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenPoetryLikesVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>

    <update id="addScreenPoetryLikesNum">
		update screen_poetry_likes
		set likes_num = likes_num + 1
		<where>
			screen_poetry_content_id = #{screenPoetryContentId}
			<if test="isDelete != null">and is_delete = #{isDelete}</if>
		</where>
	</update>
</mapper>