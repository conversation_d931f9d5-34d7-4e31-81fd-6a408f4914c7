<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenModuleLibraryMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenModuleLibrary" id="BaseResultMap">
        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
        <result property="moduleName" column="module_name"/>
        <result property="moduleGroupType" column="module_group_type"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
        <result property="presetType" column="preset_type"/>
        <result property="parentScreenModuleLibraryId" column="parent_screen_module_library_id"/>
        <result property="librarySort" column="library_sort"/>
        <result property="isPoster" column="is_poster"/>
        <!--        <result property="libraryPattern" column="library_pattern"/>-->
        <!--        <result property="posterSource" column="poster_source"/>-->
        <result property="librarySource" column="library_source"/>
        <result property="thirdId" column="thirdId"/>
    </resultMap>

    <select id="getScreenModuleLibraryListByCondition"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo">
        select a.*from screen_module_library a
        left join label_library_rel b on b.screen_module_library_id=a.screen_module_library_id
        left join label c on b.label_id=c.label_id
        <where>
            <if test="screenModuleLibraryId != null ">and a.screen_module_library_id = #{screenModuleLibraryId}</if>
            <if test="moduleName != null and moduleName != ''">and a.module_name = #{moduleName}</if>
            <if test="moduleGroupType != null ">and a.module_group_type = #{moduleGroupType}</if>
            <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
            <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
            <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
            <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
            <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
            <if test="presetType != null ">and a.preset_type = #{presetType}</if>
            <if test="parentScreenModuleLibraryId != null ">and a.parent_screen_module_library_id =
                #{parentScreenModuleLibraryId}
            </if>
            <if test="dictionaryDataOrganizationId != null ">and c.organization_id=#{dictionaryDataOrganizationId}</if>
            <if test="librarySort != null ">and a.library_sort=#{librarySort}</if>
            <if test="isPoster != null ">and a.is_poster=#{isPoster}</if>
            <!--            <if test="libraryPattern != null ">and a.library_pattern=#{libraryPattern}</if>-->
            <!--            <if test="posterSource != null ">and a.poster_source=#{posterSource}</if>-->
            <if test="librarySource != null">and a.library_source = #{librarySource}</if>
            <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
        </where>
        GROUP BY a.screen_module_library_id
        <if test="pageNo == -1 and orderBy != null and orderBy !=''">order by ${orderBy}</if>
    </select>


    <select id="getPosterList" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo">
        select a.* , (case when c.organization_id is null then 2 when c.organization_id=0 then 2 else 1 end) as isSchoolPoster
        ,group_concat(distinct c.label_name) as labelNameConcat
        from screen_module_library a
        join label_library_rel b on a.screen_module_library_id=b.screen_module_library_id and b.is_delete=0 and
        a.is_delete=0
        join label c on b.label_id=c.label_id and c.is_delete=0
        left join screen_module_library_media d on a.screen_module_library_id=d.screen_module_library_id and
        d.is_delete=0
        <where>
            <if test="isPoster != null ">and a.is_poster=#{isPoster}</if>
            <!--            <if test="libraryPattern != null ">and a.library_pattern=#{libraryPattern}</if>-->
            <!--            <if test="posterSource != null ">and a.poster_source=#{posterSource}</if>-->
            <if test="dictionaryDataOrganizationId != null">and c.organization_id=#{dictionaryDataOrganizationId}</if>
            <if test="dictionaryDataClassesId != null">and c.classes_id=#{dictionaryDataClassesId}</if>
            <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id in
                <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="notInScreenModuleLibraryIds != null and notInScreenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id not in
                <foreach collection="notInScreenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="devicePattern != null ">and d.device_pattern=#{devicePattern}</if>
            <if test="moduleName != null and moduleName != ''">and a.module_name like concat('%', #{moduleName}, '%')
            </if>
            <if test="librarySource != null">and a.library_source = #{librarySource}</if>
            <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
        </where>
        group by a.screen_module_library_id
        order by a.library_sort ASC,a.screen_module_library_id ASC
    </select>

    <select id="getPosterListSel" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo">
        select a.* , (case when c.organization_id is null then 2 when c.organization_id=0 then 2 else 1 end) as isSchoolPoster
        ,group_concat(distinct c.label_name) as labelNameConcat
        from screen_module_library a
        left join label_library_rel b on a.screen_module_library_id=b.screen_module_library_id and b.is_delete=0 and
        a.is_delete=0
        left join label c on b.label_id=c.label_id and c.is_delete=0
        left join screen_module_library_media d on a.screen_module_library_id=d.screen_module_library_id and
        d.is_delete=0
        <where>
            <if test="isPoster != null ">and a.is_poster=#{isPoster}</if>
            <!--            <if test="libraryPattern != null ">and a.library_pattern=#{libraryPattern}</if>-->
            <!--            <if test="posterSource != null ">and a.poster_source=#{posterSource}</if>-->
            <if test="dictionaryDataOrganizationId != null">and c.organization_id=#{dictionaryDataOrganizationId}</if>
            <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id in
                <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="notInScreenModuleLibraryIds != null and notInScreenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id not in
                <foreach collection="notInScreenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="devicePattern != null ">and d.device_pattern=#{devicePattern}</if>
            <if test="moduleName != null and moduleName != ''">and a.module_name like concat('%', #{moduleName}, '%')
            </if>
            <if test="librarySource != null">and a.library_source = #{librarySource}</if>
            <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
        </where>
        group by a.screen_module_library_id
        order by a.library_sort ASC,a.screen_module_library_id ASC
    </select>


    <select id="getNotConfigLabelScreenModuleLibrary"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryVo">
        select a.* from screen_module_library a
        left join (select b.* from label_library_rel b join label c on b.label_id = c.label_id and c.is_delete=0 where b.is_delete=0)t
            ON a.screen_module_library_id = t.screen_module_library_id and t.is_delete=0
        where a.is_delete=0 and t.id is null and a.is_poster=1 and a.parent_screen_module_library_id=1001
        <if test="moduleName != null and moduleName != ''">and a.module_name like concat('%', #{moduleName}, '%')
        </if>
        <if test="librarySource != null">and a.library_source = #{librarySource}</if>
        <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
        order by a.library_sort ASC,a.screen_module_library_id ASC
    </select>

    <select id="getConfigLabelScreenModuleLibraryStatistics"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryStatisticsVo">
        select count(distinct d.screen_module_library_media_id) as screenModuleLibraryMediaNumber
        from screen_module_library a
        join label_library_rel b on a.screen_module_library_id=b.screen_module_library_id and b.is_delete=0 and
        a.is_delete=0
        join label c on b.label_id=c.label_id and c.is_delete=0
        join screen_module_library_media d on a.screen_module_library_id=d.screen_module_library_id and
        d.is_delete=0
        <where>
            <if test="isPoster != null ">and a.is_poster=#{isPoster}</if>
            <if test="dictionaryDataOrganizationId != null">and c.organization_id=#{dictionaryDataOrganizationId}</if>
            <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id in
                <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="notInScreenModuleLibraryIds != null and notInScreenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id not in
                <foreach collection="notInScreenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="devicePattern != null ">and d.device_pattern=#{devicePattern}</if>
            <if test="moduleName != null and moduleName != ''">and a.module_name like concat('%', #{moduleName}, '%')
            </if>
            <if test="librarySource != null">and a.library_source = #{librarySource}</if>
            <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
        </where>
    </select>

    <select id="getNotConfigLabelScreenModuleLibraryStatistics"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryStatisticsVo">
        select count(distinct b.screen_module_library_media_id) as screenModuleLibraryMediaNumber
        from screen_module_library a
        join screen_module_library_media b on a.screen_module_library_id=b.screen_module_library_id and b.is_delete=0
        left join (select b.* from label_library_rel b join label c on b.label_id = c.label_id and c.is_delete=0 where b.is_delete=0)t
        ON. a.screen_module_library_id = t.screen_module_library_id and t.is_delete=0
        where a.is_delete=0 and t.id is null and a.is_poster=1 and a.parent_screen_module_library_id=1001
        <if test="moduleName != null and moduleName != ''">
            and a.module_name like concat('%', #{moduleName}, '%')
        </if>
        <if test="librarySource != null">and a.library_source = #{librarySource}</if>
        <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
    </select>

    <select id="getPosterSchoolNum"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo">
        select ifnull(count(distinct llr.screen_module_library_id),0) as posterNum,l.organization_id as organizationId
        from label l
        join label_library_rel llr on l.label_id = llr.label_id and llr.is_delete=0
        join screen_module_library sml on llr.screen_module_library_id = sml.screen_module_library_id and sml.is_delete=0
        where l.organization_id!= 0 and l.classes_id=0 and sml.is_poster = 1 and l.is_delete = 0
        <if test="organizationIds != null and organizationIds.size() >0">
            and l.organization_id in
            <foreach collection="organizationIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        group by l.organization_id
    </select>

    <select id="getPosterMediaNum" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenModuleLibraryNumVo">
        select count(distinct d.screen_module_library_media_id) as posterMediaNum, c.organization_id organizationId
        from screen_module_library a
        join label_library_rel b on a.screen_module_library_id=b.screen_module_library_id and b.is_delete=0 and
        a.is_delete=0
        join label c on b.label_id=c.label_id and c.is_delete=0
        join screen_module_library_media d on a.screen_module_library_id=d.screen_module_library_id and
        d.is_delete=0
        <where>
            <if test="isPoster != null ">and a.is_poster=#{isPoster}</if>
            <if test="dictionaryDataOrganizationId != null">and c.organization_id=#{dictionaryDataOrganizationId}</if>
            <if test="dictionaryDataClassesId != null">and c.classes_id = #{dictionaryDataClassesId}</if>
            <if test="screenModuleLibraryIds != null and screenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id in
                <foreach collection="screenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="notInScreenModuleLibraryIds != null and notInScreenModuleLibraryIds.size() > 0">
                and a.screen_module_library_id not in
                <foreach collection="notInScreenModuleLibraryIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="devicePattern != null ">and d.device_pattern=#{devicePattern}</if>
            <if test="moduleName != null and moduleName != ''">and a.module_name like concat('%', #{moduleName}, '%')
            </if>
            <if test="librarySource != null">and a.library_source = #{librarySource}</if>
            <if test="thirdId != null and thirdId != ''">and a.third_id = #{thirdId}</if>
            <if test="organizationIds != null and organizationIds.size() != 0">
                and c.organization_id in
                <foreach collection="organizationIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="labelIds != null and labelIds.size() != 0">
                and c.label_id in
                <foreach collection="labelIds" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by c.organization_id
    </select>
</mapper>