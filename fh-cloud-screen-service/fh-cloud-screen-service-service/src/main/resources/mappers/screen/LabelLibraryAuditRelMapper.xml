<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.LabelLibraryAuditRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.LabelLibraryAuditRelDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="screenModuleLibraryAuditId" column="screen_module_library_audit_id"/>
        <result property="labelId" column="label_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="screenModuleLibraryAuditId != null ">and screen_module_library_audit_id = #{screenModuleLibraryAuditId}</if>
			<if test="labelId != null ">and label_id = #{labelId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
			<if test="screenModuleLibraryAuditIds != null and screenModuleLibraryAuditIds.size()>0">
				and screen_module_library_audit_id in
				<foreach collection="screenModuleLibraryAuditIds" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
	</sql>

	<sql id="table_where">
		<where>
			<if test="id != null ">and id = #{id}</if>
			<if test="screenModuleLibraryAuditId != null ">and screen_module_library_audit_id = #{screenModuleLibraryAuditId}</if>
			<if test="labelId != null ">and label_id = #{labelId}</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
			t.id
	 		,t.screen_module_library_audit_id
	 		,t.label_id
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			select a.* from label_library_audit_rel a
			<include refid="table_where"></include>
		 ) t

	</sql>

	<select id="getLabelLibraryAuditRelListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getLabelLibraryAuditRelByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

    <select id="getLabelListByLibraryAuditId" resultType="com.fh.cloud.screen.service.label.entity.vo.LabelVo">
		select l.*
		from label l
		join label_library_audit_rel llar on l.label_id = llar.label_id
		<where>
			l.is_delete = 0
			and llar.is_delete = 0
			and llar.screen_module_library_audit_id = #{screenModuleLibraryAuditId}
		</where>
    </select>

    <select id="getLabelLibraryAuditRelListWithLibraryAudit"
            resultType="com.fh.cloud.screen.service.screen.entity.vo.LabelLibraryAuditRelVo">
        select llar.*
        from label_library_audit_rel llar
        join screen_module_library_audit smla
        	on llar.screen_module_library_audit_id = smla.screen_module_library_audit_id and smla.is_delete = 0
		<where>
			llar.is_delete = 0
			<if test="labelId != null">and llar.label_id = #{labelId}</if>
			<if test="auditType != null">and smla.audit_type = #{auditType}</if>
			<if test="auditTypes != null and auditTypes.size() != 0">
				and smla.audit_type in
				<foreach collection="auditTypes" item="item" index="index" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</where>
    </select>
</mapper>