<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSignaturePictureMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenSignaturePictureDto" id="BaseResultMap">
        <result property="screenSignaturePictureId" column="screen_signature_picture_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="screenSignaturePictureMediaUrl" column="screen_signature_picture_media_url"/>
        <result property="screenSignaturePictureMediaName" column="screen_signature_picture_media_name"/>
        <result property="screenSignaturePictureMediaNameOri" column="screen_signature_picture_media_name_ori"/>
        <result property="screenSignaturePictureMediaId" column="screen_signature_picture_media_id"/>
        <result property="screenSignaturePictureMediaMd5" column="screen_signature_picture_media_md5"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<sql id="common_where">
		<where>
			<if test="screenSignaturePictureId != null ">and screen_signature_picture_id = #{screenSignaturePictureId}</if>
			<if test="organizationId != null ">and organization_id = #{organizationId}</if>
			<if test="screenSignaturePictureMediaUrl != null and screenSignaturePictureMediaUrl != '' ">and screen_signature_picture_media_url like concat('%', #{screenSignaturePictureMediaUrl}, '%')</if>
			<if test="screenSignaturePictureMediaName != null and screenSignaturePictureMediaName != '' ">and screen_signature_picture_media_name like concat('%', #{screenSignaturePictureMediaName}, '%')</if>
			<if test="screenSignaturePictureMediaNameOri != null and screenSignaturePictureMediaNameOri != '' ">and screen_signature_picture_media_name_ori like concat('%', #{screenSignaturePictureMediaNameOri}, '%')</if>
			<if test="screenSignaturePictureMediaId != null and screenSignaturePictureMediaId != '' ">and screen_signature_picture_media_id like concat('%', #{screenSignaturePictureMediaId}, '%')</if>
			<if test="screenSignaturePictureMediaMd5 != null and screenSignaturePictureMediaMd5 != '' ">and screen_signature_picture_media_md5 like concat('%', #{screenSignaturePictureMediaMd5}, '%')</if>
			<if test="isDelete != null ">and is_delete = #{isDelete}</if>
		</where>
	</sql>

	<sql id="common_select">
		select
<!-- 如果主键不是id，这里需要修改 -->
	 		t.screen_signature_picture_id
	 		,t.organization_id
	 		,t.screen_signature_picture_media_url
	 		,t.screen_signature_picture_media_name
	 		,t.screen_signature_picture_media_name_ori
	 		,t.screen_signature_picture_media_id
	 		,t.screen_signature_picture_media_md5
	 		,t.create_time
	 		,t.create_by
	 		,t.update_time
	 		,t.update_by
	 		,t.is_delete
		from (
			 select a.* from screen_signature_picture a
		 ) t

	</sql>

	<select id="getScreenSignaturePictureListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
	</select>

	<select id="getScreenSignaturePictureByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSignaturePictureVo">
		<include refid="common_select"></include>
		<include refid="common_where"></include>
		limit 1
	</select>
</mapper>