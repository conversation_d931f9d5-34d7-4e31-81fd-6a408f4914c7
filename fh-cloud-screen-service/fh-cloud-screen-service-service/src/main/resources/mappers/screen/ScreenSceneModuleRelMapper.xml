<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fh.cloud.screen.service.screen.mapper.ScreenSceneModuleRelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.fh.cloud.screen.service.screen.entity.dto.ScreenSceneModuleRel" id="BaseResultMap">
	        <result property="id" column="id"/>
	        <result property="screenSceneId" column="screen_scene_id"/>
	        <result property="screenModuleDataId" column="screen_module_data_id"/>
	        <result property="screenModuleLibraryId" column="screen_module_library_id"/>
	        <result property="createTime" column="create_time"/>
	        <result property="createBy" column="create_by"/>
	        <result property="updateTime" column="update_time"/>
	        <result property="updateBy" column="update_by"/>
	        <result property="isDelete" column="is_delete"/>
	        <result property="screenModuleLibrarySelIds" column="screen_module_library_sel_ids"/>
	    </resultMap>

	<select id="getScreenSceneModuleRelListByCondition" resultType="com.fh.cloud.screen.service.screen.entity.vo.ScreenSceneModuleRelVo">
		select a.* from screen_scene_module_rel a
		join screen_module_data smd on a.screen_module_data_id = smd.screen_module_data_id and smd.is_delete=0
		join screen_scene ss on a.screen_scene_id = ss.screen_scene_id and ss.is_delete=0
	    <where>
	    				    <if test="id != null ">and a.id = #{id}</if>
						    <if test="screenSceneId != null ">and a.screen_scene_id = #{screenSceneId}</if>
						    <if test="screenModuleDataId != null ">and a.screen_module_data_id = #{screenModuleDataId}</if>
						    <if test="screenModuleLibraryId != null ">and a.screen_module_library_id = #{screenModuleLibraryId}</if>
						    <if test="createTime != null and createTime != ''">and a.create_time = #{createTime}</if>
						    <if test="createBy != null and createBy != ''">and a.create_by = #{createBy}</if>
						    <if test="updateTime != null and updateTime != ''">and a.update_time = #{updateTime}</if>
						    <if test="updateBy != null and updateBy != ''">and a.update_by = #{updateBy}</if>
						    <if test="isDelete != null ">and a.is_delete = #{isDelete}</if>
						    <if test="screenModuleLibrarySelIds != null ">and a.screen_module_library_sel_ids = #{screenModuleLibrarySelIds}</if>
				    </where>
	</select>
</mapper>