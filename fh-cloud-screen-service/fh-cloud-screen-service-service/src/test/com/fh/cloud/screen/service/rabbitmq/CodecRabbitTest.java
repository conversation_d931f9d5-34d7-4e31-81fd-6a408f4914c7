package com.fh.cloud.screen.service.rabbitmq;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.fh.cp.codec.entity.dto.CodecProcessDto;
import com.fh.cp.codec.enums.CodecBizType;
import com.fh.cp.codec.enums.CodecCmdType;
import com.fh.cp.codec.enums.IllustrateMediaType;
import com.fh.cp.codec.rabbitmq.constant.CodecRabbitConstant;
import com.google.common.collect.Lists;

import cn.hutool.core.lang.UUID;

/**
 * rabbitmq的单元测试类
 *
 * <AUTHOR>
 * @date 2023/6/7 15:02
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
class CodecRabbitTest {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 参数请参考本单元测试类的方法
     * @throws Exception
     */
    @Test
    void testAddCodecProducer() throws Exception {
        CodecProcessDto codecProcessDto = new CodecProcessDto();
        codecProcessDto.setId(2L);
        codecProcessDto.setUuid(UUID.fastUUID().toString());
        codecProcessDto.setFilePath("http://127.0.0.1:8000/SampleVideo_1280x720_10mb.flv");
        codecProcessDto.setNotInCodecTypes(Lists.newArrayList("h264", "mp3"));
        codecProcessDto.setMediaType(IllustrateMediaType.VIDEO.getCode());
        codecProcessDto.setBizType(CodecBizType.SCREEN_CONTENT_DETAIL.getValue());
        codecProcessDto.setCmdType(CodecCmdType.CODEC_MEDIA.getValue());
        this.rabbitTemplate.convertAndSend(CodecRabbitConstant.CODEC_PRODUCER_ADD_QUEUE, codecProcessDto);
        assertTrue(true);
    }
}
