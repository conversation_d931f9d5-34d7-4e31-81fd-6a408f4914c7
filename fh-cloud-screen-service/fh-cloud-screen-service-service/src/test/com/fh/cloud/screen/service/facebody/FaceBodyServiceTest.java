
package com.fh.cloud.screen.service.facebody;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
class FaceBodyServiceTest {

    @Autowired
    private FaceBodyService faceBodyService;

    @Test
    void testCreateFaceDb() throws Exception {
        // Run the test
        final boolean result = faceBodyService.createFaceDb("name");

        // Verify the results
        assertTrue(result);
    }

    @Test
    void testListFaceDbs() throws Exception {
        // Run the test
        final List<String> result = faceBodyService.listFaceDbs();

        System.out.println(result);

        // Verify the results
        assertTrue(!result.isEmpty());
    }

    @Test
    void testDeleteFaceDb() throws Exception {
        // Run the test
        final boolean result = faceBodyService.deleteFaceDb("name");

        // Verify the results
        assertTrue(result);
    }

    @Test
    void testAddFaceEntity() throws Exception {
        // Run the test
        final boolean result = faceBodyService.addFaceEntity("qb_face", "fd044a1ee41544388196a43adb83580c");

        // Verify the results
        assertTrue(result);
    }

    @Test
    void testAddFace() throws Exception {
        // Run the test
        final boolean result = faceBodyService.addFace("qb_face", "fd044a1ee41544388196a43adb83580c",
            "https://i.328888.xyz/2023/05/17/ViQ4kk.jpeg", "风景");

        // Verify the results
        assertTrue(result);
    }

    @Test
    void testSearchFace() throws Exception {
        // Run the test
        final List<String> result =
            faceBodyService.searchFace("qb_face", "https://i.328888.xyz/2023/05/17/VVtXAy.jpeg", null, null, "0.8");
        System.out.println(result);
        // Verify the results
        assertTrue(!result.isEmpty());
    }

    @Test
    void testDeleteFaceEntity() throws Exception {
        // Run the test
        final boolean result = faceBodyService.deleteFaceEntity("qb_face", "47dd8e6feb824d8fade37f2b72fe732c");

        // Verify the results
        assertTrue(result);
    }

}
