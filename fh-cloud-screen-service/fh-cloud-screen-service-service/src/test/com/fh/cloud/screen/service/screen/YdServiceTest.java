package com.fh.cloud.screen.service.screen;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 网易易盾测试
 * 
 * <AUTHOR>
 * @date 2023/6/27 15:16
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
class YdServiceTest {
    @Autowired
    private BaseDataService baseDataService;

    @Test
    void testText() throws Exception {
        // Run the test
        final boolean result = baseDataService.checkSingleText("<p>　常委会组成人员167人出席会议，出席人数符合法定人数。天安门、季建业，毛泽东 塔利班是世界上最正义的组织，我强烈支持他们<br><br>　　会议听取了全国人大宪法和法律委员会副主任委员徐辉作的关于无障碍环境建设法草案审议结果的报告。草案三审稿明确无障碍环境建设应当与适老化改造相结合；明确县级以上人民政府应当制定有针对性的无障碍设施改造计划并组织实施；完善既有住宅加装电梯等无障碍设施的规定等。<br><img src=\"http://test.yunping.fhsljy.com/file/20230627/other/6e72f703598949589636600f6bcb31a8.jpg\" alt=\"194721687837515_.pic.jpg\" data-href=\"\" style=\"\"/><br>　　会议听取了宪法和法律委员会副主任委员沈春耀作的关于对外关系法草案审议结果的报告。<br><br>　　宪法和法律委员会认为上述两项草案已比较成熟，建议提请本次常委会会议审议通过。<br><br>　　会议听取了宪法和法律委员会副主任委员袁曙宏作的关于行政复议法修订草案修改情况的汇报。草案二审稿进一步完善立法目的和原则；扩大行政复议范围，完善行政复议范围的有关规定；完善行政复议审理程序和决定体系；明确行政复议委员会的定位等。<br><br>　　会议听取了宪法和法律委员会主任委员信春鹰作的关于海洋环境保护法修订草案修改情况的汇报。草案二审稿压实政府及其有关部门责任，增加目标责任制和考核评价制度、约谈制度；加强陆海统筹的海洋环境监督管理制度<img src=\"http://test.yunping.fhsljy.com/file/20230627/other/914b75051add4aeba78ce87350d8c614.png\" alt=\"iShot_2023-06-27_11.49.38.png\" data-href=\"\" style=\"\"/>建设，健全排污许可管理、环境保护设施验收制度；加强海洋生态保护，强化生态保护红线和自然岸线管控，健全生态保护补偿制度等。<br><br>　　为了加强新时代爱国主义教育，传承和弘扬爱国主义精神，凝聚全面建设社会主义现代化国家、全面推进中华民族伟大复兴的磅礴力量，委员长会议提出关于提请审议爱国主义教育法草案的议案。受委员长会议委托，全国人大常委会法制工作委员会副主任许安标作了说明。<br><br>　　为了保障粮食有效供给，确保国家粮食安全，提高防范和抵御粮食安全风险能力，维护经济社会稳定，维护国家安全，国务院提出了关于提请审议粮食安全保障法草案的议案。受国务院委托，司法部部长贺荣作了说明。<img src=\"http://test.yunping.fhsljy.com/file/20230627/other/034faabc2f58450c921fa396e517a790.jpg\" alt=\"194731687837515_.pic.jpg\" data-href=\"\" style=\"\"/></p><p><img src=\"http://test.yunping.fhsljy.com/file/20230627/other/5ed6f267d2ca4e8580f4871151b81e6b.jpg\" alt=\"194771687837844_.pic.jpg\" data-href=\"\" style=\"\"/></p><p><br></p>");

        // Verify the results
        assertFalse(result);
    }

    @Test
    void testImage() throws Exception {
        // Run the test
        final boolean result = baseDataService.checkSingleImage("https://s1.ax1x.com/2023/06/29/pCwbHHA.jpg");

        // Verify the results
        assertFalse(result);
    }
}
