package com.fh.cloud.screen.service.tts;

import com.fh.cloud.screen.service.tts.service.TtsService;
import com.light.base.attachment.entity.vo.AttachmentVo;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 语音测试文件
 * 
 * <AUTHOR>
 * @date 2024/3/14 14:59
 */
@EnableConfigurationProperties
@RunWith(SpringRunner.class)
@SpringBootTest
public class TtsServiceTest {

    @Resource
    private TtsService ttsService;

    @Test
    void testCreateFaceDb() throws Exception {
        // Run the test
        AttachmentVo attachmentVo = ttsService.saveOnlineFile(
            "http:\\/\\/asr-tmp-resource.oss-cn-shanghai.aliyuncs.com\\/text-to-voice\\/7566b67d5afaf2d581ced7daa2713f5c.mp3?OSSAccessKeyId=LTAItIQd1kGPPB9P&Expires=1710748659&Signature=ytpLQ%2Be3XRkUR01%2FJ3YqXlAxiI0%3D",
            "62e4901cc19d44758361dca8b644ca58.mp3");

        // Verify the results
        assertTrue(attachmentVo != null);
    }
}
