package com.fh.cloud.screen.app.jwt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/5/9 5:30 下午 @description：
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = JwtProperties.prefix)
public class JwtProperties {

    public static final String prefix = "spring.screen.auth.jwt";

    public static String secret = "zjKkye4PN59B2wriTjtVCo3BOYoD1B";

    /**
     * 默认 7天
     */
    public static long expire = 7 * 24 * 60 * 60 * 1000;

    public void setExpire(long expire) {
        JwtProperties.expire = expire;
    }

    public void setSecret(String secret) {
        JwtProperties.secret = secret;
    }
}
