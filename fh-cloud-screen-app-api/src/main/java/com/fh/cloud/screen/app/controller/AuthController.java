package com.fh.cloud.screen.app.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.app.enums.SpaceGroupUseTypeEnums;
import com.fh.cloud.screen.app.enums.exception.AuthExceptionEnums;
import com.fh.cloud.screen.app.jwt.config.JwtProperties;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.app.jwt.util.JwtUtil;
import com.fh.cloud.screen.app.model.AuthModel;
import com.fh.cloud.screen.service.campus.service.ClassesScreenApiService;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.ShowDeviceApiService;
import com.fh.cloud.screen.service.space.entity.vo.SpaceDeviceRelVo;
import com.fh.cloud.screen.service.space.entity.vo.SpaceInfoVo;
import com.fh.cloud.screen.service.space.service.ClassesInfoApiService;
import com.fh.cloud.screen.service.space.service.SpaceDeviceApiService;
import com.fh.cloud.screen.service.space.service.SpaceInfoApiService;
import com.google.common.collect.Maps;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.exception.UnifiedException;
import com.light.user.clazz.entity.vo.ClazzVo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/5/7 9:49 上午 @description：
 */
@RestController
@Api(tags = "服务认证")
@RequestMapping("auth")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class AuthController {

    private final ShowDeviceApiService showDeviceApiService;

    private final SpaceDeviceApiService spaceDeviceApiService;

    private final SpaceInfoApiService spaceInfoApiService;

    private final ClassesInfoApiService classesInfoApiService;

    private final ClassesScreenApiService classesScreenApiService;

    /**
     * 生成云屏key。用于后续的云屏接口
     * 
     * @param model
     * @return
     */
    @ApiOperation("服务认证")
    @PostMapping
    public AjaxResult auth(@RequestBody AuthModel model) {

        // 获取设备信息
        final String deviceNumber = model.getDeviceNumber();
        AjaxResult<ShowDeviceVo> result = this.showDeviceApiService.getByDeviceNumber(deviceNumber);
        final ShowDeviceVo showDeviceVo = result.getData();
        if (result.isFail() || showDeviceVo == null) {
            return AjaxResult.fail("设备不存在");
        }

        // 获取设备空间关系数据
        AjaxResult<SpaceDeviceRelVo> spaceDeviceRelVoResult =
            this.spaceDeviceApiService.getByDeviceId(showDeviceVo.getShowDeviceId());
        final SpaceDeviceRelVo spaceDeviceRelVo = spaceDeviceRelVoResult.getData();
        if (spaceDeviceRelVoResult == null || spaceDeviceRelVo == null) {
            return AjaxResult.fail();
        }

        // 地点类型
        final Integer spaceGroupUseType = spaceDeviceRelVo.getSpaceGroupUseType();
        // 地点ID
        final Long spaceInfoId = spaceDeviceRelVo.getSpaceInfoId();



        // 获取 校区ID
        Long campusId = this.buildCampusId(spaceGroupUseType, spaceInfoId);
        Long organizationId = showDeviceVo.getOrganizationId();

        JwtModel jwtModel = new JwtModel();
        jwtModel.setDeviceNumber(deviceNumber);
        jwtModel.setCampusId(campusId);
        jwtModel.setOrganizationId(organizationId);
        jwtModel.setSpaceGroupUseType(spaceGroupUseType);
        jwtModel.setSpaceInfoId(spaceInfoId);

        // 生成token
        final String jwtToken = JwtUtil.generateToken(jwtModel);
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("token", jwtToken);
        resultMap.put("expire", JwtProperties.expire);

        return AjaxResult.success(resultMap);
    }

    /**
     * 处理获取校区
     * 
     * @param spaceGroupUseType
     * @param spaceInfoId
     * @return
     */
    private Long buildCampusId(Integer spaceGroupUseType, Long spaceInfoId) {

        // 根据地点获取校区
        if (spaceGroupUseType == SpaceGroupUseTypeEnums.NOT_EXECUTIVE.getVal()) {
            AjaxResult<SpaceInfoVo> spaceInfoVoAjaxResult = this.spaceInfoApiService.getById(spaceInfoId);
            if (spaceInfoVoAjaxResult.isFail()) {
                throw new UnifiedException("获取Token失败");
            }
            final SpaceInfoVo data = spaceInfoVoAjaxResult.getData();
            if(data == null || data.getIsDelete().equals(StatusEnum.ISDELETE.getCode())){
                throw new UnifiedException(AuthExceptionEnums.SPACE_DEL.getCode(),AuthExceptionEnums.SPACE_DEL.getMessage());
            }
            return data.getCampusId();
        }

        //
        if (spaceGroupUseType == SpaceGroupUseTypeEnums.EXECUTIVE.getVal()) {
            AjaxResult<ClazzVo> result = this.classesScreenApiService.getById(spaceInfoId);
            if (result.isFail()) {
                throw new UnifiedException("获取Token失败");
            }
            final ClazzVo data = result.getData();
            if(data == null){
                throw new UnifiedException(AuthExceptionEnums.SPACE_DEL.getCode(),AuthExceptionEnums.SPACE_DEL.getMessage());
            }
            return data.getCampusId();
        }
        return null;
    }
}
