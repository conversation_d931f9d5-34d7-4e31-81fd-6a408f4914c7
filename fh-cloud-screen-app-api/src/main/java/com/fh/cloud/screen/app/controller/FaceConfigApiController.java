package com.fh.cloud.screen.app.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.face.api.FaceConfigApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 人脸管理配置接口
 *
 * <AUTHOR>
 * @date 2022/11/18 16:15
 */
@RestController
@RequestMapping("/face/config")
@Api(value = "", tags = "人脸配置接口")
public class FaceConfigApiController {
    @Resource
    private FaceConfigApi faceConfigApi;

    /**
     * 查看阈值数据等参数，参数包含organizationId
     *
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @AccessTokenAuth
    @GetMapping("/detail")
    @ApiOperation(value = "查看阈值数据", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getThreshold() {
        JwtModel jwtModel = JwtContextHandler.get();
        Long organizationId = jwtModel.getOrganizationId();
        return faceConfigApi.getDetailByOrganizationId(organizationId);
    }
}
