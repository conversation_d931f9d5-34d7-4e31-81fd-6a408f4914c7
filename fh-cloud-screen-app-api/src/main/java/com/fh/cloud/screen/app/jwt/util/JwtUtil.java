package com.fh.cloud.screen.app.jwt.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.SecureUtil;
import com.fh.cloud.screen.app.jwt.config.JwtProperties;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.google.common.primitives.Bytes;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.SecretKey;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.function.Function;

public class JwtUtil {

    private static String SECRET_KEY = JwtProperties.secret;

    public static long expiration = JwtProperties.expire;

    /**
     * 获取用户名
     *
     * @param token
     * @return
     */
    public static String extractDeviceNumber(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    /**
     * 获取 时长
     *
     * @param token
     * @return
     */
    public static Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    /**
     * 获取数据信息
     *
     * @param token
     * @param claimsResolver
     * @param <T>
     * @return
     */
    public static <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 获取 claims
     *
     * @param token
     * @return
     */
    public static Claims extractAllClaims(String token) {
        final byte[] secrets = Bytes.ensureCapacity(Base64.getDecoder().decode(SECRET_KEY), 128, 0);
        final SecretKey secretKey = SecureUtil.generateKey(SignatureAlgorithm.HS256.getValue(), secrets);
        return Jwts.parser().setSigningKey(secretKey).parseClaimsJws(token).getBody();
    }

    /**
     * 是否过期
     *
     * @param token
     * @return
     */
    public static Boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    /**
     * 生成token
     *
     * @param jwtModel
     * @return
     */
    public static String generateToken(JwtModel jwtModel) {
        Map<String, Object> claims = BeanUtil.beanToMap(jwtModel);
        return createToken(claims, jwtModel.getDeviceNumber());
    }

    private static String createToken(Map<String, Object> claims, String subject) {
        final byte[] bytes = Bytes.ensureCapacity(Base64.getDecoder().decode(SECRET_KEY), 128, 0);
        final SecretKey secretKey = SecureUtil.generateKey(SignatureAlgorithm.HS256.getValue(), bytes);
        return Jwts.builder().setClaims(claims).setSubject(subject).setIssuedAt(new Date(System.currentTimeMillis()))
            .setExpiration(new Date(System.currentTimeMillis() + (expiration * 1000)))
            .signWith(SignatureAlgorithm.HS256, secretKey).compact();
    }

    /**
     * 验证token合法性
     *
     * @param token
     * @return
     */
    public static Boolean validateToken(String token) {
        return !isTokenExpired(token);
    }

    /**
     * 测试获取jwtToken
     *
     * @param args
     */
    public static void main(String[] args) {
        JwtModel jwtModel = new JwtModel();
        jwtModel.setDeviceNumber("d123dfd344");
        jwtModel.setCampusId(69L);
        jwtModel.setOrganizationId(321371000156L);
        jwtModel.setSpaceGroupUseType(2);
        jwtModel.setSpaceInfoId(43L);
        System.out.println(generateToken(jwtModel));
    }
}