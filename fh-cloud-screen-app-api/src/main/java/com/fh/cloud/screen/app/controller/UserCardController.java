package com.fh.cloud.screen.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.card.api.UserCardApi;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardVo;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 用户卡表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-26 17:17:10
 */
@RestController
@Validated
@Api(value = "", tags = "卡管理")
@RequestMapping("/userCard")
public class UserCardController {

    @Resource
    private UserCardApi userCardApi;

    /**
     * 学生卡列表
     *
     * @return student card list
     */
    @AccessTokenAuth
    @ApiOperation(value = "查询学生卡列表", httpMethod = "POST")
    @PostMapping("/getStudentCardList")
    public AjaxResult getStudentCardList() {
        JwtModel jwtModel = JwtContextHandler.get();
        UserCardListConditionBo conditionBo = new UserCardListConditionBo();
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setOrganizationId(jwtModel.getOrganizationId());
        conditionBo.setClassesId(jwtModel.getSpaceInfoId());
        AjaxResult studentCardList = userCardApi.getStudentCardList(conditionBo);
        return studentCardList;
    }

    /**
     * 教师卡列表
     *
     * @return teacher card list
     */
    @AccessTokenAuth
    @ApiOperation(value = "查询教师卡列表", httpMethod = "POST")
    @PostMapping("/getTeacherCardList")
    public AjaxResult getTeacherCardList() {
        JwtModel jwtModel = JwtContextHandler.get();
        UserCardListConditionBo conditionBo = new UserCardListConditionBo();
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setOrganizationId(jwtModel.getOrganizationId());
        AjaxResult teacherCardList = userCardApi.getTeacherCardList(conditionBo);
        if (teacherCardList.isSuccess() && teacherCardList.getData() != null) {
            Map<String, Object> dataMap = (Map<String, Object>)teacherCardList.getData();
            List<TeacherCardVo> list =
                JSONObject.parseArray(JSONObject.toJSONString(dataMap.get("list")), TeacherCardVo.class);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(teacherCardVo -> teacherCardVo.setPhone(null));
                dataMap.put("list", list);
                teacherCardList.setData(dataMap);
            }
        }
        return teacherCardList;
    }

}
