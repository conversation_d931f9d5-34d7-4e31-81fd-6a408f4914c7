package com.fh.cloud.screen.app.controller;

import cn.hutool.crypto.SecureUtil;
import com.fh.cloud.screen.app.constant.RedisKeyConstants;
import com.fh.cloud.screen.app.enums.ScopeTypeEnum;
import com.fh.cloud.screen.app.enums.SpaceGroupUseTypeEnums;
import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.screen.api.ScreenBusinessApi;
import com.fh.cloud.screen.service.screen.api.ScreenContentApi;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryMediaApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryMediaBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.redis.component.RedisComponent;
import com.light.redis.enums.RedisKeyEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 云屏首页controller
 *
 * <AUTHOR>
 * @date 2022/6/6 15:42
 */
@RestController
@Api(tags = "云屏首页")
@RequestMapping("/screen/index")
public class ScreenIndexController {

    @Resource
    private ScreenBusinessApi screenBusinessApi;
    @Resource
    private ScreenContentApi screenContentApi;
    @Resource
    private ScreenModuleLibraryMediaApi screenModuleLibraryMediaApi;
    /**
     * 云屏首页缓存开关开启标识：默认关闭，需要nacos配置才是开启.1开，0关
     */
    @Value("${screen.index.cache.enable:0}")
    private String screenIndexCacheEnable;

    @Value("${file.h5.zip.path:http://yunping.fhsljy.com/h5/h5.zip}")
    private String H5_ZIP_PATH;
    @Resource
    private RedisComponent redisComponent;

    /**
     * 云屏首页数据-不缓存，需要按模块优化，和云屏一起优化
     *
     * @return the class info list
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     */
    @AccessTokenAuth
    @PostMapping("")
    @ApiOperation(value = "云屏首页数据", httpMethod = "POST")
    public AjaxResult index() {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        return screenBusinessApi.screenIndex(screenBusinessBo);
    }

    /**
     * 云屏菜单数据-缓存策略细化
     *
     * @return the class info list
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     */
    @AccessTokenAuth
    @PostMapping("/menu")
    @ApiOperation(value = "云屏菜单数据", httpMethod = "POST")
    public AjaxResult menu(@RequestBody ScreenContentListConditionBo condition) {
        JwtModel jwtModel = JwtContextHandler.get();
        condition.setOrganizationId(jwtModel.getOrganizationId());
        condition.setCampusId(jwtModel.getCampusId());
        // 无论校级班级，校区id必传，班级判断是否非行政
        if (null != condition.getScopeType() && ScopeTypeEnum.BJ.getValue() == condition.getScopeType()) {
            // 行政教室（普通班级）查看班级
            if (SpaceGroupUseTypeEnums.EXECUTIVE.getVal() == jwtModel.getSpaceGroupUseType()) {
                condition.setClassesId(jwtModel.getSpaceInfoId());
            } else {
                // 非行政班级查看为null
                Map<String, Object> map = new HashMap<>();
                map.put("list", null);
                return AjaxResult.success(map);
            }
        }
        if (SystemConstants.YES.equals(screenIndexCacheEnable)) {
            return screenContentApi.getMenuByConditionWithCache(condition);
        }
        return screenContentApi.getMenuByCondition(condition);
    }

    /**
     * 组织数据，缓存4h
     *
     * @return the class info list
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     */
    @AccessTokenAuth
    @PostMapping("/org-info")
    @ApiOperation(value = "组织数据", httpMethod = "POST")
    public AjaxResult orgInfo() {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        if (SystemConstants.YES.equals(screenIndexCacheEnable)) {
            return screenBusinessApi.orgInfoWithCache(screenBusinessBo);
        }
        return screenBusinessApi.orgInfo(screenBusinessBo);
    }

    /**
     * 班级数据，缓存4h
     *
     * @return the class info list
     * <AUTHOR>
     * @date 2022 -04-26 17:17:10
     */
    @AccessTokenAuth
    @PostMapping("/clazz-info")
    @ApiOperation(value = "班级数据", httpMethod = "POST")
    public AjaxResult clazzInfo() {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        if (SystemConstants.YES.equals(screenIndexCacheEnable)) {
            return screenBusinessApi.clazzInfoWithCache(screenBusinessBo);
        }
        return screenBusinessApi.clazzInfo(screenBusinessBo);
    }

    /**
     * 查询云屏模块库媒体资源列表
     *
     * <AUTHOR>
     * @date 2022/6/29 15:25
     */
    @AccessTokenAuth
    @PostMapping("/screen-module-library-media/list")
    @ApiOperation(value = "查询云屏模块库媒体资源列表", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryListByCondition(@RequestBody ScreenModuleLibraryMediaBo condition) {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        condition.setDeviceNumber(screenBusinessBo.getDeviceNumber());
        condition.setWithPushLibraryProcess(true);
        return screenModuleLibraryMediaApi.getScreenModuleLibraryListByCondition(condition);
    }

    /**
     * 查询云屏模块库媒体资源列表-滚动海报模块查询海报使用
     *
     * <AUTHOR>
     * @date 2023/12/04 14:49
     */
    @AccessTokenAuth
    @PostMapping("/screen-module-library-media/list-scroll")
    @ApiOperation(value = "查询云屏模块库媒体资源列表-滚动海报模块查询海报使用", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryScrollListByCondition(@RequestBody ScreenModuleLibraryMediaBo condition) {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        condition.setDeviceNumber(screenBusinessBo.getDeviceNumber());
        return screenModuleLibraryMediaApi.getScreenModuleLibraryListByCondition(condition);
    }

    /**
     * 查询云屏模块库媒体资源列表-查询没有场景时候的默认海报，只需要传devicePattern
     *
     * @param condition the condition
     * @return the screen module library list by condition
     * <AUTHOR>
     * @date 2023 -03-27 17:16:07
     */
    @AccessTokenAuth
    @PostMapping("/screen-module-library-media/list-default")
    @ApiOperation(value = "查询云屏模块库媒体资源列表", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryListByConditionDefault(@RequestBody ScreenModuleLibraryMediaBo condition) {
        JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        condition.setDeviceNumber(screenBusinessBo.getDeviceNumber());
        if (condition.getDevicePattern() == null) {
            return AjaxResult.fail("参数错误");
        }
        return screenModuleLibraryMediaApi.getScreenModuleLibraryListByConditionDefault(condition);
    }

    /**
     * 获取H5版本
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/15 17:23
     */
    @GetMapping("/front-info")
    @ApiOperation(value = "获取H5版本", httpMethod = "GET")
    AjaxResult getH5Info() {
        Map<String, Object> map = new HashMap<>();
        String h5Version = redisComponent.hgetObjectValue(RedisKeyEnum.CONFIG_KEY.getValue(),
            RedisKeyConstants.H5_VERSION, SystemConstants.CONFIG_VALUE);
        String md5 = redisComponent.hgetObjectValue(RedisKeyEnum.CONFIG_KEY.getValue(), RedisKeyConstants.H5_VERSION,
            RedisKeyConstants.H5_MD5);
        map.put("h5ZipPath", H5_ZIP_PATH);
        map.put("h5Version", h5Version);
        map.put("md5", md5);
        return AjaxResult.success(map);
    }

}
