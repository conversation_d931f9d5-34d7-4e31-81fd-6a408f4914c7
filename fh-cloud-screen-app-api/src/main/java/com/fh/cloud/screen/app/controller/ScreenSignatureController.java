package com.fh.cloud.screen.app.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.screen.api.ScreenSignatureMessageApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureMessageConditionBo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenSignatureContentApi;
import com.fh.cloud.screen.service.screen.api.ScreenSignaturePictureApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignatureContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSignaturePictureConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 电子签名controller
 *
 * <AUTHOR>
 * @date 2023-07-12 10:17
 */
@RestController
@RequestMapping("/screen/signature")
@Api(tags = "电子签名")
public class ScreenSignatureController {
    @Resource
    private ScreenSignatureContentApi screenSignatureContentApi;
    @Resource
    private ScreenSignaturePictureApi screenSignaturePictureApi;
    @Resource
    private ScreenSignatureMessageApi screenSignatureMessageApi;

    @PostMapping("/add")
    @ApiOperation(value = "新增电子签名", notes = "新增电子签名")
    @AccessTokenAuth
    public AjaxResult addScreenSignatureContent(@RequestBody ScreenSignatureContentBo screenSignatureContentBo) {
        return screenSignatureContentApi.addScreenSignatureContent(screenSignatureContentBo);
    }

    @PostMapping("/page/list")
    @ApiOperation(value = "获取电子签名分页列表", notes = "获取电子签名分页列表")
    @AccessTokenAuth
    public AjaxResult
        getScreenSignatureContentPageListByCondition(@RequestBody ScreenSignatureContentConditionBo conditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        conditionBo.setOrganizationId(jwtModel.getOrganizationId());
        return screenSignatureContentApi.getScreenSignatureContentPageListByCondition(conditionBo);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取电子签名列表", notes = "获取电子签名列表")
    @AccessTokenAuth
    public AjaxResult
        getScreenSignatureContentListByCondition(@RequestBody ScreenSignatureContentConditionBo contentConditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        contentConditionBo.setOrganizationId(jwtModel.getOrganizationId());
        return screenSignatureContentApi.getScreenSignatureContentListByCondition(contentConditionBo);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "获取电子签名详情", notes = "获取电子签名详情")
    public AjaxResult getScreenSignatureContentDetail(@RequestParam("id") Long id) {
        return screenSignatureContentApi.getDetail(id);
    }

    @GetMapping("/picture/list")
    @ApiOperation(value = "获取电子签名图片列表", notes = "获取电子签名图片列表")
    public AjaxResult getScreenSignaturePictureList(@RequestParam("organizationId") Long organizationId) {
        ScreenSignaturePictureConditionBo conditionBo = new ScreenSignaturePictureConditionBo();
        conditionBo.setOrganizationId(organizationId);
        return screenSignaturePictureApi.getScreenSignaturePictureListByCondition(conditionBo);
    }

    @GetMapping("/message/list")
    @ApiOperation(value = "获取电子签名寄语列表", notes = "获取电子签名寄语列表")
    public AjaxResult getScreenSignatureMessageList(@RequestParam("organizationId") Long organizationId) {
        ScreenSignatureMessageConditionBo conditionBo = new ScreenSignatureMessageConditionBo();
        conditionBo.setOrganizationId(organizationId);
        return screenSignatureMessageApi.getScreenSignatureMessageListByCondition(conditionBo);
    }

}
