package com.fh.cloud.screen.app.utils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/6/24 17:23
 */
public class StringKit {
    /**
     * Remove repeat string. "张三，张三，李四" -> "张三，李四"
     *
     * @param value the value
     * @param split the split
     * @return string
     * <AUTHOR>
     * @date 2016 -11-10 13:46:57
     */
    public static String removeRepeat(String value, String split) {
        if (StringUtils.isBlank(value)) {
            return "";
        }
        String[] results = value.split(split);
        if (results == null || results.length <= 0) {
            return "";
        }
        List<String> resultList = new ArrayList<String>(Arrays.asList(results));
        ListKit.removeDuplicate(resultList);
        return StringUtils.join(resultList, split);
    }

    /**
     * 统计给定字符串中字符串出现的次数
     *
     * @param value the value
     * @param split the split
     * @return map
     * <AUTHOR>
     * @date 2016 -11-10 16:34:54
     */
    public static Map<String, String> countRepeat(String value, String split) {
        Map<String, String> map = new HashMap<String, String>();
        if (StringUtils.isBlank(value)) {
            return map;
        }
        String[] results = value.split(split);
        if (results == null || results.length <= 0) {
            return map;
        }
        List<String> resultList = new ArrayList<String>(Arrays.asList(results));
        for (String str : resultList) {
            if (StringUtils.isBlank(map.get(str))) {
                Integer count = 0;
                map.put(str, String.valueOf(count + 1));
            } else {
                map.put(str, String.valueOf(Integer.valueOf(map.get(str)) + 1));
            }
        }
        return map;
    }

    /**
     * 在value的集合列表中移除child: "张三，张三，李四" -> "李四"
     *
     * @param value the value
     * @param split the split
     * @param child the child
     * @return string
     * <AUTHOR>
     * @date 2016 -11-10 17:08:44
     */
    public static String removeChild(String value, String split, String child) {
        if (StringUtils.isBlank(value) || StringUtils.isBlank(child)) {
            return "";
        }
        String[] results = value.split(split);
        if (results == null || results.length <= 0) {
            return "";
        }
        List<String> resultList = new ArrayList<String>(Arrays.asList(results));
        for (int i = 0; i < resultList.size(); i++) {
            if (resultList.get(i).equals(child)) {
                resultList.remove(i);
                i--;
            }
        }
        return StringUtils.join(resultList, split);
    }

    /**
     * 获取fix之后的字符串，按照split分割
     *
     * @param value
     * @param split
     * @param fix
     * @return
     */
    public static String splitLastBySplit(String value, String split, String fix) {
        if (StringUtils.isBlank(value) || StringUtils.isBlank(fix)) {
            return "";
        }
        String[] results = value.split(split);
        if (results == null || results.length <= 0) {
            return "";
        }
        List<String> paramList = new ArrayList<String>(Arrays.asList(results));
        List<String> resultList = new ArrayList<String>();
        for (String str : paramList) {
            if (str.contains(fix)) {
                resultList.add(str.substring(str.indexOf(fix) + 1));
            }
        }
        return StringUtils.join(resultList, split);
    }

    /**
     * 移除value里面包含child的 :splitFirstBySplit("1-张三、1-张三、2-李四、2-李四、2-李四、3-万股为", "、", "-", "1") ->李四、李四、李四、万股为
     *
     * @param value the value
     * @param split the split
     * @param fix the fix
     * @param child the child
     * @return string
     * <AUTHOR>
     * @date 2016 -11-10 19:17:01
     */
    public static String splitFirstBySplit(String value, String split, String fix, String child) {
        if (StringUtils.isBlank(value) || StringUtils.isBlank(fix) || StringUtils.isBlank(child)) {
            return "";
        }
        String[] results = value.split(split);
        if (results == null || results.length <= 0) {
            return "";
        }
        List<String> paramList = new ArrayList<String>(Arrays.asList(results));
        List<String> resultList = new ArrayList<String>();
        for (String str : paramList) {
            if (str.contains(fix) && str.substring(0, str.indexOf(fix)).equals(child)) {
                continue;
            }
            resultList.add(str);
        }
        return StringUtils.join(resultList, split);
    }

    /**
     * string -> list ："张三，李四" -> ["张三","李四"]
     *
     * @param value the value
     * @param split the split
     * @return list
     * <AUTHOR>
     * @date 2017 -01-18 15:12:15
     */
    public static List<String> splitString2List(String value, String split) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<String>();
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }
        return new ArrayList<String>(Arrays.asList(value.split(split)));
    }

    /**
     * string -> list ："1，2" -> [1,2]
     *
     * @param value the value
     * @param split the split
     * @return list
     * <AUTHOR>
     * @date 2017 -01-18 15:12:15
     */
    public static List<Integer> splitString2ListInteger(String value, String split) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<Integer>();
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }
        List<Integer> resultList = new ArrayList<Integer>();
        for (String str : Arrays.asList(value.split(split))) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            resultList.add(Integer.valueOf(str));
        }
        return resultList;
    }

    /**
     * string -> list ："1，2" -> [1L,2L]
     *
     * @param value the value
     * @param split the split
     * @return list
     * <AUTHOR>
     * @date 2017 -01-18 15:12:15
     */
    public static List<Long> splitString2ListLong(String value, String split) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<Long>();
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }
        List<Long> resultList = new ArrayList<Long>();
        for (String str : Arrays.asList(value.split(split))) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            resultList.add(Long.valueOf(str));
        }
        return resultList;
    }

    /**
     * string -> list ："1，2" -> [1,2]
     *
     * @param value the value
     * @param split the split
     * @return list
     * <AUTHOR>
     * @date 2017 -01-18 15:12:15
     */
    public static Integer[] splitString2ListIntegerArray(String value, String split) {
        if (StringUtils.isBlank(value)) {
            return new Integer[] {};
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }
        return ListKit.list2ArrayInteger(splitString2ListInteger(value, split));
    }

    /**
     * 数组是是否包含某个元素，小于0表示不包含，大于0表示包含且在集合中第几位，从0开始
     *
     * <AUTHOR>
     * @create 2017-11-10下午03:08:38
     * @param arr 数组
     * @param item 元素
     * @return 小于0表示不包含，大于0表示包含且在集合中第几位，从0开始
     */
    public static int arrayContains(String[] arr, String item) {
        if (StringUtils.isBlank(item)) {
            return -1;
        }
        if (arr == null) {
            return -1;
        }
        for (int i = 0; i < arr.length; i++) {
            if (item.equals(arr[i])) {
                return i;
            }
        }
        return -1;
    }

    /**
     * string -> list ："1,2" -> [1,2]
     *
     * @param value the value
     * @param split the split
     * @return list
     * <AUTHOR>
     * @date 2017 -01-18 15:12:15
     */
    public static List<Integer> splitString2IntegerList(String value, String split) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<Integer>();
        }
        if (StringUtils.isBlank(split)) {
            split = ",";
        }
        List<String> strList = new ArrayList<String>(Arrays.asList(value.split(split)));
        return ListKit.convertList(strList);
    }

    /**
     * 填充带占位符的字符串并返回：我{}上班,骑自行车 ---> 我骑自行车上班
     *
     * @param msg the msg
     * @param args the args，注意如果是日期或者Long等类型一定要先转换为字符串
     * @return message message
     * <AUTHOR>
     * @date 2023 -01-13 15:01:30
     */
    public static String getMessage(String msg, Object... args) {
        String result = MessageFormat.format(msg, args);
        return result;
    }

    /**
     * 超过指定长度用...代替
     */
    public static String getEllipsis(String value, int length) {
        if (StringUtils.isBlank(value)) {
            return "";
        }
        if (value.length() > length) {
            return value.substring(0, length) + "...";
        }
        return value;
    }

    public static void main(String[] args) {
        // System.out.println(getMessage(ConstantsConfig.DEFAULT_SCENE_LAYOUT_VERTICAL, "1", "2", "3"));
        // System.out.println(getEllipsis("长度", 17));
    }

}
