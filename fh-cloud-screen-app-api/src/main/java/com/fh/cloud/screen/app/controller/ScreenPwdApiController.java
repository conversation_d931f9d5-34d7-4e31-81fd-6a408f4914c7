package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.service.screen.service.ScreenPwdApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/12 3:07 下午 @description：
 */
@RestController
@Api(tags = "组织机构密码")
@AllArgsConstructor(onConstructor = @_(@Autowired))
@RequestMapping("/screen/pwd")
public class ScreenPwdApiController {

    private final ScreenPwdApiService screenPwdApiService;

    @GetMapping("fetch")
    @ApiOperation("根据组织机构获取密码")
    public AjaxResult fetch(@RequestParam("orgId") Long orgId) {
        return this.screenPwdApiService.getByOrgId(orgId);
    }

}
