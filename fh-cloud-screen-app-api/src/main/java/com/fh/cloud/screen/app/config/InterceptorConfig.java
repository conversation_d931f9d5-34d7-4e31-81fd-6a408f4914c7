package com.fh.cloud.screen.app.config;

import com.fh.cloud.screen.app.jwt.interceptor.JwtHandlerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2022/5/12 3:50 下午 @description：
 */

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Autowired
    public JwtHandlerInterceptor jwtHandlerInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtHandlerInterceptor).addPathPatterns("/**");
    }
}
