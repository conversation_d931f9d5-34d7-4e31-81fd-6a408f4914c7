package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.fh.cloud.screen.service.space.service.SpaceDeviceApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/6 2:19 下午 @description： 设备空间关系相关业务接口
 */
@Slf4j
@RestController
@Api(tags = "空间设备接口")
@RequestMapping("/space/device-rel")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class SpaceDeviceRelApiController {

    private final SpaceDeviceApiService spaceDeviceApiService;

    /**
     * save or update space device relation info
     *
     * @param spaceDeviceRelBo
     * @return
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增地点和设备关系表", httpMethod = "POST")
    public AjaxResult addSpaceDeviceRel(@RequestBody SpaceDeviceRelBo spaceDeviceRelBo) {
        return this.spaceDeviceApiService.saveSpaceDeviceRel(spaceDeviceRelBo);
    }

}
