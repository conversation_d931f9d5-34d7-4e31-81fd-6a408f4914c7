/*
 * Copyright 2014 Focus Technology, Co., Ltd. All rights reserved.
 */
package com.fh.cloud.screen.app.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateKit {
    public static final int DISTANCE_THIRTY_ONE = -31;

    /**
     * 获取昨天 格式为 yyyy-MM-dd
     *
     * @return
     */
    public static String getYesterday() {
        // 昨天
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        return format.format(calendar.getTime());
    }

    /**
     * 获取距离现在的时间 格式为 yyyy-MM-dd
     *
     * @param distance 可以为负数。 例如-1 昨天 -2 前天。。。
     * @return
     */
    public static String getDistancePresentTime(int distance) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, distance);
        return format.format(calendar.getTime());
    }

    /**
     * 获取当前时间距离 明天零点的时间差
     *
     * @return
     */
    public static int getSeconds() {
        long startTime = System.currentTimeMillis();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        long endTime = calendar.getTime().getTime();
        return (int)((endTime - startTime) / 1000);
    }

    /**
     * 获取两个日期的时间差+添加的秒(如果不需要写0)
     *
     * @param fromDate 时间差的开始日期
     * @param endDate 时间差的结束日期
     * @param sc 秒数（时间）
     * @return seconds by date
     * <AUTHOR>
     * @date 2019 -07-22 11:25:35
     */
    public static int getSecondsByDate(Date fromDate, Date endDate, int sc) {
        if (fromDate == null || endDate == null) {
            return sc;
        }
        int res = (int)((endDate.getTime() - fromDate.getTime()) / 1000);
        if (res < 0) {
            return 0;
        }
        return res + sc;
    }

    /**
     * 返回指定的日期范围
     *
     * @param range
     * @param format
     * @return
     */
    public static List<String> getBeforeDay(Integer range, String format) {
        List<String> dayList = new ArrayList<String>();
        for (int i = range; i > 0; i--) {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE, -i);
            String dateString = date2String(cal.getTime(), format);
            dayList.add(dateString);
        }
        return dayList;
    }

    /**
     * 将日期转化为字符串，带格式
     *
     * @param date
     * @param format
     * @return
     */
    public static String date2String(Date date, String format) {
        if (date == null) {
            return "";
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sf = new SimpleDateFormat(format);
        String dateStr = sf.format(date);
        return dateStr;
    }

    /**
     * 将日期字符串转化为日期对象，带格式
     *
     * @param
     * @param format
     * @return
     */
    public static Date string2Date(String dateStr, String format) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sf = new SimpleDateFormat(format);
        try {
            Date date = sf.parse(dateStr);
            return date;
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定天数之前的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getBeforeDate(Integer range, String format) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取指定天数之前的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getBeforeDate(Integer range, Date date) {
        String format = "yyyy-MM-dd HH:mm:ss";
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.DATE, -range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取指定天数之后的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getAfterDate(Integer range, Date date) {
        String format = "yyyy-MM-dd HH:mm:ss";
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.DATE, range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取指定天数之后的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getZeroDate(Integer range, Date date) {
        String format = "yyyy-MM-dd 00:00:00";
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.DATE, range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取指定秒数之后的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getAfterSeconds(Integer range, Date date) {
        String format = "yyyy-MM-dd HH:mm:ss";
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.SECOND, range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取指定分钟之前的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getBeforeMinute(Integer range, String format) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MINUTE, -range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取指定分钟之后的日期，格式yyyy-MM-dd 00:00:00
     *
     * @param range
     * @return
     */
    public static Date getAfterMinute(Integer range, Date date) {
        String format = "yyyy-MM-dd HH:mm:ss";
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(date.getTime());
        cal.add(Calendar.MINUTE, range);
        Date beforeDate = cal.getTime();
        String beforeDateString = date2String(beforeDate, format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 某个时间后的多少分钟
     *
     * @param date
     * @param range 分钟
     * @return
     */
    public static Date getData(Date date, int day, int range, String format) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -day);
        cal.add(Calendar.MINUTE, range);

        String beforeDateString = date2String(cal.getTime(), format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    public static Date getEDate(Date date, int hour, int minute, int second, String format) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        String beforeDateString = date2String(cal.getTime(), format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 某个时间后的多少分钟
     *
     * @param date
     * @param range 分钟
     * @return
     */
    public static String getDataString(Date date, int day, int range, String format) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -day);
        cal.add(Calendar.MINUTE, range);

        String beforeDateString = date2String(cal.getTime(), format);
        return beforeDateString;
    }

    public static Date getDateFromSeconds(long seconds) {
        return getDateFromMillis(getMillis() + (seconds * 1000));
    }

    /**
     * 获取当前凌晨时间毫秒值
     *
     * @return the millis
     */
    public static long getMillis() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 00);
        calendar.set(Calendar.MINUTE, 00);
        calendar.set(Calendar.SECOND, 00);
        return calendar.getTime().getTime();
    }

    /**
     * 将时间设值
     *
     * @param millis
     * @return
     */
    public static Date getDateFromMillis(long millis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(millis);
        // calendar.add(Calendar.HOUR, -8);// 去掉GMT时间默认
        // calendar.add(Calendar.YEAR, -1970);
        return calendar.getTime();
    }

    /**
     * 获取本周礼拜一到礼拜五的日期
     * <p>
     * 起始时间 本周一 ， 结束时间本周日晚上
     * </p>
     *
     * @return
     */
    public static List<String> getDateByWeek() {
        List<String> st = new ArrayList<String>();
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        for (int i = 0; i < 7; i++) {
            calendar.set(Calendar.DAY_OF_WEEK, changeDay(i));
            String beginTime = DateFormatUtils.format(calendar.getTime(), "MM-dd");
            st.add(beginTime);
        }
        return st;
    }

    /**
     * 取一周的时间， 转化我们习惯的模式
     *
     * @param day
     * @return
     */
    public static int changeDay(int day) {
        switch (day) {
            case 0:
                return Calendar.MONDAY;
            case 1:
                return Calendar.TUESDAY;
            case 2:
                return Calendar.WEDNESDAY;
            case 3:
                return Calendar.THURSDAY;
            case 4:
                return Calendar.FRIDAY;
            case 5:
                return Calendar.SATURDAY;
            case 6:
                return Calendar.SUNDAY;
        }
        return 0;

    }

    /**
     * 获取某天的具体时间
     *
     * @param date
     * @param minute
     * @param hour
     * @param second
     * @param format
     * @return
     */
    public static Date getData(Date date, int minute, int hour, int second, String format) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        cal.add(Calendar.MINUTE, minute);
        cal.add(Calendar.HOUR, hour);
        cal.add(Calendar.SECOND, second);
        String beforeDateString = date2String(cal.getTime(), format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取某天的制定时间
     *
     * @param date
     * @param day
     * @param hour
     * @param minute
     * @param second
     * @param format
     * @return
     */
    public static String getDateString(Date date, int day, int hour, int minute, int second, String format) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, day);
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MILLISECOND, second);
        String beforeDateString = date2String(cal.getTime(), format);
        return beforeDateString;
    }

    /**
     * 获取明天的零点
     *
     * @param
     * @param day
     * @param hour
     * @param minute
     * @param second
     * @param format
     * @return
     */
    public static Date getDate(int day, int hour, int minute, int second, String format) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, day);
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MILLISECOND, second);
        String beforeDateString = date2String(cal.getTime(), format);
        return string2Date(beforeDateString, format);
    }

    /**
     * 获取当前月份
     */
    public static int getCurrentMonth() {
        Calendar cal = Calendar.getInstance();
        return cal.get(Calendar.MONTH) + 1;
    }

    /**
     * 获取当前年
     *
     * @return
     */
    public static int getCurrentYear() {
        Calendar cal = Calendar.getInstance();
        return cal.get(Calendar.YEAR);
    }

    /**
     * 当前时间
     *
     * @return
     */
    public static String getCurrentTime() {
        return date2String(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 当前时间
     *
     * @return
     */
    public static String getCurrentTime(String format) {
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        return date2String(new Date(), format);
    }

    /**
     * 当前时间
     *
     * @return
     */
    public static String getCurrentDay() {
        return date2String(new Date(), "yyyy-MM-dd");
    }

    /**
     * 当前时间是否超过指定时间
     *
     * @return
     */
    public static boolean ifMoreThenDate(String d) {
        Date date = string2Date(d, "yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        if (now.getTime() > date.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 当前时间是否超过指定时间
     *
     * @return
     */
    public static boolean ifMoreThenDate(Date d) {
        Date now = new Date();
        if (now.getTime() > d.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 当前时间是否超过指定时间-年月日
     *
     * @param d 指定时间
     * @param f 格式默认"yyyy-MM-dd"
     * @return boolean boolean
     * <AUTHOR>
     */
    public static boolean ifMoreThenDateFormat(Date d, String f) {
        if (StringUtils.isBlank(f)) {
            f = "yyyy-MM-dd";
        }
        Date now = new Date();
        d = string2Date(date2String(d, f), f);
        now = string2Date(date2String(now, f), f);
        if (now.getTime() > d.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 指定date是否在时间段之内，在则true,不在则false
     *
     * @param d 指定日期，默认为当前时间
     * @param start 开始时间
     * @param end 结束时间
     * @return boolean boolean
     * <AUTHOR>
     */
    public static boolean ifDateInSlot(Date d, Date start, Date end) {
        if (d == null) {
            d = new Date();
        }
        if (!(start.compareTo(d) > 0 || d.compareTo(end) > 0)) {
            return true;
        }
        return false;
    }

    /**
     * 格式化日期-默认【yyyy-MM-dd】
     *
     * @param d the d
     * @param f the f [yyyy-MM-dd]
     * @return date date
     * <AUTHOR>
     */
    public static Date formatDate(Date d, String f) {
        if (StringUtils.isBlank(f)) {
            f = "yyyy-MM-dd";
        }
        return string2Date(date2String(d, f), f);
    }

    /**
     * 格式化日期-默认【yyyy-MM-dd】
     *
     * @param d the d
     * @param f the f [yyyy-MM-dd]
     * @return date date
     * <AUTHOR>
     */
    public static String formatDate(String d, String f) {
        if (StringUtils.isBlank(f)) {
            f = "yyyy-MM-dd";
        }
        return date2String(string2Date(d, f), f);
    }

    /**
     * 当前时间是否小于指定时间
     *
     * @return
     */
    public static boolean ifLessThenDate(String d) {
        Date date = string2Date(d, "yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        if (now.getTime() < date.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 当前时间是否小于指定时间
     *
     * @return
     */
    public static boolean ifLessThenDate(Date d) {
        Date now = new Date();
        if (now.getTime() < d.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 当前时间是否小于指定时间
     *
     * @param d 指定时间
     * @param f 格式化格式（即指定时间的格式:默认 yyyy-MM-dd）
     * @return boolean boolean
     * <AUTHOR>
     */
    public static boolean ifLessThenDateFormat(Date d, String f) {
        if (StringUtils.isBlank(f)) {
            f = "yyyy-MM-dd";
        }
        Date now = new Date();
        d = string2Date(date2String(d, f), f);
        now = string2Date(date2String(now, f), f);
        if (now.getTime() < d.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 获取分钟数
     *
     * @param date
     * @return
     */
    public static int getMinute(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.MINUTE);
    }

    public static String getPerStrDate(int beforDay) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        // -- 结束时间必须在当前时间之前
        calendar.add(Calendar.DATE, -beforDay);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        return format.format(calendar.getTime());
    }

    /**
     * 获取指定日期的年 yyy-MM-dd 00；00:00
     *
     * @param
     * @return
     */
    public static String getDayZore(String dateStr) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat endSf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        try {
            Date date = sf.parse(dateStr);
            return endSf.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定日期的年 yyy-MM-dd 23；59:59
     *
     * @param
     * @return
     */
    public static String getDayEnd(String dateStr) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat endSf = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        try {
            Date date = sf.parse(dateStr);
            return endSf.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定时分秒+当前时间的年月日
     *
     * @param hhmm 时分秒
     * @return 指点日期的零点
     */
    public static Date getCurrentAndHHmm(String hhmm) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd ");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return format.parse(dateFormat.format(new Date()) + hhmm);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定日期的零点 yyy-MM-dd 00:00:00
     *
     * @param date 日期
     * @return 指点日期的零点
     */
    public static Date getDayZore(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0,
            0, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的零点 yyy-MM-dd 23:59:59
     *
     * @param date 日期
     * @return 指点日期的零点
     */
    public static Date getDayEnd(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 23,
            59, 59);
        return calendar.getTime();
    }

    /**
     * 获取指定日期的年 yyy-MM-dd HH:mm:ss
     *
     * @param
     * @return
     */
    @SuppressWarnings("deprecation")
    public static Integer getYearByDate(String dateStr, String format) {
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sf = new SimpleDateFormat(format);
        try {
            Date date = sf.parse(dateStr);
            return date.getYear();
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定日期的年 yyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    @SuppressWarnings("deprecation")
    public static String getYearMonthByDate(String dateStr, String format) {
        if (StringUtils.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }
        SimpleDateFormat sf = new SimpleDateFormat(format);
        try {
            Date date = sf.parse(dateStr);
            return date.getYear() + "-" + (date.getMonth() + 1);
        } catch (ParseException e) {
            return "";
        }
    }

    /**
     * 根据传入的日期返回友好时间
     *
     * @param needDate
     * @return string
     */
    public static String getFriendlyTime(Date needDate) {
        String result = "";
        Date currentDate = new Date();
        Calendar currentCal = Calendar.getInstance();
        currentCal.setTime(currentDate);
        Calendar needCal = Calendar.getInstance();
        needCal.setTime(needDate);

        boolean isSameYear = currentCal.get(Calendar.YEAR) == needCal.get(Calendar.YEAR);
        boolean isSameMonth = isSameYear && currentCal.get(Calendar.MONTH) == needCal.get(Calendar.MONTH);
        boolean isSameDate = isSameMonth && currentCal.get(Calendar.DAY_OF_MONTH) == needCal.get(Calendar.DAY_OF_MONTH);

        int year = needCal.get(Calendar.YEAR);
        int month = needCal.get(Calendar.MONTH) + 1;
        int day = needCal.get(Calendar.DATE);
        int hour = needCal.get(Calendar.HOUR_OF_DAY);
        int minute = needCal.get(Calendar.MINUTE);

        if (isSameDate) {
            result = ("今天 " + (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute));
        } else if (isSameYear) {
            result = ((month < 10 ? ("0" + month) : month) + "-" + (day < 10 ? ("0" + day) : day) + " "
                + (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute));
        } else if (!isSameYear) {
            result = (year + "-" + (month < 10 ? ("0" + month) : month) + "-" + (day < 10 ? ("0" + day) : day) + " "
                + (hour < 10 ? ("0" + hour) : hour) + ":" + (minute < 10 ? ("0" + minute) : minute));
        }
        return result;
    }

    /**
     * 返回 x月x日 10:10
     *
     * @return
     * @updateauthor sunqingbiao 安保三期修改为只根据startDate返回 2017年3月16日10:10
     */
    public static String getSmartDate(Date startDate, Date endDate) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        // Calendar endCal = Calendar.getInstance();
        // endCal.setTime(endDate);

        int year = startCal.get(Calendar.YEAR);

        int month = startCal.get(Calendar.MONTH) + 1;
        int day = startCal.get(Calendar.DATE);

        int startHour = startCal.get(Calendar.HOUR_OF_DAY);
        int startMinute = startCal.get(Calendar.MINUTE);

        // int endHour = endCal.get(Calendar.HOUR_OF_DAY);
        // int endMinute = endCal.get(Calendar.MINUTE);

        return year + "年" + month + "月" + day + "日" + (startHour < 10 ? ("0" + startHour) : startHour) + ":"
            + (startMinute < 10 ? ("0" + startMinute) : startMinute);
        // + "-"
        // + (endHour < 10 ? ("0" + endHour) : endHour) + ":" + (endMinute < 10 ? ("0" + endMinute) : endMinute);
    }

    /**
     * 返回 x月x日 10:10-x月x日11:10 如果跨年则显示 xxxx年x月x日10:10-xxxx年x月x日11:10
     *
     * @return
     */
    public static String getLeaveDate(Date startDate, Date endDate) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);

        int startYear = startCal.get(Calendar.YEAR);
        int startMonth = startCal.get(Calendar.MONTH) + 1;
        int startDay = startCal.get(Calendar.DATE);

        int endYear = endCal.get(Calendar.YEAR);
        int endMonth = endCal.get(Calendar.MONTH) + 1;
        int endDay = endCal.get(Calendar.DATE);

        int startHour = startCal.get(Calendar.HOUR_OF_DAY);
        int startMinute = startCal.get(Calendar.MINUTE);

        int endHour = endCal.get(Calendar.HOUR_OF_DAY);
        int endMinute = endCal.get(Calendar.MINUTE);

        // 班牌二期改为全部增加年份
        // if (startYear == endYear) {
        // return startMonth + "月" + startDay + "日" + (startHour < 10 ? ("0" + startHour) : startHour) + ":"
        // + (startMinute < 10 ? ("0" + startMinute) : startMinute) + "-" + endMonth + "月" + endDay + "日"
        // + (endHour < 10 ? ("0" + endHour) : endHour) + ":"
        // + (endMinute < 10 ? ("0" + endMinute) : endMinute);
        // }
        // else {
        return startYear + "年" + startMonth + "月" + startDay + "日" + (startHour < 10 ? ("0" + startHour) : startHour)
            + ":" + (startMinute < 10 ? ("0" + startMinute) : startMinute) + "-" + endYear + "年" + endMonth + "月"
            + endDay + "日" + (endHour < 10 ? ("0" + endHour) : endHour) + ":"
            + (endMinute < 10 ? ("0" + endMinute) : endMinute);
        // }

    }

    /**
     * 获取相册日期，返回 x月x日 如果跨年则显示 xxxx年x月x日
     *
     * @param date the date
     * @return album pic date
     */
    public static String getAlbumPicDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        Calendar calendarNow = Calendar.getInstance();
        int nowYear = calendarNow.get(Calendar.YEAR);

        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int day = calendar.get(Calendar.DATE);

        if (nowYear == year) {
            return month + "月" + day + "日";
        }
        return year + "年" + month + "月" + day + "日";
    }

    /**
     * 把毫秒转化成日期
     *
     * @param dateFormat(日期格式，例如：MM/ dd/yyyy HH:mm:ss)
     * @param millSec(毫秒数)
     * @return
     */
    public static String transferLongToDate(String dateFormat, Long millSec) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Date date = new Date(millSec);
        return sdf.format(date);
    }

    /**
     * 获取1970年的起始日期
     *
     * @return
     * @throws ParseException
     */
    public static Date getFirstDate() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dt = sdf.parse("1970-01-01 00:00:01");
        return dt;
    }

    /**
     * 返回一周中的第几天 1=Sunday,2=Monday,,,7=Saturday。
     *
     * @return
     */
    public static int getDayOfWeek() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        return cal.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 将时间转换为周几那天的时间，例如 ("2016-10-12 12:12:12",5) -> "2016-10-14 12:12:12"
     *
     * @param addTime
     * @param updateTime
     * @return
     */
    public static String getAfterDateTime(String currentDateTime, Integer range) {
        int currentWeek = getDayOfWeek() - 1;
        return date2String(getAfterDate(range - currentWeek, string2Date(currentDateTime, "yyyy-MM-dd HH:mm:ss")),
            "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 将时间转换为周几那天的时间，例如 ("2016-10-12 12:12:12",5) -> "2016-10-14 12:12:12"
     *
     * @param
     * @param
     * @return
     */
    public static String getAfterDateTime(Date currentDateTime, Integer range) {
        int currentWeek = getDayOfWeek() - 1;
        return date2String(getAfterDate(range - currentWeek, currentDateTime), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 将时间转换为周几那天的时间，例如 ("2016-10-12 12:12:12",5) -> "2016-10-14 12:12:12"
     *
     * @param
     * @param
     * @return
     */
    public static Date getDateByWeekId(Date currentDateTime, Integer range) {
        int currentWeek = getDayOfWeek() - 1;
        return getAfterDate(range - currentWeek, currentDateTime);
    }

    /**
     * 这里面的根据java语言的weekId获取对应的中文星期值
     * 
     * @param addTime
     * @param updateTime
     * @return
     */
    public static String getOnTeachingTime(Date addTime, Date updateTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat hsdf = new SimpleDateFormat("HH:mm");
        String ymdTime = sdf.format(addTime);
        String addHmTime = hsdf.format(addTime);
        String updateHmTime = hsdf.format(updateTime);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(addTime);
        String weekTime = "";
        StringBuffer sb = new StringBuffer();
        switch (calendar.get(Calendar.DAY_OF_WEEK)) {
            case 1:
                weekTime = "星期日";
                break;
            case 2:
                weekTime = "星期一";
                break;
            case 3:
                weekTime = "星期二";
                break;
            case 4:
                weekTime = "星期三";
                break;
            case 5:
                weekTime = "星期四";
                break;
            case 6:
                weekTime = "星期五";
                break;
            case 7:
                weekTime = "星期六";
                break;
            default:
                break;
        }
        sb.append(ymdTime).append(" ").append(weekTime).append(" ").append(addHmTime).append("-").append(updateHmTime);
        return sb.toString();
    }

    /**
     * 返回当天，格式为 x月x日
     *
     * @return
     * @updateauthor sunqingbiao
     */
    public static String getCurrentMonthDay() {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DATE);
        return month + "月" + day + "日";
    }

    /**
     * 根据（我们业务定义的）weekId获取weekName，业务定义的周一到周日为1-7
     *
     * @param weekId the week id
     * @return week name by week id
     * <AUTHOR>
     */
    public static String getWeekNameByWeekId(int weekId) {
        String weekTime = "";
        switch (weekId) {
            case 7:
                weekTime = "星期日";
                break;
            case 1:
                weekTime = "星期一";
                break;
            case 2:
                weekTime = "星期二";
                break;
            case 3:
                weekTime = "星期三";
                break;
            case 4:
                weekTime = "星期四";
                break;
            case 5:
                weekTime = "星期五";
                break;
            case 6:
                weekTime = "星期六";
                break;
            default:
                break;
        }
        return weekTime;
    }

    /**
     * 获取两个日期之间的星期(周一到周日分别为 1-7)
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return week id by date
     * <AUTHOR>
     */
    public static List<Integer> getWeekIdByDate(Date startDate, Date endDate) {
        List<Integer> resultList = new ArrayList<Integer>();
        if (startDate == null || endDate == null) {
            return resultList;
        }

        Date tempDate = startDate;
        while (tempDate.getTime() <= endDate.getTime()) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(tempDate);
            int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
            if (week_index == 0) {
                week_index = 7;
            }
            resultList.add(week_index);
            tempDate = getAfterDate(1, tempDate);
        }
        return resultList;
    }

    /**
     * 获取两个日期的指定格式拼接
     *
     * @param d1 the d 1
     * @param d2 the d 2
     * @param concat the concat
     * @param format the format
     * @return concat date
     * <AUTHOR>
     */
    public static String getConcatDate(Date d1, Date d2, String concat, String format) {
        String strD1 = date2String(d1, format);
        String strD2 = date2String(d2, format);
        return strD1 + concat + strD2;
    }

    /**
     * 获取课程授课时间
     *
     * @param week the week
     * @param startTime the start time
     * @param endTime the end time
     * @return course room date time
     * <AUTHOR>
     */
    public static String getCourseRoomDateTime(String week, Date startTime, Date endTime) {
        return week + date2String(startTime, "HH:mm") + "~" + date2String(endTime, "HH:mm");
    }

    /**
     * 取上月最后一天
     *
     * @return 上月最后一天
     * <AUTHOR>
     * @create 2017-12-11上午11:01:37
     */
    public static Date getPerMonthLastDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sdfDay.format(calendar.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 取上月第一天
     *
     * @return 上月第一天
     */
    public static Date getPerMonthFirstDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        try {
            return sdf.parse(sdf.format(calendar.getTime()));
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 取当月最后一天
     *
     * @return 当月最后一天
     */
    public static Date getCurrentMonthLastDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sdfDay.format(calendar.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 取当月第几天
     *
     * @param day 第几天
     * @return 当月第几天
     */
    public static Date getCurrentMonthFirstDay(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, day);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        try {
            return sdf.parse(sdf.format(calendar.getTime()));
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定日期所在月的最后一天
     *
     * @param date 日期
     * @return 指定日期所在月的最后一天
     */
    public static Date getlastDayByMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sdfDay.format(calendar.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 取下个月最后一天
     *
     * @return 下个月最后一天
     */
    public static Date getNextMonthLastDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 2);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sdfDay.format(calendar.getTime()) + " 23:59:59");
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 取下个月第几天
     *
     * @param day 第几天
     * @return 下个月第几天
     */
    public static Date getNextMonthFirstDay(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        try {
            return sdf.parse(sdf.format(calendar.getTime()));
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 取本周几时间
     *
     * @param week 1-7表示周一到周日
     * @return 本周几时间
     */
    public static Date getDayCurrentWeek(int week) {
        Calendar c = Calendar.getInstance();
        int day_of_week = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        c.add(Calendar.DATE, -day_of_week + week);
        return c.getTime();
    }

    /**
     * 下周几的时间
     *
     * @param week 1-7表示周一到周日
     * @return 下周几的时间
     */
    public static Date getDayNextWeek(int week) {
        Calendar c = Calendar.getInstance();
        int day_of_week = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        c.add(Calendar.DATE, -day_of_week + week + 7);
        return c.getTime();
    }

    /**
     * 根据第一个时间的yyyy-MM-dd+第一个时间的HH:mm组成新的时间
     *
     * @param date yyyy-MM-dd时间
     * @param time HH:mm时间
     * @return 组合后时间
     */
    public static Date getDateByDateAndTime(Date date, Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH);
        int day = cal.get(Calendar.DATE);
        cal.setTime(time);
        int hrs = cal.get(Calendar.HOUR_OF_DAY);
        int min = cal.get(Calendar.MINUTE);
        cal.set(year, month, day, hrs, min, 0);
        return cal.getTime();
    }

    /**
     * 获取指定日期所在月的天数
     *
     * @param date 日期
     * @return 日期所在月的天数
     */
    public static int countDaysByDateMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取指定日期所在月的号
     *
     * @param date 日期
     * @return 日期所在月的号
     */
    public static int getDayByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取日期对应的星期几，注：星期天为7。将java语言定义的week定义转换成我们业务的week定义（周一到周日为1-7）
     *
     * @param date 日期
     * @return 星期几，星期天为7
     */
    public static int getWeekIdByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekId = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekId == 0) {
            return 7;
        }
        return weekId;
    }

    /**
     * 获取日期对应第几周,按第一天为周一计算
     *
     * @param date 日期
     * @return 日期对应的第几周
     */
    public static int getWeekByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        return cal.get(Calendar.WEEK_OF_MONTH);
    }

    /**
     * 获取指定日期所在月的指定天的日期
     *
     * @param date 日期
     * @param day 指定的第几天
     * @return 日期所在月的指定天的日期
     */
    public static Date getDateByDayMonth(Date date, int day) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, day);
        return cal.getTime();
    }

    /**
     * 获取明天零点时间例如现在是2018-07-17 15:20:08 那么获取到的是 2018-07-18 00:00:00
     *
     * @return date
     * <AUTHOR>
     */
    public static Date getTomorrowZeroTime() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取零点时间例如现在是2018-07-17 15:20:08 那么获取到的是 2018-07-17 00:00:00
     *
     * @return date
     * <AUTHOR>
     */
    public static Date getTodayZeroTime() {
        Calendar cal = Calendar.getInstance();
        // cal.add(Calendar.DATE, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 某个时间后的多少分钟
     *
     * @param date the date
     * @param day the day
     * @param hour the hour
     * @param minute the minute
     * @param second the second
     * @param format the format
     * @return data
     */
    public static Date getData(Date date, int day, int hour, int minute, int second, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        String beforeDateString = date2String(calendar.getTime(), format);
        Date retDate = string2Date(beforeDateString, format);
        return retDate;
    }

    /**
     * 获取本周1-7对应的日期
     *
     * @param weekId 星期1-7
     * @return 日期
     */
    public static Date getDateByCurrentWeekId(int weekId) {
        Calendar c = Calendar.getInstance();
        int day_of_week = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        if (weekId > 7) {
            weekId = 7;
        }
        if (weekId < 1) {
            weekId = 1;
        }
        c.add(Calendar.DATE, -day_of_week + weekId);
        return c.getTime();
    }

    /**
     * 将时间长度格式化为"mm:ss"或"HH:mm:ss" 例如：00:05、01:00、01:01:01
     *
     * @param durationMillis 持续时间（单位：毫秒）
     * @return java.lang.String 小于一小时：“mm:ss”; 大于一小时：“"HH:mm:ss"”
     */
    public static String formatDurationToMsOrHms(long durationMillis) {
        // 一小时换算成毫秒
        long anHour = 60 * 60 * 1000;
        return DurationFormatUtils.formatDuration(durationMillis, durationMillis < anHour ? "mm:ss" : "HH:mm:ss");
    }

    /**
     * 获取两个日期之间月份：20210401,20210501,20210601
     *
     * @param start 2021-04-20
     * @param end 2021-06-14
     * @return list
     */
    public static List<String> listDateSub(Date start, Date end) {
        if (start == null || end == null) {
            return null;
        }
        List<String> list = new ArrayList<String>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        int startYear = calendar.get(Calendar.YEAR);
        int startMonth = calendar.get(Calendar.MONTH) + 1;
        calendar.setTime(end);
        int endYear = calendar.get(Calendar.YEAR);
        int endMonth = calendar.get(Calendar.MONTH) + 1;
        // 同年
        if (startYear == endYear) {
            for (; startMonth <= endMonth; startMonth++) {
                if (startMonth < 10) {
                    list.add(startYear + "0" + startMonth + "01");
                } else {
                    list.add(startYear + "" + startMonth + "01");
                }
            }
        }
        // 不同年
        else {
            // 处理前面多余月份
            for (; startMonth <= 12; startMonth++) {
                if (startMonth < 10) {
                    list.add(startYear + "0" + startMonth + "01");
                } else {
                    list.add(startYear + "" + startMonth + "01");
                }
            }
            startYear++;
            for (; startYear < endYear; startYear++) {
                for (int i = 1; i <= 12; i++) {
                    if (i < 10) {
                        list.add(startYear + "0" + i + "01");
                    } else {
                        list.add(startYear + "" + i + "01");
                    }
                }
            }
            // 处理后面多余月份
            for (int j = 1; j <= endMonth; j++) {
                if (endMonth < 10) {
                    list.add(endYear + "0" + j + "01");
                } else {
                    list.add(endYear + "" + j + "01");
                }
            }
        }
        return list;
    }

    /**
     * 将指定日期的年月日转换为当天的时间，例如：1970-01-01 14:47:29 --> 2022-06-09 14:47:29
     *
     * @return
     */
    public static Date transferYMD2CurrentDay(Date oriDate) {
        if (oriDate == null) {
            return null;
        }

        Calendar oriCal = Calendar.getInstance();
        oriCal.setTime(oriDate);

        Calendar nowCal = Calendar.getInstance();
        nowCal.setTime(new Date());

        oriCal.set(Calendar.YEAR, nowCal.get(Calendar.YEAR));
        oriCal.set(Calendar.MONTH, nowCal.get(Calendar.MONTH));
        oriCal.set(Calendar.DATE, nowCal.get(Calendar.DATE));
        return oriCal.getTime();
    }

    public static void main(String[] args) {
        Date date = string2Date("2022-06-12 14:47:29", null);
        System.out.println(getWeekIdByDate(date));
    }

}
