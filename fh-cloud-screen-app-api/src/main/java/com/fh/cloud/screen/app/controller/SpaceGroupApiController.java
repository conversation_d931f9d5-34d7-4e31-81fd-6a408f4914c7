package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.service.space.entity.vo.SpaceGroupVo;
import com.fh.cloud.screen.service.space.service.SpaceGroupApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6 2:19 下午 @description： 空间区域组相关业务接口
 */
@Slf4j
@RestController
@Api(tags = "空间区域组")
@RequestMapping("/space/group")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class SpaceGroupApiController {

    private final SpaceGroupApiService spaceGroupApiService;

    @GetMapping("findAll")
    @ApiOperation(value = "获取所有区域组信息")
    public AjaxResult<List<SpaceGroupVo>> findAll() {
        return this.spaceGroupApiService.findAll();
    }

}
