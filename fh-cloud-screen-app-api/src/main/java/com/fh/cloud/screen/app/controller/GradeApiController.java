package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.service.grade.service.GradeScreenApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/6/1 5:48 下午 @description：
 */
@Slf4j
@RestController
@Api(tags = "年级接口")
@RequestMapping("grade")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class GradeApiController {

    private final GradeScreenApiService gradeScreenApiService;

    /**
     * 根据组织机构ID 所属学段 获取年级
     */
    @ApiOperation("根据组织机构获取年级")
    @GetMapping("/org-section")
    public AjaxResult getBySectionOrgId(@RequestParam("orgId") Long orgId) {
        return this.gradeScreenApiService.getBySectionOrgId(orgId);
    }

}
