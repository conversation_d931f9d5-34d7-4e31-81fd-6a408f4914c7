package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.screen.api.ScreenPoetryContentApi;
import com.fh.cloud.screen.service.screen.api.ScreenPoetryLikesApi;
import com.fh.cloud.screen.service.screen.api.ScreenPoetryPictureApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryContentConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenPoetryPictureConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 共话诗词controller
 *
 * <AUTHOR>
 * @date 2023-06-26 17:17
 */
@RestController
@RequestMapping("/screen/poetry")
@Api(tags = "共话诗词")
public class ScreenPoetryController {
    @Resource
    private ScreenPoetryContentApi screenPoetryContentApi;
    @Resource
    private ScreenPoetryLikesApi screenPoetryLikesApi;
    @Resource
    private ScreenPoetryPictureApi screenPoetryPictureApi;

    @PostMapping("/add")
    @ApiOperation(value = "新增共话诗词", notes = "新增共话诗词")
    @AccessTokenAuth
    public AjaxResult addScreenPoetryContent(@RequestBody ScreenPoetryContentBo screenPoetryContentBo) {
        return screenPoetryContentApi.addScreenPoetryContent(screenPoetryContentBo);
    }

    @PostMapping("/page/list")
    @ApiOperation(value = "获取共话诗词分页列表", notes = "获取共话诗词分页列表")
    @AccessTokenAuth
    public AjaxResult getScreenPoetryContentPageListByCondition(@RequestBody ScreenPoetryContentConditionBo conditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        conditionBo.setOrganizationId(jwtModel.getOrganizationId());
        return screenPoetryContentApi.getScreenPoetryContentPageListByCondition(conditionBo);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取共话诗词列表", notes = "获取共话诗词列表")
    @AccessTokenAuth
    public AjaxResult getScreenPoetryContentListByCondition(@RequestBody ScreenPoetryContentConditionBo contentConditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        contentConditionBo.setOrganizationId(jwtModel.getOrganizationId());
        return screenPoetryContentApi.getScreenPoetryContentListByCondition(contentConditionBo);
    }

    @GetMapping("/addLikesNum")
    @ApiOperation(value = "点赞", notes = "点赞")
    @AccessTokenAuth
    public AjaxResult addScreenPoetryLikesNum(@RequestParam("id") Long id) {
        return screenPoetryLikesApi.addScreenPoetryLikesNum(id);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "获取共话诗词详情", notes = "获取共话诗词详情")
    public AjaxResult getScreenPoetryContentDetail(@RequestParam("id") Long id) {
        return screenPoetryContentApi.getDetail(id);
    }

    @GetMapping("/picture/list")
    public AjaxResult getScreenPoetryPictureList(@RequestParam("organizationId") Long organizationId) {
        ScreenPoetryPictureConditionBo conditionBo = new ScreenPoetryPictureConditionBo();
        conditionBo.setOrganizationId(organizationId);
        return screenPoetryPictureApi.getScreenPoetryPictureListByCondition(conditionBo);
    }

}
