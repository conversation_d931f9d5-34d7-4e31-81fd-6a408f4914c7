package com.fh.cloud.screen.app.jwt.util;

import cn.hutool.json.JSONUtil;
import com.light.core.entity.AjaxResult;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class ResponseUtils {

    public static void responseJson(HttpServletResponse response, AjaxResult result) {
        response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_UTF8_VALUE);
        try {
            response.getWriter().println(JSONUtil.toJsonStr(result));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
