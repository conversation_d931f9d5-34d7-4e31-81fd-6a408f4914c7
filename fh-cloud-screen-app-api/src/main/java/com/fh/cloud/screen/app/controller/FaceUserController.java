package com.fh.cloud.screen.app.controller;

/**
 * 人脸用户接口
 * 
 * <AUTHOR>
 * @date 2022/12/1
 */

import com.fh.cloud.screen.app.enums.SpaceGroupUseTypeEnums;
import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardVo;
import com.fh.cloud.screen.service.device.api.ShowDeviceApi;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.enums.FaceModType;
import com.fh.cloud.screen.service.er.api.ExamInfoStudentApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoStudentBo;
import com.fh.cloud.screen.service.face.api.FaceConfigApi;
import com.fh.cloud.screen.service.face.api.FaceRecordStudentApi;
import com.fh.cloud.screen.service.face.api.FaceRecordTeacherApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人脸管理配置接口
 *
 * <AUTHOR>
 * @date 2022/11/18 16:15
 */
@RestController
@RequestMapping("/face")
@Api(value = "", tags = "人脸用户接口")
public class FaceUserController {
    @Resource
    private FaceRecordTeacherApi faceRecordTeacherApi;
    @Resource
    private FaceRecordStudentApi faceRecordStudentApi;
    @Resource
    private ShowDeviceApi showDeviceApi;

    /**
     * 查询教师人脸数据列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/1 11:29
     */
    @AccessTokenAuth
    @PostMapping("/teacher/list")
    @ApiOperation(value = "查看人脸教师列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getFaceTeacherList(FaceRecordTeacherConditionBo conditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        conditionBo.setOrganizationId(jwtModel.getOrganizationId());
        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())) {
            AjaxResult<List<FaceRecordTeacherVo>> a =
                faceRecordTeacherApi.getFaceRecordTeacherListByCondition(conditionBo);
            Map<String, Object> map = new HashMap<>(4);
            map.put("list", a.getData());
            map.put("total", a.getData().size());
            return AjaxResult.success(map);
        }
        return faceRecordTeacherApi.getFaceRecordTeacherPageListByCondition(conditionBo);
    }

    /**
     * 人脸教师批量更新
     *
     * @param faceRecordTeacherBoList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/1 11:51
     */
    @AccessTokenAuth
    @PostMapping("/teacher/update/batch")
    public AjaxResult updateFaceRecordTeacherBatch(@RequestBody List<FaceRecordTeacherBo> faceRecordTeacherBoList) {
        return faceRecordTeacherApi.updateFaceRecordTeacherBatch(faceRecordTeacherBoList);
    }

    /**
     * 查看人脸学生列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/1 11:31
     */
    @AccessTokenAuth
    @PostMapping("/student/list")
    @ApiOperation(value = "查看人脸学生列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getFaceStudentList(FaceRecordStudentConditionBo conditionBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        String deviceNumber = jwtModel.getDeviceNumber();
        // 班级建模过滤，班级为空查询设备是否配置了班级建模
        if (conditionBo.getClassesId() == null) {
            AjaxResult<ShowDeviceVo> byDeviceNumber = showDeviceApi.getByDeviceNumber(deviceNumber);
            if (byDeviceNumber.isSuccess() && byDeviceNumber.getData() != null) {
                ShowDeviceVo showDeviceVo = byDeviceNumber.getData();
                if (showDeviceVo.getFaceModType() != null
                    && showDeviceVo.getFaceModType().equals(FaceModType.CLASSES.getValue())
                    && jwtModel.getSpaceGroupUseType() != null
                    && jwtModel.getSpaceGroupUseType().equals(SpaceGroupUseTypeEnums.EXECUTIVE.getVal())) {
                    conditionBo.setClassesId(jwtModel.getSpaceInfoId());
                }
            }
        }

        if (SystemConstants.NO_PAGE.equals(conditionBo.getPageNo())) {
            AjaxResult<List<FaceRecordStudentVo>> a =
                faceRecordStudentApi.getFaceRecordStudentListByCondition(conditionBo);
            Map<String, Object> map = new HashMap<>(4);
            map.put("list", a.getData());
            map.put("total", a.getData().size());
            return AjaxResult.success(map);
        }
        return faceRecordStudentApi.getFaceRecordStudentPageListByCondition(conditionBo);
    }

    /**
     * 人脸学生批量更新
     *
     * @param faceRecordStudentBoList
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/1 11:51
     */
    @AccessTokenAuth
    @PostMapping("/student/update/batch")
    public AjaxResult updateFaceRecordStudentBatch(@RequestBody List<FaceRecordStudentBo> faceRecordStudentBoList) {
        return faceRecordStudentApi.updateFaceRecordStudentBatch(faceRecordStudentBoList);
    }

    /**
     * 搜索阿里人脸信息（暂未使用）
     *
     * @param faceRecordTeacherConditionBo the face record teacher condition bo
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2022 /12/1 11:31
     */
    @AccessTokenAuth
    @PostMapping("/ali-face/search")
    @ApiOperation(value = "查看人脸数据列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult searchAliFace(@RequestBody FaceRecordTeacherConditionBo faceRecordTeacherConditionBo) {
        if (StringUtils.isBlank(faceRecordTeacherConditionBo.getImageUrl())) {
            return AjaxResult.fail("未捕捉到人脸信息");
        }
        JwtModel jwtModel = JwtContextHandler.get();
        faceRecordTeacherConditionBo.setOrganizationId(jwtModel.getOrganizationId());
        return faceRecordTeacherApi.searchAliFace(faceRecordTeacherConditionBo);
    }
}
