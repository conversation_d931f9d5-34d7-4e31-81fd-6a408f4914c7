package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2023-08-25 14:40
 */
@RestController
@RequestMapping("/leave-school-scene")
@Slf4j
@Api(value = "放学场景", tags = "放学场景")
public class LeaveSchoolController {
    @Resource
    private LeaveSchoolConfigApi leaveSchoolConfigApi;

    /**
     * 班级列表
     *
     * @param clazzConditionBoExt
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/25 16:18
     **/
    @PostMapping("/clazz-list")
    @ApiOperation(value = "班级列表", notes = "班级列表")
    public AjaxResult listClazz(@RequestBody ClazzConditionBoExt clazzConditionBoExt) {
        // 组织id为空，返回空数组
        if (clazzConditionBoExt == null || clazzConditionBoExt.getOrganizationId() == null) {
            return AjaxResult.success(new ArrayList<>());
        }
        return leaveSchoolConfigApi.getClazzList(clazzConditionBoExt);
    }
}
