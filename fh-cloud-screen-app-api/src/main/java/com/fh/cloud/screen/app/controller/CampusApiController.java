package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.service.campus.entity.bo.CampusListConditionBo;
import com.fh.cloud.screen.service.campus.service.CampusScreenApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/5/13 2:24 下午 @description：
 */
@Slf4j
@RestController
@Api(tags = "校区接口")
@RequestMapping("campus")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class CampusApiController {

    private final CampusScreenApiService campusApiService;

    @PostMapping("list")
    @ApiOperation("校区列表")
    public AjaxResult list(@RequestBody CampusListConditionBo bo) {
        return campusApiService.getCampusByCondition(bo);
    }
}
