package com.fh.cloud.screen.app.controller;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.fh.cloud.screen.app.enums.AttendanceMethodType;
import com.fh.cloud.screen.app.utils.ListKit;
import com.fh.cloud.screen.app.utils.StringKit;
import com.fh.cloud.screen.service.attendance.api.AttendanceRuleApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceLogBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceLogCensusVo;
import com.fh.cloud.screen.service.attendance.service.AttendanceLogApiService;
import com.light.core.entity.AjaxResult;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "考勤流水")
@RestController
@RequestMapping("/attendance-log")
@Slf4j
public class AttendanceLogController {

    @Resource
    private AttendanceLogApiService attendanceLogApiService;
    @Resource
    private AttendanceRuleApi attendanceRuleApi;

    @Value("${not.support.attendance.card.orgs:}")
    private String notSupportAttendanceCardOrgs;

    /**
     * 新增考勤流水表，不用于业务查询
     */
    @AccessTokenAuth
    @PostMapping("/add")
    @ApiOperation(value = "新增考勤流水表，不用于业务查询", httpMethod = "POST")
    public AjaxResult addAttendanceLog(@Validated @RequestBody AttendanceLogBo attendanceLogBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        attendanceLogBo.setOrganizationId(jwtModel.getOrganizationId());
        // 增加不允许打卡的机构校验
        if (isNotSupportAttendanceCardOrgs(jwtModel.getOrganizationId())
            && attendanceLogBo.getAttendanceMethod() != null
            && attendanceLogBo.getAttendanceMethod() == AttendanceMethodType.CARD.getValue()) {
            return AjaxResult.fail("当前机构不支持打卡方式考勤");
        }
        return attendanceLogApiService.addAttendanceLog(attendanceLogBo);
    }

    /**
     * 班级所有学生 当前考勤状况
     *
     * @param cacheKey the cache key
     * @return clock student census
     * <AUTHOR>
     * @update sunqingbiao
     * @date 2023 -08-26 14:51:44
     */
    @AccessTokenAuth
    @GetMapping("/clock-student-census")
    @ApiOperation(value = "学生打卡统计", httpMethod = "GET")
    public AjaxResult<AttendanceDayCensusVo> getClockStudentCensus() {
        JwtModel jwtModel = JwtContextHandler.get();
        return attendanceLogApiService.getClockStudentCensusByClassesId(jwtModel.getOrganizationId(),
            jwtModel.getSpaceInfoId());
    }

    /**
     * 班级所有学生历史考勤信息
     *
     * @param classesId the classesId 班级ID
     * @param dateTime 时间必须传今天之前的时间，默认是昨天
     * @return student current day info by classes id
     * <AUTHOR>
     * @update sunqingbiao
     * @date 2023 -08-26 14:00:36
     */
    @AccessTokenAuth
    @GetMapping("/student-census-class")
    @ApiOperation(value = "班级所有学生历史考勤信息", httpMethod = "GET")
    public AjaxResult<AttendanceLogCensusVo>
        getStudentCurrentDayInfoByClassesId(@RequestParam("classesId") Long classesId, String dateTime) {
        if (dateTime == null) {
            dateTime = DateUtil.formatDateTime(DateUtil.yesterday());
        }
        dateTime = DateUtil.formatDate(DateUtil.parseDateTime(dateTime));
        return attendanceLogApiService.getStudentCensusByClassesId(classesId, dateTime);
    }

    /**
     * 学校所有老师当天打卡统计信息。暂时废弃，云屏设备上面暂时不显示教师打卡统计
     *
     * @return
     */
    @Deprecated
    @AccessTokenAuth
    @GetMapping("/teacher-census-org")
    @ApiOperation(value = "老师打卡统计", httpMethod = "GET")
    public AjaxResult<AttendanceLogCensusVo> getTeacherCurrentDayInfoByOrgId(@RequestParam("signKey") String cacheKey) {
        JwtModel jwtModel = JwtContextHandler.get();
        return attendanceLogApiService.getClockTeacherCensusByOrgId(jwtModel.getOrganizationId(), cacheKey);
    }

    /**
     * app查询缓存中的考勤规则
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/7/5 9:43
     */
    @AccessTokenAuth
    @PostMapping("/rule-info")
    @ApiOperation(value = "app查询缓存中的考勤规则", httpMethod = "POST")
    public AjaxResult getAttendanceRule(@RequestBody AttendanceRuleBo attendanceRuleBo) {
        JwtModel jwtModel = JwtContextHandler.get();
        attendanceRuleBo.setOrganizationId(jwtModel.getOrganizationId());
        return attendanceRuleApi.getInfo(attendanceRuleBo);
    }

    /**
     * 是否是不支持考勤卡的机构
     * 
     * @param orgId
     * @return 是不支持的机构返回true，不是不支持的机构返回false
     */
    private boolean isNotSupportAttendanceCardOrgs(Long orgId) {
        if (StringUtils.isBlank(notSupportAttendanceCardOrgs) || orgId == null) {
            return false;
        }
        String orgIdStr = String.valueOf(orgId);
        List<String> notSupportAttendanceCardOrgsList = StringKit.splitString2List(notSupportAttendanceCardOrgs, null);
        return notSupportAttendanceCardOrgsList.contains(orgIdStr);
    }

}
