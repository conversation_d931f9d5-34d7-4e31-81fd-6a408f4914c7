package com.fh.cloud.screen.app.controller;

import com.alibaba.fastjson.JSONObject;
import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarApi;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarDayApi;
import com.fh.cloud.screen.service.calendar.api.SchoolCalendarWeekApi;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarDayListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.bo.SchoolCalendarWeekListConditionBo;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarVo;
import com.fh.cloud.screen.service.device.api.ShowDeviceApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/5/10
 */
@Api(tags = "校历接口")
@RequestMapping("/calendar")
@RestController
public class CalendarController {

    @Resource
    private SchoolCalendarApi schoolCalendarApi;

    @Resource
    private SchoolCalendarWeekApi schoolCalendarWeekApi;

    @Resource
    private SchoolCalendarDayApi schoolCalendarDayApi;

    @Resource
    private ShowDeviceApi showDeviceApi;

    /**
     * 查询校历全部列表
     *
     * @param deviceNumBer,version
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/6 17:51
     */
    @AccessTokenAuth
    @GetMapping("/list")
    @ApiOperation(value = "查询校历所有列表", httpMethod = "GET")
    public AjaxResult getSchoolCalendarWeekListByCondition(String version) {
        JwtModel jwtModel = JwtContextHandler.get();
        Long organizationId = jwtModel.getOrganizationId();

        Map<String, Object> map = new HashMap<>(4);
        // map.put("schoolCalendarVo", null);
        map.put("schoolCalendarWeekList", null);
        map.put("schoolCalendarDayList", null);
        AjaxResult detailResult = schoolCalendarApi.getDetail(organizationId);
        if (detailResult.isSuccess()) {
            SchoolCalendarVo schoolCalendarVo =
                JSONObject.parseObject(JSONObject.toJSONString(detailResult.getData()), SchoolCalendarVo.class);

            SchoolCalendarWeekListConditionBo condition = new SchoolCalendarWeekListConditionBo();
            condition.setSchoolCalendarId(schoolCalendarVo.getSchoolCalendarId());
            condition.setOrganizationId(organizationId);
            AjaxResult weekListResult = schoolCalendarWeekApi.getSchoolCalendarWeekListByCondition(condition);
            if (weekListResult.isSuccess()) {
                map.put("schoolCalendarWeekList",
                    ((Map<String, Object>)weekListResult.getData()).get("schoolCalendarWeekList"));
            }
            SchoolCalendarDayListConditionBo conditionBo = new SchoolCalendarDayListConditionBo();
            conditionBo.setSchoolCalendarId(schoolCalendarVo.getSchoolCalendarId());
            conditionBo.setOrganizationId(organizationId);
            AjaxResult dayListResult = schoolCalendarDayApi.getSchoolCalendarDayListByCondition(conditionBo);
            if (dayListResult.isSuccess()) {
                map.put("schoolCalendarDayList",
                    ((Map<String, Object>)dayListResult.getData()).get("schoolCalendarDayList"));
            }
        }
        // 更新设备版本
        if (StringUtils.isNotBlank(version)){
            showDeviceApi.updateVersionByDeviceNumber(jwtModel.getDeviceNumber(), version);
        }

        return AjaxResult.success(map);
    }

}
