package com.fh.cloud.screen.app.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.syllabus.api.SyllabusInfoApi;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 课表接口
 * 
 * <AUTHOR>
 * @date 2023/09/19
 */
@Api(tags = "课表接口")
@RestController
@RequestMapping("/syllabus-info")
public class SyllabusInfoController {
    @Resource
    private SyllabusInfoApi syllabusInfoApi;

    /**
     * 获取班级课表
     *
     * @param conditionBo 参数：organizationId,classesId
     * @return the classes syllabus info
     * <AUTHOR>
     * @date 2023 -09-19 15:41:10
     */
    @AccessTokenAuth
    @ApiOperation(value = "获取班级课表", httpMethod = "POST")
    @PostMapping("/classes")
    public AjaxResult getClassesSyllabusInfo() {
        JwtModel jwtModel = JwtContextHandler.get();
        SyllabusInfoConditionBo conditionBo = new SyllabusInfoConditionBo();
        conditionBo.setOrganizationId(jwtModel.getOrganizationId());
        conditionBo.setClassesId(jwtModel.getSpaceInfoId());
        return syllabusInfoApi.getSyllabusInfoOfClasses(conditionBo);
    }
}
