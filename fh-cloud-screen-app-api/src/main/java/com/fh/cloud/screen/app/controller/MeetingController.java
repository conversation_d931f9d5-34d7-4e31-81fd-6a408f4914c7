package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingUserBo;
import com.light.swagger.constants.SwaggerConstant;
import io.swagger.annotations.ApiImplicitParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingConditionBo;
import com.fh.cloud.screen.service.meeting.service.MeetingApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 会议表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-08-16 17:51:00
 */
@RestController
@Validated
@RequestMapping("meeting")
@Api(value = "", tags = "会议表接口")
public class MeetingController {

    @Autowired
    private MeetingApiService meetingApiService;

    /**
     * 获取当前及下一个会议（当天)
     * 
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/now-meeting")
    @AccessTokenAuth
    @ApiOperation(value = "获取当前及下一个会议", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getMeetingPageListByCondition(@RequestBody MeetingConditionBo condition) {
        JwtModel jwtModel = JwtContextHandler.get();
        condition.setSpaceGroupUseType(jwtModel.getSpaceGroupUseType());
        condition.setSpaceInfoId(jwtModel.getSpaceInfoId());
        return meetingApiService.getNowAndNextMeeting(condition);
    }

    /**
     * 结束会议
     *
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/update")
    @ApiOperation(value = "结束会议", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateMeeting(@RequestBody MeetingBo meetingBo) {
        return meetingApiService.updateMeeting(meetingBo);
    }

    /**
     * 会议签到
     *
     * @param meetingUserBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-08-16 17:51:00
     */
    @PostMapping("/sign")
    @ApiOperation(value = "会议签到", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult signIn(@Validated @RequestBody MeetingUserBo meetingUserBo) {
        return meetingApiService.signIn(meetingUserBo);
    }

    /**
     * 获取会议及与会人员详情-缓存
     *
     * @param meetingId 会议主键
     * @return 会议-缓存
     * <AUTHOR>
     * @date 2023/4/10 16:44
     */
    @GetMapping("/detail-cache")
    @ApiOperation(value = "查询会议表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "meetingId", value = "会议表meetingId", required = true,
        dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult getDetailCacheByMeetingId(@RequestParam("meetingId") Long meetingId) {
        if (null == meetingId) {
            return AjaxResult.fail("会议表id不能为空");
        }
        return meetingApiService.getDetailCacheByMeetingId(meetingId);
    }

}
