package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.app.utils.DateKit;
import com.fh.cloud.screen.service.device.api.ShowDeviceCaptureApi;
import com.fh.cloud.screen.service.device.api.ShowDeviceLogApi;
import com.fh.cloud.screen.service.device.api.ShowDeviceSwitchApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceCaptureBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLogBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.device.service.ShowDeviceApiService;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/6 2:19 下午 @description：
 */
@Slf4j
@RestController
@Api(tags = "设备接口")
@RequestMapping("device")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class ShowDeviceApiController {

    private final ShowDeviceApiService showDeviceApiService;
    private final ShowDeviceSwitchApi showDeviceSwitchApi;
    private final ShowDeviceCaptureApi showDeviceCaptureApi;
    private final ShowDeviceLogApi showDeviceLogApi;

    @ApiOperation("设备激活")
    @PostMapping("/activate")
    public AjaxResult<ShowDeviceVo> activate(@RequestBody ShowDeviceBo bo) {
        return this.showDeviceApiService.activate(bo);
    }

    @AccessTokenAuth
    @ApiOperation("更新设备状态")
    @PostMapping("/status/{status}")
    public AjaxResult status(@PathVariable("status") Integer status) {
        final JwtModel jwtModel = JwtContextHandler.get();
        final String deviceNumber = jwtModel.getDeviceNumber();
        return this.showDeviceApiService.updateStatusByDeviceNum(deviceNumber, status);
    }

    @AccessTokenAuth
    @ApiOperation("更改设备模式")
    @PostMapping("/changePattern/{pattern}")
    public AjaxResult changePattern(@PathVariable("pattern") Integer pattern) {
        final JwtModel jwtModel = JwtContextHandler.get();
        final String deviceNumber = jwtModel.getDeviceNumber();
        return this.showDeviceApiService.changePattern(deviceNumber, pattern);
    }

    /**
     * 查询一个学校/校区的开关机设置规则
     *
     * @param organizationId 学校id
     * @param campusId 校区id
     * @param nowDay 是否只获取今天
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -06-09 10:38:36
     */
    @ApiOperation("开关机设置规则")
    @PostMapping("/switch-list")
    public AjaxResult<ShowDeviceVo> switchList(@RequestParam("organizationId") Long organizationId, Long campusId,
        Boolean nowDay) {
        if (nowDay == null) {
            nowDay = false;
        }
        ShowDeviceSwitchListConditionBo conditionBo = new ShowDeviceSwitchListConditionBo();
        conditionBo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        conditionBo.setPageNo(SystemConstants.NO_PAGE);
        conditionBo.setOrganizationId(organizationId);
        conditionBo.setCampusId(campusId);
        if (nowDay) {
            conditionBo.setWeek(DateKit.getWeekIdByDate(new Date()));
        }
        return this.showDeviceSwitchApi.getShowDeviceSwitchListByCondition(conditionBo);
    }

    @AccessTokenAuth
    @ApiOperation("修改设备全屏模式")
    @PostMapping("/full")
    public AjaxResult<ShowDeviceVo> full(@RequestParam("deviceFullType") Integer deviceFullType) {
        final JwtModel jwtModel = JwtContextHandler.get();
        final String deviceNumber = jwtModel.getDeviceNumber();
        ShowDeviceBo bo = new ShowDeviceBo();
        bo.setDeviceNumber(deviceNumber);
        bo.setDeviceFullType(deviceFullType);
        return this.showDeviceApiService.full(bo);
    }

    @AccessTokenAuth
    @ApiOperation("修改虹软激活码")
    @PostMapping("/arc-code")
    public AjaxResult<ShowDeviceVo> arcCode(@RequestParam("arcsoftFaceCode") String arcsoftFaceCode) {
        final JwtModel jwtModel = JwtContextHandler.get();
        final String deviceNumber = jwtModel.getDeviceNumber();
        ShowDeviceBo bo = new ShowDeviceBo();
        bo.setDeviceNumber(deviceNumber);
        bo.setArcsoftFaceCode(arcsoftFaceCode);
        return this.showDeviceApiService.arcCode(bo);
    }

    /**
     * 根据设备号获取设备详情
     *
     * @param deviceNumber
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/11/4 11:53
     */
    @AccessTokenAuth
    @GetMapping("/about")
    @ApiOperation("根据设备号获取设备详情")
    public AjaxResult aboutByDeviceNumber(@RequestParam("deviceNumber") String deviceNumber) {
        if (StringUtils.isBlank(deviceNumber)) {
            final JwtModel jwtModel = JwtContextHandler.get();
            deviceNumber = jwtModel.getDeviceNumber();
        }
        return this.showDeviceApiService.aboutByDeviceNum(deviceNumber);
    }

    /**
     * 获取云屏设备二维码内容：该内容已拼接跳转地址
     *
     * @param f the f
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -12-20 16:29:09
     */
    @AccessTokenAuth
    @ApiOperation("获取云屏设备二维码的内容")
    @PostMapping("/qrcode/content")
    public AjaxResult<String> qrcodeContent(String f) {
        final JwtModel jwtModel = JwtContextHandler.get();
        ScreenBusinessBo screenBusinessBo = JwtModel.convert2ScreenBusinessBo(jwtModel);
        screenBusinessBo.setF(f);
        return this.showDeviceApiService.qrcodeContent(screenBusinessBo);
    }

    /**
     * 提交设备截图,参数只需要：deviceCaptureFileOid和deviceCaptureMediaUrl
     *
     * @param showDeviceCaptureBo the show device capture bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -12-20 16:31:03
     */
    @AccessTokenAuth
    @ApiOperation("提交设备截图")
    @PostMapping("/capture")
    public AjaxResult<String> capture(@RequestBody ShowDeviceCaptureBo showDeviceCaptureBo) {
        final JwtModel jwtModel = JwtContextHandler.get();
        showDeviceCaptureBo.setDeviceNumber(jwtModel.getDeviceNumber());
        showDeviceCaptureBo.setOrganizationId(jwtModel.getOrganizationId());
        return this.showDeviceCaptureApi.uploadShowDeviceCapture(showDeviceCaptureBo);
    }

    /**
     * 提交设备日志,参数只需要：deviceLogFileOid和deviceLogMediaUrl
     *
     * @param showDeviceLogBo the show device log bo
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -12-20 16:31:03
     */
    @AccessTokenAuth
    @ApiOperation("提交设备日志")
    @PostMapping("/log")
    public AjaxResult<String> deviceLog(@RequestBody ShowDeviceLogBo showDeviceLogBo) {
        final JwtModel jwtModel = JwtContextHandler.get();
        showDeviceLogBo.setDeviceNumber(jwtModel.getDeviceNumber());
        showDeviceLogBo.setOrganizationId(jwtModel.getOrganizationId());
        return this.showDeviceLogApi.uploadShowDeviceLog(showDeviceLogBo);
    }

}
