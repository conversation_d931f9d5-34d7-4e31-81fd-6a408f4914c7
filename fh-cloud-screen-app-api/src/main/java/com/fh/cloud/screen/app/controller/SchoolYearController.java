package com.fh.cloud.screen.app.controller;

import com.fh.cloud.screen.app.jwt.ano.AccessTokenAuth;
import com.fh.cloud.screen.app.jwt.local.JwtContextHandler;
import com.fh.cloud.screen.app.jwt.model.JwtModel;
import com.fh.cloud.screen.service.schoolyear.api.SchoolYearApi;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.bo.OrganizationTermBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 学年信息查询
 *
 * <AUTHOR>
 * @date 2022 /4/11 21:22
 */
@RestController
@RequestMapping("/school-year")
@Api(value = "学年信息查询", tags = "学年信息查询")
public class SchoolYearController {
    @Resource
    private SchoolYearApi schoolYearApi;

    /**
     * 查看学期列表
     *
     * @param organizationTermBo the organization term bo
     * @return the ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查看学期列表", httpMethod = "POST")
    @AccessTokenAuth
    @RequestMapping(value = "/term-list", method = RequestMethod.POST)
    public AjaxResult listTerm() {
        JwtModel jwtModel = JwtContextHandler.get();
        OrganizationTermBo organizationTermBo = new OrganizationTermBo();
        organizationTermBo.setOrganizationId(jwtModel.getOrganizationId());
        return schoolYearApi.listTerm(organizationTermBo);
    }
}
