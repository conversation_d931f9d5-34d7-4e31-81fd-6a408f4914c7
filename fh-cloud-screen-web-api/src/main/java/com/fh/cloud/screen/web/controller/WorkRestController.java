package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.rest.api.WorkRestApi;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestBo;
import com.fh.cloud.screen.service.rest.entity.bo.WorkRestListConditionBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 作息时间管理等controller
 *
 * <AUTHOR>
 * @date 2022/8/16 15:50
 */
@RestController
@RequestMapping("/rest")
@Api(value = "作息时间管理", tags = "作息时间管理")
public class WorkRestController {

    @Resource
    private WorkRestApi workRestApi;

    /**
     * 查询作息时间列表
     * 
     * @param workRestListConditionBo
     * @return
     */
    @ApiOperation(value = "查询作息时间列表", httpMethod = "POST")
    @PostMapping("/list")
    public AjaxResult listWorkRest(@RequestBody WorkRestListConditionBo workRestListConditionBo) {
        return workRestApi.getWorkRestListByCondition(workRestListConditionBo);
    }

    /**
     * 删除作息时间
     * 
     * @param workRestId
     * @return
     */
    @ApiOperation(value = "删除作息时间", httpMethod = "GET")
    @GetMapping("/delete")
    public AjaxResult deleteWorkRest(@RequestParam("workRestId") Long workRestId) {
        return workRestApi.delete(workRestId);
    }

    /**
     * 保存作息时间(新增或者更新)
     *
     * @param workRestBo the work rest bo
     * @return ajax result
     */
    @ApiOperation(value = "保存作息时间", httpMethod = "POST")
    @PostMapping("/save")
    public AjaxResult saveWorkRest(@RequestBody WorkRestBo workRestBo) {
        return workRestApi.saveWorkRestWithDetail(workRestBo);
    }

    /**
     * 修改status只需要如下参数：{"organizationId":1,"workRestId:","status":1}
     *
     * @param workRestBo the work rest bo
     * @return ajax result
     */
    @ApiOperation(value = "修改作息时间状态", httpMethod = "POST")
    @PostMapping("/status")
    public AjaxResult changeStatus(@RequestBody WorkRestBo workRestBo) {
        return workRestApi.changeStatus(workRestBo);
    }
}
