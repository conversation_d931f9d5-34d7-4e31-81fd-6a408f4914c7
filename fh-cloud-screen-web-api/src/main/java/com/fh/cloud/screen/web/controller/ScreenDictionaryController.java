package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.common.api.ScreenDictionaryDataApi;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云屏字典数据接口（db在云屏业务库）
 *
 * <AUTHOR>
 * @date 2022/5/30 17:42
 */
@RestController
@RequestMapping("/screen-dictionary")
@Api(value = "云屏字典管理", tags = "云屏字典管理")
public class ScreenDictionaryController {
    @Resource
    private ScreenDictionaryDataApi screenDictionaryDataApi;

    /**
     * 查询字段数据列表
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询字典数据列表", httpMethod = "POST")
    @RequestMapping(value = "/data-list", method = RequestMethod.POST)
    public AjaxResult listDictionaryDataListByCondition(@RequestBody DictionaryDataListConditionBo conditionBo)
        throws Exception {
        return screenDictionaryDataApi.getDictionaryDataListByCondition(conditionBo);
    }
}
