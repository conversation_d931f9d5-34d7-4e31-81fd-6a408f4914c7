package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.screen.api.ScreenSceneAuditApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenSceneAuditConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 云屏场景审核管理
 *
 * <AUTHOR>
 * @date 2023-11-30 14:49
 */
@RestController
@RequestMapping("/screen/scene/audit")
@Slf4j
@Api(value = "云屏场景审核管理", tags = "云屏场景审核管理")
public class ScreenSceneAuditController {
    @Resource
    private ScreenSceneAuditApi screenSceneAuditApi;

    /**
     * 云屏场景审核列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 14:53
     **/
    @PostMapping("/audit-list")
    @ApiOperation(value = "云屏场景审核列表")
    public AjaxResult getScreenSceneAuditList(@RequestBody ScreenSceneAuditConditionBo condition) {
        return screenSceneAuditApi.getScreenSceneAuditListByCondition(condition);
    }

    /**
     * 审核
     *
     * @param bo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 14:55
     **/
    @PostMapping("/update-audit")
    @ApiOperation(value = "审核")
    public AjaxResult screenSceneAudit(@RequestBody ScreenSceneAuditBo bo) {
        return screenSceneAuditApi.screenSceneAudit(bo);
    }

    /**
     * 批量审核
     *
     * @param bo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 15:22
     **/
    @PostMapping("/update-audit-batch")
    @ApiOperation(value = "批量审核")
    public AjaxResult screenSceneAuditBatch(@RequestBody ScreenSceneAuditBo bo) {
        return screenSceneAuditApi.screenSceneAuditBatch(bo);
    }

    /**
     * 待发布云屏数据
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/11/30 16:11
     **/
    @GetMapping("/screen-to-audit")
    @ApiOperation(value = "待发布云屏数据")
    public AjaxResult screenSceneAuditDetail(@RequestParam("screenSceneAuditId") Long screenSceneAuditId) {
        return screenSceneAuditApi.screenToAudit(screenSceneAuditId);
    }

    /**
     * 获取审核数量
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/1 9:24
     **/
    @PostMapping("/audit-count")
    public AjaxResult screenSceneAuditCount(@RequestBody ScreenSceneAuditConditionBo condition) {
        return screenSceneAuditApi.getScreenSceneAuditCount(condition);
    }

}
