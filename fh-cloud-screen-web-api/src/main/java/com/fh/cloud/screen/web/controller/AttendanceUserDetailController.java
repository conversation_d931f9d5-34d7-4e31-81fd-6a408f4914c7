package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserDetailVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.attendance.service.AttendanceUserApiService;
import com.fh.cloud.screen.service.attendance.service.AttendanceUserDetailApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "考勤记录详情管理")
@RestController
@RequestMapping("/attendance-user-detail")
@Slf4j
public class AttendanceUserDetailController {

    @Resource
    private AttendanceUserDetailApiService attendanceUserDetailApiService;

    /**
     * 根据 考勤记录获取 考勤详情列表
     * 
     * @param attendanceUserId
     * @return
     */
    @ApiOperation("根据考勤记录获取考勤详情列表")
    @PostMapping("/listByAttendanceUserId/{attendanceUserId}")
    public AjaxResult<List<AttendanceUserDetailVo>>
        getListByAttendanceUserId(@PathVariable("attendanceUserId") Long attendanceUserId) {
        return this.attendanceUserDetailApiService.getListByAttendanceUserId(attendanceUserId);
    }

}
