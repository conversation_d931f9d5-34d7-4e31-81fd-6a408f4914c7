package com.fh.cloud.screen.web.controller;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fh.cloud.screen.service.attendance.api.AttendanceLogApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceUserListConditionBo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceDayCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserAllDayVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserCensusVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserResultVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserShowVo;
import com.fh.cloud.screen.service.attendance.entity.vo.AttendanceUserVo;
import com.fh.cloud.screen.service.attendance.service.AttendanceUserApiService;
import com.fh.cloud.screen.service.calendar.entity.vo.SchoolCalendarDayOfMonthVo;
import com.fh.cloud.screen.service.calendar.service.SchoolCalendarApiService;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardExportVo;
import com.fh.cloud.screen.web.enums.AttendanceUserTypeEnums;
import com.fh.cloud.screen.web.utils.EasyPoiUtil;
import com.light.core.entity.AjaxResult;
import com.light.redis.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "考勤记录管理")
@RestController
@RequestMapping("/attendance-user")
@Slf4j
public class AttendanceUserController {

    @Resource
    private AttendanceUserApiService attendanceUserApiService;
    @Resource
    private SchoolCalendarApiService schoolCalendarApiService;
    @Resource
    private AttendanceLogApi attendanceLogApi;

    /**
     * 条件查询 日期必填
     * 
     * @param condition
     * @return
     */
    @PostMapping("list-date")
    @ApiOperation(value = "日期条件查询用户考勤信息", httpMethod = "POST")
    public AjaxResult<AttendanceUserCensusVo>
        getListByDateCondition(@RequestBody AttendanceUserListConditionBo condition) {
        return attendanceUserApiService.getListByDateCondition(condition);
    }

    /**
     * 日期条件导出用户考勤信息
     *
     * @param condition
     * @return
     */
    @PostMapping("/list-date/export")
    @ApiOperation(value = "日期条件导出用户考勤信息", httpMethod = "POST")
    public void exportListByDateCondition(@RequestBody AttendanceUserListConditionBo condition,
        HttpServletResponse response) throws IOException {
        Integer attendanceType = condition.getAttendanceType();
        AjaxResult<AttendanceUserResultVo> ajaxResult =
            attendanceUserApiService.getListExportByDateCondition(condition);
        AttendanceUserResultVo data = ajaxResult.getData();
        if (ajaxResult.isFail() || data == null) {
            return;
        }
        List<AttendanceUserAllDayVo> attendanceUserAllDayVos = data.getAttendanceUserAllDayVos();

        // 封装三个sheet页数据
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attendanceUserAllDayVos)) {
            for (AttendanceUserAllDayVo attendanceUserAllDayVo : attendanceUserAllDayVos) {
                // 创建参数对象（用来设定excel得sheet得内容等信息）
                ExportParams deptExportParams = new ExportParams();
                // 设置sheet得名称
                deptExportParams.setSheetName("第" + attendanceUserAllDayVo.getAttendanceRuleDayIndex() + "阶段签到");
                // 设置sheet表头名称
                // deptExportParams.setTitle("第" + attendanceUserAllDayVo.getAttendanceRuleDayIndex() + "阶段签到");
                // 创建sheet1使用得map
                Map<String, Object> deptExportMap = new HashMap<>();
                // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
                deptExportMap.put("title", deptExportParams);
                // 模版导出对应得实体类型
                deptExportMap.put("entity", AttendanceUserShowVo.class);
                // sheet中要填充得数据
                deptExportMap.put("data", attendanceUserAllDayVo.getAttendanceUserShowVos());
                // 根据导出类型隐藏字段（注意：需要展示的必须设置为false，因为设置为true会影响到后面请求的数据）
                for (AttendanceUserShowVo attendanceUserShowVo : attendanceUserAllDayVo.getAttendanceUserShowVos()) {
                    if (attendanceType != null && attendanceType.equals(AttendanceUserTypeEnums.TEACHER.getCode())) {
                        EasyPoiUtil<AttendanceUserShowVo> easyPoiUtil = new EasyPoiUtil<>();
                        easyPoiUtil.t = attendanceUserShowVo;
                        try {
                            easyPoiUtil.hihdColumn("classesName", true);
                        } catch (Exception e) {
                            log.error("列隐藏转换失败：{}", e.getMessage());
                        }
                    } else {
                        EasyPoiUtil<AttendanceUserShowVo> easyPoiUtil = new EasyPoiUtil<>();
                        easyPoiUtil.t = attendanceUserShowVo;
                        try {
                            easyPoiUtil.hihdColumn("classesName", false);
                        } catch (Exception e) {
                            log.error("列隐藏转换失败：{}", e.getMessage());
                        }
                    }
                }
                sheetsList.add(deptExportMap);
            }
        }
        if (CollectionUtil.isNotEmpty(sheetsList)) {
            ExcelUtils.defaultExport(sheetsList, "考勤信息统计", ExcelType.HSSF, response);
        } else {
            ExcelUtils.defaultExport(Collections.EMPTY_LIST, "考勤信息统计", ExcelType.HSSF, response);
        }
    }

    /**
     * 用户月份考勤信息
     * 
     * @param condition
     * @return
     */
    @PostMapping("user-day-map")
    @ApiOperation(value = "用户月份考勤信息", httpMethod = "POST")
    public AjaxResult list(@RequestBody AttendanceUserListConditionBo condition) {
        if (StrUtil.isEmpty(condition.getUserOid())) {
            return AjaxResult.fail("用户信息不能为空");
        }
        final String attendanceMonth = condition.getAttendanceMonth();
        if (StrUtil.isEmpty(attendanceMonth)) {
            return AjaxResult.fail("日期不能为空");
        }
        final Long organizationId = condition.getOrganizationId();

        // 获取数据
        final AjaxResult<List<AttendanceUserVo>> result = attendanceUserApiService.list(condition);
        final List<AttendanceUserVo> data = result.getData();
        if (result.isFail()) {
            return result;
        }

        // 获取学校校历信息
        AjaxResult<List<SchoolCalendarDayOfMonthVo>> schoolCalendarResult =
            schoolCalendarApiService.getDayInfoByMonthAndOrgId(attendanceMonth, organizationId);
        if (schoolCalendarResult.isFail()) {
            return AjaxResult.fail("查询失败");
        }

        // 校历数据处理
        final List<SchoolCalendarDayOfMonthVo> calendarDayOfMonthVos = schoolCalendarResult.getData();
        final Map<Integer, SchoolCalendarDayOfMonthVo> dayOfSchoolCalendarMap = calendarDayOfMonthVos.stream()
            .collect(Collectors.toMap(SchoolCalendarDayOfMonthVo::getDay, x -> x, (k1, k2) -> k2));

        // 数据转map key ： 日期 天 value：当前对象
        final Map<Integer, AttendanceUserVo> dayOfAttendanceUserMap =
            data.stream().collect(Collectors.toMap(this::buildDay, Function.identity(), (v1, v2) -> v1));

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("dayOfAttendanceUserMap", dayOfAttendanceUserMap);
        resultMap.put("dayOfSchoolCalendarMap", dayOfSchoolCalendarMap);
        return AjaxResult.success(resultMap);
    }

    /**
     * 构建日期 d key
     * 
     * @param vo
     * @return
     */
    private Integer buildDay(AttendanceUserVo vo) {
        final DateTime dateTime = DateUtil.parseDate(vo.getAttendanceDay());
        final int i = DateUtil.dayOfMonth(dateTime);
        return i;
    }

    /**
     * 更改考勤
     * 
     * @param bo
     * @return
     */
    @ApiOperation(value = "修改考勤状态", httpMethod = "POST")
    @PostMapping("changeState")
    public AjaxResult change(@RequestBody AttendanceUserBo bo) {
        return this.attendanceUserApiService.changeState(bo);
    }

    /**
     * 班级所有学生当天打卡统计
     *
     * @param cacheKey the cache key
     * @return clock student census
     * <AUTHOR>
     * @update sunqingbiao
     * @date 2023 -08-26 14:51:44
     */
    @GetMapping("/student-census")
    @ApiOperation(value = "班级所有学生当天打卡统计", httpMethod = "GET")
    public AjaxResult<AttendanceDayCensusVo> getClockStudentCensus(@RequestParam("organizationId") Long organizationId,
        @RequestParam("classesId") Long classesId) {
        return attendanceLogApi.getClockStudentCensusByClassesId(organizationId, classesId);
    }
}
