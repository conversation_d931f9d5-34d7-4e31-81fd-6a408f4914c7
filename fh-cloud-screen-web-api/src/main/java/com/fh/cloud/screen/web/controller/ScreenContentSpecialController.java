package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.screen.api.ScreenContentSpecialApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentSpecialListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云屏紧急发布内容controller
 *
 * <AUTHOR>
 * @date 2022 /5/7 17:05
 */
@RestController
@RequestMapping("/screen/content-special")
@Api(value = "云屏紧急发布内容管理", tags = "云屏紧急发布内容管理")
public class ScreenContentSpecialController {

    @Resource
    private ScreenContentSpecialApi screenContentSpecialApi;

    /**
     * 查询云屏紧急内容历史列表
     *
     * @param condition the condition
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏紧急内容历史列表", httpMethod = "POST")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public AjaxResult listContentSpecial(@RequestBody ScreenContentSpecialListConditionBo condition) throws Exception {
        condition.setSingleShow(true);
        return screenContentSpecialApi.getScreenContentSpecialListByCondition(condition);
    }

    /**
     * 保存紧急发布（新增或者修改）
     *
     * @param screenContentSpecialBo the screen content special bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -06-06 11:18:08
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存云屏紧急发布内容表", httpMethod = "POST")
    public AjaxResult addContentSpecial(@RequestBody ScreenContentSpecialBo screenContentSpecialBo) {
        return screenContentSpecialApi.saveScreenContentSpecial(screenContentSpecialBo);
    }

    /**
     * 紧急发布-撤回
     *
     * @param screenContentSpecialId the screen content special id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -06-06 11:19:10
     */
    @PostMapping("/cancel")
    @ApiOperation(value = "紧急发布撤回", httpMethod = "POST")
    public AjaxResult cancelSubmit(@RequestParam("screenContentSpecialId") Long screenContentSpecialId) {
        return screenContentSpecialApi.cancelSubmit(screenContentSpecialId);
    }

    /**
     * 紧急发布-查看
     *
     * @param screenContentSpecialId the screen content special id
     * @return the detail
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询云屏紧急发布内容表详情", httpMethod = "GET")
    public AjaxResult getDetail(@RequestParam("screenContentSpecialId") Long screenContentSpecialId) {
        return screenContentSpecialApi.getDetail(screenContentSpecialId);
    }

    /**
     * 紧急发布发布（需要：screenContentSpecialId和organizationId和campusId）
     *
     * @param screenContentSpecialBo the screen content special bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -06-06 11:19:10
     */
    @PostMapping("/submit")
    @ApiOperation(value = "紧急发布发布", httpMethod = "POST")
    public AjaxResult submit(@RequestBody ScreenContentSpecialBo screenContentSpecialBo) {
        return screenContentSpecialApi.submit(screenContentSpecialBo);
    }
}
