package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoBo;
import com.fh.cloud.screen.service.space.entity.bo.SpaceInfoListConditionBo;
import com.fh.cloud.screen.service.space.service.SpaceInfoApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/31
 */
@Api(tags = "空间管理接口")
@RestController
@RequestMapping("/space-info")
public class SpaceInfoController {

    @Resource
    private SpaceInfoApiService spaceInfoApiService;

    @PostMapping("/list")
    @ApiOperation(value = "查询非区域信息表列表", httpMethod = "POST")
    public AjaxResult getSpaceInfoListByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        return this.spaceInfoApiService.getSpaceInfoListByCondition(condition);
    }

    @ApiOperation(value = "新增区域信息表", httpMethod = "POST")
    @PostMapping("/add")
    public AjaxResult addSpaceInfo(@RequestBody SpaceInfoBo spaceInfoBo) {
        return this.spaceInfoApiService.addSpaceInfo(spaceInfoBo);
    }

    @ApiOperation(value = "修改区域信息表", httpMethod = "POST")
    @PostMapping("/update")
    public AjaxResult updateSpaceInfo(@RequestBody SpaceInfoBo spaceInfoBo) {
        return this.spaceInfoApiService.updateSpaceInfo(spaceInfoBo);
    }

    /**
     * 查询校区表列表
     */
    @GetMapping("/campus/list")
    @ApiOperation(value = "查询校区表列表", httpMethod = "GET")
    AjaxResult getCampusListByCondition(@RequestParam("organizationId") Long organizationId) {
        return this.spaceInfoApiService.getCampusListByCondition(organizationId);
    }

    /**
     * 删除区域信息表
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除区域信息表", httpMethod = "GET")
    AjaxResult delete(@RequestParam("spaceInfoId") Long spaceInfoId) {
        return this.spaceInfoApiService.delete(spaceInfoId);
    }

    /**
     * 查询区域设备数
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/8 13:52
     **/
    @PostMapping("/device-count")
    @ApiOperation(value = "查询区域设备数", httpMethod = "POST")
    public AjaxResult getSpaceInfoDeviceCountByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        return this.spaceInfoApiService.getSpaceInfoDeviceCountByCondition(condition);
    }

    /**
     * 查询空间设备列表（不包含没有设备的地点）  根据监管教育局id批量查询多校地点设备
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/7/31 15:22
     **/
    @PostMapping("/space-devices")
    @ApiOperation(value = "查询空间设备列表", httpMethod = "POST")
    public AjaxResult getSpaceDeviceListByCondition(@RequestBody SpaceInfoListConditionBo condition) {
        return this.spaceInfoApiService.getSpaceDeviceListByCondition(condition);
    }

    /**
     * 查询教育局监管学校设备地点
     *
     * @param parentOrganizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/2 9:51
     **/
    @ApiOperation(value = "查询教育局监管学校设备地点", httpMethod = "GET")
    @GetMapping("/supervise-space/by-parentId")
    public AjaxResult getSuperviseSpaceByParentId(@RequestParam("parentOrganizationId") Long parentOrganizationId) {
        return this.spaceInfoApiService.getSuperviseSpaceByParentId(parentOrganizationId);
    }
}
