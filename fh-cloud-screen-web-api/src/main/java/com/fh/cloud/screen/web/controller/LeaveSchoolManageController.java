package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolConfigApi;
import com.fh.cloud.screen.service.leaveschool.api.LeaveSchoolRecordApi;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolConfigConditionBo;
import com.fh.cloud.screen.service.leaveschool.entity.bo.LeaveSchoolRecordBo;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.vo.OrganizationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 放学管理
 *
 * <AUTHOR>
 * @date 2023-08-23 11:01
 */
@RestController
@RequestMapping("/leave-school/manage")
@Slf4j
@Api(value = "放学管理", tags = "放学管理")
public class LeaveSchoolManageController {
    @Resource
    private LeaveSchoolConfigApi leaveSchoolConfigApi;
    @Resource
    private LeaveSchoolRecordApi leaveSchoolRecordApi;
    @Value("${leaveSchoolIds:}")
    private String leaveSchoolIds;

    /**
     * 新增放学配置
     *
     * @param leaveSchoolConfigBo 
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 11:33
     **/
    @PostMapping("/add")
    @ApiOperation(value = "新增放学配置", notes = "新增放学配置")
    public AjaxResult addLeaveSchoolConfig(@RequestBody LeaveSchoolConfigBo leaveSchoolConfigBo) {
        return leaveSchoolConfigApi.addLeaveSchoolConfig(leaveSchoolConfigBo);
    }

    /**
     * 编辑放学配置
     *
     * @param leaveSchoolConfigBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 14:26
     **/
    @PostMapping("/update")
    @ApiOperation(value = "编辑放学配置", notes = "编辑放学配置")
    public AjaxResult updateLeaveSchoolConfig(@RequestBody LeaveSchoolConfigBo leaveSchoolConfigBo) {
        return leaveSchoolConfigApi.updateLeaveSchoolConfig(leaveSchoolConfigBo);
    }

    /**
     * 获取放学配置
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 14:34
     **/
    @PostMapping("/get-detail")
    @ApiOperation(value = "获取放学配置", notes = "获取放学配置")
    public AjaxResult getLeaveSchoolConfig(@RequestBody LeaveSchoolConfigConditionBo condition) {
        return leaveSchoolConfigApi.getLeaveSchoolConfig(condition);
    }

    /**
     * 获取班主任班级列表
     *
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 18:48
     **/
    @GetMapping("/teacher-classes")
    @ApiOperation(value = "获取班主任班级列表", notes = "获取班主任班级列表")
    public AjaxResult getTeacherClasses() {
        return leaveSchoolConfigApi.getTeacherClasses();
    }

    /**
     * 获取放学记录
     *
     * @param leaveSchoolRecordBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 18:59
     **/
    @PostMapping("/leave-school-record")
    @ApiOperation(value = "获取放学记录", notes = "获取放学记录")
    public AjaxResult getLeaveSchoolRecord(@RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo) {
        if (leaveSchoolRecordBo.getOrganizationId() == null) {
            return AjaxResult.fail("组织id不能为空");
        }
        if (leaveSchoolRecordBo.getSpaceInfoId() == null) {
            return AjaxResult.fail("区域id或班级id不能为空");
        }
        if (leaveSchoolRecordBo.getSpaceGroupUseType() == null) {
            return AjaxResult.fail("区域类型不能为空");
        }
        leaveSchoolRecordBo.setLeaveSchoolDay(leaveSchoolRecordBo.getLeaveSchoolDay() != null ? leaveSchoolRecordBo.getLeaveSchoolDay() : new Date());
        return leaveSchoolRecordApi.getLeaveSchoolRecord(leaveSchoolRecordBo);
    }

    /**
     * 更新放学状态
     *
     * @param leaveSchoolRecordBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/8/23 19:41
     **/
    @PostMapping("/leave-school-record/update")
    @ApiOperation(value = "更新放学状态", notes = "更新放学状态")
    public AjaxResult updateLeaveSchoolRecord(@RequestBody LeaveSchoolRecordBo leaveSchoolRecordBo) {
        return leaveSchoolRecordApi.updateLeaveSchoolRecord(leaveSchoolRecordBo);
    }

    /**
     * 获取开启放学的组织列表
     *
     * @param
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/9/12 9:30
     **/
    @GetMapping("/leave-school/organization-list")
    @ApiOperation("获取开启放学应用的组织列表")
    public AjaxResult getLeaveSchoolOrganizationList() {
        AjaxResult<List<OrganizationVo>> ajaxResult = leaveSchoolConfigApi.getLeaveSchoolOrganizationList();
        // 如果配置了学校id，只返回配置的学校列表，不配置返回全部开了放学的学校
        if (ajaxResult.isSuccess() && CollectionUtils.isNotEmpty(ajaxResult.getData()) && StringUtils.isNotBlank(leaveSchoolIds)) {
            List<String> schoolIdList = Stream.of(leaveSchoolIds.split(",")).collect(Collectors.toList());
            List<OrganizationVo> organizationVos = ajaxResult.getData().stream()
                    .filter(s -> schoolIdList.contains(s.getId().toString())).collect(Collectors.toList());
            ajaxResult.setData(organizationVos);
        }
        return ajaxResult;
    }
}
