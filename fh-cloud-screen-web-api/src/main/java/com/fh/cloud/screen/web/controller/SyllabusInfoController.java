package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.syllabus.api.SyllabusInfoApi;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoBo;
import com.fh.cloud.screen.service.syllabus.entity.bo.SyllabusInfoConditionBo;
import com.fh.cloud.screen.service.thirdinfo.api.ThirdInfoApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.applet.AppletContext;

/**
 * 课表接口
 * 
 * <AUTHOR>
 * @date 2023/09/19
 */
@Api(tags = "课表接口")
@RestController
@RequestMapping("/syllabus-info")
public class SyllabusInfoController {
    @Resource
    private SyllabusInfoApi syllabusInfoApi;
    @Resource
    private ThirdInfoApi thirdInfoApi;
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 获取班级课表
     *
     * @param conditionBo 参数：organizationId,classesId
     * @return the classes syllabus info
     * <AUTHOR>
     * @date 2023 -09-19 15:41:10
     */
    @ApiOperation(value = "获取班级课表", httpMethod = "POST")
    @PostMapping("/classes")
    public AjaxResult getClassesSyllabusInfo(@RequestBody SyllabusInfoConditionBo conditionBo) {
        return syllabusInfoApi.getSyllabusInfoOfClasses(conditionBo);
    }

    /**
     * 同步金中的课表(目前没有区别学校，实际上只有会同步金中课表数据)
     *
     * <AUTHOR>
     * @date 2023-09-18 15:16:07
     */
    @ApiOperation(value = "同步金中的课表", httpMethod = "POST")
    @PostMapping("/third/jz/sync")
    public AjaxResult<String> syncJz(@RequestBody SyllabusInfoBo syllabusInfoBo) {
        return thirdInfoApi.syncThirdSyllabusInfo(syllabusInfoBo);
    }
}
