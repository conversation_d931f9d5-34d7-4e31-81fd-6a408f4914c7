package com.fh.cloud.screen.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.fh.cloud.screen.service.card.api.UserCardApi;
import com.fh.cloud.screen.service.card.entity.bo.StudentCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.TeacherCardImportBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardBo;
import com.fh.cloud.screen.service.card.entity.bo.UserCardListConditionBo;
import com.fh.cloud.screen.service.card.entity.vo.StudentCardExportVo;
import com.fh.cloud.screen.service.card.entity.vo.TeacherCardExportVo;
import com.fh.cloud.screen.service.grade.api.GradeScreenApi;
import com.light.core.entity.AjaxResult;
import com.light.redis.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;

@Api(tags = "卡管理")
@RestController
@RequestMapping("/userCard")
@Validated
@Slf4j
public class UserCardController {

    @Resource
    private UserCardApi userCardApi;

    @Resource
    private GradeScreenApi gradeScreenApi;

    @ApiOperation(value = "查询学生卡列表", httpMethod = "POST")
    @PostMapping("/getStudentCardList")
    public AjaxResult getStudentCardList(@RequestBody UserCardListConditionBo conditionBo) {
        return userCardApi.getStudentCardList(conditionBo);
    }

    @ApiOperation(value = "查询教师卡列表", httpMethod = "POST")
    @PostMapping("/getTeacherCardList")
    public AjaxResult getTeacherCardList(@RequestBody UserCardListConditionBo conditionBo) {
        return userCardApi.getTeacherCardList(conditionBo);
    }

    @ApiOperation(value = "学生卡统计", httpMethod = "POST")
    @PostMapping("/getStudentCardCount")
    public AjaxResult getStudentCardCount(@RequestBody UserCardBo userCardBo) {
        return userCardApi.getStudentCardCount(userCardBo);
    }

    @ApiOperation(value = "教师卡统计", httpMethod = "POST")
    @PostMapping("/getTeacherCardCount")
    public AjaxResult getTeacherCardCount(@RequestBody UserCardBo userCardBo) {
        return userCardApi.getTeacherCardCount(userCardBo);
    }

    @ApiOperation(value = "查询用户卡详情", httpMethod = "GET")
    @GetMapping("/getDetail")
    public AjaxResult getDetail(@NotNull(message = "请选择用户") Long userCardId) {
        return userCardApi.getDetail(userCardId);
    }

    @ApiOperation(value = "编辑卡", httpMethod = "POST")
    @PostMapping("/updateCard")
    public AjaxResult updateCard(@RequestBody UserCardBo userCardBo) {
        return userCardApi.updateCard(userCardBo);
    }

    @ApiOperation(value = "绑卡", httpMethod = "POST")
    @PostMapping("/tiedCard")
    public AjaxResult tiedCard(@RequestBody UserCardBo userCardBo) {
        return userCardApi.tiedCard(userCardBo);
    }

    @ApiOperation(value = "解绑卡", httpMethod = "GET")
    @GetMapping("/unbindCard")
    public AjaxResult unbindCard(@NotNull(message = "请选择用户") Long userCardId) {
        return userCardApi.unbindCard(userCardId);
    }

    @ApiOperation(value = "批量解绑卡", httpMethod = "GET")
    @GetMapping("/batchUnbindCard")
    public AjaxResult batchUnbindCard(@NotBlank(message = "请选择用户") String userCardIds) {
        return userCardApi.batchUnbindCard(userCardIds);
    }

    @ApiOperation(value = "学生卡导出", httpMethod = "POST")
    @PostMapping("/studentCardExport")
    public void studentCardExport(@RequestBody UserCardBo userCardBo, HttpServletResponse response) throws IOException {
        List<StudentCardExportVo> exportList = userCardApi.studentCardExport(userCardBo);
        if (CollectionUtil.isNotEmpty(exportList)) {
            ExcelUtils.exportExcel(exportList, "导入学生卡", "导入学生卡", StudentCardExportVo.class, "导入学生卡", response);
        } else {
            ExcelUtils.exportExcel(Collections.EMPTY_LIST, "导入学生卡", "导入学生卡", StudentCardExportVo.class, "导入学生卡",
                response);
        }
    }

    @ApiOperation(value = "学生卡导入", httpMethod = "POST")
    @PostMapping("/studentCardImport")
    public AjaxResult studentCardImport(MultipartFile file, Long classesId) throws IOException {
        List<StudentCardImportBo> list =
            ExcelUtils.importExcelByDict(file.getInputStream(), 1, 1, StudentCardImportBo.class);
        UserCardBo userCardBo = new UserCardBo();
        userCardBo.setClassesId(classesId);
        userCardBo.setStudentCardImportList(list);
        return userCardApi.studentCardImport(userCardBo);
    }

    @ApiOperation(value = "教师卡导出", httpMethod = "POST")
    @PostMapping("/teacherCardExport")
    public void teacherCardExport(@RequestBody UserCardBo userCardBo, HttpServletResponse response) throws IOException {
        List<TeacherCardExportVo> exportList = userCardApi.teacherCardExport(userCardBo);
        if (CollectionUtil.isNotEmpty(exportList)) {
            ExcelUtils.exportExcel(exportList, "导入教师卡", "导入教师卡", TeacherCardExportVo.class, "导入教师卡", response);
        } else {
            ExcelUtils.exportExcel(Collections.EMPTY_LIST, "导入教师卡", "导入教师卡", TeacherCardExportVo.class, "导入教师卡",
                response);
        }
    }

    @ApiOperation(value = "教师卡导入", httpMethod = "POST")
    @PostMapping("/teacherCardImport")
    public AjaxResult teacherCardImport(MultipartFile file) throws IOException {
        List<TeacherCardImportBo> list =
            ExcelUtils.importExcelByDict(file.getInputStream(), 1, 1, TeacherCardImportBo.class);
        return userCardApi.teacherCardImport(list);
    }

    @ApiOperation(value = "下载教师卡导入模板", httpMethod = "GET")
    @GetMapping("/downloadTeacherTemplate")
    public void downloadTeacherTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            String teacherTemplateName = "教师卡导入模板" + ".xls";
            URI uri = new URI(null, null, teacherTemplateName, null);
            OutputStream out = response.getOutputStream();
            response.reset();
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-Disposition",
                "attachment; filename=" + uri.toASCIIString() + ";filename*=utf-8''" + uri.toASCIIString());
            response.setContentType("application/vnd.ms-excel");
            InputStream in = ResourceUtil.getStream("templates/teacher_import_template.xls");
            // 后续如果需要修改模板可以在这里改写流
            IoUtil.copy(in, out);
        } catch (SecurityException | IllegalArgumentException | URISyntaxException e) {
            log.error("/userCard/downloadTeacherTemplate error1:", e);
        } catch (Exception e) {
            log.error("/userCard/downloadTeacherTemplate error2:", e);
        }
    }

    /**
     * 当前学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查询当前学年", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/current", method = RequestMethod.GET)
    public AjaxResult currentYear() throws Exception {
        return gradeScreenApi.currentYear();
    }
}
