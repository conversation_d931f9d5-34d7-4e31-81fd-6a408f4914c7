package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.schoolyear.api.SchoolYearApi;
import com.light.core.entity.AjaxResult;
import com.light.user.organization.entity.bo.OrganizationTermBo;
import com.light.user.organization.entity.bo.OrganizationTermDelSaveBo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.text.ParseException;

/**
 * 学年信息查询
 *
 * <AUTHOR>
 * @date 2022 /4/11 21:22
 */
@RestController
@RequestMapping("/school-year")
@Api(value = "学年信息查询", tags = "学年信息查询")
public class SchoolYearController {
    @Resource
    private SchoolYearApi schoolYearApi;

    /**
     * years当前学年和下一学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:44:04
     */
    @ApiOperation(value = "查询当前学年和下一学年", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/years", method = RequestMethod.GET)
    public AjaxResult listSchoolYears() throws Exception {
        return schoolYearApi.listSchoolYears();
    }

    /**
     * 当前学年
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查询当前学年", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/current", method = RequestMethod.GET)
    public AjaxResult currentYear() throws Exception {
        return schoolYearApi.currentYear();
    }

    /**
     * 根据年级和学年获取入学年份
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "根据年级和学年获取入学年份", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/year", method = RequestMethod.GET)
    public AjaxResult getYear(@RequestParam(value = "grade") String grade,
        @RequestParam(value = "schoolYear") String schoolYear) throws Exception {
        return schoolYearApi.getYear(grade, schoolYear);
    }

    /**
     * 获取学年月日
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "学年开始月日", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/month-day", method = RequestMethod.GET)
    public AjaxResult getMonthDay() throws Exception {
        return schoolYearApi.getMonthDay();
    }

    /**
     * 保存学期设置
     *
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "保存学期设置", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/term-save", method = RequestMethod.POST)
    public AjaxResult saveTerm(@RequestBody OrganizationTermDelSaveBo organizationTermDelSaveBo) throws Exception {
        return schoolYearApi.saveTerm(organizationTermDelSaveBo);
    }

    /**
     * 查看学期列表
     *
     * @param organizationTermBo the organization term bo
     * @return the ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-11 21:47:04
     */
    @ApiOperation(value = "查看学期列表", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/term-list", method = RequestMethod.POST)
    public AjaxResult listTerm(@RequestBody OrganizationTermBo organizationTermBo) throws Exception {
        return schoolYearApi.listTerm(organizationTermBo);
    }

    /**
     * 查看当前月份的教学周
     * 
     * @param organizationId 组织id
     * @param month 要查看的月份
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "查看教学周", httpMethod = "POST")
    @RequestMapping(value = "/week-list", method = RequestMethod.GET)
    public AjaxResult getSchoolTeacherWeekByMonth(@RequestParam("organizationId") Long organizationId,
        @RequestParam("month") String month) throws ParseException {
        return schoolYearApi.getSchoolTeacherWeekByMonth(organizationId, month);
    }

}
