package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.face.api.FaceRecordStudentApi;
import com.fh.cloud.screen.service.face.api.FaceRecordStudentApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceCountQueryBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordStudentConditionBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordStudentVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 学生人脸库管理
 * 
 * <AUTHOR>
 * @date 2022/11/18 16:15
 */
@RestController
@RequestMapping("/face/record/student")
@Api(value = "", tags = "学生人脸库管理")
public class FaceRecordStudentController {

    @Resource
    private FaceRecordStudentApi faceRecordStudentApi;

    /**
     * 查看学生人脸库列表数据-分页
     *
     * @param faceRecordStudentConditionBo the face record Student condition bo
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/list-page")
    @ApiOperation(value = "查看学生人脸库列表数据-分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getFaceRecordStudentPageListByCondition(
            @RequestBody FaceRecordStudentConditionBo faceRecordStudentConditionBo) {
        return faceRecordStudentApi.getFaceRecordStudentPageListByConditionWithUser(faceRecordStudentConditionBo);
    }

    /**
     * 人脸照片业务数据上传接口
     *
     * @param faceRecordStudentConditionBos the face record Student condition bos
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/upload")
    @ApiOperation(value = "人脸照片业务数据上传接口", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult upload(@RequestBody List<FaceRecordStudentConditionBo> faceRecordStudentConditionBos) {
        if (faceRecordStudentConditionBos.isEmpty()) {
            return AjaxResult.fail("参数不可以为空");
        }
        return faceRecordStudentApi.upload(faceRecordStudentConditionBos);
    }

    /**
     * 删除照片接口
     *
     * @param faceRecordStudentId the face record Student id
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除照片接口", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult delete(@RequestParam("faceRecordStudentId") Long faceRecordStudentId) {
        return faceRecordStudentApi.delete(faceRecordStudentId);
    }

    /**
     * 统计信息接口（未上传10人，识别中10人，已通过50人，识别失败40人。）
     *
     * @param faceCountQueryBo the organization id
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/count")
    @ApiOperation(value = "统计信息接口", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult count(@RequestBody FaceCountQueryBo faceCountQueryBo) {
        return faceRecordStudentApi.count(faceCountQueryBo);
    }

    /**
     * 学生统计总览（总100人，未上传0人，识别中10人，已通过50人，识别失败40人。）
     *
     * @param faceCountQueryBo the organization id
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/overview")
    @ApiOperation(value = "学生统计总览", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult overview(@RequestBody FaceCountQueryBo faceCountQueryBo) {
        return faceRecordStudentApi.overview(faceCountQueryBo);
    }
}
