package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.grade.entity.bo.ClazzConditionBoExt;
import com.fh.cloud.screen.service.grade.service.GradeScreenApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/6/1 5:48 下午 @description：
 */
@Slf4j
@RestController
@Api(tags = "年级接口")
@RequestMapping("grade")
@AllArgsConstructor(onConstructor = @_(@Autowired))
public class GradeApiController {

    private final GradeScreenApiService gradeScreenApiService;

    /**
     * 根据组织机构ID 所属学段 获取年级
     */
    @ApiOperation("根据组织机构获取年级(全部)")
    @GetMapping("/org-section")
    public AjaxResult getBySectionOrgId(@RequestParam("orgId") Long orgId) {
        return this.gradeScreenApiService.getBySectionOrgId(orgId);
    }

    /**
     * 根据组织机构ID 所属学段 获取年级
     */
    @ApiOperation("根据组织机构获取年级(按照学段分组)")
    @GetMapping("/section-group")
    public AjaxResult getBySectionOrgIdGroup(@RequestParam("orgId") Long orgId) {
        return this.gradeScreenApiService.getBySectionOrgIdGroup(orgId);
    }

    /**
     * 根据组织机构ID获取学段
     */
    @ApiOperation("根据组织机构获取所有学段")
    @GetMapping("/sections")
    public AjaxResult getSectionByOrgId(@RequestParam("orgId") Long orgId) {
        return this.gradeScreenApiService.getSectionByOrgId(orgId);
    }

    /**
     * 获取班级列表
     */
    @ApiOperation("获取班级列表")
    @PostMapping("/classes")
    public AjaxResult getClassesListByCondition(@RequestBody ClazzConditionBoExt clazzConditionBo) {
        return this.gradeScreenApiService.getClassesListByCondition(clazzConditionBo);
    }

    /**
     * 查询一个学校年级的时候顺带查询班级、学生
     *
     * @return ajax result
     * <AUTHOR>
     * @date 2022 -10-17 11:52:36
     */
    @ApiOperation("获取年级的时候同时获取班级列表和学生列表")
    @GetMapping("/grade-student")
    public AjaxResult listGradesWithClassesAndStudents(@RequestParam("orgId") Long orgId) {
        if (orgId == null) {
            return AjaxResult.success();
        }
        return gradeScreenApiService.listGradesWithClassesAndStudents(orgId);
    }

}
