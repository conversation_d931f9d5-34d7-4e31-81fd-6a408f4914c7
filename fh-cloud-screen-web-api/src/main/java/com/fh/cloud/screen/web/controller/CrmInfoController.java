package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.crm.api.CrmInfoApi;
import com.fh.cloud.screen.service.crm.entity.bo.CrmInfoBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * CRM商讯管理
 * 
 * <AUTHOR>
 * @date 2023/12/29 10:31
 */
@Api(tags = "CRM商讯管理")
@RequestMapping("/crm-info")
@RestController
public class CrmInfoController {
    @Resource
    private CrmInfoApi crmInfoApi;

    /**
     * 新增商讯记录
     *
     */
    @PostMapping("/add-with-contact")
    @ApiOperation(value = "新增商讯记录", httpMethod = "POST")
    public AjaxResult addCrmInfoWithContact(@RequestBody CrmInfoBo crmInfoBo) {
        return crmInfoApi.addCrmInfoWithContact(crmInfoBo);
    }
}
