package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.screen.api.ScreenContentApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenContentListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云屏内容controller
 *
 * <AUTHOR>
 * @date 2022/5/7 17:05
 */
@RestController
@RequestMapping("/screen/content")
@Api(value = "云屏内容管理", tags = "云屏内容管理")
public class ScreenContentController {

    @Resource
    private ScreenContentApi screenContentApi;

    /**
     * 查询云屏内容表列表
     *
     * @param screenContentListConditionBo the screen content list condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏内容表列表", httpMethod = "POST")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public AjaxResult listContent(@RequestBody ScreenContentListConditionBo screenContentListConditionBo)
        throws Exception {
        screenContentListConditionBo.setSingleShow(true);
        return screenContentApi.getScreenContentListByCondition(screenContentListConditionBo);
    }

    /**
     * 查询云屏内容
     *
     * @param screenContentId the screen content id
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏内容表详情", httpMethod = "GET")
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public AjaxResult listContent(@RequestParam("screenContentId") Long screenContentId) throws Exception {
        return screenContentApi.getDetail(screenContentId);
    }

    /**
     * 保存云屏内容
     *
     * @param screenContentBo the screen content bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "保存云屏内容表详情", httpMethod = "POST")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public AjaxResult saveContent(@RequestBody ScreenContentBo screenContentBo) throws Exception {
        return screenContentApi.saveScreenContent(screenContentBo);
    }

    /**
     * 删除云屏内容
     *
     * @param screenContentId the screen content id
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "删除云屏内容表详情", httpMethod = "GET")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public AjaxResult deleteContent(@RequestParam("screenContentId") Long screenContentId) throws Exception {
        return screenContentApi.delete(screenContentId);
    }

    /**
     * 内容发布-撤回
     *
     * @param screenContentSpecialId the screen content special id
     * @return the ajax result
     * <AUTHOR>
     * @date 2022 -06-06 11:19:10
     */
    @PostMapping("/cancel")
    @ApiOperation(value = "内容发布撤回", httpMethod = "POST")
    public AjaxResult cancelSubmit(@RequestParam("screenContentId") Long screenContentId) {
        return screenContentApi.cancelSubmit(screenContentId);
    }
}
