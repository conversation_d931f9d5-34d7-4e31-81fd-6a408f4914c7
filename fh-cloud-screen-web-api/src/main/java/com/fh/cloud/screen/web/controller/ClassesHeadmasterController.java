package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.baseinfo.entity.bo.ClazzHeadmasterConditionBoExt;
import com.fh.cloud.screen.service.classheadmaster.api.ClassesHeadmasterApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024-01-17 9:15
 */
@RestController
@RequestMapping("/classes-headmaster")
@Slf4j
public class ClassesHeadmasterController {

    @Resource
    private ClassesHeadmasterApi classesHeadmasterApi;

    /**
     * 班主任任教班级列表
     *
     * @param clazzHeadmasterConditionBoExt the clazz headmaster condition bo ext
     * @return com.light.core.entity.AjaxResult ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -04-29 16:15:00
     */
    @ApiOperation(value = "班主任任教班级列表", httpMethod = "POST")
    @RequestMapping(value = "/headmaster-classes", method = RequestMethod.POST)
    public AjaxResult headmasterClasses(@RequestBody ClazzHeadmasterConditionBoExt clazzHeadmasterConditionBoExt) throws Exception {
        return classesHeadmasterApi.headmasterClasses(clazzHeadmasterConditionBoExt);
    }

}
