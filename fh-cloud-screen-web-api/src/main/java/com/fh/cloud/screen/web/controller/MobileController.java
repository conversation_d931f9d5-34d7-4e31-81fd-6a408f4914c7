package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.screen.api.ScreenBusinessApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenBusinessBo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

/**
 * H5移动端相关接口
 * 
 * <AUTHOR>
 * @date 2022/10/28 14:32
 */
@RestController
@RequestMapping("/mobile")
@Api(value = "", tags = "移动h5相关接口")
public class MobileController {
    @Resource
    private ScreenBusinessApi screenBusinessApi;

    /**
     * 查看云屏数据
     *
     * @return the class info list
     * <AUTHOR>
     * @date 2022 -10-28 14:17:10
     */
    @PostMapping("/screen")
    @ApiOperation(value = "云屏数据", httpMethod = "POST")
    public AjaxResult screen(@RequestBody ScreenBusinessBo screenBusinessBo) {
        // 查询地点
        screenBusinessBo.setGetSpaceInfo(true);
        // 查询云屏数据
        return screenBusinessApi.screenIndex(screenBusinessBo);
    }

}
