package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.web.service.DictionaryDataService;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典查询（调用的是公共cloud的字典数据）
 *
 * <AUTHOR>
 * @date 2022 /4/15 14:37
 */
@Slf4j
@RestController
@RequestMapping("/dict")
@Api(value = "公共cloud字典查询", tags = "公共cloud字典查询")
public class DictController {
    @Autowired
    private DictionaryDataService dictionaryDataService;

    /**
     * 获取字典数据
     *
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:40:40
     */
    @ApiOperation(value = "获取字典数据", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/dicts", method = RequestMethod.POST)
    public AjaxResult updateAccount(@RequestBody List<String> dictTypes) throws Exception {

        try {
            List<DictionaryDataVo> dictionaryDataVos = dictionaryDataService.listValueByTypes(dictTypes);
            Map<String, List<DictionaryDataVo>> resMap =
                dictionaryDataVos.stream().collect(Collectors.groupingBy(DictionaryDataVo::getDictType));
            return AjaxResult.success(resMap);
        } catch (Exception e) {
            log.error("密码解密失败!原因:{}", e.getMessage());
        }
        return AjaxResult.fail("获取字典失败");
    }
}
