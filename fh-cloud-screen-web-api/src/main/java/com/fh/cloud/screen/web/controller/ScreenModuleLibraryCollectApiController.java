package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.screen.service.ScreenModuleLibraryCollectApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.light.core.constants.SystemConstants;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryCollectBo;

import com.light.core.entity.AjaxResult;

/**
 * 海报收藏表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2022-09-15 15:51:24
 */
@RestController
@Validated
@RequestMapping("screen/module/library/collect")
@Api(value = "", tags = "海报收藏表接口")
public class ScreenModuleLibraryCollectApiController {

    @Autowired
    private ScreenModuleLibraryCollectApiService screenModuleLibraryCollectApiService;

    /**
     * 新增海报收藏表
     * 
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增海报收藏表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult
        addScreenModuleLibraryCollect(@RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        return screenModuleLibraryCollectApiService.addScreenModuleLibraryCollect(screenModuleLibraryCollectBo);
    }

    /**
     * 删除海报收藏表
     * 
     * @param screenModuleLibraryCollectBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-15 15:51:24
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除海报收藏表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult delete(@RequestBody ScreenModuleLibraryCollectBo screenModuleLibraryCollectBo) {
        return screenModuleLibraryCollectApiService.delete(screenModuleLibraryCollectBo);
    }
}
