package com.fh.cloud.screen.web.controller;

import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.exception.UnifiedException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 媒体资源
 *
 * <AUTHOR>
 * @date 2023/4/25
 */
@RestController
@Validated
@RequestMapping("/media")
@Api(value = "", tags = "媒体资源接口")
@Slf4j
public class MediaController {

    @Resource
    private AttachmentApiService attachmentApiService;
    @Value("${file.web.preview.prefix:http://yunping.fhsljy.com/file}")
    private String PREFIX;

    /**
     * 重定向img等比例缩略图的文件地址
     *
     * @param oid 文件OID
     * @param scale 比例值
     * @return {@link AjaxResult}<{@link byte[]}>
     */
    @GetMapping(value = "/img-scale-thumbnails/url/{oid}")
    public void getImgScaleThumbnailsUrl(@PathVariable("oid") String oid, @RequestParam("scale") Double scale,
        HttpServletResponse response) throws IOException {
        final AjaxResult<byte[]> imgThumbnails = this.attachmentApiService.getImgScaleThumbnails(oid, scale);
        String path = "";
        if (imgThumbnails.isFail()) {
            throw new UnifiedException("获取失败");
        } else {
            AttachmentVo vo = attachmentApiService.getByFileOid(oid).getData();
            if (null != vo) {
                String ext = vo.getFileExtName();
                path = vo.getFilePath().replace((SystemConstants.SEPERATOR_POINT + ext),
                    (SystemConstants.UNDERLINE + String.valueOf(scale) + SystemConstants.SEPERATOR_POINT + ext));
            }
        }
        response.sendRedirect(PREFIX + path);
    }
}
