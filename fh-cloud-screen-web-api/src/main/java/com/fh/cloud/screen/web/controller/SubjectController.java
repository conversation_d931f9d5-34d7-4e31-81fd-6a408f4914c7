package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.subject.api.SubjectApi;
import com.fh.cloud.screen.service.subject.entity.bo.SubjectBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "科目接口")
@RestController
@RequestMapping("/subject")
public class SubjectController {
    @Resource
    private SubjectApi subjectApi;

    @PostMapping("/list")
    @ApiOperation(value = "科目列表查询", httpMethod = "POST")
    public AjaxResult getSpaceInfoListByCondition(@RequestBody SubjectBo subjectBo) {
        return subjectApi.listSubject(subjectBo);
    }
}
