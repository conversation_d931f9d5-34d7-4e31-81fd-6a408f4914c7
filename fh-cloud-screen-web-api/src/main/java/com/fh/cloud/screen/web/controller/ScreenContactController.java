package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.screen.entity.bo.ScreenContactBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.screen.api.ScreenContactApi;

import io.swagger.annotations.Api;

/**
 * 云屏产品咨询联系信息控制层
 * 
 * <AUTHOR>
 * @date 2024/7/22 11:00
 */
@RestController
@RequestMapping("/screen/contact")
@Api(value = "云屏产品咨询联系信息", tags = "云屏产品咨询联系信息")
public class ScreenContactController {
    @Resource
    private ScreenContactApi screenContactApi;

    /**
     * 添加云屏产品咨询联系信息
     */
    @ApiOperation(value = "添加云屏产品咨询联系信息", httpMethod = "POST")
    @PostMapping("/add")
    public AjaxResult addScreenContact(@RequestBody ScreenContactBo screenContactBo) {
        return screenContactApi.addScreenContact(screenContactBo);
    }
}
