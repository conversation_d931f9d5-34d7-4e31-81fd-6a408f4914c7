package com.fh.cloud.screen.web.service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.light.core.utils.StringUtils;

/**
 * 稿定对接处理service
 *
 * <AUTHOR>
 * @date 2023/12/25 17:16
 */
@Service
public class GaoDingService {
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    @Value("${gaoding.ak:}")
    private String AK;
    @Value("${gaoding.sk:}")
    private String SK;

    /**
     * 获取sso授权码
     *
     * @param uuid
     * @return
     * @throws Exception
     */
    public Object fetchSsoAuthCode(String userOid) throws Exception {
        String result = "";
        if (StringUtils.isBlank(AK) || StringUtils.isBlank(SK)) {
            throw new Exception("AK或SK为空");
        }
        // 联调测试阶段，测试AK、SK请联系稿定商务获取。在正式上线后通知商务，申请正式AK、SK
        String ak = AK;
        String sk = SK;
        String method = "POST";
        String uri = "/sso/oauth/authorize/sdk/";
        // 请求body参数用户uid，根据实际需要，前端传过来或者后端取
        String body = "{\"uid\":\"" + userOid + "\"}";
        long timestamp = System.currentTimeMillis() / 1000;
        // 加密体
        String requestRaw = method + "@" + uri + "@@" + Long.toString(timestamp) + "@" + body;
        // 使用 HMAC-SHA1 签名方法对data进行签名，genHmac方法实现在底部
        String signature = genHmac(requestRaw, sk);
        CloseableHttpClient client = HttpClientBuilder.create().build();
        StringEntity entity = new StringEntity(body);
        HttpPost httpPost = new HttpPost("http://api.open.gaoding.com/sso/oauth/authorize/sdk");
        CloseableHttpResponse response = null;
        try {
            httpPost.addHeader("X-Timestamp", Long.toString(timestamp));
            httpPost.addHeader("X-AccessKey", ak);
            httpPost.addHeader("X-Signature", signature);
            httpPost.addHeader("Content-Type", "application/json");
            httpPost.setEntity(entity);
            response = client.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            System.out.println("响应状态为:" + response.getStatusLine());
            result = EntityUtils.toString(responseEntity);
        } finally {
            try {
                if (client != null) {
                    client.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return JSONObject.parse(result);
    }

    /**
     * 获取个人作图记录
     *
     * @param uuid
     * @return
     * @throws Exception
     */
    public Object fetchPersonalContents(String userOid, String keyword, Integer pageNo, Integer pageSize)
        throws Exception {
        String result = "";
        if (StringUtils.isBlank(AK) || StringUtils.isBlank(SK)) {
            throw new Exception("AK或SK为空");
        }
        Map<String, Object> resultMap = new HashMap<>();
        // 联调测试阶段，测试AK、SK请联系稿定商务获取。在正式上线后通知商务，申请正式AK、SK
        String ak = AK;
        String sk = SK;
        String method = "GET";
        String uri = "/cds/" + userOid + "/contents/";
        // 请求body参数用户uid，根据实际需要，前端传过来或者后端取
        String requestParam = "";
        // 添加过滤只查询模板。注意参数必须要按照 ASCII 码从小到大排序！！！！比如有参数，必须c在前，k，然后p
        requestParam = requestParam +"content_type=0";
        if (StringUtils.isNotBlank(keyword)) {
            requestParam = requestParam + "&keyword=" + keyword;
        }
        requestParam = requestParam + "&page_num=" + pageNo + "&page_size=" + pageSize;
        long timestamp = System.currentTimeMillis() / 1000;
        // 加密体
        String requestRaw = method + "@" + uri + "@" + requestParam + "@" + Long.toString(timestamp);
        // 使用 HMAC-SHA1 签名方法对data进行签名，genHmac方法实现在底部
        String signature = genHmac(requestRaw, sk);
        CloseableHttpClient client = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet("http://api.open.gaoding.com/cds/" + userOid + "/contents" + "?" + requestParam);
        CloseableHttpResponse response = null;
        try {
            httpGet.addHeader("X-Timestamp", Long.toString(timestamp));
            httpGet.addHeader("X-AccessKey", ak);
            httpGet.addHeader("X-Signature", signature);
            httpGet.addHeader("Content-Type", "application/json");
            response = client.execute(httpGet);
            HttpEntity responseEntity = response.getEntity();
            Header pageHeader = response.getFirstHeader("x-pagination");
            System.out.println("响应状态为:" + response.getStatusLine());
            result = EntityUtils.toString(responseEntity);
            resultMap.put("data", JSONObject.parse(result));
            resultMap.put("pagination", JSONObject.parse(pageHeader.getValue()));
        } finally {
            try {
                if (client != null) {
                    client.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultMap;
    }

    /**
     * 使用 HMAC-SHA1 签名方法对data进行签名
     *
     * @param data 被签名的字符串
     * @param key 密钥
     * @return 加密后的字符串
     */
    private String genHmac(String data, String key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA1_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signingKey);
            return new String(Base64.getEncoder().encode(mac.doFinal(data.getBytes(StandardCharsets.UTF_8))));
        } catch (Exception e) {
        }
        return null;
    }
}
