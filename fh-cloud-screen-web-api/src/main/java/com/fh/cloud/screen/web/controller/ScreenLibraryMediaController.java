package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.common.api.ScreenDictionaryDataApi;
import com.fh.cloud.screen.service.common.entity.bo.DictionaryDataListConditionBo;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryApi;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryAuditApi;
import com.fh.cloud.screen.service.screen.entity.bo.*;
import com.light.core.constants.SystemConstants;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryMediaApi;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/7 10:49
 */
@RestController
@RequestMapping("/screen/library-media")
@Api(value = "云屏模块库媒体资源管理", tags = "云屏模块库媒体资源管理")
public class ScreenLibraryMediaController {
    @Resource
    private ScreenModuleLibraryMediaApi screenModuleLibraryMediaApi;
    @Resource
    private ScreenModuleLibraryApi screenModuleLibraryApi;
    @Resource
    private ScreenDictionaryDataApi screenDictionaryDataApi;
    @Resource
    private ScreenModuleLibraryAuditApi screenModuleLibraryAuditApi;

    /**
     * 查询云屏模块库媒体资源列表(云屏组件，云屏设备使用)
     *
     * <AUTHOR>
     * @date 2022/6/29 15:25
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询云屏模块库媒体资源列表", httpMethod = "POST")
    AjaxResult getScreenModuleLibraryListByCondition(@RequestBody ScreenModuleLibraryMediaBo condition) {
        return screenModuleLibraryMediaApi.getScreenModuleLibraryListByCondition(condition);
    }

    /**
     * 分组列表
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/12/30 14:09
     */
    @PostMapping("/group-list")
    AjaxResult getPosterGroupListByCondition(@RequestBody DictionaryDataListConditionBo condition) {
        condition.setPageNo(SystemConstants.NO_PAGE);
        return screenDictionaryDataApi.getPosterGroupListByCondition(condition);
    }

    /**
     * 全部海报列表
     *
     * @param selectType 包含主题热门海报
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/16 15:21
     */
    @Deprecated
    @GetMapping("/posters")
    @ApiOperation(value = "全部海报列表", httpMethod = "GET")
    public AjaxResult getThemePosterList(@RequestParam("organizationId") Long organizationId,
        @RequestParam("selectType") Integer selectType, Integer pattern) {
        return screenModuleLibraryMediaApi.getThemePosterList(organizationId, selectType, pattern);
    }

    /**
     * 海报前20个分组和前10主题列表（移动端）
     *
     * @param organizationId, selectType, pattern
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 17:58
     */
    @GetMapping("/posters-page")
    @ApiOperation(value = "海报前20个分组和前10主题列表（移动端）", httpMethod = "GET")
    public AjaxResult getPosterPageList(@RequestParam("organizationId") Long organizationId,
        @RequestParam("selectType") Integer selectType, Integer pattern,
        @RequestParam(value = "classesId", required = false) Long classesId) {
        return screenModuleLibraryMediaApi.getPosterPageList(organizationId, selectType, pattern, classesId);
    }

    /**
     * 海报条件查询主题列表，只查询关联了标签的海报
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 16:43
     */
    @PostMapping("/poster-list")
    @ApiOperation(value = "海报条件查询主题列表", httpMethod = "POST")
    public AjaxResult getPosterList(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryApi.getPosterList(conditionBo);
    }

    /**
     * 海报条件查询主题列表，已选择的，忽略是否关联标签
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/3 16:43
     */
    @PostMapping("/poster-list-sel")
    @ApiOperation(value = "海报条件查询主题列表", httpMethod = "POST")
    public AjaxResult getPosterListSel(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        if (StringUtils.isBlank(conditionBo.getScreenModuleLibrarySelIds())) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("list", null);
            return AjaxResult.success(map);
        }
        return screenModuleLibraryApi.getPosterListSel(conditionBo);
    }

    /**
     * 设备关联标签关联海报主题列表(订阅海报列表)
     *
     * @param conditionBo the deviceNumber
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/5/8 9:38
     */
    @PostMapping("/device-poster-list")
    public AjaxResult getDevicePosterListByDeviceNumber(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryApi.getDevicePosterListByDeviceNumber(conditionBo);
    }

    /**
     * 我收藏的海报模块列表
     *
     * @param organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/9/19 9:39
     */
    @GetMapping("/my-posters")
    public AjaxResult getMyCollectPosters(@RequestParam("organizationId") Long organizationId, Integer pattern) {
        return screenModuleLibraryMediaApi.getMyCollectPosters(organizationId, pattern);
    }

    /**
     * 分页获取我收藏的海报列表
     *
     * @param libraryCollectConditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/20 15:31
     */
    @ApiOperation(value = "分页获取我收藏的海报列表", httpMethod = "POST")
    @PostMapping("/my-posters-page")
    public AjaxResult
        getMyCollectPostersPage(@RequestBody ScreenModuleLibraryCollectConditionBo libraryCollectConditionBo) {
        return screenModuleLibraryMediaApi.getMyCollectPostersPage(libraryCollectConditionBo);
    }

    /**
     * 我创建的海报模块列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/6 17:26
     */
    @GetMapping("/posters-person")
    public AjaxResult getPersonPosters(@RequestBody ScreenModuleLibraryListConditionBo conditionBo) {
        return screenModuleLibraryApi.getPersonPosters(conditionBo);
    }

    // /**
    // * 新增云屏模块库媒体资源表
    // *
    // * <AUTHOR>
    // * @date 2023-01-03 14:57:48
    // */
    // @ApiOperation(value = "新增云屏模块库媒体资源表", httpMethod = "POST")
    // @PostMapping("/add")
    // public AjaxResult addScreenModuleLibraryMedia(@RequestBody ScreenModuleLibraryMediaBo screenModuleLibraryMediaBo)
    // {
    // if (screenModuleLibraryMediaBo.getScreenModuleLibraryId() == null ||
    // screenModuleLibraryMediaBo.getScreenModuleLibraryMediaUrl() == null ||
    // screenModuleLibraryMediaBo.getScreenModuleLibraryMediaNameOri() == null) {
    // return AjaxResult.fail("参数错误");
    // }
    // return screenModuleLibraryMediaApi.addScreenModuleLibraryMedia(screenModuleLibraryMediaBo);
    // }
    //
    //
    // /**
    // * 修改云屏模块库媒体资源表
    // *
    // * @param screenModuleLibraryMediaBo
    // * @return
    // * @returnType AjaxResult
    // * <AUTHOR>
    // * @date 2023-01-03 14:57:48
    // */
    // @ApiOperation(value = "修改云屏模块库媒体资源", httpMethod = "POST")
    // @PostMapping("/update")
    // public AjaxResult updateScreenModuleLibraryMedia(@RequestBody ScreenModuleLibraryMediaBo
    // screenModuleLibraryMediaBo) {
    // return screenModuleLibraryMediaApi.updateScreenModuleLibraryMedia(screenModuleLibraryMediaBo);
    // }
    //
    // /**
    // * 删除云屏模块库媒体资源
    // *
    // * @param screenModuleLibraryMediaId
    // * @return com.light.core.entity.AjaxResult
    // * <AUTHOR>
    // * @date 2023/4/3 17:06
    // */
    // @ApiOperation(value = "删除云屏模块库媒体资源", httpMethod = "GET")
    // @GetMapping("/delete")
    // public AjaxResult deleteScreenModuleLibraryMediaApi(@RequestParam("screenModuleLibraryMediaId") Long
    // screenModuleLibraryMediaId) {
    // return screenModuleLibraryMediaApi.delete(screenModuleLibraryMediaId);
    // }

    /**
     * 获取模块库审核表列表（包含分类标签和资源信息）
     *
     * @param condition
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/6 16:29
     **/
    @PostMapping("/poster-audit-page")
    public AjaxResult getPosterAuditPage(@RequestBody ScreenModuleLibraryAuditConditionBo condition) {
        condition.setOrderBy("create_time desc");
        return screenModuleLibraryAuditApi.getPosterAuditPage(condition);
    }

    /**
     * 保存海报审核数据
     *
     * @param bo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/6 16:43
     **/
    @PostMapping("/save-poster-audit")
    public AjaxResult addPosterAudit(@RequestBody ScreenModuleLibraryAuditBo bo) {
        return screenModuleLibraryAuditApi.addPosterAudit(bo);
    }

    /**
     * 更新海报审核数据
     *
     * @param bo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/12 11:46
     **/
    @PostMapping("/update-poster-audit")
    public AjaxResult updatePosterAudit(@RequestBody ScreenModuleLibraryAuditBo bo) {
        return screenModuleLibraryAuditApi.updatePosterAudit(bo);
    }

    /**
     * 获取海报审核数据详情
     *
     * @param screenModuleLibraryAuditId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/12 17:14
     **/
    @GetMapping("/poster-audit-detail")
    public AjaxResult
        getPosterAuditDetail(@RequestParam("screenModuleLibraryAuditId") Long screenModuleLibraryAuditId) {
        return screenModuleLibraryAuditApi.getPosterAuditDetail(screenModuleLibraryAuditId);
    }

    /**
     * 海报资源审核
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/13 9:25
     **/
    @PostMapping("/audit")
    public AjaxResult audit(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        return screenModuleLibraryAuditApi.audit(screenModuleLibraryAuditBo);
    }

    /**
     * 海报资源批量审核
     *
     * @param screenModuleLibraryAuditBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/13 9:26
     **/
    @PostMapping("/audit-batch")
    public AjaxResult auditBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        return screenModuleLibraryAuditApi.auditBatch(screenModuleLibraryAuditBo);
    }

    /**
     * 海报资源发布、取消发布
     *
     * @param screenModuleLibraryAuditBo，需要传参：screenModuleLibraryAuditId、releaseType
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/13 9:29
     **/
    @PostMapping("/release-poster")
    public AjaxResult releasePoster(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        return screenModuleLibraryAuditApi.releasePoster(screenModuleLibraryAuditBo);
    }

    /**
     * 海报审核数据删除
     *
     * @param screenModuleLibraryAuditId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/13 9:29
     **/
    @GetMapping("/poster-audit-delete")
    public AjaxResult posterAuditDelete(@RequestParam("screenModuleLibraryAuditId") Long screenModuleLibraryAuditId) {
        return screenModuleLibraryAuditApi.delete(screenModuleLibraryAuditId);
    }

    /* 资源2.0新增接口 */
    /**
     * 2.0-1批量更换海报审核（label_library_audit_rel）的主题:auditType、labelBos、screenModuleLibraryAuditIds、organizationId
     * 逻辑：会删除原来所有的标签，转入新的标签里面
     * 
     * @param screenModuleLibraryAuditBo
     * @return
     */
    @PostMapping("/update-poster-audit-label")
    public AjaxResult updatePosterAuditLabel(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        return screenModuleLibraryAuditApi.updatePosterAuditLabel(screenModuleLibraryAuditBo);
    }

    /**
     * 2.0-3批量删除海报审核（screen_module_library_audit）
     *
     * @param screenModuleLibraryAuditBo 参数只需要传：screenModuleLibraryAuditIds
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2023 /12/13 9:29
     */
    @PostMapping("/poster-audit-delete-batch")
    public AjaxResult posterAuditDeleteBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        return screenModuleLibraryAuditApi.deleteBatch(screenModuleLibraryAuditBo);
    }

    /**
     * 2.0-4批量发布/取消发布
     *
     * @param screenModuleLibraryAuditBo ，需要传参：screenModuleLibraryAuditIds数组、releaseType
     * @return com.light.core.entity.AjaxResult ajax result
     * <AUTHOR>
     * @date 2023 /12/13 9:29
     */
    @PostMapping("/release-poster-batch")
    public AjaxResult releasePosterBatch(@RequestBody ScreenModuleLibraryAuditBo screenModuleLibraryAuditBo) {
        AjaxResult ajaxResult = screenModuleLibraryAuditApi.releasePosterBatch(screenModuleLibraryAuditBo);
        return ajaxResult;
    }
}
