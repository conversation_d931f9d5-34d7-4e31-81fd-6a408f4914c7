package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.device.api.ShowDeviceSwitchApi;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBatchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceSwitchListConditionBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/12 14:18
 */
@RestController
@RequestMapping("/device/switch")
@Api(value = "设备开关机设置管理", tags = "设备开关机设置设备管理")
public class DeviceSwitchController {

    @Resource
    private ShowDeviceSwitchApi showDeviceSwitchApi;

    /**
     * 查询开关机设置列表
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询开关机设置列表", httpMethod = "POST")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public AjaxResult listShowDeviceDataByCondition(@RequestBody ShowDeviceSwitchListConditionBo conditionBo)
        throws Exception {
        return showDeviceSwitchApi.getShowDeviceSwitchListByCondition(conditionBo);
    }

    /**
     * 新增开关机设置
     *
     * @param showDeviceSwitchBo the show device switch bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "新增开关机设置", httpMethod = "POST")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public AjaxResult addShowDeviceSwitch(@RequestBody ShowDeviceSwitchBo showDeviceSwitchBo) throws Exception {
        return showDeviceSwitchApi.addShowDeviceSwitch(showDeviceSwitchBo);
    }

    /**
     * 修改开关机设置
     *
     * @param showDeviceSwitchBo the show device switch bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "修改开关机设置", httpMethod = "POST")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateShowDeviceSwitch(@RequestBody ShowDeviceSwitchBo showDeviceSwitchBo) throws Exception {
        return showDeviceSwitchApi.updateShowDeviceSwitch(showDeviceSwitchBo);
    }

    /**
     * 保存开关机设置（新增或修改）
     *
     * @param showDeviceSwitchBatchBo the show device switch batch bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "保存开关机设置（新增或修改）", httpMethod = "POST")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public AjaxResult saveShowDeviceSwitch(@RequestBody ShowDeviceSwitchBatchBo showDeviceSwitchBatchBo)
        throws Exception {
        return showDeviceSwitchApi.saveShowDeviceSwitch(showDeviceSwitchBatchBo);
    }
}
