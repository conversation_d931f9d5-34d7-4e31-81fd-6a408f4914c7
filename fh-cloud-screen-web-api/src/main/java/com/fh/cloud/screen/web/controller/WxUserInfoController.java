package com.fh.cloud.screen.web.controller;

import javax.websocket.server.PathParam;

import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.mp.api.WxMpUserService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.light.core.entity.AjaxResult;

import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;

/**
 * jsapi 演示接口的 controller.
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 * @date 2020-04-25
 */
@AllArgsConstructor
@RestController
@RequestMapping("/wx/userinfo")
public class WxUserInfoController {
    private final WxMpService wxService;

    /**
     * 获取微信用户信息
     *
     * @param url the web url
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/11/10 13:56
     */
    @GetMapping("/getUserinfo")
    public AjaxResult getUserinfo(@PathParam("openid") String openid) throws WxErrorException {
        WxMpUserService userService = this.wxService.getUserService();
        WxMpUser wxMpUser = userService.userInfo(openid, null);
        return AjaxResult.success(wxMpUser);
    }
}
