package com.fh.cloud.screen.web.controller;

import com.light.core.entity.AjaxResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AllArgsConstructor;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;

import javax.websocket.server.PathParam;

/**
 * jsapi 演示接口的 controller.
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 * @date 2020-04-25
 */
@AllArgsConstructor
@RestController
@RequestMapping("/wx/jsApi")
public class WxJsApiController {
    private final WxMpService wxService;

    /**
     * 获取微信授权签名
     *
     * @param url the web url
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/11/10 13:56
     */
    @GetMapping("/getJsApiTicket")
    public AjaxResult getJsApiTicket(@PathParam("url") String url) throws WxErrorException {
        final WxJsapiSignature jsapiSignature = this.wxService.createJsapiSignature(url);
        return AjaxResult.success(jsapiSignature);
    }
}
