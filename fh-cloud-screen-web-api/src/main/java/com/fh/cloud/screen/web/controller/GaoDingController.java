package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.gd.api.GdContentRecordApi;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordBo;
import com.fh.cloud.screen.service.gd.entity.bo.GdContentRecordConditionBo;
import com.fh.cloud.screen.service.gd.entity.vo.GdContentRecordVo;
import com.fh.cloud.screen.web.service.GaoDingService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 稿定对接接口
 *
 * <AUTHOR>
 * @date 2023/12/25 14:39
 */
@RestController
@RequestMapping("/gao-ding")
@Api(value = "稿定对接", tags = "稿定对接")
@Slf4j
public class GaoDingController {

    @Resource
    private GaoDingService gaoDingService;
    @Resource
    private GdContentRecordApi gdContentRecordApi;

    /**
     * hello示例
     *
     * @return the ajax result
     */
    @GetMapping("/hello")
    @ApiOperation(value = "hello示例", notes = "hello示例")
    public AjaxResult hello() {
        return AjaxResult.success("hello result");
    }

    /**
     * 获取sso授权码
     *
     * @param userOid 用户唯一标识（暂时可以用oa的id传递过来）
     * @return the ajax result
     */
    @GetMapping("/fetchSsoAuthCode")
    @ApiOperation(value = "获取sso授权码", notes = "获取sso授权码")
    public AjaxResult fetchSsoAuthCode(@RequestParam("userOid") String userOid) {
        try {
            Object ssoAuthCode = gaoDingService.fetchSsoAuthCode(userOid);
            return AjaxResult.success(ssoAuthCode);
        } catch (Exception e) {
            log.error("获取sso授权码异常", e);
        }
        return AjaxResult.fail("获取sso授权码异常");
    }

    /**
     * 获取个人作图记录
     *
     * @param userOid 用户唯一标识（暂时可以用oa的id传递过来）
     * @param keyword 搜索关键词
     * @param pageNo 当前页数 (默认1)
     * @param pageSize 每页数量（默认20）
     * @return the ajax result
     */
    @GetMapping("/cds/{userOid}/contents")
    @ApiOperation(value = "获取个人作图记录", notes = "获取个人作图记录")
    public AjaxResult fetchPersonalContents(@PathVariable("userOid") String userOid,
        @RequestParam("keyword") String keyword, @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
        @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        try {
            Object personalContents = gaoDingService.fetchPersonalContents(userOid, keyword, pageNo, pageSize);
            return AjaxResult.success(personalContents);
        } catch (Exception e) {
            log.error("获取个人作图记录异常", e);
        }
        return AjaxResult.fail("获取个人作图记录异常");
    }

    /**
     * 添加作图记录到云屏数据库
     *
     * @param gdContentRecordBo the gd content record bo
     * @return ajax result
     * <AUTHOR>
     * @date 2024 -07-12 14:06:12
     */
    @PostMapping("/content-record/add")
    @ApiOperation(value = "添加作图记录到云屏数据库", notes = "添加作图记录到云屏数据库")
    public AjaxResult addContentRecord(@RequestBody GdContentRecordBo gdContentRecordBo) {
        return gdContentRecordApi.addGdContentRecord(gdContentRecordBo);
    }

    /**
     * 修改稿定内容记录
     *
     * @param gdContentRecordBo the gd content record bo
     * @return ajax result
     * @returnType AjaxResult
     */
    @Deprecated
    @PostMapping("/content-record/update")
    @ApiOperation(value = "修改稿定内容记录", notes = "修改稿定内容记录")
    public AjaxResult updateContentRecord(@RequestBody GdContentRecordBo gdContentRecordBo) {
        return gdContentRecordApi.updateGdContentRecord(gdContentRecordBo);
    }

    /**
     * 查询稿定内容记录列表
     *
     * @param gdContentRecordConditionBo the gd content record condition bo
     * @return gd content record list by condition
     * <AUTHOR>
     * @date 2024 -07-12 14:06:09
     * @returnType AjaxResult
     */
    @PostMapping("/content-record/list")
    @ApiOperation(value = "查询稿定内容记录列表", notes = "查询稿定内容记录列表")
    public AjaxResult
        getGdContentRecordListByCondition(@RequestBody GdContentRecordConditionBo gdContentRecordConditionBo) {
        return gdContentRecordApi.getGdContentRecordListByCondition(gdContentRecordConditionBo);
    }

    /**
     * 查询稿定内容记录详情
     *
     * @param id the id
     * @return detail
     * @returnType AjaxResult
     */
    @Deprecated
    @GetMapping("/content-record/detail")
    @ApiOperation(value = "查询稿定内容记录详情", notes = "查询稿定内容记录详情")
    public AjaxResult getDetail(@RequestParam("id") Long id) {
        return gdContentRecordApi.getDetail(id);
    }

    /**
     * 查询稿定内容记录详情-条件查询
     *
     * @param id the id
     * @return delete
     * @returnType AjaxResult
     */
    @PostMapping("/content-record/detail-condition")
    public AjaxResult<GdContentRecordVo>
        getDetailCondition(@RequestBody GdContentRecordConditionBo gdContentRecordConditionBo) {
        return gdContentRecordApi.getDetailCondition(gdContentRecordConditionBo);
    }

    /**
     * 更新稿定内容记录-条件更新
     *
     * @param id the id
     * @return delete
     * @returnType AjaxResult
     */
    @Deprecated
    @PostMapping("/content-record/update-condition")
    public AjaxResult<GdContentRecordVo>
        updateContentRecordCondition(@RequestBody GdContentRecordConditionBo gdContentRecordConditionBo) {
        return gdContentRecordApi.updateCondition(gdContentRecordConditionBo);
    }

    /**
     * 保存，新增或更新（根据gdId）
     *
     * @param gdContentRecordBo the gd content record bo
     * @return delete ajax result
     * <AUTHOR>
     * @date 2024 -07-12 15:28:27
     * @returnType AjaxResult
     */
    @PostMapping("/content-record/save")
    public AjaxResult<GdContentRecordVo> save(@RequestBody GdContentRecordBo gdContentRecordBo) {
        return gdContentRecordApi.save(gdContentRecordBo);
    }

}
