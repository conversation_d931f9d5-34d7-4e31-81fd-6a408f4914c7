package com.fh.cloud.screen.web;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * web端基础信息服务平台业务接口
 *
 * <AUTHOR>
 * @date 2022/2/18
 */
@EnableFeignClients(basePackages = {"com.fh.cloud.screen", "com.light"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},
    scanBasePackages = {"com.fh.cloud.screen.**.**.*", "com.light.**.**.*"})
@EnableSwagger2
@EnableKnife4j
@Import({cn.hutool.extra.spring.SpringUtil.class})
public class CloudScreenWebApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(CloudScreenWebApiApplication.class);
    }
}
