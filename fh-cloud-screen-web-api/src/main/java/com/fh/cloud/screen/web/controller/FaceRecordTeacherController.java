package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.face.api.FaceRecordTeacherApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceCountQueryBo;
import com.fh.cloud.screen.service.face.entity.bo.FaceRecordTeacherConditionBo;
import com.fh.cloud.screen.service.face.entity.vo.FaceRecordTeacherVo;
import com.light.core.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.face.api.FaceConfigApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教师人脸库
 *
 * <AUTHOR>
 * @date 2022/11/18 16:15
 */
@RestController
@RequestMapping("/face/record/teacher")
@Api(value = "", tags = "人脸配置接口")
public class FaceRecordTeacherController {
    @Resource
    private FaceRecordTeacherApi faceRecordTeacherApi;

    /**
     * 查看教师人脸库列表数据-分页
     *
     * @param faceRecordTeacherConditionBo the face record teacher condition bo
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/list-page")
    @ApiOperation(value = "查看教师人脸库列表数据-分页", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getFaceRecordTeacherPageListByCondition(
            @RequestBody FaceRecordTeacherConditionBo faceRecordTeacherConditionBo) {
        return faceRecordTeacherApi.getFaceRecordTeacherPageListByConditionWithUser(faceRecordTeacherConditionBo);
    }

    /**
     * 人脸照片业务数据上传接口
     *
     * @param faceRecordTeacherConditionBos the face record teacher condition bos
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/upload")
    @ApiOperation(value = "人脸照片业务数据上传接口", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult upload(@RequestBody List<FaceRecordTeacherConditionBo> faceRecordTeacherConditionBos) {
        if (faceRecordTeacherConditionBos.isEmpty()) {
            return AjaxResult.fail("参数不可以为空");
        }
        return faceRecordTeacherApi.upload(faceRecordTeacherConditionBos);
    }

    /**
     * 删除照片接口
     *
     * @param faceRecordTeacherId the face record teacher id
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除照片接口", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult delete(@RequestParam("faceRecordTeacherId") Long faceRecordTeacherId) {
        return faceRecordTeacherApi.delete(faceRecordTeacherId);
    }

    /**
     * 统计信息接口（未上传10人，识别中10人，已通过50人，识别失败40人。）
     *
     * @param faceCountQueryBo the organization id
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/count")
    @ApiOperation(value = "统计信息接口", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult count(@RequestBody FaceCountQueryBo faceCountQueryBo) {
        return faceRecordTeacherApi.count(faceCountQueryBo);
    }
}
