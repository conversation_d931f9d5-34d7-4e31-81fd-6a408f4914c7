package com.fh.cloud.screen.web.service;

import com.alibaba.fastjson.JSONObject;
import com.light.base.dictionary.entity.bo.DictionaryDataListConditionBo;
import com.light.base.dictionary.entity.vo.DictionaryDataVo;
import com.light.base.dictionary.service.DictionaryDataApiService;
import com.light.core.entity.AjaxResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典调用service
 *
 * <AUTHOR>
 * @date 2022/4/12 15:06
 */
@Service
public class DictionaryDataService {
    @Autowired
    private DictionaryDataApiService dictionaryDataApiService;

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictTypes the dict types
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    public List<DictionaryDataVo> listValueByTypes(List<String> dictTypes) {
        if (CollectionUtils.isEmpty(dictTypes)) {
            return new ArrayList<>();
        }

        // 去重
        dictTypes = dictTypes.stream().distinct().collect(Collectors.toList());

        List<DictionaryDataVo> resultList = new ArrayList<>();
        for (String dictType : dictTypes) {
            List<DictionaryDataVo> temp = listValueByTypeAndLabel(dictType);
            if (CollectionUtils.isEmpty(temp)) {
                continue;
            }
            resultList.addAll(temp);
        }
        return resultList;
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictType the dict type
     * @param dictLabel the dict label
     * @return value by type and label
     * <AUTHOR>
     * @date 2022 -04-21 17:58:28
     */
    public List<DictionaryDataVo> listValueByTypeAndLabel(String dictType) {
        DictionaryDataListConditionBo dictionaryDataListConditionBo = new DictionaryDataListConditionBo();
        dictionaryDataListConditionBo.setDictType(dictType);
        dictionaryDataListConditionBo.setPageNo(1);
        dictionaryDataListConditionBo.setPageSize(1000);
        AjaxResult<Object> ajaxResult =
            dictionaryDataApiService.getAllDictionaryDataList(dictionaryDataListConditionBo);

        if (ajaxResult.isFail()) {
            return new ArrayList<>();
        }
        Object data = ajaxResult.getData();

        List<DictionaryDataVo> dictionaryDataVos =
            JSONObject.parseArray(JSONObject.toJSONString(data), DictionaryDataVo.class);
        return dictionaryDataVos;
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictType the dict type
     * @param dictLabel the dict label
     * @return value by type and label
     */
    public String getValueByTypeAndLabel(String dictType, String dictLabel) {
        List<DictionaryDataVo> dictionaryDataVos = listValueByTypeAndLabel(dictType);
        if (CollectionUtils.isEmpty(dictionaryDataVos)) {
            return "";
        }
        for (DictionaryDataVo dictionaryDataVo : dictionaryDataVos) {
            if (dictionaryDataVo == null) {
                continue;
            }
            if (dictionaryDataVo.getDictLabel().equals(dictLabel)) {
                return dictionaryDataVo.getDictValue();
            }
        }
        return "";
    }

    /**
     * 获取指定类型的指定lable的value
     *
     * @param dictType the dict type
     * @param dictLabel the dict label
     * @return value by type and label
     */
    public Integer getValueByTypeAndLabelInteger(String dictType, String dictLabel) {
        String value = getValueByTypeAndLabel(dictType, dictLabel);
        return StringUtils.isBlank(value) ? null : Integer.valueOf(value);
    }
}
