package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.enums.ExamPlanType;
import com.fh.cloud.screen.service.er.api.ExamPlanApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamPlanConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamPlanVo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 考场考试计划接口
 *
 * <AUTHOR>
 * @date 2022 /9/29 14:57
 */
@RestController
@Validated
@RequestMapping("/er/plan")
@Api(value = "", tags = "考场考试计划接口")
public class ErPlanController {

    @Resource
    private ExamPlanApi examPlanApi;

    /**
     * 查询考试计划分页列表
     *
     * @param condition the condition
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/list-page")
    @ApiOperation(value = "分页查询考试计划列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getExamPlanPageList(@RequestBody ExamPlanConditionBo condition) {
        condition.setOrderBy("update_time desc");
        return examPlanApi.getExamPlanPageListByCondition(condition);
    }

    /**
     * 查询考试计划列表
     *
     * @param condition the condition
     * @return the exam plan list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询考试计划列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getExamPlanList(@RequestBody ExamPlanConditionBo condition) {
        condition.setOrderBy("update_time desc");
        return examPlanApi.getExamPlanListByCondition(condition);
    }

    /**
     * 新增考场_考试计划
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增考试计划", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addExamPlan(@RequestBody ExamPlanBo examPlanBo) {
        examPlanBo.setExamPlanType(null);
        return examPlanApi.addExamPlan(examPlanBo);
    }

    /**
     * 修改考场_考试计划
     * 
     * @param examPlanBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/update")
    public AjaxResult updateExamPlan(@RequestBody ExamPlanBo examPlanBo) {
        examPlanBo.setExamPlanType(null);
        return examPlanApi.updateExamPlan(examPlanBo);
    }

    /**
     * 查询考场_考试计划详情
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/detail")
    public AjaxResult<ExamPlanVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("examPlanId") Long examPlanId) {
        return examPlanApi.getDetail(examPlanId);
    }

    /**
     * 删除考场_考试计划
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("examPlanId") Long examPlanId) {
        return examPlanApi.delete(examPlanId);
    }

    /**
     * 发布_考试计划
     *
     * @param examPlanId the exam plan id
     * @return ajax result
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022 -09-29 14:35:17
     */
    @GetMapping("/publish")
    public AjaxResult publish(@NotNull(message = "请选择数据") @RequestParam("examPlanId") Long examPlanId,
        @RequestParam("organizationId") Long organizationId) {
        ExamPlanBo examPlanBo = new ExamPlanBo();
        examPlanBo.setExamPlanId(examPlanId);
        examPlanBo.setExamPlanType(ExamPlanType.PUBLISHED.getValue());
        examPlanBo.setExamPlanName(null);
        examPlanBo.setExamPlanRemark(null);
        examPlanBo.setExamPlanEndTime(null);
        examPlanBo.setExamPlanStartTime(null);
        examPlanBo.setIsDelete(null);
        examPlanBo.setOrganizationId(organizationId);
        return examPlanApi.pubsubExamPlan(examPlanBo);
    }

    /**
     * 取消发布_考试计划
     *
     * @param examPlanId the exam plan id
     * @return ajax result
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022 -09-29 14:35:17
     */
    @GetMapping("/cancel")
    public AjaxResult cancel(@NotNull(message = "请选择数据") @RequestParam("examPlanId") Long examPlanId,
        @RequestParam("organizationId") Long organizationId) {
        ExamPlanBo examPlanBo = new ExamPlanBo();
        examPlanBo.setExamPlanId(examPlanId);
        examPlanBo.setExamPlanType(ExamPlanType.NOT_PUBLISHED.getValue());
        examPlanBo.setExamPlanName(null);
        examPlanBo.setExamPlanRemark(null);
        examPlanBo.setExamPlanEndTime(null);
        examPlanBo.setExamPlanStartTime(null);
        examPlanBo.setIsDelete(null);
        examPlanBo.setOrganizationId(organizationId);
        return examPlanApi.pubsubExamPlan(examPlanBo);
    }
}
