package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.space.api.SpaceDeviceApi;
import com.fh.cloud.screen.service.space.entity.bo.SpaceDeviceRelBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 空间设备关系
 *
 * <AUTHOR>
 * @date 2022 /9/22 20:09
 */
@RestController
@RequestMapping("/space-device")
@Api(value = "空间设备管理", tags = "空间设备管理")
public class SpaceDeviceRelController {

    @Resource
    private SpaceDeviceApi spaceDeviceApi;

    /**
     * 更新空间设备
     *
     * @param spaceDeviceRelBo the space device rel bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "更新空间设备", httpMethod = "POST")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updateSpaceDevice(@RequestBody SpaceDeviceRelBo spaceDeviceRelBo) throws Exception {
        return spaceDeviceApi.updateSpaceDeviceRel(spaceDeviceRelBo);
    }
}
