package com.fh.cloud.screen.web.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import com.fh.cloud.screen.service.er.entity.bo.ImportExamInfoModel;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStatisticsVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoStudentVo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoSubjectVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.er.api.ExamInfoApi;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoBo;
import com.fh.cloud.screen.service.er.entity.bo.ExamInfoConditionBo;
import com.fh.cloud.screen.service.er.entity.vo.ExamInfoVo;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import org.springframework.web.multipart.MultipartFile;

/**
 * 考场考试接口
 *
 * <AUTHOR>
 * @date 2022 /9/29 14:57
 */
@RestController
@Validated
@RequestMapping("/er/exam")
@Api(value = "", tags = "考场考试接口")
public class ErExamController {

    @Resource
    private ExamInfoApi examInfoApi;

    /**
     * 查询考场_考试计划里面一次考试信息分页列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/list-page")
    public AjaxResult<PageInfo<ExamInfoVo>> getExamInfoPageListByCondition(@RequestBody ExamInfoConditionBo condition) {
        condition.setOrderBy("update_time desc");
        return examInfoApi.getExamInfoPageListByCondition(condition);
    }

    /**
     * 查询考场_考试计划里面一次考试信息列表
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/list")
    public AjaxResult<List<ExamInfoVo>> getExamInfoListByCondition(@RequestBody ExamInfoConditionBo condition) {
        condition.setOrderBy("update_time desc");
        return examInfoApi.getExamInfoListByCondition(condition);
    }

    /**
     * 新增考场_考试计划里面一次考试信息（顺带添加科目、学生、老师）
     * 
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/add")
    public AjaxResult addExamInfo(@RequestBody ExamInfoBo examInfoBo) {
        return examInfoApi.addExamInfo(examInfoBo);
    }

    /**
     * 修改考场_考试计划里面一次考试信息（顺带编辑科目、学生、老师）
     * 
     * @param examInfoBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @PostMapping("/update")
    public AjaxResult updateExamInfo(@RequestBody ExamInfoBo examInfoBo) {
        return examInfoApi.updateExamInfo(examInfoBo);
    }

    /**
     * 查询考场_考试计划里面一次考试信息详情（顺带查询科目、学生、老师）
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/detail")
    public AjaxResult<ExamInfoVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("examInfoId") Long examInfoId) {
        return examInfoApi.getDetail(examInfoId);
    }

    /**
     * 删除考场_考试计划里面一次考试信息
     * 
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2022-09-29 14:35:17
     */
    @GetMapping("/delete")
    public AjaxResult delete(@NotNull(message = "请选择数据") @RequestParam("examInfoId") Long examInfoId) {
        return examInfoApi.delete(examInfoId);
    }

    /**
     * 考试导入，模板在前端项目里面
     *
     * @param file, examPlanId, organizationId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/10/17 10:32
     */
    @PostMapping("/import")
    public AjaxResult importExamInfo(MultipartFile file, Long examPlanId, Long organizationId) {
        ImportExamInfoModel importExamInfoModel = new ImportExamInfoModel();
        importExamInfoModel.setFile(file);
        importExamInfoModel.setOrganizationId(organizationId);
        importExamInfoModel.setExamPlanId(examPlanId);
        return examInfoApi.importExamInfo(importExamInfoModel);
    }

    /**
     * 根据考试计划查询一次考试计划的所有考场号信息（带出考试地点冗余展示），复用考试信息列表/er/exam/list
     */

    /**
     * 根据考场号查询所有的考试科目信息
     */
    @GetMapping("/subject/list")
    public AjaxResult<List<ExamInfoSubjectVo>> listSubjectByExamInfoId(@RequestParam("examInfoId") Long examInfoId) {
        return examInfoApi.listSubjectByExamInfoId(examInfoId);
    }

    /**
     * 根据选择的考试科目关系表id查询本场考试的所有学生信息（包含签到和未签到）,参数：examInfoSubjectId,attendanceStatus
     */
    @PostMapping("/attendance-member")
    public AjaxResult<List<ExamInfoStudentVo>> attendanceMember(@RequestBody ExamInfoConditionBo examInfoConditionBo) {
        return examInfoApi.attendanceMember(examInfoConditionBo);
    }

    /**
     * 本次科目考试的总览信息（不根据用户筛选条件变化）,参数：examInfoSubjectId,attendanceStatus
     */
    @PostMapping("/attendance-statistics")
    public AjaxResult<ExamInfoStatisticsVo> attendanceStatistics(@RequestBody ExamInfoConditionBo examInfoConditionBo) {
        return examInfoApi.attendanceStatistics(examInfoConditionBo);
    }
}
