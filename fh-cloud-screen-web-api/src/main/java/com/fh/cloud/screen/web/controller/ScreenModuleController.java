package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.screen.api.ScreenModuleApi;
import com.fh.cloud.screen.service.screen.api.ScreenModuleLibraryApi;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleDataListConditionBo;
import com.fh.cloud.screen.service.screen.entity.bo.ScreenModuleLibraryListConditionBo;
import com.fh.cloud.screen.web.enums.ScreenModuleSourceType;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云屏模块controller
 *
 * <AUTHOR>
 * @date 2022/5/26 14:28
 */
@RestController
@RequestMapping("/screen/module")
@Api(value = "云屏模块管理", tags = "云屏模块管理")
public class ScreenModuleController {
    @Resource
    private ScreenModuleLibraryApi screenModuleLibraryApi;
    @Resource
    private ScreenModuleApi screenModuleApi;

    /**
     * 查询云屏模块库列表
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏模块库列表", httpMethod = "POST")
    @RequestMapping(value = "/library-list", method = RequestMethod.POST)
    public AjaxResult listModuleLibraryByCondition(@RequestBody ScreenModuleLibraryListConditionBo conditionBo)
        throws Exception {
        return screenModuleLibraryApi.getScreenModuleLibraryListByCondition(conditionBo);
    }

    /**
     * 查询云屏模块库分组显示
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏模块库分组显示", httpMethod = "POST")
    @RequestMapping(value = "/library-group", method = RequestMethod.POST)
    public AjaxResult getModuleLibraryMap(@RequestBody ScreenModuleLibraryListConditionBo conditionBo)
        throws Exception {
        return screenModuleLibraryApi.getScreenModuleLibraryGroupMapByCondition(conditionBo);
    }

    /**
     * 查询云屏模块列表
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询云屏模块分组", httpMethod = "POST")
    @RequestMapping(value = "/group", method = RequestMethod.POST)
    public AjaxResult getModuleMap(@RequestBody ScreenModuleDataListConditionBo conditionBo) throws Exception {
        return screenModuleApi.getScreenModuleDataGroupMapByConditionOfSchool(conditionBo);
    }

    /**
     * 学校模块新增自定义模块(仅允许新增自定义模块)
     *
     * @return ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -05-30 15:51:31
     */
    @ApiOperation(value = "学校模块新增自定义模块", httpMethod = "POST")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public AjaxResult addModule(@RequestBody ScreenModuleDataBo screenModuleDataBo) throws Exception {
        screenModuleDataBo.setModuleSource(ScreenModuleSourceType.CUSTOM.getValue());
        return screenModuleApi.addScreenModuleData(screenModuleDataBo);
    }

    /**
     * 学校模块删除自定义模块
     *
     * @return ajax result
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -05-30 15:51:31
     */
    @ApiOperation(value = "学校模块删除自定义模块", httpMethod = "GET")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public AjaxResult deleteModule(@RequestParam("screenModuleDataId") Long screenModuleDataId) throws Exception {
        return screenModuleApi.delete(screenModuleDataId);
    }

    /**
     * 学校模块新增或移除预置模块（注意模块id不可改变）
     *
     * @return
     */
    @ApiOperation(value = "学校模块新增或移除预置模块", httpMethod = "POST")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public AjaxResult updatePresetModule(@RequestBody ScreenModuleDataBo screenModuleDataBo) {
        return screenModuleApi.updatePresetModule(screenModuleDataBo);
    }
}
