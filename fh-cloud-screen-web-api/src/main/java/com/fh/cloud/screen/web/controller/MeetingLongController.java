package com.fh.cloud.screen.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongBo;
import com.fh.cloud.screen.service.meeting.entity.bo.MeetingLongConditionBo;
import com.fh.cloud.screen.service.meeting.service.MeetingLongApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.swagger.constants.SwaggerConstant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * 长期会议表
 * 
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping("/meeting-long")
@Api(value = "", tags = "长期会议表接口")
public class MeetingLongController {

    @Autowired
    private MeetingLongApiService meetingLongApiService;

    /**
     * 查询会议表分页列表,查询我创建的则多传userOid
     * 
     * <AUTHOR>
     * @date 2023-12-11 17:51:00
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询会议表列表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getMeetingPageListByCondition(@RequestBody MeetingLongConditionBo condition) {
        condition.setOrderBy("create_time DESC");
        return meetingLongApiService.getMeetingLongPageListByCondition(condition);
    }

    /**
     * 新增或修改长期会议表
     *
     * @param meetingBo the meeting bo
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -12-11 16:38:36
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增或修改长期会议表", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addMeeting(@RequestBody MeetingLongBo meetingBo) {
        return meetingLongApiService.addMeetingLong(meetingBo);
    }

    /**
     * 查询长期会议表详情
     *
     * @param id the id
     * @return detail detail
     * <AUTHOR>
     * @date 2022 -08-16 17:51:00
     * @returnType AjaxResult
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询长期会议表详情", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "长期会议表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult getDetail(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("长期会议表id不能为空");
        }
        return meetingLongApiService.getDetail(id);
    }

    /**
     * 删除长期会议表
     *
     * @param id the id
     * @return the ajax result
     * <AUTHOR>
     * @date 2023 -12-11 16:38:48
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除长期会议表", httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "长期会议表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG,
        paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@RequestParam("id") Long id) {
        if (null == id) {
            return AjaxResult.fail("请选择需要删除的数据");
        }
        return meetingLongApiService.delete(id);
    }

}
