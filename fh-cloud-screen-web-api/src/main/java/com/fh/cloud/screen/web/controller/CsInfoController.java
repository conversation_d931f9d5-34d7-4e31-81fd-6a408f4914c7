package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.cs.api.CsContactApi;
import com.fh.cloud.screen.service.cs.api.CsLikeApi;
import com.fh.cloud.screen.service.cs.entity.bo.CsContactBo;
import com.fh.cloud.screen.service.cs.entity.bo.CsLikeBo;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * CS文化小站
 *
 * <AUTHOR>
 * @date 2024/01/19 10:31
 */
@Api(tags = "CS文化小站")
@RequestMapping("/cs-info")
@RestController
public class CsInfoController {

    @Resource
    private CsLikeApi csLikeApi;
    @Resource
    private CsContactApi csContactApi;

    /**
     * 新增喜欢记录
     *
     */
    @PostMapping("/add-like")
    @ApiOperation(value = "新增喜欢记录", httpMethod = "POST")
    public AjaxResult addLike(@RequestBody CsLikeBo csLikeBo) {
        return csLikeApi.addCsLike(csLikeBo);
    }

    /**
     * 新增联系人记录
     *
     */
    @PostMapping("/add-contact")
    @ApiOperation(value = "新增联系人记录", httpMethod = "POST")
    public AjaxResult addContact(@RequestBody CsContactBo csContactBo) {
        return csContactApi.addCsContact(csContactBo);
    }
}
