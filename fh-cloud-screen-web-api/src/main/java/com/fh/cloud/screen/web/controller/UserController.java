package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.user.service.UserScreenApiService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserScreenApiService userScreenApiService;

    /**
     * 根据姓名学校查询老师列表
     *
     * @param realName the realName 姓名
     * @param orgId the orgId 组织机构ID
     * @return
     */
    @GetMapping("/teacher/getByRealName")
    @ApiOperation(value = "根据姓名学校查询老师列表")
    public AjaxResult getTeacherListByRealName(@RequestParam("realName") String realName,
        @RequestParam("orgId") Long orgId) {
        return this.userScreenApiService.getTeacherListByRealName(realName, orgId);
    }

    /**
     * 根据班级id查询学生列表
     *
     * @param classesId the classes id
     * @return student list by classes id
     */
    @GetMapping("/student/list")
    @ApiOperation(value = "根据班级id查询学生列表")
    public AjaxResult getStudentListByClassesId(@RequestParam("classesId") Long classesId) {
        return this.userScreenApiService.getStudentListByClassesId(classesId);
    }

    /**
     * 根据班级id查询学生列表
     *
     * @param organizationId the organization id
     * @return student list by classes id
     */
    @GetMapping("/teacher/list")
    @ApiOperation(value = "根据班级id查询学生列表")
    public AjaxResult getTeacherListByOrganizationId(@RequestParam("organizationId") Long organizationId) {
        return this.userScreenApiService.getTeacherListByOrganizationId(organizationId);
    }
}
