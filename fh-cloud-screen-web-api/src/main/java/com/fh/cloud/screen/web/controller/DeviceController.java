package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.device.api.ShowDeviceApi;
import com.fh.cloud.screen.service.device.api.ShowDeviceCaptureApi;
import com.fh.cloud.screen.service.device.entity.bo.*;
import com.fh.cloud.screen.service.device.entity.vo.ShowDevicePatrolVo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceVo;
import com.fh.cloud.screen.service.wx.api.WxMsgSubDeviceApi;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 设备管理器
 *
 * <AUTHOR>
 * @date 2022/5/11 14:38
 */
@RestController
@RequestMapping("/device")
@Api(value = "设备管理", tags = "设备管理")
public class DeviceController {
    @Resource
    private ShowDeviceApi showDeviceApi;
    @Resource
    private ShowDeviceCaptureApi showDeviceCaptureApi;
    @Resource
    private WxMsgSubDeviceApi wxMsgSubDeviceApi;

    /**
     * 查询绑定了地点的设备列表
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查询绑定了地点的设备列表", httpMethod = "POST")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public AjaxResult listShowDeviceDataByCondition(@RequestBody ShowDeviceListConditionBo conditionBo)
        throws Exception {
        return showDeviceApi.listShowDeviceDataByCondition(conditionBo);
    }

    /**
     * 开机关机
     *
     * @param showDeviceOperateBo the show device operate bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "开机关机", httpMethod = "POST")
    @RequestMapping(value = "/operate", method = RequestMethod.POST)
    public AjaxResult listShowDeviceDataByCondition(@RequestBody ShowDeviceOperateBo showDeviceOperateBo)
        throws Exception {
        return showDeviceApi.switchOperate(showDeviceOperateBo);
    }

    /**
     * 根据二维码加密信息查询非加密信息
     *
     * @param conditionBo the condition bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "根据二维码加密信息查询非加密信息", httpMethod = "GET")
    @RequestMapping(value = "/qr-decode", method = RequestMethod.GET)
    public AjaxResult qrDecode(@RequestParam("qrcodeContent") String qrcodeContent) throws Exception {
        return showDeviceApi.qrDecode(qrcodeContent);
    }

    /**
     * 用户发起一次截图请求
     *
     * @param showDeviceCaptureBo the show device capture bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "用户发起一次截图请求", httpMethod = "POST")
    @RequestMapping(value = "/capture/launch", method = RequestMethod.POST)
    public AjaxResult captureLaunch(@RequestBody ShowDeviceCaptureBo showDeviceCaptureBo) throws Exception {
        return showDeviceCaptureApi.launchShowDeviceCapture(showDeviceCaptureBo);
    }

    /**
     * 查看设备的最后一次截图请求的数据
     *
     * @param deviceNumber the device number
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "查看设备的最后一次截图请求的数据", httpMethod = "GET")
    @RequestMapping(value = "/capture/last", method = RequestMethod.GET)
    public AjaxResult captureLast(@RequestParam("deviceNumber") String deviceNumber) throws Exception {
        return showDeviceCaptureApi.getDetailByNumber(deviceNumber);
    }

    /**
     * 根据设备id 设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @ApiOperation(value = "设置海报推送规则")
    @RequestMapping(value = "/set/poster", method = RequestMethod.POST)
    public AjaxResult setPosterRule(@RequestBody ShowDeviceBo showDeviceBo) {
        return showDeviceApi.setPosterRule(showDeviceBo);
    }

    /**
     * 根据设备所属空间 批量设置设备是否主动推送及设备标签列表
     *
     * @param showDeviceBo the pushType labelBos
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @ApiOperation(value = "批量设置海报推送规则")
    @RequestMapping(value = "/set/poster-batch", method = RequestMethod.POST)
    public AjaxResult setPosterRuleBatch(@RequestBody ShowDeviceBo showDeviceBo) {
        return showDeviceApi.setPosterRuleBatch(showDeviceBo);
    }

    /**
     * 根据设备id 获取设备主动推送及设备标签列表
     *
     * @param showDeviceId
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/3/27 15:23
     */
    @ApiOperation(value = "获取海报推送规则")
    @RequestMapping(value = "/get/poster", method = RequestMethod.GET)
    public AjaxResult getPosterRule(@RequestParam("showDeviceId") Long showDeviceId) {
        return showDeviceApi.getPosterRule(showDeviceId);
    }

    /**
     * 更新设备横竖版、是否全屏、海报播放间隔时长(通知app设备变更)
     *
     * @param showDeviceBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/4/18 15:12
     */
    @ApiOperation(value = "更新设备横竖版、是否全屏、海报播放间隔时长")
    @RequestMapping(value = "/update/format", method = RequestMethod.POST)
    public AjaxResult updateDeviceByDeviceNumberWithSendApp(@RequestBody ShowDeviceBo showDeviceBo) {
        return showDeviceApi.updateDeviceByDeviceNumberWithSendApp(showDeviceBo);
    }

    /**
     * 查询设备巡查记录
     *
     * @param deviceNumber
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/4 11:42
     **/
    @ApiOperation(value = "查询设备巡查记录")
    @GetMapping("/get/patrol")
    public AjaxResult getPatrolByDeviceNumber(@RequestParam("deviceNumber") String deviceNumber) {
        AjaxResult<ShowDeviceVo> showDeviceVoResult = showDeviceApi.getByDeviceNumber(deviceNumber);
        if (showDeviceVoResult.isSuccess() && showDeviceVoResult.getData() != null) {
            ShowDevicePatrolVo showDevicePatrolVo = new ShowDevicePatrolVo();
            BeanUtils.copyProperties(showDeviceVoResult.getData(), showDevicePatrolVo);
            return AjaxResult.success(showDevicePatrolVo);
        }
        return showDeviceVoResult;
    }

    /**
     * 更新设备巡查记录
     *
     * @param showDeviceBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2023/12/4 11:44
     **/
    @ApiOperation(value = "更新设备巡查记录")
    @PostMapping("/update/patrol")
    public AjaxResult updatePatrolByDeviceNumber(@RequestBody ShowDeviceBo showDeviceBo) {
        // 设置请求数据，防止更新其他信息
        ShowDeviceBo bo = new ShowDeviceBo();
        bo.setShowDeviceId(showDeviceBo.getShowDeviceId());
        bo.setPatrolRemark(showDeviceBo.getPatrolRemark());
        bo.setPatrolType(showDeviceBo.getPatrolType());
        return showDeviceApi.updateShowDevice(bo);
    }

    /**
     * 查询关注设备数
     *
     * @return
     */
    @ApiOperation("查询关注设备数")
    @GetMapping("/wx-msg-sub/count")
    public AjaxResult getWxMsgSubCount() {
        return wxMsgSubDeviceApi.wxMsgSubCount();
    }

    /**
     * 根据监管教育局id查询绑定了地点的设备列表
     *
     * @param conditionBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2024/8/5 14:49
     **/
    @ApiOperation(value = "根据监管教育局id查询绑定了地点的设备列表")
    @RequestMapping(value = "/list-by-parentId", method = RequestMethod.POST)
    public AjaxResult listShowDeviceDataByParentOrganizationId(@RequestBody ShowDeviceListConditionBo conditionBo) {
        return showDeviceApi.listShowDeviceDataByParentOrganizationId(conditionBo);
    }

    /**
     * 批量开机关机
     *
     * @param operateListBo the show device operate bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 14:44:04
     */
    @ApiOperation(value = "开机关机", httpMethod = "POST")
    @RequestMapping(value = "/operate-batch", method = RequestMethod.POST)
    public AjaxResult listShowDeviceDataBatchByCondition(@RequestBody ShowDeviceOperateListBo operateListBo)
            throws Exception {
        return showDeviceApi.switchOperateBatch(operateListBo);
    }

    /**
     * 修改设备人脸建模类型-支持批量
     *
     * @param showDeviceOperateBo the show device operate bo
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2024 -09-23 11:44:04
     */
    @ApiOperation(value = "开机关机", httpMethod = "POST")
    @RequestMapping(value = "/facemod-change", method = RequestMethod.POST)
    public AjaxResult changeFaceMod(@RequestBody ShowDeviceOperateBo showDeviceOperateBo)
            throws Exception {
        return showDeviceApi.changeFaceMod(showDeviceOperateBo);
    }

}
