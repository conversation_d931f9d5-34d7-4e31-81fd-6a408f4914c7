package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.face.api.FaceConfigApi;
import com.fh.cloud.screen.service.face.entity.bo.FaceConfigBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 人脸管理配置接口
 * 
 * <AUTHOR>
 * @date 2022/11/18 16:15
 */
@RestController
@RequestMapping("/face/config")
@Api(value = "", tags = "人脸配置接口")
public class FaceConfigController {
    @Resource
    private FaceConfigApi faceConfigApi;

    /**
     * 查看阈值数据等参数，参数包含organizationId
     *
     * @param organizationId the organization id
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查看阈值数据", httpMethod = SystemConstants.GET_REQUEST)
    public AjaxResult getThreshold(@RequestParam("organizationId") Long organizationId) {
        return faceConfigApi.getDetailByOrganizationId(organizationId);
    }

    /**
     * 新增或修改阈值数据等参数
     *
     * @param faceConfigBo the face config bo
     * @return the exam plan page list
     * <AUTHOR>
     * @date 2022 -09-29 17:51:00
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或修改阈值数据", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult saveThreshold(@RequestBody FaceConfigBo faceConfigBo) {
        return faceConfigApi.saveFaceConfig(faceConfigBo);
    }
}
