package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.enums.ScreenConfigType;
import com.fh.cloud.screen.service.screenConfig.api.ScreenConfigApi;
import com.fh.cloud.screen.service.screenConfig.entity.bo.ScreenConfigConditionBo;
import com.fh.cloud.screen.service.screenConfig.entity.vo.ScreenConfigVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 云屏配置controller
 *
 * @Author: liuzeyu
 * @CreateTime: 2024-07-29  14:37
 */
@RestController
@RequestMapping("/screen/config")
@Slf4j
@Api("云屏配置controller")
public class ScreenConfigController {
    @Resource
    private ScreenConfigApi screenConfigApi;

    /**
     * 查询偏好配置
     *
     * @param organizationId
     * @return
     */
    @ApiOperation("查询偏好配置")
    @GetMapping("/get-preference-config")
    public AjaxResult getPreferenceConfig(@RequestParam Long organizationId) {
        ScreenConfigConditionBo condition = new ScreenConfigConditionBo();
        condition.setOrganizationId(organizationId);
        condition.setType(ScreenConfigType.PREFERENCE.getCode());
        return screenConfigApi.getByOrganizationIdAndType(condition);
    }
}
