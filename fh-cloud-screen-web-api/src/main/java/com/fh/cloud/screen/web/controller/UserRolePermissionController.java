package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import com.fh.app.role.service.role.entity.bo.RoleDataAuthorityConditionBo;
import com.fh.cloud.screen.service.baseinfo.BaseDataService;
import com.fh.cloud.screen.service.role.service.UserRolePermissionApiService;
import com.fh.sso.service.index.entity.bo.IndexListConditionBo;
import org.springframework.web.bind.annotation.*;

import com.fh.app.role.service.role.entity.bo.RoleAppRelConditionBo;

import com.light.core.entity.AjaxResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2022/7/20
 */
@RestController
@RequestMapping("/role")
@Api(value = "获取用户角色权限", tags = "获取用户角色权限")
public class UserRolePermissionController {

    @Resource
    private UserRolePermissionApiService userRolePermissionApiService;

    @ApiOperation(value = "获取用户功能权限", httpMethod = "POST")
    @PostMapping("/permission/list")
    public AjaxResult getUserDataAuthority(@RequestBody RoleAppRelConditionBo condition) {
        return userRolePermissionApiService.getUserPermissionAuthority(condition);
    }

    @ApiOperation(value = "获取用户最大数据权限", httpMethod = "POST")
    @PostMapping("/max/data/authority")
    public AjaxResult getUserMaxDataAuthority(@RequestBody RoleDataAuthorityConditionBo conditionBo) {
        return userRolePermissionApiService.getUserMaxDataAuthority(conditionBo);
    }

    @ApiOperation(value = "获取用户角色codes", httpMethod = "POST")
    @PostMapping("/codes")
    public AjaxResult getUserRoleCodes() {
        return userRolePermissionApiService.getUserRoleCodes();
    }

    @ApiOperation(value = "获取用户应用信息", httpMethod = "POST")
    @PostMapping("/user/app/info")
    public AjaxResult getUserAppInfo(@RequestBody IndexListConditionBo conditionBo) {
        return userRolePermissionApiService.getUserAppInfo(conditionBo);
    }
}
