package com.fh.cloud.screen.web.controller;

import javax.annotation.Resource;

import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubConfigBo;
import com.fh.cloud.screen.service.wx.entity.bo.WxMsgSubDeviceBo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fh.cloud.screen.service.wx.api.WxMsgSubConfigApi;
import com.fh.cloud.screen.service.wx.api.WxMsgSubDeviceApi;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.StringUtils;
import com.light.security.service.CurrentUserService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 微信消息订阅controller
 * 
 * <AUTHOR>
 * @date 2024/4/2 15:10
 */
@Api(tags = "微信消息订阅")
@RestController
@RequestMapping("/wx-msg")
public class WxMsgSubController {

    @Resource
    private WxMsgSubConfigApi wxMsgSubConfigApi;
    @Resource
    private WxMsgSubDeviceApi wxMsgSubDeviceApi;
    @Resource
    private CurrentUserService currentUserService;

    /**
     * 查询用户微信消息订阅设置
     * 
     * @return
     */
    @ApiOperation(value = "查询用户微信消息订阅设置", httpMethod = "GET")
    @GetMapping("/config/detail")
    public AjaxResult getWxMsgSubConfig() {
        String currentOid = currentUserService.getCurrentOid();
        if (StringUtils.isBlank(currentOid)) {
            return AjaxResult.fail("未获取到用户信息");
        }
        return wxMsgSubConfigApi.getDetailByUserOid(currentOid);
    }

    /**
     * 保存用户微信消息订阅设置
     * 
     * @param wxMsgSubConfigBo
     * @return
     */
    @ApiOperation(value = "保存用户微信消息订阅设置", httpMethod = "POST")
    @PostMapping("/config/save")
    public AjaxResult saveWxMsgSubConfig(@RequestBody WxMsgSubConfigBo wxMsgSubConfigBo) {
        String currentOid = currentUserService.getCurrentOid();
        if (StringUtils.isBlank(currentOid)) {
            return AjaxResult.fail("未获取到用户信息");
        }
        wxMsgSubConfigBo.setUserOid(currentOid);
        return wxMsgSubConfigApi.saveWxMsgSubConfig(wxMsgSubConfigBo);
    }

    /**
     * 关注/取消关注设备
     * 
     * @param wxMsgSubDeviceBo
     * @return
     */
    @ApiOperation(value = "关注/取消关注设备", httpMethod = "POST")
    @PostMapping("/device/follow")
    public AjaxResult followDevice(@RequestBody WxMsgSubDeviceBo wxMsgSubDeviceBo) {
        String currentOid = currentUserService.getCurrentOid();
        if (StringUtils.isBlank(currentOid)) {
            return AjaxResult.fail("未获取到用户信息");
        }
        wxMsgSubDeviceBo.setUserOid(currentOid);
        return wxMsgSubDeviceApi.followDevice(wxMsgSubDeviceBo);
    }
}
