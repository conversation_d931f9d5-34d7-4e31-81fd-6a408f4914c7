package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.device.service.ShowDeviceLabelRelApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.light.swagger.constants.SwaggerConstant;
import com.light.core.constants.SystemConstants;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelConditionBo;
import com.fh.cloud.screen.service.device.entity.bo.ShowDeviceLabelRelBo;
import com.fh.cloud.screen.service.device.entity.vo.ShowDeviceLabelRelVo;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 设备订阅标签表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-23 11:26:35
 */
@RestController
@Validated
@RequestMapping("show/device/label/rel")
@Api(value = "", tags = "设备订阅标签表接口" )
public class DeviceLabelRelController {

    @Autowired
    private ShowDeviceLabelRelApiService showDeviceLabelRelApiService;

    /**
     * 查询设备订阅标签表分页列表
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("/page/list")
    @ApiOperation(value = "分页查询设备订阅标签表列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getShowDeviceLabelRelPageListByCondition(@RequestBody ShowDeviceLabelRelConditionBo condition){
        PageInfo<ShowDeviceLabelRelVo> page = showDeviceLabelRelApiService.getShowDeviceLabelRelPageListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", page.getList());
        map.put("total", page.getTotal());
        return AjaxResult.success(map);
    }

    /**
     * 查询设备订阅标签表列表
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("/list")
    @ApiOperation(value = "查询设备订阅标签表列表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult getShowDeviceLabelRelListByCondition(@RequestBody ShowDeviceLabelRelConditionBo condition){
        List<ShowDeviceLabelRelVo> list = showDeviceLabelRelApiService.getShowDeviceLabelRelListByCondition(condition).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return AjaxResult.success(map);
    }


    /**
     * 新增设备订阅标签表
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增设备订阅标签表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult addShowDeviceLabelRel(@RequestBody ShowDeviceLabelRelBo showDeviceLabelRelBo){
        return showDeviceLabelRelApiService.addShowDeviceLabelRel(showDeviceLabelRelBo);
    }

    /**
     * 修改设备订阅标签表
     * @param showDeviceLabelRelBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新设备订阅标签表",httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult updateShowDeviceLabelRel(@RequestBody ShowDeviceLabelRelBo showDeviceLabelRelBo) {
        if(null == showDeviceLabelRelBo.getId()) {
            return AjaxResult.fail("设备订阅标签表id不能为空");
        }
        return showDeviceLabelRelApiService.updateShowDeviceLabelRel(showDeviceLabelRelBo);
    }

    /**
     * 查询设备订阅标签表详情
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询设备订阅标签表详情",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "设备订阅标签表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult<ShowDeviceLabelRelVo> getDetail(@RequestParam("id") Long id) {
        if(null == id) {
            return AjaxResult.fail("设备订阅标签表id不能为空");
        }
        ShowDeviceLabelRelVo vo = showDeviceLabelRelApiService.getDetail(id).getData();
        Map<String, Object> map = new HashMap<>();
        map.put("showDeviceLabelRelVo", vo);
        return AjaxResult.success(map);
    }

    /**
     * 删除设备订阅标签表
     * @param id
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2023-03-23 11:26:35
     */
    @GetMapping("/delete")
    @ApiOperation(value = "删除设备订阅标签表",httpMethod = SystemConstants.GET_REQUEST)
    @ApiImplicitParam(name = "id", value = "设备订阅标签表id", required = true, dataType = SwaggerConstant.DATA_TYPE_LONG, paramType = SwaggerConstant.PARAM_TYPE_QUERY)
    public AjaxResult delete(@NotNull(message = "ID不能为空") Long id) {
        return showDeviceLabelRelApiService.delete(id);
    }
}
