package com.fh.cloud.screen.web.controller;

import com.fh.cloud.screen.service.attendance.api.AttendanceRuleApi;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleAddBo;
import com.fh.cloud.screen.service.attendance.entity.bo.AttendanceRuleBo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * 考勤规则表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-25 15:33:10
 */
@RestController
@Api(value = "", tags = "考勤规则接口")
@RequestMapping("/attendance-rule")
public class AttendanceRuleController {

    @Resource
    private AttendanceRuleApi attendanceRuleApi;

    /**
     * 新增或修改考勤规则表
     *
     * @param attendanceRuleAddBo
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/6 16:07
     */
    @PostMapping("/save-or-update")
    @ApiOperation(value = "新增或修改考勤规则表", httpMethod = "POST")
    public AjaxResult saveOrUpdateAttendanceRule(@Validated @RequestBody AttendanceRuleAddBo attendanceRuleAddBo) {
        return attendanceRuleApi.saveOrUpdateAttendanceRule(attendanceRuleAddBo);
    }

    /**
     * 查询考勤规则表详情
     *
     * @param attendanceRuleBo 根据组织id查询，必填
     * @return com.light.core.entity.AjaxResult
     * <AUTHOR>
     * @date 2022/6/8 15:04
     */
    @PostMapping("/detail")
    @ApiOperation(value = "查询考勤规则表详情", httpMethod = "POST")
    public AjaxResult getDetail(@RequestBody AttendanceRuleBo attendanceRuleBo) {
        if (null == attendanceRuleBo.getOrganizationId()) {
            return AjaxResult.fail("参数错误");
        }
        return attendanceRuleApi.getDetail(attendanceRuleBo);
    }

}
