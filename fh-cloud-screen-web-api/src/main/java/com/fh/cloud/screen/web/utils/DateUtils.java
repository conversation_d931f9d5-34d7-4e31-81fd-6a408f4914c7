package com.fh.cloud.screen.web.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/3
 */
public class DateUtils {
    /**
     * 获取指定两个日期范围内指定月份的所有天
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param month 指定月份 "yyyy-mm
     * @return
     */
    public static List<Date> getDateListByStartAndEndDate(Date startDate, Date endDate, String month) {
        String[] split = month.split("-");
        List<Date> dateList = new ArrayList<>();
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            // 开始日期小于结束日期，开始日期+1天
            while (endDate.after(calendar.getTime()) || endDate.compareTo(calendar.getTime()) == 0) {
                // 同年同月
                if (Integer.parseInt(split[0]) == calendar.get(Calendar.YEAR)
                    && calendar.get(Calendar.MONTH) + 1 == Integer.parseInt(split[1])) {
                    dateList.add(calendar.getTime());
                }
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dateList;
    }

    /**
     * 获取日期对应的星期几，注：星期天为7。将java语言定义的week定义转换成我们业务的week定义（周一到周日为1-7）
     *
     * @param date 日期
     * @return 星期几，星期天为7
     */
    public static int getWeekIdByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int weekId = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (weekId == 0) {
            return 7;
        }
        return weekId;
    }

}
