<?xml version="1.0" encoding="UTF-8"?>

<configuration>

    <springProperty scope="context" name="logPath" source="logging.path" defaultValue="default"/>
    <springProperty scope="context" name="applicationName" source="spring.application.name" defaultValue="service"/>
    <springProperty scope="context" name="logstashServer" source="logging.server.host" defaultValue="127.0.0.1"/>

    <!-- 日志输出格式
     %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n
     -->

    <!-- 控制台 appender-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level ---[${applicationName},traceId:%X{traceId},spanId:%X{spanId}][ %thread] %logger{50} - %msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <!-- 文件 滚动日志 (all)-->
    <!--    <appender name="servicelog"  class="ch.qos.logback.core.rolling.RollingFileAppender">-->
    <!--        &lt;!&ndash; 当前日志输出路径、文件名 &ndash;&gt;-->
    <!--        <file>${logPath}/${applicationName}.log</file>-->
    <!--        &lt;!&ndash;日志输出格式&ndash;&gt;-->
    <!--        <encoder>-->
    <!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level -&#45;&#45;[ ${applicationName} traceId:%X{X-B3-TraceId:-}][     %thread] %logger{50} - %msg%n</pattern>-->
    <!--            <charset>UTF-8</charset>-->
    <!--        </encoder>-->
    <!--        &lt;!&ndash;历史日志归档策略&ndash;&gt;-->
    <!--        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
    <!--            &lt;!&ndash; 历史日志： 归档文件名 &ndash;&gt;-->
    <!--            <fileNamePattern>${logPath}/%d{yyyy-MM, aux}/${applicationName}All.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>-->
    <!--            &lt;!&ndash;单个文件的最大大小&ndash;&gt;-->
    <!--            <maxFileSize>64MB</maxFileSize>-->
    <!--            &lt;!&ndash;日志文件保留天数&ndash;&gt;-->
    <!--            <maxHistory>15</maxHistory>-->
    <!--        </rollingPolicy>-->
    <!--    </appender>-->


    <!-- 文件 滚动日志 (仅error)-->
    <appender name="errorlog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 当前日志输出路径、文件名 -->
        <file>${logPath}/${applicationName}Error.log</file>
        <!--日志输出格式-->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level ---[
                ${applicationName},traceId:%X{traceId},spanId:%X{spanId}][ %thread] %logger{50} - %msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!--历史日志归档策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 历史日志： 归档文件名 -->
            <fileNamePattern>${logPath}/%d{yyyy-MM, aux}/${applicationName}error.%d{yyyy-MM-dd}.%i.log.gz
            </fileNamePattern>
            <!--单个文件的最大大小-->
            <maxFileSize>64MB</maxFileSize>
            <!--日志文件保留天数-->
            <maxHistory>180</maxHistory>
        </rollingPolicy>

        <!-- 此日志文档只记录error级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--输出到logstash的appender-->
    <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <!--可以访问的logstash日志收集端口-->
        <destination>${logstashServer}</destination>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <logger name="com.alibaba.nacos.client.config" level="OFF"/>
    <logger name="com.alibaba.nacos.client.naming" level="OFF"/>
    <logger name="com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.grpc.netty" level="OFF"/>


    <!-- root 级别的配置 -->
    <root level="debug">
        <appender-ref ref="CONSOLE"/>
        <!--        <appender-ref ref="servicelog" />-->
        <appender-ref ref="errorlog"/>
        <appender-ref ref="LOGSTASH"/>
    </root>


</configuration>